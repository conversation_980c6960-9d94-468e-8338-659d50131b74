package com.stpl.tech.attendance.domain;
import java.sql.Timestamp;
public class ShiftCoveragePlan extends ShiftCoveragePlanAbstract
{
	public ShiftCoveragePlan(Timestamp businessDate
	, Timestamp processingDate
	)
	{
		super(businessDate
		,processingDate
		);
		// You must not modify this constructor. <PERSON><PERSON><PERSON> calls this internally.
		// You can call this constructor. You can also add new constructors.
	}

	public ShiftCoveragePlan(Timestamp businessDate)
	{
		super(businessDate);
	}
}
