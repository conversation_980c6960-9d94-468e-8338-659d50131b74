# Attendance Metadata System

## Overview

The Attendance Metadata System provides a flexible, department-wise configuration system for attendance-related settings. It allows organizations to configure different attendance policies for different departments while maintaining default values for common configurations.

## Table Structure

### EMP_ATTENDANCE_METADATA

| Column | Type | Description | Constraints |
|--------|------|-------------|-------------|
| EMP_ATTENDANCE_METADATA_ID | INT | Primary key | AUTO_INCREMENT |
| DEPT_ID | INT | Department ID | NOT NULL |
| ATTRIBUTE_ID | INT | Attribute identifier | NULL |
| ATTRIBUTE_CODE | VARCHAR(45) | Attribute type enum | NOT NULL |
| ATTRIBUTE_TYPE | VARCHAR(45) | Human-readable type | NULL |
| ATTRIBUTE_VALUE | VARCHAR(45) | Configuration value | NULL |
| MAPPING_STATUS | VARCHAR(45) | Status (ACTIVE/INACTIVE) | NOT NULL |
| CREATED_BY | VARCHAR(100) | Creator user | NULL |
| UPDATED_BY | VARCHAR(100) | Last updater | NULL |
| CREATED_AT | TIMESTAMP | Creation timestamp | DEFAULT CURRENT_TIMESTAMP |
| UPDATED_AT | TIMESTAMP | Update timestamp | DEFAULT CURRENT_TIMESTAMP ON UPDATE |

## Attribute Types

### Boolean Attributes
- `IS_LEAVE_ALLOWED` - Whether leave is allowed for the department
- `IS_OD_ALLOWED` - Whether Out Duty (OD) is allowed
- `IS_WFH_ALLOWED` - Whether Work From Home (WFH) is allowed
- `IS_REGULARISATION_ALLOWED` - Whether attendance regularisation is allowed
- `IS_HOLIDAY_ALLOWED` - Whether holidays are allowed for the department

### Cycle Attributes
- `LEAVE_CREDIT_CYCLE` - Leave credit cycle (YEARLY, QUARTERLY, HALF_YEAR)

### Numeric Attributes
- `TOTAL_NUMBER_OF_LEAVES` - Total leaves allowed per year
- `PAYROLL_PROCESSING_START_DAY` - Payroll processing start day
- `PAYROLL_PROCESSING_END_DAY` - Payroll processing end day

### Weekend Days Attributes
- `WEEKEND_DAYS` - Comma-separated weekend days (e.g., "SATURDAY,SUNDAY")

## Default Configuration

The system uses `DEPT_ID = -1` to store default configurations that apply when no department-specific configuration exists. This ensures that all departments have fallback values.

## Architecture Components

### 1. Entity (`EmpAttendanceMetadata`)
- JPA entity representing the database table
- Includes helper methods for value type conversion
- Supports audit fields (created/updated by/at)

### 2. Holiday Entity (`EmpHoliday`)
- JPA entity for storing system-wide holidays
- Supports financial year grouping
- Includes full day/half day holiday types
- Audit fields for tracking changes

### 3. Repository (`EmpAttendanceMetadataRepository`)
- Extends JpaRepository for basic CRUD operations
- Custom queries for finding metadata by department and attribute
- Special query for finding default metadata (DEPT_ID = -1)

### 4. Holiday Repository (`EmpHolidayRepository`)
- Extends JpaRepository for holiday CRUD operations
- Custom queries for financial year and date range searches
- Methods for checking holiday existence and pattern matching

### 5. Cache Service (`AttendanceMetadataCacheService`)
- Spring Cache integration with `@Cacheable`, `@CachePut`, `@CacheEvict`
- Cache key format: `{deptId}_{attributeCode}`
- Automatic fallback logic: department-specific → default values
- Cache refresh capabilities

### 6. Service (`AttendanceMetadataService`)
- Business logic for metadata operations
- Type-safe methods for different attribute types
- Convenience methods for common checks (isLeaveAllowed, isOdAllowed, etc.)
- Bulk operations support

### 7. Holiday Service (`HolidayService`)
- Business logic for holiday management
- Financial year-based holiday operations
- Date range and pattern-based searches
- Holiday validation and management

### 8. Controller (`AttendanceMetadataController`)
- RESTful API endpoints following project standards
- Extends `BaseController` for consistent response handling
- Comprehensive CRUD operations
- Cache management endpoints

### 9. Holiday Controller (`HolidayController`)
- RESTful API endpoints for holiday management
- Financial year and date-based operations
- Holiday CRUD operations
- Search and validation endpoints

## Usage Examples

### Basic Usage

```java
@Autowired
private AttendanceMetadataService metadataService;

// Check if leave is allowed for department 100
boolean isLeaveAllowed = metadataService.isLeaveAllowed(100);

// Get total number of leaves for department 100
BigDecimal totalLeaves = metadataService.getTotalNumberOfLeaves(100);

// Get leave credit cycle
String cycle = metadataService.getLeaveCreditCycle(100);

// Check if holidays are allowed
boolean holidaysAllowed = metadataService.isHolidayAllowed(100);

// Get weekend days
String weekendDays = metadataService.getWeekendDays(100);

// Check if fixed weekend is allowed
boolean fixedWeekendAllowed = metadataService.isFixedWeekendAllowed(100);
```

### Advanced Usage

```java
// Get all metadata for a department
Map<AttendanceAttributeType, AttendanceMetadataResponse> allMetadata = 
    metadataService.getAllMetadataForDept(100);

// Get specific metadata value
AttendanceMetadataResponse response = 
    metadataService.getMetadataValue(100, AttendanceAttributeType.IS_WFH_ALLOWED);

// Check if WFH is allowed
boolean isWfhAllowed = response.getBooleanValue();
```

### Creating/Updating Metadata

```java
EmpAttendanceMetadata metadata = EmpAttendanceMetadata.builder()
    .deptId(100)
    .attributeCode(AttendanceAttributeType.IS_WFH_ALLOWED)
    .attributeType("BOOLEAN")
    .attributeValue("true")
    .mappingStatus(MappingStatus.ACTIVE)
    .createdBy("ADMIN_USER")
    .build();

EmpAttendanceMetadata saved = metadataService.saveMetadata(metadata);
```

### Holiday Management Usage

```java
@Autowired
private HolidayService holidayService;

// Get holidays for current financial year
List<HolidayResponse> holidays = holidayService.getCurrentFinancialYearHolidays();

// Check if a date is a holiday
boolean isHoliday = holidayService.isHoliday(LocalDate.of(2025, 1, 26));

// Get holidays for a specific financial year
List<HolidayResponse> fyHolidays = holidayService.getHolidaysByFinancialYear("2024-25");

// Create a new holiday
EmpHoliday newHoliday = EmpHoliday.builder()
    .financialYear("2024-25")
    .holidayName("Republic Day")
    .holidayDate(LocalDate.of(2025, 1, 26))
    .isFullDay(true)
    .createdBy("ADMIN_USER")
    .build();

EmpHoliday saved = holidayService.saveHoliday(newHoliday);
```

## API Endpoints

### GET Operations
- `GET /api/v1/attendance/metadata/{deptId}` - Get all metadata for a department
- `GET /api/v1/attendance/metadata/{deptId}/{attributeCode}` - Get specific metadata
- `GET /api/v1/attendance/metadata/{deptId}/leave-allowed` - Check if leave is allowed
- `GET /api/v1/attendance/metadata/{deptId}/od-allowed` - Check if OD is allowed
- `GET /api/v1/attendance/metadata/{deptId}/wfh-allowed` - Check if WFH is allowed
- `GET /api/v1/attendance/metadata/{deptId}/regularisation-allowed` - Check if regularisation is allowed
- `GET /api/v1/attendance/metadata/{deptId}/leave-credit-cycle` - Get leave credit cycle
- `GET /api/v1/attendance/metadata/{deptId}/total-leaves` - Get total number of leaves
- `GET /api/v1/attendance/metadata/{deptId}/entities` - Get all metadata entities

### POST Operations
- `POST /api/v1/attendance/metadata` - Create/update metadata
- `POST /api/v1/attendance/metadata/bulk` - Bulk create/update metadata
- `POST /api/v1/attendance/metadata/{deptId}/refresh-cache` - Refresh cache for department
- `POST /api/v1/attendance/metadata/refresh-all-cache` - Refresh all cache

## Holiday Management API Endpoints

### GET Operations
- `GET /api/v1/attendance/holidays/current` - Get holidays for current financial year
- `GET /api/v1/attendance/holidays/financial-year/{financialYear}` - Get holidays for specific financial year
- `GET /api/v1/attendance/holidays/date-range?startDate={date}&endDate={date}` - Get holidays for date range
- `GET /api/v1/attendance/holidays/check/{date}` - Check if a specific date is a holiday
- `GET /api/v1/attendance/holidays/date/{date}` - Get holiday information for a specific date
- `GET /api/v1/attendance/holidays/financial-years` - Get all available financial years
- `GET /api/v1/attendance/holidays/search?namePattern={pattern}` - Search holidays by name pattern

### POST Operations
- `POST /api/v1/attendance/holidays` - Create/update holiday

### DELETE Operations
- `DELETE /api/v1/attendance/holidays/{holidayId}` - Delete holiday by ID

### Cache Management Operations
- `POST /api/v1/attendance/holidays/cache/refresh/{financialYear}` - Refresh cache for specific financial year
- `POST /api/v1/attendance/holidays/cache/evict-all` - Evict all holiday cache entries
- `POST /api/v1/attendance/holidays/cache/evict/{financialYear}` - Evict cache for specific financial year

## Caching Strategy

### Cache Keys
- Format: `{deptId}_{attributeCode}`
- Example: `100_IS_LEAVE_ALLOWED`

### Cache Operations
- **Read**: `@Cacheable` - Returns cached value or null (cache miss)
- **Write**: `@CachePut` - Updates cache with new value
- **Delete**: `@CacheEvict` - Removes specific or all cache entries

### Fallback Logic
1. Check cache for department-specific value
2. If not found, check database for department-specific value
3. If not found, check database for default value (DEPT_ID = -1)
4. Cache the resolved value for future use

## Holiday Caching Strategy

### Cache Keys
- **Financial Year**: `financial_year_{financialYear}` (e.g., `financial_year_2024-25`)
- **Date Range**: `date_range_{startDate}_{endDate}` (e.g., `date_range_2025-01-01_2025-12-31`)
- **Specific Date**: `date_{date}` (e.g., `date_2025-01-26`)
- **Holiday Existence**: `exists_{date}` (e.g., `exists_2025-01-26`)
- **Name Pattern Search**: `search_{pattern}` (e.g., `search_Republic`)
- **Financial Years List**: `financial_years`

### Cache Operations
- **Read**: `@Cacheable` - Returns cached value or null (cache miss)
- **Write**: `@CachePut` - Updates cache with new value
- **Delete**: `@CacheEvict` - Removes specific cache entries

### Cache Invalidation Strategy
- **On Holiday Creation/Update**: Evicts date-specific, financial year, and related caches
- **On Holiday Deletion**: Evicts all related caches for data consistency
- **Manual Cache Management**: API endpoints for manual cache refresh and eviction

## Database Migration

The system includes Flyway migration scripts:

### V1.0.30 - EMP_ATTENDANCE_METADATA Table
- Creates the `EMP_ATTENDANCE_METADATA` table
- Adds appropriate indexes for performance
- Inserts default configuration values including new holiday and weekend attributes
- Sets up audit fields

### V1.0.31 - EMP_HOLIDAYS Table
- Creates the `EMP_HOLIDAYS` table for system-wide holidays
- Supports financial year grouping
- Includes full day/half day holiday types
- Adds sample holiday data for 2024-25 financial year

## Testing

Comprehensive test coverage includes:
- Service layer unit tests with Mockito
- Cache service functionality testing
- Repository query testing
- Controller endpoint testing

## Performance Considerations

- **Indexes**: Composite index on (DEPT_ID, ATTRIBUTE_CODE, MAPPING_STATUS)
- **Caching**: Spring Cache with department-specific keys
- **Lazy Loading**: Values are loaded only when requested
- **Bulk Operations**: Support for bulk metadata updates

## Security

- All endpoints require proper authentication
- Audit trail maintained for all changes
- Input validation for attribute values
- Role-based access control (if implemented)

## Monitoring

- Comprehensive logging at all levels
- Cache hit/miss metrics
- Performance monitoring for database queries
- Error tracking and alerting

## Future Enhancements

- **Validation Rules**: Custom validation for attribute values
- **Versioning**: Support for metadata versioning
- **Approval Workflow**: Approval process for metadata changes
- **Audit History**: Detailed change history tracking
- **Bulk Import/Export**: CSV/Excel import/export functionality
- **Notification System**: Alerts for metadata changes


