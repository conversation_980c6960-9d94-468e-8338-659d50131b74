package com.stpl.tech.attendance.service.RosteringService.impl;

import com.gs.fw.common.mithra.MithraManager;
import com.gs.fw.common.mithra.MithraTransaction;
import com.stpl.tech.attendance.context.JwtContext;
import com.stpl.tech.attendance.dto.RosteringDto.ShiftCafeMappingRequestDTO;
import com.stpl.tech.attendance.entity.RosteringEntity.RosteringConstants;
import com.stpl.tech.attendance.entity.RosteringEntity.ShiftCafeMapping;
import com.stpl.tech.attendance.repository.RosteringRepository.ShiftCafeMappingRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ReladomoServiceImplShiftCoveragePlanTest {

    @Mock
    private ShiftCafeMappingRepository shiftCafeMappingRepository;

    @Mock
    private MithraManager mithraManager;

    @Mock
    private MithraTransaction mithraTransaction;

    @InjectMocks
    private ReladomoServiceImpl reladomoService;

    private ShiftCafeMappingRequestDTO testRequest;
    private ShiftCafeMapping testShiftCafeMapping;

    @BeforeEach
    void setUp() {
        // Setup test data
        testShiftCafeMapping = ShiftCafeMapping.builder()
                .shiftCafeMappingId(1)
                .shiftId(100)
                .unitId(200)
                .status(RosteringConstants.ACTIVE)
                .build();

        testRequest = ShiftCafeMappingRequestDTO.builder()
                .shiftId(100)
                .unitId(200)
                .dayWiseIdealCount(Map.of(
                        "1", 5,  // Monday
                        "2", 6,  // Tuesday
                        "3", 4   // Wednesday
                ))
                .build();
    }

    @Test
    void testUpdateShiftCoveragePlan_WithDayWiseIdealCount_Success() {
        // Arrange
        when(shiftCafeMappingRepository.findByShiftIdAndUnitIdAndStatus(
                testRequest.getShiftId(), testRequest.getUnitId(), RosteringConstants.ACTIVE))
                .thenReturn(Optional.of(testShiftCafeMapping));

        try (MockedStatic<JwtContext> jwtContextMock = mockStatic(JwtContext.class);
             MockedStatic<MithraManager> mithraMock = mockStatic(MithraManager.class)) {
            
            JwtContext mockJwtContext = mock(JwtContext.class);
            jwtContextMock.when(JwtContext::getInstance).thenReturn(mockJwtContext);
            when(mockJwtContext.getUserId()).thenReturn(1001);

            mithraMock.when(MithraManager::getInstance).thenReturn(mithraManager);
            when(mithraManager.startOrContinueTransaction()).thenReturn(mithraTransaction);

            // Act & Assert - Should not throw exception
            assertDoesNotThrow(() -> reladomoService.updateShiftCoveragePlan(testRequest));

            // Verify transaction was committed
            verify(mithraTransaction).commit();
            verify(mithraTransaction, never()).rollback();
        }
    }

    @Test
    void testUpdateShiftCoveragePlan_WithAllDaySame_Success() {
        // Arrange
        testRequest = ShiftCafeMappingRequestDTO.builder()
                .shiftId(100)
                .unitId(200)
                .allDaySame(true)
                .idealCount(5)
                .build();

        when(shiftCafeMappingRepository.findByShiftIdAndUnitIdAndStatus(
                testRequest.getShiftId(), testRequest.getUnitId(), RosteringConstants.ACTIVE))
                .thenReturn(Optional.of(testShiftCafeMapping));

        try (MockedStatic<JwtContext> jwtContextMock = mockStatic(JwtContext.class);
             MockedStatic<MithraManager> mithraMock = mockStatic(MithraManager.class)) {
            
            JwtContext mockJwtContext = mock(JwtContext.class);
            jwtContextMock.when(JwtContext::getInstance).thenReturn(mockJwtContext);
            when(mockJwtContext.getUserId()).thenReturn(1001);

            mithraMock.when(MithraManager::getInstance).thenReturn(mithraManager);
            when(mithraManager.startOrContinueTransaction()).thenReturn(mithraTransaction);

            // Act & Assert
            assertDoesNotThrow(() -> reladomoService.updateShiftCoveragePlan(testRequest));

            verify(mithraTransaction).commit();
        }
    }

    @Test
    void testUpdateShiftCoveragePlan_ShiftCafeMappingNotFound() {
        // Arrange
        when(shiftCafeMappingRepository.findByShiftIdAndUnitIdAndStatus(
                testRequest.getShiftId(), testRequest.getUnitId(), RosteringConstants.ACTIVE))
                .thenReturn(Optional.empty());

        try (MockedStatic<JwtContext> jwtContextMock = mockStatic(JwtContext.class)) {
            JwtContext mockJwtContext = mock(JwtContext.class);
            jwtContextMock.when(JwtContext::getInstance).thenReturn(mockJwtContext);
            when(mockJwtContext.getUserId()).thenReturn(1001);

            // Act & Assert - Should complete without error (just logs warning)
            assertDoesNotThrow(() -> reladomoService.updateShiftCoveragePlan(testRequest));
        }
    }

    @Test
    void testValidateShiftCoveragePlanRequest_InvalidDay() {
        // Arrange
        testRequest.setDayWiseIdealCount(Map.of("8", 5)); // Invalid day (should be 1-7)

        try (MockedStatic<JwtContext> jwtContextMock = mockStatic(JwtContext.class)) {
            JwtContext mockJwtContext = mock(JwtContext.class);
            jwtContextMock.when(JwtContext::getInstance).thenReturn(mockJwtContext);
            when(mockJwtContext.getUserId()).thenReturn(1001);

            when(shiftCafeMappingRepository.findByShiftIdAndUnitIdAndStatus(
                    testRequest.getShiftId(), testRequest.getUnitId(), RosteringConstants.ACTIVE))
                    .thenReturn(Optional.of(testShiftCafeMapping));

            // Act & Assert
            RuntimeException exception = assertThrows(RuntimeException.class, 
                    () -> reladomoService.updateShiftCoveragePlan(testRequest));
            
            assertTrue(exception.getMessage().contains("Failed to update shift coverage plan"));
        }
    }

    @Test
    void testValidateShiftCoveragePlanRequest_NegativeIdealCount() {
        // Arrange
        testRequest.setDayWiseIdealCount(Map.of("1", -1)); // Negative ideal count

        try (MockedStatic<JwtContext> jwtContextMock = mockStatic(JwtContext.class)) {
            JwtContext mockJwtContext = mock(JwtContext.class);
            jwtContextMock.when(JwtContext::getInstance).thenReturn(mockJwtContext);
            when(mockJwtContext.getUserId()).thenReturn(1001);

            when(shiftCafeMappingRepository.findByShiftIdAndUnitIdAndStatus(
                    testRequest.getShiftId(), testRequest.getUnitId(), RosteringConstants.ACTIVE))
                    .thenReturn(Optional.of(testShiftCafeMapping));

            // Act & Assert
            RuntimeException exception = assertThrows(RuntimeException.class, 
                    () -> reladomoService.updateShiftCoveragePlan(testRequest));
            
            assertTrue(exception.getMessage().contains("Failed to update shift coverage plan"));
        }
    }
}
