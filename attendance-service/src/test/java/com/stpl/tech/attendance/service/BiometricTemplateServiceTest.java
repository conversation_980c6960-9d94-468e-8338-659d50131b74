package com.stpl.tech.attendance.service;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import static org.mockito.ArgumentMatchers.any;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;

import com.stpl.tech.attendance.dto.BiometricTemplateRequest;
import com.stpl.tech.attendance.dto.BiometricTemplateResponse;
import com.stpl.tech.attendance.entity.BiometricTemplate;
import com.stpl.tech.attendance.repository.BiometricTemplateRepository;
import com.stpl.tech.attendance.service.impl.BiometricTemplateServiceImpl;
import com.stpl.tech.attendance.util.TestDataUtil;

@ExtendWith(MockitoExtension.class)
class BiometricTemplateServiceTest {

    @Mock
    private BiometricTemplateRepository biometricTemplateRepository;

    @InjectMocks
    private BiometricTemplateServiceImpl biometricTemplateService;

    private BiometricTemplateRequest validRequest;
    private BiometricTemplate savedTemplate;

    @BeforeEach
    void setUp() throws IOException {
        // Load test image
        byte[] templateData = TestDataUtil.loadTestImage("test-images/fingerprint1.jpg");
        
        // Setup valid request
        validRequest = BiometricTemplateRequest.builder()
                .empId(1)
                .unitId(1)
                .templateVersion("1.0")
                .templateData(templateData)
                .enrollmentDeviceId("TEST-DEVICE-001")
                .enrollmentDeviceType("FINGERPRINT")
                .enrollmentIpAddress("***********")
                .build();

        // Setup saved template
        savedTemplate = BiometricTemplate.builder()
                .biometricTemplateId(1)
                .empId(1)
                .unitId(1)
                .templateVersion("1.0")
                .templateData(templateData)
                .enrollmentDeviceId("TEST-DEVICE-001")
                .enrollmentDeviceType("FINGERPRINT")
                .enrollmentIpAddress("***********")
                .status(BiometricTemplate.Status.ACTIVE)
                .approvalStatus(BiometricTemplate.ApprovalStatus.PENDING)
                .build();
    }

    @Test
    void createBiometricTemplate_Success() {
        // Arrange
        when(biometricTemplateRepository.save(any(BiometricTemplate.class)))
                .thenReturn(savedTemplate);

        // Act
        BiometricTemplateResponse response = biometricTemplateService.createBiometricTemplate(validRequest);

        // Assert
        assertNotNull(response);
        assertEquals(1, response.getBiometricTemplateId());
        assertEquals(1, response.getEmpId());
        assertEquals("1.0", response.getTemplateVersion());
        assertEquals("TEST-DEVICE-001", response.getEnrollmentDeviceId());
        assertEquals("ACTIVE", response.getStatus());
        assertEquals("PENDING", response.getApprovalStatus());
    }

    @Test
    void createBiometricTemplate_WithEmptyTemplateData_ShouldThrowException() {
        // Arrange
        validRequest.setTemplateData(new byte[0]);

        // Act & Assert
        Exception exception = assertThrows(IllegalArgumentException.class, () -> 
            biometricTemplateService.createBiometricTemplate(validRequest));
        assertNotNull(exception.getMessage());
    }

    @Test
    void createBiometricTemplate_WithNullEmpId_ShouldThrowException() {
        // Arrange
        validRequest.setEmpId(null);

        // Act & Assert
        Exception exception = assertThrows(IllegalArgumentException.class, () -> 
            biometricTemplateService.createBiometricTemplate(validRequest));
            assertNotNull(exception.getMessage());
    }
} 