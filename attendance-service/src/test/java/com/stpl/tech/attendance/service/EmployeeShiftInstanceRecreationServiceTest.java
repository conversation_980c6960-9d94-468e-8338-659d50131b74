package com.stpl.tech.attendance.service;

import com.stpl.tech.attendance.cache.service.EmployeeShiftDataCacheService;
import com.stpl.tech.attendance.entity.AttendanceStatus;
import com.stpl.tech.attendance.entity.EmployeeShiftInstances;
import com.stpl.tech.attendance.entity.EmployeeShiftUnitAttendance;
import com.stpl.tech.attendance.entity.RosteringEntity.Shift;
import com.stpl.tech.attendance.repository.EmployeeShiftInstancesRepository;
import com.stpl.tech.attendance.repository.EmployeeShiftUnitAttendanceRepository;
import com.stpl.tech.attendance.service.impl.EmployeeShiftInstanceRecreationServiceImpl;
import com.stpl.tech.attendance.service.UnitResolutionService;
import com.stpl.tech.attendance.util.ShiftHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class EmployeeShiftInstanceRecreationServiceTest {

    @Mock
    private EmployeeShiftInstancesRepository employeeShiftInstancesRepository;

    @Mock
    private EmployeeShiftUnitAttendanceRepository employeeShiftUnitAttendanceRepository;

    @Mock
    private UnitResolutionService unitResolutionService;

    @Mock
    private EmployeeShiftDataCacheService employeeShiftDataCacheService;

    @Mock
    private ShiftHelper shiftHelper;

    @Mock
    private Shift mockShift;

    @InjectMocks
    private EmployeeShiftInstanceRecreationServiceImpl recreationService;

    private static final Integer TEST_EMP_ID = 123;
    private static final Integer TEST_UNIT_ID = 456;
    private static final Integer TEST_SHIFT_ID = 789;
    private static final LocalDate TODAY = LocalDate.now();
    private static final LocalDateTime NOW = LocalDateTime.now();
    private static final LocalDateTime EXPECTED_START = TODAY.atTime(9, 0);
    private static final LocalDateTime EXPECTED_END = TODAY.atTime(17, 0);
    private static final LocalDateTime ACTUAL_START = TODAY.atTime(9, 15);
    private static final LocalDateTime ACTUAL_END = TODAY.atTime(17, 30);
    private static final BigDecimal IDEAL_HOURS = BigDecimal.valueOf(8.0);
    private static final BigDecimal ACTUAL_HOURS = BigDecimal.valueOf(8.25);

    @BeforeEach
    void setUp() {
        // Setup mock shift
        when(mockShift.getShiftId()).thenReturn(TEST_SHIFT_ID);
        when(mockShift.getShiftName()).thenReturn("Test Shift");
        
        // Setup ShiftHelper mocks
        when(shiftHelper.getShiftForEmployee(TEST_EMP_ID, TODAY)).thenReturn(mockShift);
        when(shiftHelper.isUniversalShift(mockShift)).thenReturn(false);
        when(shiftHelper.calculateExpectedStartTime(mockShift, TODAY)).thenReturn(EXPECTED_START);
        when(shiftHelper.calculateExpectedEndTime(mockShift, TODAY)).thenReturn(EXPECTED_END);
        when(shiftHelper.calculateIdealHours(EXPECTED_START, EXPECTED_END)).thenReturn(IDEAL_HOURS);
        
        // Setup UnitResolutionService mock
        when(unitResolutionService.getUnitIdForEmployee(TEST_EMP_ID, TODAY)).thenReturn(TEST_UNIT_ID);
    }

    @Test
    void testRecreateShiftInstancesFromEffectiveDate_CurrentDate_CarriesForwardActualData() {
        // Given: Effective date is current date and existing instance has actual data
        LocalDate effectiveDate = TODAY;
        
        // Create existing instance with actual data
        EmployeeShiftInstances existingInstance = createExistingInstanceWithActualData();
        
        // Mock repository calls
        when(employeeShiftInstancesRepository.findLatestInactiveInstanceByEmpIdAndBusinessDate(TEST_EMP_ID, TODAY))
            .thenReturn(Optional.of(existingInstance));
        when(employeeShiftInstancesRepository.findByEmpIdAndBusinessDateBetweenAndInstanceStatus(
            eq(TEST_EMP_ID), any(LocalDate.class), any(LocalDate.class), eq("ACTIVE")))
            .thenReturn(Arrays.asList(existingInstance));
        when(employeeShiftInstancesRepository.save(any(EmployeeShiftInstances.class)))
            .thenAnswer(invocation -> {
                EmployeeShiftInstances instance = invocation.getArgument(0);
                instance.setId(2L); // Set new ID for saved instance
                return instance;
            });
        
        // Mock unit attendance data
        List<EmployeeShiftUnitAttendance> unitAttendanceRecords = createUnitAttendanceRecords();
        when(employeeShiftUnitAttendanceRepository.findByShiftInstanceId(1L))
            .thenReturn(unitAttendanceRecords);
        when(employeeShiftUnitAttendanceRepository.save(any(EmployeeShiftUnitAttendance.class)))
            .thenAnswer(invocation -> invocation.getArgument(0));

        // When: Recreate shift instances
        recreationService.recreateShiftInstancesFromEffectiveDate(TEST_EMP_ID, effectiveDate, "TEST_USER");

        // Then: Verify that actual data was carried forward
        verify(employeeShiftInstancesRepository, times(1)).save(argThat(instance -> 
            instance.getActualStartTime().equals(ACTUAL_START) &&
            instance.getActualEndTime().equals(ACTUAL_END) &&
            instance.getActualHours().equals(ACTUAL_HOURS) &&
            instance.getStatus().equals(AttendanceStatus.PRESENT)
        ));
        
        // Verify unit attendance data was carried forward
        verify(employeeShiftUnitAttendanceRepository, times(2)).save(any(EmployeeShiftUnitAttendance.class));
        
        // Verify cache was evicted
        verify(employeeShiftDataCacheService, atLeastOnce()).evictEmployeeShiftData(eq(TEST_EMP_ID), any(LocalDate.class));
    }

    @Test
    void testRecreateShiftInstancesFromEffectiveDate_FutureDate_DoesNotCarryForwardData() {
        // Given: Effective date is future date
        LocalDate effectiveDate = TODAY.plusDays(1);
        
        // Mock repository calls for future date
        when(employeeShiftInstancesRepository.findLatestInactiveInstanceByEmpIdAndBusinessDate(TEST_EMP_ID, effectiveDate))
            .thenReturn(Optional.empty());
        when(employeeShiftInstancesRepository.findByEmpIdAndBusinessDateBetweenAndInstanceStatus(
            eq(TEST_EMP_ID), any(LocalDate.class), any(LocalDate.class), eq("ACTIVE")))
            .thenReturn(Arrays.asList());
        when(employeeShiftInstancesRepository.save(any(EmployeeShiftInstances.class)))
            .thenAnswer(invocation -> {
                EmployeeShiftInstances instance = invocation.getArgument(0);
                instance.setId(2L);
                return instance;
            });

        // When: Recreate shift instances
        recreationService.recreateShiftInstancesFromEffectiveDate(TEST_EMP_ID, effectiveDate, "TEST_USER");

        // Then: Verify that no actual data was carried forward (instance should have default values)
        verify(employeeShiftInstancesRepository, times(1)).save(argThat(instance -> 
            instance.getActualStartTime() == null &&
            instance.getActualEndTime() == null &&
            instance.getActualHours() == null &&
            instance.getStatus().equals(AttendanceStatus.ABSENT)
        ));
        
        // Verify no unit attendance data was processed
        verify(employeeShiftUnitAttendanceRepository, never()).save(any(EmployeeShiftUnitAttendance.class));
    }

    @Test
    void testRecreateShiftInstancesFromEffectiveDate_CurrentDate_NoActualData_DoesNotCarryForward() {
        // Given: Effective date is current date but no actual data exists
        LocalDate effectiveDate = TODAY;
        
        // Create existing instance without actual data
        EmployeeShiftInstances existingInstance = createExistingInstanceWithoutActualData();
        
        // Mock repository calls
        when(employeeShiftInstancesRepository.findLatestInactiveInstanceByEmpIdAndBusinessDate(TEST_EMP_ID, TODAY))
            .thenReturn(Optional.of(existingInstance));
        when(employeeShiftInstancesRepository.findByEmpIdAndBusinessDateBetweenAndInstanceStatus(
            eq(TEST_EMP_ID), any(LocalDate.class), any(LocalDate.class), eq("ACTIVE")))
            .thenReturn(Arrays.asList(existingInstance));
        when(employeeShiftInstancesRepository.save(any(EmployeeShiftInstances.class)))
            .thenAnswer(invocation -> {
                EmployeeShiftInstances instance = invocation.getArgument(0);
                instance.setId(2L);
                return instance;
            });

        // When: Recreate shift instances
        recreationService.recreateShiftInstancesFromEffectiveDate(TEST_EMP_ID, effectiveDate, "TEST_USER");

        // Then: Verify that no actual data was carried forward
        verify(employeeShiftInstancesRepository, times(1)).save(argThat(instance -> 
            instance.getActualStartTime() == null &&
            instance.getActualEndTime() == null &&
            instance.getActualHours() == null &&
            instance.getStatus().equals(AttendanceStatus.ABSENT)
        ));
        
        // Verify no unit attendance data was processed
        verify(employeeShiftUnitAttendanceRepository, never()).save(any(EmployeeShiftUnitAttendance.class));
    }

    @Test
    void testRecreateShiftInstancesFromEffectiveDate_CurrentDate_MultipleInactiveInstances_UsesLatestOne() {
        // Given: Effective date is current date with multiple inactive instances
        LocalDate effectiveDate = TODAY;
        
        // Create multiple inactive instances with different actual data
        EmployeeShiftInstances olderInstance = createExistingInstanceWithActualData();
        olderInstance.setId(1L);
        olderInstance.setActualStartTime(TODAY.atTime(8, 0));
        olderInstance.setActualEndTime(TODAY.atTime(16, 0));
        olderInstance.setActualHours(BigDecimal.valueOf(8.0));
        olderInstance.setCreatedAt(LocalDateTime.now().minusHours(2));
        olderInstance.setUpdatedAt(LocalDateTime.now().minusHours(2));
        
        EmployeeShiftInstances latestInstance = createExistingInstanceWithActualData();
        latestInstance.setId(2L);
        latestInstance.setActualStartTime(ACTUAL_START);
        latestInstance.setActualEndTime(ACTUAL_END);
        latestInstance.setActualHours(ACTUAL_HOURS);
        latestInstance.setCreatedAt(LocalDateTime.now().minusMinutes(30));
        latestInstance.setUpdatedAt(LocalDateTime.now().minusMinutes(30));
        
        // Mock repository calls - should return the latest instance
        when(employeeShiftInstancesRepository.findLatestInactiveInstanceByEmpIdAndBusinessDate(TEST_EMP_ID, TODAY))
            .thenReturn(Optional.of(latestInstance));
        when(employeeShiftInstancesRepository.findByEmpIdAndBusinessDateBetweenAndInstanceStatus(
            eq(TEST_EMP_ID), any(LocalDate.class), any(LocalDate.class), eq("ACTIVE")))
            .thenReturn(Arrays.asList(olderInstance, latestInstance));
        when(employeeShiftInstancesRepository.save(any(EmployeeShiftInstances.class)))
            .thenAnswer(invocation -> {
                EmployeeShiftInstances instance = invocation.getArgument(0);
                instance.setId(3L); // Set new ID for saved instance
                return instance;
            });
        
        // Mock unit attendance data for the latest instance
        List<EmployeeShiftUnitAttendance> unitAttendanceRecords = createUnitAttendanceRecords();
        when(employeeShiftUnitAttendanceRepository.findByShiftInstanceId(2L))
            .thenReturn(unitAttendanceRecords);
        when(employeeShiftUnitAttendanceRepository.save(any(EmployeeShiftUnitAttendance.class)))
            .thenAnswer(invocation -> invocation.getArgument(0));

        // When: Recreate shift instances
        recreationService.recreateShiftInstancesFromEffectiveDate(TEST_EMP_ID, effectiveDate, "TEST_USER");

        // Then: Verify that actual data from the latest instance was carried forward
        verify(employeeShiftInstancesRepository, times(1)).save(argThat(instance -> 
            instance.getActualStartTime().equals(ACTUAL_START) &&
            instance.getActualEndTime().equals(ACTUAL_END) &&
            instance.getActualHours().equals(ACTUAL_HOURS) &&
            instance.getStatus().equals(AttendanceStatus.PRESENT)
        ));
        
        // Verify unit attendance data from the latest instance was carried forward
        verify(employeeShiftUnitAttendanceRepository, times(2)).save(any(EmployeeShiftUnitAttendance.class));
        
        // Verify cache was evicted
        verify(employeeShiftDataCacheService, atLeastOnce()).evictEmployeeShiftData(eq(TEST_EMP_ID), any(LocalDate.class));
    }

    private EmployeeShiftInstances createExistingInstanceWithActualData() {
        return EmployeeShiftInstances.builder()
            .id(1L)
            .empId(TEST_EMP_ID)
            .businessDate(TODAY)
            .shiftId(TEST_SHIFT_ID)
            .unitId(TEST_UNIT_ID)
            .expectedStartTime(EXPECTED_START)
            .expectedEndTime(EXPECTED_END)
            .idealHours(IDEAL_HOURS)
            .actualStartTime(ACTUAL_START)
            .actualEndTime(ACTUAL_END)
            .actualHours(ACTUAL_HOURS)
            .status(AttendanceStatus.PRESENT)
            .instanceStatus("INACTIVE")
            .createdBy("SYSTEM")
            .build();
    }

    private EmployeeShiftInstances createExistingInstanceWithoutActualData() {
        return EmployeeShiftInstances.builder()
            .id(1L)
            .empId(TEST_EMP_ID)
            .businessDate(TODAY)
            .shiftId(TEST_SHIFT_ID)
            .unitId(TEST_UNIT_ID)
            .expectedStartTime(EXPECTED_START)
            .expectedEndTime(EXPECTED_END)
            .idealHours(IDEAL_HOURS)
            .actualStartTime(null)
            .actualEndTime(null)
            .actualHours(null)
            .status(AttendanceStatus.ABSENT)
            .instanceStatus("INACTIVE")
            .createdBy("SYSTEM")
            .build();
    }

    private List<EmployeeShiftUnitAttendance> createUnitAttendanceRecords() {
        EmployeeShiftUnitAttendance record1 = EmployeeShiftUnitAttendance.builder()
            .id(1L)
            .shiftInstanceId(1L)
            .actualUnitId(100)
            .actualStartTime(ACTUAL_START)
            .actualEndTime(ACTUAL_END)
            .build();
            
        EmployeeShiftUnitAttendance record2 = EmployeeShiftUnitAttendance.builder()
            .id(2L)
            .shiftInstanceId(1L)
            .actualUnitId(200)
            .actualStartTime(ACTUAL_START.plusHours(1))
            .actualEndTime(ACTUAL_END.minusHours(1))
            .build();
            
        return Arrays.asList(record1, record2);
    }
} 