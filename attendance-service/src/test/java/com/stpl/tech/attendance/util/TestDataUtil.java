package com.stpl.tech.attendance.util;

import org.springframework.core.io.ClassPathResource;
import org.springframework.util.StreamUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

public class TestDataUtil {
    
    public static byte[] loadTestImage(String imagePath) throws IOException {
        ClassPathResource resource = new ClassPathResource(imagePath);
        return StreamUtils.copyToByteArray(resource.getInputStream());
    }

    public static String loadTestImageAsBase64(String imagePath) throws IOException {
        byte[] imageBytes = loadTestImage(imagePath);
        return Base64.getEncoder().encodeToString(imageBytes);
    }
} 