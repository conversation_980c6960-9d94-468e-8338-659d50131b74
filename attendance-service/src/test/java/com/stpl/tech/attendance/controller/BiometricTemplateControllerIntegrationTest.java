package com.stpl.tech.attendance.controller;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stpl.tech.attendance.config.TestConfig;
import com.stpl.tech.attendance.dto.BiometricTemplateRequest;
import com.stpl.tech.attendance.util.TestDataUtil;

@SpringBootTest
@AutoConfigureMockMvc
@Import(TestConfig.class)
@ActiveProfiles("test")
class BiometricTemplateControllerIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void createBiometricTemplate_Success() throws Exception {
        // Load test image
        byte[] templateData = TestDataUtil.loadTestImage("test-images/fingerprint1.jpg");
        
        // Create request
        BiometricTemplateRequest request = BiometricTemplateRequest.builder()
                .empId(1)
                .unitId(1)
                .templateVersion("1.0")
                .templateData(templateData)
                .enrollmentDeviceId("TEST-DEVICE-001")
                .enrollmentDeviceType("FINGERPRINT")
                .enrollmentIpAddress("***********")
                .build();

        // Perform request and verify response
        mockMvc.perform(post("/api/v1/biometric-templates")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.biometricTemplateId").exists())
                .andExpect(jsonPath("$.empId").value(1))
                .andExpect(jsonPath("$.templateVersion").value("1.0"))
                .andExpect(jsonPath("$.enrollmentDeviceId").value("TEST-DEVICE-001"))
                .andExpect(jsonPath("$.status").value("ACTIVE"))
                .andExpect(jsonPath("$.approvalStatus").value("PENDING"));
    }

    @Test
    void createBiometricTemplate_WithInvalidData_ShouldReturnBadRequest() throws Exception {
        // Create invalid request (missing required fields)
        BiometricTemplateRequest request = BiometricTemplateRequest.builder()
                .empId(1)
                .build();

        // Perform request and verify response
        mockMvc.perform(post("/api/v1/biometric-templates")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void createBiometricTemplate_WithEmptyTemplateData_ShouldReturnBadRequest() throws Exception {
        // Create request with empty template data
        BiometricTemplateRequest request = BiometricTemplateRequest.builder()
                .empId(1)
                .unitId(1)
                .templateVersion("1.0")
                .templateData(new byte[0])
                .enrollmentDeviceId("TEST-DEVICE-001")
                .enrollmentDeviceType("FINGERPRINT")
                .enrollmentIpAddress("***********")
                .build();

        // Perform request and verify response
        mockMvc.perform(post("/api/v1/biometric-templates")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }
} 