package com.stpl.tech.attendance.cache.service.impl;

import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class UnitCacheServiceImplTest {

    @Mock
    private HazelcastInstance instance;

    @Mock
    private IMap<Integer, UnitBasicDetail> unitsBasicDetails;

    @InjectMocks
    private UnitCacheServiceImpl unitCacheService;

    private UnitBasicDetail mumbaiUnit;
    private UnitBasicDetail delhiUnit;
    private UnitBasicDetail bangaloreUnit;

    @BeforeEach
    void setUp() {
        // Setup test units
        mumbaiUnit = new UnitBasicDetail();
        mumbaiUnit.setId(1);
        mumbaiUnit.setLocationCode("MUMBAI");
        mumbaiUnit.setUnitZone("WEST");

        delhiUnit = new UnitBasicDetail();
        delhiUnit.setId(2);
        delhiUnit.setLocationCode("DELHI");
        delhiUnit.setUnitZone("NORTH");

        bangaloreUnit = new UnitBasicDetail();
        bangaloreUnit.setId(3);
        bangaloreUnit.setLocationCode("BANGALORE");
        bangaloreUnit.setUnitZone("SOUTH");

        // Mock the cache initialization
        when(instance.getMap("MasterDataCache:unitsBasicDetails")).thenReturn(unitsBasicDetails);
    }

    @Test
    void getUnitsByCity_WithValidCity_ShouldReturnUnits() {
        // Given
        List<UnitBasicDetail> allUnits = Arrays.asList(mumbaiUnit, delhiUnit, bangaloreUnit);
        when(unitsBasicDetails.values()).thenReturn(allUnits);

        // When
        List<Integer> result = unitCacheService.getUnitsByCity("MUMBAI");

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.contains(1));
    }

    @Test
    void getUnitsByCity_WithCaseInsensitive_ShouldReturnUnits() {
        // Given
        List<UnitBasicDetail> allUnits = Arrays.asList(mumbaiUnit, delhiUnit, bangaloreUnit);
        when(unitsBasicDetails.values()).thenReturn(allUnits);

        // When
        List<Integer> result = unitCacheService.getUnitsByCity("mumbai");

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.contains(1));
    }

    @Test
    void getUnitsByCity_WithNonExistentCity_ShouldReturnEmptyList() {
        // Given
        List<UnitBasicDetail> allUnits = Arrays.asList(mumbaiUnit, delhiUnit, bangaloreUnit);
        when(unitsBasicDetails.values()).thenReturn(allUnits);

        // When
        List<Integer> result = unitCacheService.getUnitsByCity("CHENNAI");

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void getUnitsByCity_WithNullCity_ShouldReturnEmptyList() {
        // Given
        List<UnitBasicDetail> allUnits = Arrays.asList(mumbaiUnit, delhiUnit, bangaloreUnit);
        when(unitsBasicDetails.values()).thenReturn(allUnits);

        // When
        List<Integer> result = unitCacheService.getUnitsByCity(null);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void getUnitsByCity_WithEmptyCity_ShouldReturnEmptyList() {
        // Given
        List<UnitBasicDetail> allUnits = Arrays.asList(mumbaiUnit, delhiUnit, bangaloreUnit);
        when(unitsBasicDetails.values()).thenReturn(allUnits);

        // When
        List<Integer> result = unitCacheService.getUnitsByCity("");

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void getUnitsByRegion_WithValidRegion_ShouldReturnUnits() {
        // Given
        List<UnitBasicDetail> allUnits = Arrays.asList(mumbaiUnit, delhiUnit, bangaloreUnit);
        when(unitsBasicDetails.values()).thenReturn(allUnits);

        // When
        List<Integer> result = unitCacheService.getUnitsByRegion("WEST");

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.contains(1));
    }

    @Test
    void getUnitsByRegion_WithCaseInsensitive_ShouldReturnUnits() {
        // Given
        List<UnitBasicDetail> allUnits = Arrays.asList(mumbaiUnit, delhiUnit, bangaloreUnit);
        when(unitsBasicDetails.values()).thenReturn(allUnits);

        // When
        List<Integer> result = unitCacheService.getUnitsByRegion("west");

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.contains(1));
    }

    @Test
    void getUnitsByRegion_WithNonExistentRegion_ShouldReturnEmptyList() {
        // Given
        List<UnitBasicDetail> allUnits = Arrays.asList(mumbaiUnit, delhiUnit, bangaloreUnit);
        when(unitsBasicDetails.values()).thenReturn(allUnits);

        // When
        List<Integer> result = unitCacheService.getUnitsByRegion("EAST");

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void getUnitsByRegion_WithNullRegion_ShouldReturnEmptyList() {
        // Given
        List<UnitBasicDetail> allUnits = Arrays.asList(mumbaiUnit, delhiUnit, bangaloreUnit);
        when(unitsBasicDetails.values()).thenReturn(allUnits);

        // When
        List<Integer> result = unitCacheService.getUnitsByRegion(null);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void getUnitsByRegion_WithEmptyRegion_ShouldReturnEmptyList() {
        // Given
        List<UnitBasicDetail> allUnits = Arrays.asList(mumbaiUnit, delhiUnit, bangaloreUnit);
        when(unitsBasicDetails.values()).thenReturn(allUnits);

        // When
        List<Integer> result = unitCacheService.getUnitsByRegion("");

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void getUnitsByCity_WithException_ShouldReturnEmptyList() {
        // Given
        when(unitsBasicDetails.values()).thenThrow(new RuntimeException("Cache error"));

        // When
        List<Integer> result = unitCacheService.getUnitsByCity("MUMBAI");

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void getUnitsByRegion_WithException_ShouldReturnEmptyList() {
        // Given
        when(unitsBasicDetails.values()).thenThrow(new RuntimeException("Cache error"));

        // When
        List<Integer> result = unitCacheService.getUnitsByRegion("WEST");

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
} 