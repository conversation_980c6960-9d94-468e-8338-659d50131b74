-- Add UNIT_ID column to EMP_SHIFT_MAPPING table
ALTER TABLE EMP_SHIFT_MAPPING ADD COLUMN UNIT_ID INT NOT NULL DEFAULT 26091;

-- Create index for better performance on unit-based queries
CREATE INDEX idx_emp_shift_mapping_unit_id ON EMP_SHIFT_MAPPING(UNIT_ID);
CREATE INDEX idx_emp_shift_mapping_emp_unit_status ON EMP_SHIFT_MAPPING(EMP_ID, UNIT_ID, STATUS);

-- Populate UNIT_ID based on emp eligibility mapping
-- Find the first active emp eligibility mapping for each employee and update the unit_id
UPDATE EMP_SHIFT_MAPPING esm
SET UNIT_ID = (
    SELECT CAST(eem.value AS UNSIGNED)
    FROM EMP_ELIGIBILITY_MAPPING eem
    WHERE eem.emp_id = CAST(esm.emp_id AS CHAR)
    AND eem.mapping_type = 'UNIT'
    AND eem.eligibility_type = 'ATTENDANCE'
    AND eem.status = 'ACTIVE'
    AND (eem.start_date IS NULL OR eem.start_date <= CURDATE())
    AND (eem.end_date IS NULL OR eem.end_date >= CURDATE())
    ORDER BY eem.id ASC
    LIMIT 1
)
WHERE EXISTS (
    SELECT 1
    FROM EMP_ELIGIBILITY_MAPPING eem
    WHERE eem.emp_id = CAST(esm.emp_id AS CHAR)
    AND eem.mapping_type = 'UNIT'
    AND eem.eligibility_type = 'ATTENDANCE'
    AND eem.status = 'ACTIVE'
    AND (eem.start_date IS NULL OR eem.start_date <= CURDATE())
    AND (eem.end_date IS NULL OR eem.end_date >= CURDATE())
);

-- Add comment for the new column
COMMENT ON COLUMN EMP_SHIFT_MAPPING.UNIT_ID IS 'Unit ID where the employee is assigned for this shift mapping'; 