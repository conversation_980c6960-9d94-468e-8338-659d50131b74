-- Migration: Update Attendance Attribute Types and Defaults
-- Version: 1.0.42
-- Description: Updates existing attendance metadata with proper attribute types and adds missing default values

-- First, let's update the existing records to use the correct attribute types from our enhanced enum
UPDATE EMP_ATTENDANCE_METADATA 
SET ATTRIBUTE_TYPE = 'BOOLEAN' 
WHERE ATTRIBUTE_CODE IN ('IS_LEAVE_ALLOWED', 'IS_OD_ALLOWED', 'IS_WFH_ALLOWED', 'IS_REGULARISATION_ALLOWED', 'IS_HOLIDAY_ALLOWED');

UPDATE EMP_ATTENDANCE_METADATA 
SET ATTRIBUTE_TYPE = 'CYCLIC' 
WHERE ATTRIBUTE_CODE = 'LEAVE_CREDIT_CYCLE';

UPDATE EMP_ATTENDANCE_METADATA 
SET ATTRIBUTE_TYPE = 'INTEGER' 
WHERE ATTRIBUTE_CODE IN ('TOTAL_NUMBER_OF_LEAVES', 'PAYROLL_PROCESSING_START_DAY', 'PAYROLL_PROCESSING_END_DAY');

UPDATE EMP_ATTENDANCE_METADATA 
SET ATTRIBUTE_TYPE = 'STRING' 
WHERE ATTRIBUTE_CODE = 'WEEKEND_DAYS';

-- Update existing values to match our enhanced enum expectations
UPDATE EMP_ATTENDANCE_METADATA 
SET ATTRIBUTE_VALUE = 'true' 
WHERE ATTRIBUTE_CODE = 'IS_LEAVE_ALLOWED' AND ATTRIBUTE_VALUE != 'true';

UPDATE EMP_ATTENDANCE_METADATA 
SET ATTRIBUTE_VALUE = 'true' 
WHERE ATTRIBUTE_CODE = 'IS_OD_ALLOWED' AND ATTRIBUTE_VALUE != 'true';

UPDATE EMP_ATTENDANCE_METADATA 
SET ATTRIBUTE_VALUE = 'false' 
WHERE ATTRIBUTE_CODE = 'IS_WFH_ALLOWED' AND ATTRIBUTE_VALUE != 'false';

UPDATE EMP_ATTENDANCE_METADATA 
SET ATTRIBUTE_VALUE = 'true' 
WHERE ATTRIBUTE_CODE = 'IS_REGULARISATION_ALLOWED' AND ATTRIBUTE_VALUE != 'true';

UPDATE EMP_ATTENDANCE_METADATA 
SET ATTRIBUTE_VALUE = 'YEARLY' 
WHERE ATTRIBUTE_CODE = 'LEAVE_CREDIT_CYCLE' AND ATTRIBUTE_VALUE != 'YEARLY';

UPDATE EMP_ATTENDANCE_METADATA 
SET ATTRIBUTE_VALUE = '21' 
WHERE ATTRIBUTE_CODE = 'TOTAL_NUMBER_OF_LEAVES' AND ATTRIBUTE_VALUE != '21';

UPDATE EMP_ATTENDANCE_METADATA 
SET ATTRIBUTE_VALUE = '25' 
WHERE ATTRIBUTE_CODE = 'PAYROLL_PROCESSING_START_DAY' AND ATTRIBUTE_VALUE != '25';

UPDATE EMP_ATTENDANCE_METADATA 
SET ATTRIBUTE_VALUE = '5' 
WHERE ATTRIBUTE_CODE = 'PAYROLL_PROCESSING_END_DAY' AND ATTRIBUTE_VALUE != '5';

UPDATE EMP_ATTENDANCE_METADATA 
SET ATTRIBUTE_VALUE = 'true' 
WHERE ATTRIBUTE_CODE = 'IS_HOLIDAY_ALLOWED' AND ATTRIBUTE_VALUE != 'true';

UPDATE EMP_ATTENDANCE_METADATA 
SET ATTRIBUTE_VALUE = 'SATURDAY,SUNDAY' 
WHERE ATTRIBUTE_CODE = 'WEEKEND_DAYS' AND ATTRIBUTE_VALUE != 'SATURDAY,SUNDAY';

-- Insert any missing default records for DEPT_ID = -1 (global defaults)
INSERT IGNORE INTO EMP_ATTENDANCE_METADATA (DEPT_ID, ATTRIBUTE_CODE, ATTRIBUTE_TYPE, ATTRIBUTE_VALUE, MAPPING_STATUS, CREATED_BY) VALUES
(-1, 'IS_LEAVE_ALLOWED', 'BOOLEAN', 'true', 'ACTIVE', 'SYSTEM'),
(-1, 'IS_OD_ALLOWED', 'BOOLEAN', 'true', 'ACTIVE', 'SYSTEM'),
(-1, 'IS_WFH_ALLOWED', 'BOOLEAN', 'false', 'ACTIVE', 'SYSTEM'),
(-1, 'IS_REGULARISATION_ALLOWED', 'BOOLEAN', 'true', 'ACTIVE', 'SYSTEM'),
(-1, 'LEAVE_CREDIT_CYCLE', 'CYCLIC', 'YEARLY', 'ACTIVE', 'SYSTEM'),
(-1, 'TOTAL_NUMBER_OF_LEAVES', 'INTEGER', '21', 'ACTIVE', 'SYSTEM'),
(-1, 'PAYROLL_PROCESSING_START_DAY', 'INTEGER', '25', 'ACTIVE', 'SYSTEM'),
(-1, 'PAYROLL_PROCESSING_END_DAY', 'INTEGER', '5', 'ACTIVE', 'SYSTEM'),
(-1, 'IS_HOLIDAY_ALLOWED', 'BOOLEAN', 'true', 'ACTIVE', 'SYSTEM'),
(-1, 'WEEKEND_DAYS', 'STRING', 'SATURDAY,SUNDAY', 'ACTIVE', 'SYSTEM');

-- Create a reference table for valid attribute types (optional but recommended for data integrity)
CREATE TABLE IF NOT EXISTS ATTENDANCE_ATTRIBUTE_TYPES (
    ATTRIBUTE_TYPE_ID INT AUTO_INCREMENT PRIMARY KEY,
    ATTRIBUTE_TYPE_CODE VARCHAR(20) NOT NULL UNIQUE,
    ATTRIBUTE_TYPE_NAME VARCHAR(50) NOT NULL,
    DESCRIPTION TEXT,
    VALID_VALUES TEXT COMMENT 'Comma-separated list of valid values for this type',
    CREATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UPDATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert the valid attribute types
INSERT IGNORE INTO ATTENDANCE_ATTRIBUTE_TYPES (ATTRIBUTE_TYPE_CODE, ATTRIBUTE_TYPE_NAME, DESCRIPTION, VALID_VALUES) VALUES
('BOOLEAN', 'Boolean', 'Boolean values (true/false)', 'true,false'),
('INTEGER', 'Integer', 'Integer values', ''),
('STRING', 'String', 'String values', ''),
('CYCLIC', 'Cyclic', 'Cyclic values (YEARLY, QUARTERLY, HALF_YEAR)', 'YEARLY,QUARTERLY,HALF_YEAR');

-- Add comments to the new table
ALTER TABLE ATTENDANCE_ATTRIBUTE_TYPES COMMENT = 'Reference table for valid attendance attribute data types';

-- Create indexes for better performance
CREATE INDEX idx_attendance_attribute_types_code ON ATTENDANCE_ATTRIBUTE_TYPES(ATTRIBUTE_TYPE_CODE);

-- Add foreign key constraint to ensure data integrity (optional)
-- ALTER TABLE EMP_ATTENDANCE_METADATA 
-- ADD CONSTRAINT FK_EMP_ATTENDANCE_METADATA_ATTRIBUTE_TYPE 
-- FOREIGN KEY (ATTRIBUTE_TYPE) REFERENCES ATTENDANCE_ATTRIBUTE_TYPES(ATTRIBUTE_TYPE_CODE);

-- Update table comment to reflect the enhanced structure
ALTER TABLE EMP_ATTENDANCE_METADATA COMMENT = 'Stores department-wise attendance configuration metadata with enhanced type information (BOOLEAN, INTEGER, STRING, CYCLIC)';
