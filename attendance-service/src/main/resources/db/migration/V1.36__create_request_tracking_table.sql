-- Create generic request tracking table
CREATE TABLE request_tracking (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    request_id VARCHAR(50) NOT NULL UNIQUE,
    unit_id INT NOT NULL,
    api_identifier VARCHAR(200) NOT NULL,
    reference_id VARCHAR(100),
    request_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    completion_time TIMESTAMP NULL,
    error_message TEXT,
    error_code VARCHAR(50),
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_request_id (request_id),
    INDEX idx_unit_id (unit_id),
    INDEX idx_api_identifier (api_identifier),
    INDEX idx_reference_id (reference_id),
    INDEX idx_request_time (request_time),
    INDEX idx_completion_time (completion_time),
    INDEX idx_error_code (error_code)
);

-- Add comments to table and columns
ALTER TABLE request_tracking COMMENT = 'Generic table to track API request processing status and metrics';

ALTER TABLE request_tracking 
MODIFY COLUMN request_id VARCHAR(50) COMMENT 'Unique request ID from MDC context (RequestIdInterceptor)',
MODIFY COLUMN unit_id INT COMMENT 'Unit ID from JWT context',
MODIFY COLUMN api_identifier VARCHAR(200) COMMENT 'API path (e.g., /api/attendance/punch)',
MODIFY COLUMN reference_id VARCHAR(100) COMMENT 'Business reference ID (e.g., attendance request ID)',
MODIFY COLUMN request_time TIMESTAMP COMMENT 'Request start time',
MODIFY COLUMN completion_time TIMESTAMP COMMENT 'Request completion time',
MODIFY COLUMN error_message TEXT COMMENT 'Error message if request failed',
MODIFY COLUMN error_code VARCHAR(50) COMMENT 'Error code if request failed',
MODIFY COLUMN updated_at TIMESTAMP COMMENT 'Last update time';
