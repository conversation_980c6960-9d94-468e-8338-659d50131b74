-- Create SHIFT_COVERAGE_PLAN table for unitemporal data
CREATE TABLE SHIFT_COVERAGE_PLAN (
    ID INT AUTO_INCREMENT PRIMARY KEY,
    SHIFT_CAFE_MAPPING_ID INT NOT NULL,
    DAY STRING ,
    IDEAL_COUNT INT NOT NULL,
    PROCESSING_FROM TIMESTAMP NULL,
    PROCESSING_TO TIMESTAMP NULL,
    STATUS VARCHAR(45) NOT NULL DEFAULT 'ACTIVE',
    CREATED_BY VARCHAR(100),
    CREATION_TIME TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UPDATED_BY VARCHAR(100),
    UPDATION_TIME TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign key constraint
   -- CONSTRAINT FK_SHIFT_COVERAGE_PLAN_SHIFT_CAFE_MAPPING_ID FOREIGN KEY (SHIFT_CAFE_MAPPING_ID) REFERENCES SHIFT_CAFE_MAPPING(SHIFT_CAFE_MAPPING_ID)
);

-- <PERSON>reate indexes for better performance
CREATE INDEX idx_shift_coverage_plan_shift_cafe_mapping_id ON SHIFT_COVERAGE_PLAN(SHIFT_CAFE_MAPPING_ID);
CREATE INDEX idx_shift_coverage_plan_day ON SHIFT_COVERAGE_PLAN(DAY);
CREATE INDEX idx_shift_coverage_plan_status ON SHIFT_COVERAGE_PLAN(STATUS);
CREATE INDEX idx_shift_coverage_plan_processing_from ON SHIFT_COVERAGE_PLAN(PROCESSING_FROM);
CREATE INDEX idx_shift_coverage_plan_processing_to ON SHIFT_COVERAGE_PLAN(PROCESSING_TO);

-- Create composite indexes for common queries
CREATE INDEX idx_shift_coverage_plan_mapping_day ON SHIFT_COVERAGE_PLAN(SHIFT_CAFE_MAPPING_ID, DAY);
CREATE INDEX idx_shift_coverage_plan_mapping_status ON SHIFT_COVERAGE_PLAN(SHIFT_CAFE_MAPPING_ID, STATUS);
CREATE INDEX idx_shift_coverage_plan_processing_range ON SHIFT_COVERAGE_PLAN(PROCESSING_FROM, PROCESSING_TO);

-- Create unique constraint to prevent duplicate active mappings for same shift_cafe_mapping_id and day
CREATE UNIQUE INDEX idx_shift_coverage_plan_unique_active ON SHIFT_COVERAGE_PLAN(SHIFT_CAFE_MAPPING_ID, DAY, STATUS, PROCESSING_TO);

-- Add comments
COMMENT ON TABLE SHIFT_COVERAGE_PLAN IS 'Stores shift coverage plans with ideal employee counts per day using unitemporal data';
COMMENT ON COLUMN SHIFT_COVERAGE_PLAN.ID IS 'Primary key for shift coverage plan';
COMMENT ON COLUMN SHIFT_COVERAGE_PLAN.SHIFT_CAFE_MAPPING_ID IS 'Foreign key reference to SHIFT_CAFE_MAPPING table';
COMMENT ON COLUMN SHIFT_COVERAGE_PLAN.DAY IS 'Day of week (1-7: Monday to Sunday)';
COMMENT ON COLUMN SHIFT_COVERAGE_PLAN.IDEAL_COUNT IS 'Ideal number of employees for this shift on this day';
COMMENT ON COLUMN SHIFT_COVERAGE_PLAN.PROCESSING_FROM IS 'Processing start time for unitemporal data';
COMMENT ON COLUMN SHIFT_COVERAGE_PLAN.PROCESSING_TO IS 'Processing end time for unitemporal data (NULL means current/active)';
COMMENT ON COLUMN SHIFT_COVERAGE_PLAN.STATUS IS 'Status of the coverage plan (ACTIVE, INACTIVE)';
COMMENT ON COLUMN SHIFT_COVERAGE_PLAN.CREATED_BY IS 'User who created the coverage plan';
COMMENT ON COLUMN SHIFT_COVERAGE_PLAN.CREATION_TIME IS 'Timestamp when coverage plan was created';
COMMENT ON COLUMN SHIFT_COVERAGE_PLAN.UPDATED_BY IS 'User who last updated the coverage plan';
COMMENT ON COLUMN SHIFT_COVERAGE_PLAN.UPDATION_TIME IS 'Timestamp when coverage plan was last updated';
