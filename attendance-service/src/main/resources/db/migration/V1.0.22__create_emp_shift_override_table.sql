-- V1.0.22__create_emp_shift_override_table.sql
CREATE TABLE emp_shift_override (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    emp_id INT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    shift_id INT NOT NULL,
    override_reason VARCHAR(255) NULL,
    status VARCHAR(45) NOT NULL DEFAULT 'ACTIVE',
    created_by VARCHAR(100) NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(100) NULL,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_emp_id (emp_id),
    INDEX idx_start_date (start_date),
    INDEX idx_end_date (end_date),
    INDEX idx_shift_id (shift_id),
    INDEX idx_status (status),
    INDEX idx_date_range (start_date, end_date)
); 