-- Create EMP_SHIFT_OVERRIDE table for employee shift overrides
CREATE TABLE emp_shift_override (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    emp_id INT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    shift_id INT NOT NULL,
    status VARCHAR(45) NOT NULL DEFAULT 'ACTIVE',
    created_by <PERSON><PERSON><PERSON><PERSON>(100),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(100),
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    CONSTRAINT FK_emp_shift_override_emp_id FOREIGN KEY (emp_id) REFERENCES employee(id),
    CONSTRAINT FK_emp_shift_override_shift_id FOREIGN KEY (shift_id) REFERENCES shift(shift_id)
);

-- Create indexes for better performance
CREATE INDEX idx_emp_shift_override_emp_id ON emp_shift_override(emp_id);
CREATE INDEX idx_emp_shift_override_shift_id ON emp_shift_override(shift_id);
CREATE INDEX idx_emp_shift_override_status ON emp_shift_override(status);
CREATE INDEX idx_emp_shift_override_date_range ON emp_shift_override(start_date, end_date);

-- Create composite indexes for common queries
CREATE INDEX idx_emp_shift_override_emp_status ON emp_shift_override(emp_id, status);
CREATE INDEX idx_emp_shift_override_emp_date ON emp_shift_override(emp_id, start_date, end_date);

-- Create unique constraint to prevent duplicate active overrides for same employee and date range
CREATE UNIQUE INDEX idx_emp_shift_override_unique_active ON emp_shift_override(emp_id, start_date, end_date, status);

-- Add comments
COMMENT ON TABLE emp_shift_override IS 'Stores employee shift overrides for temporary shift assignments';
COMMENT ON COLUMN emp_shift_override.id IS 'Primary key for employee shift override';
COMMENT ON COLUMN emp_shift_override.emp_id IS 'Foreign key reference to employee table';
COMMENT ON COLUMN emp_shift_override.start_date IS 'Start date of the override period';
COMMENT ON COLUMN emp_shift_override.end_date IS 'End date of the override period';
COMMENT ON COLUMN emp_shift_override.shift_id IS 'Foreign key reference to shift table';
COMMENT ON COLUMN emp_shift_override.status IS 'Status of the override (ACTIVE, INACTIVE)';
COMMENT ON COLUMN emp_shift_override.created_by IS 'User who created the override';
COMMENT ON COLUMN emp_shift_override.created_at IS 'Timestamp when override was created';
COMMENT ON COLUMN emp_shift_override.updated_by IS 'User who last updated the override';
COMMENT ON COLUMN emp_shift_override.updated_at IS 'Timestamp when override was last updated'; 