-- V1.0.21__create_employee_shift_instances_table.sql
CREATE TABLE employee_shift_instances (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    emp_id INT NOT NULL,
    business_date DATE NOT NULL,
    shift_id INT NOT NULL,
    expected_start_time DATETIME NOT NULL,
    expected_end_time DATETIME NOT NULL,
    ideal_hours DECIMAL(5,2) NOT NULL,
    actual_start_time DATETIME NULL,
    actual_end_time DATETIME NULL,
    actual_hours DECIMAL(5,2) NULL,
    schedule_status VARCHAR(20) NOT NULL DEFAULT 'SCHEDULED',
    is_override BOOLEAN NOT NULL DEFAULT FALSE,
    override_reason VARCHAR(255) NULL,
    created_by VARCHAR(100) NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(100) NULL,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_emp_date (emp_id, business_date),
    INDEX idx_emp_id (emp_id),
    INDEX idx_business_date (business_date),
    INDEX idx_shift_id (shift_id),
    INDEX idx_schedule_status (schedule_status)
); 