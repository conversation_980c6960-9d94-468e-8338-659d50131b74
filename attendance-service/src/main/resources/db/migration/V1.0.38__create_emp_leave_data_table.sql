-- Migration: Create EMP_LEAVE_DATA table for employee leave balance management
-- This table stores the available leave counts for each employee by leave type

CREATE TABLE EMP_LEAVE_DATA (
    EMP_LEAVE_DATA_ID INT AUTO_INCREMENT PRIMARY KEY,
    EMP_ID INT NOT NULL,
    LEAVE_COUNT INT NOT NULL DEFAULT 0,
    COMP_OFF_COUNT INT NOT NULL DEFAULT 0,
    LWP_COUNT INT NOT NULL DEFAULT 0,
    WEEK_OFF_COUNT INT NOT NULL DEFAULT 0,
    CREATED_ON DATETIME DEFAULT CURRENT_TIMESTAMP,
    UPDATED_ON DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    CREATED_BY VARCHAR(50),
    UPDATED_BY VARCHAR(50)
);

-- Add unique constraint to ensure one record per employee
ALTER TABLE EMP_LEAVE_DATA ADD CONSTRAINT UQ_EMP_LEAVE_DATA_EMP_ID UNIQUE (EMP_ID);

-- Add foreign key constraint to EMP_LEAVE_DATA table
-- Note: This assumes there's an employee table with EMP_ID as primary key
-- ALTER TABLE EMP_LEAVE_DATA ADD CONSTRAINT FK_EMP_LEAVE_DATA_EMP_ID 
--     FOREIGN KEY (EMP_ID) REFERENCES EMPLOYEE(EMP_ID);

-- Add indexes for better query performance
CREATE INDEX IDX_EMP_LEAVE_DATA_EMP_ID ON EMP_LEAVE_DATA(EMP_ID);
CREATE INDEX IDX_EMP_LEAVE_DATA_LEAVE_COUNT ON EMP_LEAVE_DATA(LEAVE_COUNT);
CREATE INDEX IDX_EMP_LEAVE_DATA_COMP_OFF_COUNT ON EMP_LEAVE_DATA(COMP_OFF_COUNT);
CREATE INDEX IDX_EMP_LEAVE_DATA_LWP_COUNT ON EMP_LEAVE_DATA(LWP_COUNT);

-- Add comments to document the table purpose
COMMENT ON TABLE EMP_LEAVE_DATA IS 'Stores employee leave balance counts for different leave types';
COMMENT ON COLUMN EMP_LEAVE_DATA.EMP_LEAVE_DATA_ID IS 'Primary key for leave balance record';
COMMENT ON COLUMN EMP_LEAVE_DATA.EMP_ID IS 'Employee ID (foreign key to employee table)';
COMMENT ON COLUMN EMP_LEAVE_DATA.LEAVE_COUNT IS 'Available leave count for the employee';
COMMENT ON COLUMN EMP_LEAVE_DATA.COMP_OFF_COUNT IS 'Available comp-off count for the employee';
COMMENT ON COLUMN EMP_LEAVE_DATA.LWP_COUNT IS 'Available LWP (Leave Without Pay) count for the employee';
COMMENT ON COLUMN EMP_LEAVE_DATA.WEEK_OFF_COUNT IS 'Available week-off count for the employee';
COMMENT ON COLUMN EMP_LEAVE_DATA.CREATED_ON IS 'Timestamp when the record was created';
COMMENT ON COLUMN EMP_LEAVE_DATA.UPDATED_ON IS 'Timestamp when the record was last updated';
COMMENT ON COLUMN EMP_LEAVE_DATA.CREATED_BY IS 'User who created the record';
COMMENT ON COLUMN EMP_LEAVE_DATA.UPDATED_BY IS 'User who last updated the record'; 