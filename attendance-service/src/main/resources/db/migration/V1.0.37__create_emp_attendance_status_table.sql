-- Create EMP_ATTENDANCE_STATUS table for employee attendance status tracking
-- This table will be the parent table of EMP_ATTENDANCE_REQUEST
CREATE TABLE EMP_ATTENDANCE_STATUS (
    ID BIGINT AUTO_INCREMENT PRIMARY KEY,
    EMP_ATTENDANCE_APPROVAL_ID BIGINT,
    EMP_ID BIGINT NOT NULL,
    FROM_DATE DATETIME NOT NULL,
    TO_DATE DATETIME NOT NULL,
    STATUS VARCHAR(50),
    TYPE VARCHAR(45),
    CREATED_BY VARCHAR(50),
    CREATED_ON DATETIME DEFAULT CURRENT_TIMESTAMP,
    UPDATED_BY VARCHAR(50),
    UPDATED_ON DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    CONSTRAINT FK_emp_attendance_status_emp_id FOREIGN KEY (EMP_ID) REFERENCES employee(id),
    CONSTRAINT FK_emp_attendance_status_approval_id FOREIGN KEY (EMP_ATTENDANCE_APPROVAL_ID) REFERENCES EMP_ATTENDANCE_REQUEST(ID)
);

-- Create indexes for better performance
CREATE INDEX idx_emp_attendance_status_emp_id ON EMP_ATTENDANCE_STATUS(EMP_ID);
CREATE INDEX idx_emp_attendance_status_approval_id ON EMP_ATTENDANCE_STATUS(EMP_ATTENDANCE_APPROVAL_ID);
CREATE INDEX idx_emp_attendance_status_status ON EMP_ATTENDANCE_STATUS(STATUS);
CREATE INDEX idx_emp_attendance_status_type ON EMP_ATTENDANCE_STATUS(TYPE);
CREATE INDEX idx_emp_attendance_status_date_range ON EMP_ATTENDANCE_STATUS(FROM_DATE, TO_DATE);

-- Create composite indexes for common queries
CREATE INDEX idx_emp_attendance_status_emp_status ON EMP_ATTENDANCE_STATUS(EMP_ID, STATUS);
CREATE INDEX idx_emp_attendance_status_emp_type ON EMP_ATTENDANCE_STATUS(EMP_ID, TYPE);
CREATE INDEX idx_emp_attendance_status_emp_date ON EMP_ATTENDANCE_STATUS(EMP_ID, FROM_DATE, TO_DATE);

-- Add comments
COMMENT ON TABLE EMP_ATTENDANCE_STATUS IS 'Stores employee attendance status records as parent table of EMP_ATTENDANCE_REQUEST';
COMMENT ON COLUMN EMP_ATTENDANCE_STATUS.ID IS 'Primary key for employee attendance status';
COMMENT ON COLUMN EMP_ATTENDANCE_STATUS.EMP_ATTENDANCE_APPROVAL_ID IS 'Foreign key reference to EMP_ATTENDANCE_REQUEST table';
COMMENT ON COLUMN EMP_ATTENDANCE_STATUS.EMP_ID IS 'Foreign key reference to employee table';
COMMENT ON COLUMN EMP_ATTENDANCE_STATUS.FROM_DATE IS 'Start date of the attendance period';
COMMENT ON COLUMN EMP_ATTENDANCE_STATUS.TO_DATE IS 'End date of the attendance period';
COMMENT ON COLUMN EMP_ATTENDANCE_STATUS.STATUS IS 'Status of the attendance record (PENDING, APPROVED, REJECTED, etc.)';
COMMENT ON COLUMN EMP_ATTENDANCE_STATUS.TYPE IS 'Type of attendance record (LEAVE, OD_WFH, REGULARISATION, etc.)';
COMMENT ON COLUMN EMP_ATTENDANCE_STATUS.CREATED_BY IS 'User who created the record';
COMMENT ON COLUMN EMP_ATTENDANCE_STATUS.CREATED_ON IS 'Timestamp when the record was created';
COMMENT ON COLUMN EMP_ATTENDANCE_STATUS.UPDATED_BY IS 'User who last updated the record';
COMMENT ON COLUMN EMP_ATTENDANCE_STATUS.UPDATED_ON IS 'Timestamp when the record was last updated'; 