-- V1.0.37__create_biometric_additional_images_table.sql
-- Create table for storing additional images during biometric registration

CREATE TABLE BIOMETRIC_ADDITIONAL_IMAGES (
    ID BIGINT NOT NULL AUTO_INCREMENT,
    BIOMETRIC_REGISTRATION_ID BIGINT NOT NULL,
    IMAGE_TYPE VARCHAR(50) NOT NULL,
    BASE64_IMAGE TEXT NOT NULL,
    METADATA TEXT NULL,
    CREATED_AT TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UPDATED_AT TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    CREATED_BY VARCHAR(100) NULL,
    UPDATED_BY VARCHAR(100) NULL,
    PRIMARY KEY (ID),
    CONSTRAINT FK_BAI_BIOMETRIC_REGISTRATION_ID FOREIGN KEY (BIOMETRIC_REGISTRATION_ID) 
        REFERENCES BIOMETRIC_REGISTRATION(ID) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IDX_BAI_BIOMETRIC_REGISTRATION_ID ON BIOMETRIC_ADDITIONAL_IMAGES(BIOMETRIC_REGISTRATION_ID) USING BTREE;
CREATE INDEX IDX_BAI_IMAGE_TYPE ON BIOMETRIC_ADDITIONAL_IMAGES(IMAGE_TYPE) USING BTREE;
CREATE INDEX IDX_BAI_CREATED_AT ON BIOMETRIC_ADDITIONAL_IMAGES(CREATED_AT) USING BTREE;
