-- Create attendance_images table
CREATE TABLE attendance_images (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    attendance_record_id BIGINT NOT NULL,
    image_type VARCHAR(50) NOT NULL,
    image_url VARCHAR(500),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key constraint
    CONSTRAINT fk_attendance_images_record_id 
        FOREIGN KEY (attendance_record_id) 
        REFERENCES attendance_records(id) 
        ON DELETE CASCADE,
    
    -- Index for better performance
    INDEX idx_attendance_images_record_id (attendance_record_id),
    INDEX idx_attendance_images_type (image_type),
    INDEX idx_attendance_images_created_at (created_at)
);

-- Add comment to table
ALTER TABLE attendance_images COMMENT = 'Stores attendance images with their types and links to attendance records'; 