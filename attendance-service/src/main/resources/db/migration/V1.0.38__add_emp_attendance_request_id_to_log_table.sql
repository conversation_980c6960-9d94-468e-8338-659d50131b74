-- Migration: Add EMP_ATTENDANCE_REQUEST_ID column to EMP_ATTENDANCE_REQUEST_LOG table
-- This column references the primary key of the EMP_ATTENDANCE_REQUEST table

-- Add the new column
ALTER TABLE EMP_ATTENDANCE_REQUEST_LOG 
ADD COLUMN EMP_ATTENDANCE_REQUEST_ID BIGINT NOT NULL DEFAULT 0;

-- Add comment for the new column
COMMENT ON COLUMN EMP_ATTENDANCE_REQUEST_LOG.EMP_ATTENDANCE_REQUEST_ID IS 'Foreign key reference to EMP_ATTENDANCE_REQUEST table primary key';

-- Create index for better query performance
CREATE INDEX IDX_EMP_ATTENDANCE_REQUEST_LOG_REQUEST_ID ON EMP_ATTENDANCE_REQUEST_LOG(EMP_ATTENDANCE_REQUEST_ID);

-- Add foreign key constraint (optional - can be added later if needed)
-- ALTER TABLE EMP_ATTENDANCE_REQUEST_LOG 
-- ADD CONSTRAINT FK_EMP_ATTENDANCE_REQUEST_LOG_REQUEST_ID 
-- FOREIGN KEY (EMP_ATTENDANCE_REQUEST_ID) REFERENCES EMP_ATTENDANCE_REQUEST(ID);

-- Update existing records if any (set to 0 as default)
-- This is a safety measure in case there are existing log records
UPDATE EMP_ATTENDANCE_REQUEST_LOG 
SET EMP_ATTENDANCE_REQUEST_ID = 0 
WHERE EMP_ATTENDANCE_REQUEST_ID IS NULL;
