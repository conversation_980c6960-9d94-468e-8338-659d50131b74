-- Add INSTANCE_STATUS column to employee_shift_instances table
ALTER TABLE employee_shift_instances ADD COLUMN instance_status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE';

-- Create index for better performance on instance status queries
CREATE INDEX idx_employee_shift_instances_instance_status ON employee_shift_instances(instance_status);
CREATE INDEX idx_employee_shift_instances_emp_instance_status ON employee_shift_instances(emp_id, instance_status);

-- Add comment for the new column
COMMENT ON COLUMN employee_shift_instances.instance_status IS 'Status of the shift instance (ACTIVE/INACTIVE) - separate from attendance status'; 