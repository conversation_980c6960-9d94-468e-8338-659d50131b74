-- Add UNIT_ID column to employee_shift_instances table
ALTER TABLE employee_shift_instances ADD COLUMN unit_id INT NOT NULL DEFAULT 26091;

-- Create index for better performance on unit-based queries
CREATE INDEX idx_employee_shift_instances_unit_id ON employee_shift_instances(unit_id);
CREATE INDEX idx_employee_shift_instances_emp_unit_date ON employee_shift_instances(emp_id, unit_id, business_date);

-- Populate UNIT_ID based on emp eligibility mapping
-- Find the first active emp eligibility mapping for each employee and update the unit_id
UPDATE employee_shift_instances esi
SET unit_id = (
    SELECT CAST(eem.value AS UNSIGNED)
    FROM EMP_ELIGIBILITY_MAPPING eem
    WHERE eem.emp_id = CAST(esi.emp_id AS CHAR)
    AND eem.mapping_type = 'UNIT'
    AND eem.eligibility_type = 'ATTENDANCE'
    AND eem.status = 'ACTIVE'
    AND (eem.start_date IS NULL OR eem.start_date <= esi.business_date)
    AND (eem.end_date IS NULL OR eem.end_date >= esi.business_date)
    ORDER BY eem.id ASC
    LIMIT 1
)
WHERE EXISTS (
    SELECT 1
    FROM EMP_ELIGIBILITY_MAPPING eem
    WHERE eem.emp_id = CAST(esi.emp_id AS CHAR)
    AND eem.mapping_type = 'UNIT'
    AND eem.eligibility_type = 'ATTENDANCE'
    AND eem.status = 'ACTIVE'
    AND (eem.start_date IS NULL OR eem.start_date <= esi.business_date)
    AND (eem.end_date IS NULL OR eem.end_date >= esi.business_date)
);

-- Add comment for the new column
COMMENT ON COLUMN employee_shift_instances.unit_id IS 'Unit ID where the employee is assigned for this shift instance'; 