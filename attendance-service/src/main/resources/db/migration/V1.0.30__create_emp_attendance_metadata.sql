-- Migration: Create EMP_ATTENDANCE_METADATA table
-- Version: 1.0.30
-- Description: Creates table for storing department-wise attendance configuration metadata

CREATE TABLE EMP_ATTENDANCE_METADATA (
    EMP_ATTENDANCE_METADATA_ID INT AUTO_INCREMENT PRIMARY KEY,
    DEPT_ID INT NOT NULL,
    ATTRIBUTE_ID INT,
    ATTRIBUTE_CODE VARCHAR(45) NOT NULL,
    ATTRIBUTE_TYPE VARCHAR(45),
    ATTRIBUTE_VALUE VARCHAR(45),
    MAPPING_STATUS VARCHAR(45) NOT NULL DEFAULT 'ACTIVE',
    CREATED_BY VARCHAR(100),
    UPDATED_BY VARCHAR(100),
    CREATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UPDATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX idx_emp_attendance_metadata_dept_id ON EMP_ATTENDANCE_METADATA(DEPT_ID);
CREATE INDEX idx_emp_attendance_metadata_attribute_code ON EMP_ATTENDANCE_METADATA(ATTRIBUTE_CODE);
CREATE INDEX idx_emp_attendance_metadata_mapping_status ON EMP_ATTENDANCE_METADATA(MAPPING_STATUS);
CREATE INDEX idx_emp_attendance_metadata_dept_attr ON EMP_ATTENDANCE_METADATA(DEPT_ID, ATTRIBUTE_CODE, MAPPING_STATUS);

-- Insert default metadata (DEPT_ID = -1) for common configurations
INSERT INTO EMP_ATTENDANCE_METADATA (DEPT_ID, ATTRIBUTE_CODE, ATTRIBUTE_TYPE, ATTRIBUTE_VALUE, MAPPING_STATUS, CREATED_BY) VALUES
(-1, 'IS_LEAVE_ALLOWED', 'BOOLEAN', 'true', 'ACTIVE', 'SYSTEM'),
(-1, 'IS_OD_ALLOWED', 'BOOLEAN', 'true', 'ACTIVE', 'SYSTEM'),
(-1, 'IS_WFH_ALLOWED', 'BOOLEAN', 'false', 'ACTIVE', 'SYSTEM'),
(-1, 'IS_REGULARISATION_ALLOWED', 'BOOLEAN', 'true', 'ACTIVE', 'SYSTEM'),
(-1, 'LEAVE_CREDIT_CYCLE', 'CYCLE', 'YEARLY', 'ACTIVE', 'SYSTEM'),
(-1, 'TOTAL_NUMBER_OF_LEAVES', 'NUMERIC', '21', 'ACTIVE', 'SYSTEM'),
(-1, 'PAYROLL_PROCESSING_START_DAY', 'NUMERIC', '25', 'ACTIVE', 'SYSTEM'),
(-1, 'PAYROLL_PROCESSING_END_DAY', 'NUMERIC', '5', 'ACTIVE', 'SYSTEM'),
(-1, 'IS_HOLIDAY_ALLOWED', 'BOOLEAN', 'true', 'ACTIVE', 'SYSTEM'),
(-1, 'WEEKEND_DAYS', 'WEEKEND_DAYS', 'SATURDAY,SUNDAY', 'ACTIVE', 'SYSTEM');

-- Add comments to the table
ALTER TABLE EMP_ATTENDANCE_METADATA COMMENT = 'Stores department-wise attendance configuration metadata including leave permissions, cycles, and payroll processing days';


