-- Create DAILY_ATTENDANCE_SUMMARY_LOGS table
CREATE TABLE DAILY_ATTENDANCE_SUMMARY_LOGS (
    ID INT AUTO_INCREMENT PRIMARY KEY,
    DAILY_ATTENDANCE_SUMMARY_ID INT NOT NULL,
    FIRST_CHECK_IN DATETIME,
    LAST_CHECK_OUT DATETIME,
    STATUS VARCHAR(45),
    ACTION_TYPE VARCHAR(20) NOT NULL,
    UPDATED_BY VARCHAR(100) NOT NULL,
    UPDATION_TIME DATETIME NOT NULL,
    CREATED_AT DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- Add foreign key constraint
    CONSTRAINT FK_DAILY_ATTENDANCE_SUMMARY_LOGS_SUMMARY 
        FOREIGN KEY (DAILY_ATTENDANCE_SUMMARY_ID) 
        REFERENCES daily_attendance_summary(ID) 
        ON DELETE CASCADE,
    
    -- Add index for better performance
    INDEX IDX_DAILY_ATTENDANCE_SUMMARY_LOGS_SUMMARY_ID (DAILY_ATTENDANCE_SUMMARY_ID),
    INDEX IDX_DAILY_ATTENDANCE_SUMMARY_LOGS_ACTION_TYPE (ACTION_TYPE),
    INDEX IDX_DAILY_ATTENDANCE_SUMMARY_LOGS_CREATED_AT (CREATED_AT)
);
