-- Migration: Change leave balance columns from FLOAT to DECIMAL for better precision
-- This migration updates the EMP_ATTENDANCE_BALANCE_DATA table to use DECIMAL instead of FLOAT

-- Update EMP_ATTENDANCE_BALANCE_DATA table columns to use DECIMAL(10,2)
ALTER TABLE EMP_ATTENDANCE_BALANCE_DATA 
MODIFY COLUMN LEAVE_COUNT DECIMAL(10,2) DEFAULT 0.00;

ALTER TABLE EMP_ATTENDANCE_BALANCE_DATA 
MODIFY COLUMN COMP_OFF_COUNT DECIMAL(10,2) DEFAULT 0.00;

ALTER TABLE EMP_ATTENDANCE_BALANCE_DATA 
MODIFY COLUMN LWP_COUNT DECIMAL(10,2) DEFAULT 0.00;

-- Update EMP_ATTENDANCE_RESERVE_DATA table columns to use DECIMAL(10,2)
ALTER TABLE EMP_ATTENDANCE_RESERVE_DATA 
MODIFY COLUMN RESERVED_COUNT DECIMAL(10,2) DEFAULT 0.00;

-- Add comments to document the precision
COMMENT ON COLUMN EMP_ATTENDANCE_BALANCE_DATA.LEAVE_COUNT IS 'Available leave count for the employee (DECIMAL for precision)';
COMMENT ON COLUMN EMP_ATTENDANCE_BALANCE_DATA.COMP_OFF_COUNT IS 'Available comp-off count for the employee (DECIMAL for precision)';
COMMENT ON COLUMN EMP_ATTENDANCE_BALANCE_DATA.LWP_COUNT IS 'Available LWP count for the employee (DECIMAL for precision)';
COMMENT ON COLUMN EMP_ATTENDANCE_RESERVE_DATA.RESERVED_COUNT IS 'Reserved leave count for the employee (DECIMAL for precision)';
