-- Migration: Create EMP_LEAVE_DATA_LOGS table for tracking leave balance changes
-- This table stores logs of all leave balance deductions and restorations

CREATE TABLE EMP_LEAVE_DATA_LOGS (
    ID INT AUTO_INCREMENT PRIMARY KEY,
    EMP_LEAVE_DATA_ID INT NOT NULL,
    STATUS_FROM VARCHAR(45) NOT NULL COMMENT 'Previous status (DEBIT/CREDIT)',
    STATUS_TO VARCHAR(45) NOT NULL COMMENT 'New status (DEBIT/CREDIT)',
    TYPE VARCHAR(45) NOT NULL COMMENT 'Leave type (LEAVE, COMP_OFF, LWP, WEEK_OFF)',
    OLD_COUNT DECIMAL(10,2) COMMENT 'Leave balance before the change',
    NEW_COUNT DECIMAL(10,2) COMMENT 'Leave balance after the change',
    UPDATED_BY VARCHAR(45) NOT NULL COMMENT 'User who performed the update',
    UPDATED_ON DATETIME NOT NULL COMMENT 'Timestamp when the update occurred'
);

-- Add foreign key constraint to EMP_LEAVE_DATA table
ALTER TABLE EMP_LEAVE_DATA_LOGS ADD CONSTRAINT FK_EMP_LEAVE_DATA_LOGS_EMP_LEAVE_DATA_ID
    FOREIGN KEY (EMP_LEAVE_DATA_ID) REFERENCES EMP_LEAVE_DATA(EMP_LEAVE_DATA_ID);

-- Add indexes for better query performance
CREATE INDEX IDX_EMP_LEAVE_DATA_LOGS_EMP_LEAVE_DATA_ID ON EMP_LEAVE_DATA_LOGS(EMP_LEAVE_DATA_ID);
CREATE INDEX IDX_EMP_LEAVE_DATA_LOGS_TYPE ON EMP_LEAVE_DATA_LOGS(TYPE);
CREATE INDEX IDX_EMP_LEAVE_DATA_LOGS_STATUS_FROM ON EMP_LEAVE_DATA_LOGS(STATUS_FROM);
CREATE INDEX IDX_EMP_LEAVE_DATA_LOGS_STATUS_TO ON EMP_LEAVE_DATA_LOGS(STATUS_TO);
CREATE INDEX IDX_EMP_LEAVE_DATA_LOGS_UPDATED_ON ON EMP_LEAVE_DATA_LOGS(UPDATED_ON);

-- Add comments to document the table purpose
COMMENT ON TABLE EMP_LEAVE_DATA_LOGS IS 'Stores logs of all leave balance changes (deductions and restorations)';
COMMENT ON COLUMN EMP_LEAVE_DATA_LOGS.ID IS 'Primary key for the log entry';
COMMENT ON COLUMN EMP_LEAVE_DATA_LOGS.EMP_LEAVE_DATA_ID IS 'Foreign key to EMP_LEAVE_DATA table';
COMMENT ON COLUMN EMP_LEAVE_DATA_LOGS.STATUS_FROM IS 'Previous status before the change (DEBIT/CREDIT)';
COMMENT ON COLUMN EMP_LEAVE_DATA_LOGS.STATUS_TO IS 'New status after the change (DEBIT/CREDIT)';
COMMENT ON COLUMN EMP_LEAVE_DATA_LOGS.TYPE IS 'Type of leave that was modified (LEAVE, COMP_OFF, LWP, WEEK_OFF)';
COMMENT ON COLUMN EMP_LEAVE_DATA_LOGS.OLD_COUNT IS 'Leave balance before the change occurred';
COMMENT ON COLUMN EMP_LEAVE_DATA_LOGS.NEW_COUNT IS 'Leave balance after the change occurred';
COMMENT ON COLUMN EMP_LEAVE_DATA_LOGS.UPDATED_BY IS 'User ID who performed the leave balance update';
COMMENT ON COLUMN EMP_LEAVE_DATA_LOGS.UPDATED_ON IS 'Timestamp when the leave balance was updated';
