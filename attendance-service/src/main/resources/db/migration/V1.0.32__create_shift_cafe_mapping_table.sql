-- Create SHIFT_CAFE_MAPPING table
CREATE TABLE SHIFT_CAFE_MAPPING (
    SHIFT_CAFE_MAPPING_ID INT AUTO_INCREMENT PRIMARY KEY,
    SHIFT_ID INT NOT NULL,
    UNIT_ID INT NOT NULL,
    STATUS VARCHAR(45) NOT NULL DEFAULT 'ACTIVE',
    CREATED_BY VARCHAR(100),
    CREATION_TIME TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UPDATED_BY VARCHAR(100),
    UPDATION_TIME TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign key constraint
    --CONSTRAINT FK_SHIFT_CAFE_MAPPING_SHIFT_ID FOREIGN KEY (SHIFT_ID) REFERENCES SHIFTS(SHIFT_ID)
);

-- Create indexes for better performance
CREATE INDEX idx_shift_cafe_mapping_shift_id ON SHIFT_CAFE_MAPPING(SHIFT_ID);
CREATE INDEX idx_shift_cafe_mapping_unit_id ON SHIFT_CAFE_MAPPING(UNIT_ID);
CREATE INDEX idx_shift_cafe_mapping_status ON SHIFT_CAFE_MAPPING(STATUS);

-- Create unique constraint to prevent duplicate active mappings
CREATE UNIQUE INDEX idx_shift_cafe_mapping_unique ON SHIFT_CAFE_MAPPING(SHIFT_ID, UNIT_ID, STATUS);

-- Add comments
COMMENT ON TABLE SHIFT_CAFE_MAPPING IS 'Maps shifts to units/cafes';
COMMENT ON COLUMN SHIFT_CAFE_MAPPING.SHIFT_CAFE_MAPPING_ID IS 'Primary key for shift cafe mapping';
COMMENT ON COLUMN SHIFT_CAFE_MAPPING.SHIFT_ID IS 'Foreign key reference to SHIFTS table';
COMMENT ON COLUMN SHIFT_CAFE_MAPPING.UNIT_ID IS 'Unit/Cafe ID where the shift is applicable';
COMMENT ON COLUMN SHIFT_CAFE_MAPPING.STATUS IS 'Status of the mapping (ACTIVE, INACTIVE)';
COMMENT ON COLUMN SHIFT_CAFE_MAPPING.CREATED_BY IS 'User who created the mapping';
COMMENT ON COLUMN SHIFT_CAFE_MAPPING.CREATION_TIME IS 'Timestamp when mapping was created';
COMMENT ON COLUMN SHIFT_CAFE_MAPPING.UPDATED_BY IS 'User who last updated the mapping';
COMMENT ON COLUMN SHIFT_CAFE_MAPPING.UPDATION_TIME IS 'Timestamp when mapping was last updated';
