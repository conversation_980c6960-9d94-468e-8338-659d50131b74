-- Migration: Create EMP_HOLIDAYS table
-- Version: 1.0.31
-- Description: Creates table for storing system-wide holidays with financial year grouping

CREATE TABLE EMP_HOLIDAYS (
    HOLIDAY_ID INT AUTO_INCREMENT PRIMARY KEY,
    FINANCIAL_YEAR VARCHAR(10) NOT NULL,
    H<PERSON><PERSON>AY_NAME VARCHAR(100) NOT NULL,
    HOLIDAY_DATE DATE NOT NULL,
    IS_FULL_DAY BOOLEAN NOT NULL DEFAULT TRUE,
    CREATED_BY VARCHAR(100),
    UPDATED_BY VARCHAR(100),
    CREATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UPDATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX idx_emp_holidays_financial_year ON EMP_HOLIDAYS(FINANCIAL_YEAR);
CREATE INDEX idx_emp_holidays_holiday_date ON EMP_HOLIDAYS(HOLIDAY_DATE);
CREATE INDEX idx_emp_holidays_name ON EMP_HOLIDAYS(HOLIDAY_NAME);

-- Insert sample holidays for 2024-25 financial year
INSERT INTO EMP_HOLIDAYS (FINANCIAL_YEAR, HOLIDAY_NAME, HOLIDAY_DATE, IS_FULL_DAY, CREATED_BY) VALUES
('2024-25', 'Republic Day', '2025-01-26', TRUE, 'SYSTEM'),
('2024-25', 'Independence Day', '2025-08-15', TRUE, 'SYSTEM'),
('2024-25', 'Gandhi Jayanti', '2025-10-02', TRUE, 'SYSTEM'),
('2024-25', 'Christmas', '2024-12-25', TRUE, 'SYSTEM'),
('2024-25', 'New Year', '2025-01-01', TRUE, 'SYSTEM');

-- Add comments to the table
ALTER TABLE EMP_HOLIDAYS COMMENT = 'Stores system-wide holidays with financial year grouping, including full day and half day holidays';
