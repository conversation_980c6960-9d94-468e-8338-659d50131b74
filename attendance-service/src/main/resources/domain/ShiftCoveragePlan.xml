<MithraObject objectType="transactional">
    <PackageName>com.stpl.tech.attendance.domain</PackageName>
    <ClassName>ShiftCoveragePlan</ClassName>
    <DefaultTable>SHIFT_COVERAGE_PLAN</DefaultTable>

    <!-- 1. AsOfAttribute for unitemporal data (only processingDate) -->
    <AsOfAttribute name="processingDate"
                   fromColumnName="PROCESSING_FROM"
                   toColumnName="PROCESSING_TO"
                   toIsInclusive="false"
                   isProcessingDate="true"
                   infinityDate="[java.sql.Timestamp.valueOf(&quot;9999-12-01 23:59:00.000&quot;)]"
                   defaultIfNotSpecified="[java.sql.Timestamp.valueOf(java.time.LocalDateTime.now())]"
                   futureExpiringRowsExist="true"/>

    <!-- 2. Then attributes -->
    <Attribute name="id" javaType="int" primaryKey="true" columnName="ID" primaryKeyGeneratorStrategy="Max"/>
    <Attribute name="shiftCafeMappingId" javaType="int" columnName="SHIFT_CAFE_MAPPING_ID"/>
    <Attribute name="day" javaType="String" columnName="DAY"/>
    <Attribute name="idealCount" javaType="int" columnName="IDEAL_COUNT"/>
    <Attribute name="status" javaType="String" columnName="STATUS"/>
    <Attribute name="createdBy" javaType="String" columnName="CREATED_BY"/>
    <Attribute name="creationTime" javaType="Timestamp" columnName="CREATION_TIME"/>
    <Attribute name="updatedBy" javaType="String" columnName="UPDATED_BY"/>
    <Attribute name="updationTime" javaType="Timestamp" columnName="UPDATION_TIME"/>
</MithraObject>