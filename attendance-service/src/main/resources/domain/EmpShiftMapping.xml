<MithraObject objectType="transactional">
    <PackageName>com.stpl.tech.attendance.domain</PackageName>
    <ClassName>EmpShiftMapping</ClassName>
    <DefaultTable>EMP_SHIFT_MAPPING</DefaultTable>

    <!-- 1. AsOfAttributes first -->
    <AsOfAttribute name="businessDate"
                   fromColumnName="BUSINESS_FROM"
                   toColumnName="BUSINESS_TO"
                   toIsInclusive="false"
                   isProcessingDate="false"
                   infinityDate="[java.sql.Timestamp.valueOf(&quot;9999-12-01 23:59:00.000&quot;)]"
                   defaultIfNotSpecified="[java.sql.Timestamp.valueOf(java.time.LocalDateTime.now())]"
                   futureExpiringRowsExist="true"/>

    <AsOfAttribute name="processingDate"
                   fromColumnName="PROCESSING_FROM"
                   toColumnName="PROCESSING_TO"
                   toIsInclusive="false"
                   isProcessingDate="true"
                   infinityDate="[java.sql.Timestamp.valueOf(&quot;9999-12-01 23:59:00.000&quot;)]"
                   defaultIfNotSpecified="[java.sql.Timestamp.valueOf(java.time.LocalDateTime.now())]"
                   futureExpiringRowsExist="true"/>

    <!-- 3. Then attributes -->
    <Attribute name="id" javaType="int" primaryKey="true" primaryKeyGeneratorStrategy="Max" columnName="ID"/>
    <Attribute name="shiftId" javaType="int" columnName="SHIFT_ID"/>
    <Attribute name="empId" javaType="int" columnName="EMP_ID"/>
    <Attribute name="unitId" javaType="int" columnName="UNIT_ID"/>
    <Attribute name="expectedStartDate" javaType="Timestamp" columnName="EXPECTED_START_DATE"/>
    <Attribute name="expectedEndDate" javaType="Timestamp" columnName="EXPECTED_END_DATE"/>
    <Attribute name="status" javaType="String" columnName="STATUS"/>
    <Attribute name="createdBy" javaType="String" columnName="CREATED_BY"/>
    <Attribute name="creationTime" javaType="Timestamp" columnName="CREATION_TIME"/>
    <Attribute name="updatedBy" javaType="String" columnName="UPDATED_BY"/>
    <Attribute name="updationTime" javaType="Timestamp" columnName="UPDATION_TIME"/>
</MithraObject>
