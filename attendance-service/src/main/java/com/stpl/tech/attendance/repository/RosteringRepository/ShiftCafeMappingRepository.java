package com.stpl.tech.attendance.repository.RosteringRepository;

import com.stpl.tech.attendance.entity.RosteringEntity.ShiftCafeMapping;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ShiftCafeMappingRepository extends JpaRepository<ShiftCafeMapping, Integer> {
    
    /**
     * Find all mappings by shift ID
     */
    List<ShiftCafeMapping> findByShiftIdAndStatusAndUnitId(Integer shiftId, String status, Integer unitId);

    List<ShiftCafeMapping> findByShiftIdAndStatus(Integer shiftId, String status);

    /**
     * Find all mappings by unit ID
     */
    List<ShiftCafeMapping> findByUnitIdAndStatus(Integer unitId, String status);
    
    /**
     * Find mapping by shift ID and unit ID
     */
    @Query("SELECT scm FROM ShiftCafeMapping scm WHERE scm.shiftId = :shiftId AND scm.unitId = :unitId")
    List<ShiftCafeMapping> findByShiftIdAndUnitIdAndStatus(
        @Param("shiftId") Integer shiftId,
        @Param("unitId") Integer unitId);

    @Query("SELECT scm FROM ShiftCafeMapping scm WHERE scm.shiftId = :shiftId AND scm.unitId = :unitId AND scm.status = :status")
    ShiftCafeMapping findByShiftIdAndUnitIdAndStatusUpdate(Integer shiftId, Integer unitId, String status);

    /**
     * Find all active mappings
     */
    List<ShiftCafeMapping> findByStatus(String status);
    
    /**
     * Get all units for a specific shift
     */
    @Query("SELECT scm.unitId FROM ShiftCafeMapping scm WHERE scm.shiftId = :shiftId AND scm.status = :status")
    List<Integer> findUnitIdsByShiftIdAndStatus(@Param("shiftId") Integer shiftId, @Param("status") String status);
    
    /**
     * Get all shifts for a specific unit
     */
    @Query("SELECT scm.shiftId FROM ShiftCafeMapping scm WHERE scm.unitId = :unitId AND scm.status = :status")
    List<Integer> findShiftIdsByUnitIdAndStatus(@Param("unitId") Integer unitId, @Param("status") String status);
    
    /**
     * Check if mapping exists
     */
    boolean existsByShiftIdAndUnitIdAndStatus(Integer shiftId, Integer unitId, String status);
    
    /**
     * Get shift cafe mappings with shift details
     */
    @Query("SELECT scm FROM ShiftCafeMapping scm JOIN FETCH scm.shift s WHERE scm.unitId = :unitId AND scm.status = :status")
    List<ShiftCafeMapping> findByUnitIdAndStatusWithShift(@Param("unitId") Integer unitId, @Param("status") String status);

    List<ShiftCafeMapping> findByShiftId(Integer shiftId);
}
