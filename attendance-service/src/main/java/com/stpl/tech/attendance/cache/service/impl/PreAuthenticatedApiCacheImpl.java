package com.stpl.tech.attendance.cache.service.impl;

import com.hazelcast.collection.IList;
import com.hazelcast.core.HazelcastInstance;
import com.stpl.tech.attendance.cache.service.PreAuthenticatedApiCache;
import com.stpl.tech.attendance.cache.constants.CacheConstants;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;

@Log4j2
@Service
public class PreAuthenticatedApiCacheImpl implements PreAuthenticatedApiCache {

    @Autowired
    @Qualifier(value = "MasterHazelCastInstance")
    private HazelcastInstance instance;

    private IList<String> preAuthenticatedAPIs;

    @Override
    public IList<String> getPreAuthenticatedAPIs() {
        return preAuthenticatedAPIs;
    }

    @Override
    public void setPreAuthenticatedAPIs(List<String> preAuthenticatedAPIs) {
        this.preAuthenticatedAPIs.clear();
        this.preAuthenticatedAPIs.addAll(preAuthenticatedAPIs);
    }

    @PostConstruct
    public void loadPreAuthenticatedApiCache() {
        log.info("POST-CONSTRUCT PreAuthenticatedApiCache - STARTED");
        long time = System.currentTimeMillis();
        this.preAuthenticatedAPIs = instance.getList(CacheConstants.PREAUTH_API_CACHE);
        log.info("POST-CONSTRUCT PreAuthenticatedApiCache took {} ms", System.currentTimeMillis() - time);
    }

    @Override
    public String toString() {
        return "PreAuthenticatedApiCache{" + "preAuthenticatedAPIs=" + this.preAuthenticatedAPIs.size() + '}';
    }
} 