package com.stpl.tech.attendance.service;

import com.stpl.tech.master.core.external.acl.service.TokenDao;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import com.stpl.tech.attendance.model.JWTToken;

import java.util.Map;

public interface TokenService<T> {
    /**
     * Parse and validate a token string into a token object
     * @param token The token object to populate
     * @param tokenString The token string to parse
     * @throws Exception if token is invalid or expired
     */
    void parseToken(T object, String jwt) throws Exception;



} 