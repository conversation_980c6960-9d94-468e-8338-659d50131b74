package com.stpl.tech.attendance.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "EMP_ATTENDANCE_REQUEST_LOG")
public class EmployeeAttendanceRequestLog {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "EMP_ATTENDANCE_REQUEST_LOG_ID")
    private Long empAttendanceRequestLogId;
    
    @Column(name = "EMP_ATTENDANCE_REQUEST_ID", nullable = false)
    private Long empAttendanceRequestId;
    
    @Column(name = "EMP_ID", nullable = false)
    private Integer empId;
    
    @Column(name = "FROM_STATUS", length = 50)
    private String fromStatus;

    @Column(name = "TO_STATUS", length = 50)
    private String toStatus;
    
    @Column(name = "TYPE", length = 45)
    private String type;

    @Column(name = "UPDATED_BY", length = 50)
    private String updatedBy;

    @Column(name = "UPDATED_ON")
    private LocalDateTime updatedOn;
} 