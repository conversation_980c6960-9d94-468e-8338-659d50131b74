package com.stpl.tech.attendance.cache.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stpl.tech.attendance.cache.service.BiometricCache;
import com.stpl.tech.attendance.cache.service.BiometricCacheOperations;
import com.stpl.tech.attendance.cache.service.UnitCacheService;
import com.stpl.tech.attendance.cache.service.UserCacheService;
import com.stpl.tech.attendance.dto.BiometricRegistrationDTO;
import com.stpl.tech.attendance.entity.BiometricRegistration;
import com.stpl.tech.attendance.enums.BiometricStatus;
import com.stpl.tech.attendance.repository.BiometricRegistrationRepository;
import com.stpl.tech.attendance.service.RedisMetricsService;
import io.micrometer.core.instrument.Timer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@RequiredArgsConstructor
public class BiometricCacheImpl implements BiometricCache {

    private static final String CACHE_KEY_PREFIX = "biometric:registration:";
    private static final long CACHE_TTL_HOURS = 24;
    private static final int BATCH_SIZE = 100;

    private final RedisTemplate<String, Object> redisTemplate;
    private final RedisMetricsService redisMetricsService;
    private final BiometricRegistrationRepository biometricRegistrationRepository;
    private final UserCacheService userCacheService;
    private final UnitCacheService unitCacheService;
    private final ObjectMapper objectMapper;
    private final BiometricCacheOperations biometricCacheOperations;

    @Override
    @Cacheable(value = "biometricRegistrations", key = "#empId")
    public BiometricRegistrationDTO getBiometricRegistration(String empId) {
        log.debug("Fetching biometric registration for employee: {}", empId);
        Optional<BiometricRegistration> registrationOptional = biometricRegistrationRepository.findByEmpIdAndStatusIn(empId,List.of(BiometricStatus.APPROVED, BiometricStatus.PENDING));
        return registrationOptional.map(biometricRegistration -> BiometricRegistrationDTO.fromEntity(biometricRegistration, unitCacheService)).orElse(null);
    }

    /**
     * Updates the cache with new biometric registration data using @CachePut
     * This method will always update the cache regardless of whether the key exists
     * 
     * @param empId the employee ID
     * @return the updated BiometricRegistrationDTO, or null if not found
     */
    @Override
    public BiometricRegistrationDTO updateBiometricRegistration(String empId) {
        // Delegate to the separate cache operations service to avoid Spring proxy issues
        return biometricCacheOperations.updateBiometricRegistration(empId);
    }

    @Override
    public void removeBiometricRegistration(String empId) {
        // Delegate to the separate cache operations service to avoid Spring proxy issues
        biometricCacheOperations.removeBiometricRegistration(empId);
    }

      @Override
    public void handleRegistrationStatusChange(String empId, BiometricStatus status) {
          log.info("Handling registration status change for employee: {} to status: {}", empId, status);
          // This will work because we're calling through a separate service (Spring proxy will intercept)
          BiometricRegistrationDTO updatedRegistration = biometricCacheOperations.updateBiometricRegistration(empId);
          if (updatedRegistration != null) {
              log.info("Successfully updated cache for employee: {} with status: {}", empId, status);
          } else {
              log.warn("No registration found for employee: {} after status change to: {}", empId, status);
          }
    }

    //@PostConstruct
    public void initializeCache() {
        log.info("Initializing biometric registration cache");
        List<BiometricRegistration> allRegistrations = biometricRegistrationRepository.findAll();
        
        // Process in batches to avoid memory issues
        for (int i = 0; i < allRegistrations.size(); i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, allRegistrations.size());
            List<BiometricRegistration> batch = allRegistrations.subList(i, endIndex);
            
            // Update cache for each registration in batch
            for (BiometricRegistration registration : batch) {
                BiometricRegistrationDTO dto = BiometricRegistrationDTO.fromEntity(registration, unitCacheService);
                cacheBiometricRegistration(dto);
            }
            
            log.info("Cached batch of {} registrations", batch.size());
        }

        
        log.info("Cache initialization completed");
    }

    @Override
    public void cacheBiometricRegistration(BiometricRegistrationDTO registration) {
        String key = CACHE_KEY_PREFIX + registration.getEmpId();
        try {
            String jsonValue = objectMapper.writeValueAsString(registration);
            redisTemplate.opsForValue().set(key, jsonValue, CACHE_TTL_HOURS, TimeUnit.HOURS);
            log.debug("Cached biometric registration for employee: {}", registration.getEmpId());
        } catch (JsonProcessingException e) {
            log.error("Failed to cache biometric registration for employee: {}", registration.getEmpId(), e);
        }
    }
}