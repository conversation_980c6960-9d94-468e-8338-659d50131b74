package com.stpl.tech.attendance.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "EMP_ATTENDANCE_REQUEST_DETAIL")
public class EmployeeAttendanceRequestDetail {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;
    
    @Column(name = "ATTENDANCE_REQ_ID", nullable = false)
    private Long attendanceReqId;
    
    @Column(name = "DATE", nullable = false)
    private LocalDateTime date;
    
    @Column(name = "TYPE", length = 45, nullable = false)
    private String type; // LEAVE, COMP_OFF, LWP
    
    @Column(name = "REQUEST_TYPE", length = 45, nullable = false)
    private String requestType; // FULL_DAY, FIRST_HALF, SECOND_HALF
}
