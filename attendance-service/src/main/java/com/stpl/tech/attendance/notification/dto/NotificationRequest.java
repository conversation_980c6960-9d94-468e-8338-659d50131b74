package com.stpl.tech.attendance.notification.dto;

import com.stpl.tech.attendance.notification.entity.Notification.NotificationType;
import com.stpl.tech.attendance.notification.entity.Notification.Priority;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationRequest {
    private NotificationType type;
    private String title;
    private String message;
    private Priority priority;
    private List<String> recipientIds;
    private Map<String, Object> metadata;
    private String requesterId;
} 