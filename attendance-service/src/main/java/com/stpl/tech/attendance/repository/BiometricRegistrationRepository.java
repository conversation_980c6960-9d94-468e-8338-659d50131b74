package com.stpl.tech.attendance.repository;

import com.stpl.tech.attendance.entity.BiometricRegistration;
import com.stpl.tech.attendance.enums.ApprovalStatus;
import com.stpl.tech.attendance.enums.BiometricStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface BiometricRegistrationRepository extends JpaRepository<BiometricRegistration, Long> {
    boolean existsByEmpIdAndStatusNot(String empId, BiometricStatus status);
    boolean existsByEmpIdAndStatusIn(String empId, List<BiometricStatus> statuses);
    Optional<BiometricRegistration> findByEmpIdAndStatusNot(String empId, BiometricStatus status);
    BiometricRegistration findByEmpId(String empId);

    List<BiometricRegistration> findByEmpIdInAndStatus(List<String> employeeIds, BiometricStatus status);
    Optional<BiometricRegistration> findByEmpIdAndStatus(String empId, BiometricStatus status);
    Optional<BiometricRegistration> findByEmpIdAndStatusIn(String empId, List<BiometricStatus> status);
}