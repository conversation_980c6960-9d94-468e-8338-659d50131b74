package com.stpl.tech.attendance.model.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ErrorResponse {
    private String message;
    private String code;
    private Map<String, String> details;

    public ErrorResponse(String message, String code) {
        this.message= message;
        this.code = code;
    }
}