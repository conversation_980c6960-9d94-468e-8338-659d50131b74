package com.stpl.tech.attendance.cache.service.impl;

import com.hazelcast.core.HazelcastInstance;
import com.stpl.tech.attendance.cache.service.ACLCache;
import com.stpl.tech.attendance.cache.constants.CacheConstants;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Log4j2
@Service
public class ACLCacheImpl implements ACLCache {

    @Autowired
    @Qualifier(value = "MasterHazelCastInstance")
    private HazelcastInstance instance;

    private static Map<String, Map<String, Integer>> permissionMap;

    @Override
    public Boolean addPermissions(String sessionKey, Map<String, Integer> permissions) {
        if (Objects.nonNull(sessionKey)) {
            Map<String, Integer> permissionData = permissionMap.get(sessionKey);
            if (!CollectionUtils.isEmpty(permissionData)) {
                permissionMap.remove(sessionKey);
            }
            permissionMap.put(sessionKey, permissions);
            return true;
        }
        return false;
    }

    @Override
    public void removeFromCache(String sessionKey) {
        Map<String, Integer> permissionData = permissionMap.get(sessionKey);
        if (!CollectionUtils.isEmpty(permissionData)) {
            permissionMap.remove(sessionKey);
        } else {
            log.info("Trying to remove permissions for sessionKey {} which don't exist", sessionKey);
        }
    }

    @Override
    public Map<String, Map<String, Integer>> getPermissions(String sessionKey) {
        if (Objects.nonNull(sessionKey)) {
            Map<String, Map<String, Integer>> map = new HashMap<>();
            map.put(sessionKey, permissionMap.get(sessionKey));
            return map;
        }
        return null;
    }

    @PostConstruct
    public void createMaps() {
        log.info("POST-CONSTRUCT ACLCache - STARTED");
        long time = System.currentTimeMillis();
        permissionMap = instance.getMap(CacheConstants.ACL_CACHE);
        log.info("Inside POSTCONSTRUCT - ACLCache OVERALL : took {} ms", time - System.currentTimeMillis());
    }
} 