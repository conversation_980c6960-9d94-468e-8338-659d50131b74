package com.stpl.tech.attendance.model.response;

import com.stpl.tech.attendance.dto.EmployeeInfoDTO;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmployeeAttendanceApproval {
    
    private String requestType; // LEAVE_APPROVAL, OD_WFH_APPROVAL, REGULARISATION_APPROVAL
    private LocalDateTime startTime; // start time from attendance record
    private LocalDateTime endTime; // end time from attendance record
    private String status;
    private String reason;
    private EmployeeAttendanceMetadata metadata;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EmployeeAttendanceMetadata {
        private String comments;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        private EmployeeInfoDTO createdBy;
        private String documents; // empty for now
    }
} 