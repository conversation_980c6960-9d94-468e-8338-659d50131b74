package com.stpl.tech.attendance.validation;

import com.stpl.tech.attendance.cache.service.UserCacheService;
import com.stpl.tech.attendance.constants.AppConstants;
import com.stpl.tech.attendance.context.JwtContext;
import com.stpl.tech.attendance.dto.AttendanceMetadataResponse;
import com.stpl.tech.attendance.entity.EmployeeAttendanceRequest;
import com.stpl.tech.attendance.enums.AttendanceAttributeType;
import com.stpl.tech.attendance.exception.BusinessException;
import com.stpl.tech.attendance.repository.EmployeeAttendanceRequestRepository;
import com.stpl.tech.attendance.service.AttendanceMetadataService;
import com.stpl.tech.attendance.service.impl.AttendanceRequestUtil;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.time.LocalDateTime;
import java.util.Map;
import java.math.BigDecimal;

/**
 * Abstract base validator for attendance-related requests.
 * Contains common validation logic that can be shared across different attendance request types.
 */
@Slf4j
public abstract class AttendanceRequestValidator<T> extends BaseValidator<T> {
    
    protected final AttendanceRequestUtil attendanceRequestUtil;
    protected final UserCacheService userCacheService;
    protected final AttendanceMetadataService attendanceMetadataService;
    protected final EmployeeAttendanceRequestRepository employeeAttendanceRequestRepository;

    
    protected AttendanceRequestValidator(AttendanceRequestUtil attendanceRequestUtil, UserCacheService userCacheService, AttendanceMetadataService attendanceMetadataService, EmployeeAttendanceRequestRepository employeeAttendanceRequestRepository) {
        this.attendanceRequestUtil = attendanceRequestUtil;
        this.userCacheService = userCacheService;
        this.attendanceMetadataService = attendanceMetadataService;
        this.employeeAttendanceRequestRepository = employeeAttendanceRequestRepository;
    }

    /**
     * Validates that the employee ID exists in the JWT context
     * 
     * @return The employee ID from the context
     * @throws BusinessException if employee ID is not found
     */
    protected Integer validateEmployeeContext() {
        Integer empId = JwtContext.getInstance().getUserId();
        if (empId == null) {
            throw new BusinessException("Employee ID not found in authentication context");
        }
        return empId;
    }

    public void validateFromMetadata(Integer empId, String type) {
        final AttendanceAttributeType attributeType = getAnEnum(type);
        EmployeeBasicDetail employee = userCacheService.getUserById(empId);

        // 1. Default metadata
        AttendanceMetadataResponse effectiveMetadata = attendanceMetadataService.getDefaultMetadata(attributeType);
        // 2. Department wise metadata
        Map<AttendanceAttributeType, AttendanceMetadataResponse> metadataMap =
                attendanceMetadataService.getAllMetadataForDept(employee.getDepartmentId());
        //override if present in dept wise metadata
        if (metadataMap.containsKey(attributeType)) {
            effectiveMetadata = metadataMap.get(attributeType);
        }
        if(attributeType == AttendanceAttributeType.FIXED_WEEK_OFF){
            if ("true".equalsIgnoreCase(effectiveMetadata.getAttributeValue())) {
                throw new BusinessException("Fixed week off is not allowed for your department");
            }
        }else if ("false".equalsIgnoreCase(effectiveMetadata.getAttributeValue())) {
            assert attributeType != null;
            throw new BusinessException(type + " is not allowed for your department");
        }
    }


    @Nullable
    private static AttendanceAttributeType getAnEnum(String type) {
        AttendanceAttributeType AttributeType = null;
        if(type.equalsIgnoreCase(AppConstants.REGULARISATION)){
            AttributeType = AttendanceAttributeType.IS_REGULARISATION_ALLOWED;
        } else if(type.equalsIgnoreCase(AppConstants.LEAVE)){
            AttributeType = AttendanceAttributeType.IS_LEAVE_ALLOWED;
        } else if(type.equalsIgnoreCase(AppConstants.OD)){
            AttributeType = AttendanceAttributeType.IS_OD_ALLOWED;
        } else if(type.equalsIgnoreCase(AppConstants.WFH)){
            AttributeType = AttendanceAttributeType.IS_WFH_ALLOWED;
        } else if(type.equalsIgnoreCase(AppConstants.WEEK_OFF)){
            AttributeType = AttendanceAttributeType.FIXED_WEEK_OFF;
        }
        return AttributeType;
    }


    public void validateNoDateRangeOverlaps(Integer empId, List<LocalDateTime> requestDates) {
        if (requestDates == null || requestDates.isEmpty()) {
            return;
        }

        // Get all existing approved/pending requests for the employee
        List<EmployeeAttendanceRequest> existingRequests = employeeAttendanceRequestRepository
                .findByEmpIdAndStatusInAndTypeIn(empId, List.of(AppConstants.STATUS_APPROVED, AppConstants.STATUS_PENDING), List.of(AppConstants.LWP, AppConstants.COMP_OFF, AppConstants.LEAVE, AppConstants.OD, AppConstants.WFH, AppConstants.REGULARISATION));

        if (existingRequests.isEmpty()) {
            return; // No existing requests to check against
        }
        // Check each new request date against existing request date ranges
        for (LocalDateTime requestDate : requestDates) {
            LocalDate requestDateOnly = requestDate.toLocalDate();

            for (EmployeeAttendanceRequest existingRequest : existingRequests) {
                if (hasDateRangeOverlap(requestDateOnly, existingRequest)) {
                    log.error("Date range overlap detected for employee {} on date {}. Existing request: ID={}, Type={}, Date={}, StartTime={}, EndTime={}",
                            empId, requestDateOnly, existingRequest.getId(), existingRequest.getType(),
                            existingRequest.getDate(), existingRequest.getStartTime(), existingRequest.getEndTime());
                    throw new BusinessException("Request already present for this date");
                }
            }
        }
    }
    private boolean hasDateRangeOverlap(LocalDate requestDate, EmployeeAttendanceRequest existingRequest) {
        if(existingRequest.getDate() == null ){
            return false;
        }
        if (existingRequest.getStartTime() == null || existingRequest.getEndTime() == null) {
            // If start/end times are not set, fall back to simple date comparison
            return requestDate.equals(existingRequest.getDate().toLocalDate());
        }
        LocalDate existingStartDate = existingRequest.getStartTime().toLocalDate();
        LocalDate existingEndDate = existingRequest.getEndTime().toLocalDate();
        // Check if request date falls within the existing date range (inclusive)
        return !requestDate.isBefore(existingStartDate) && !requestDate.isAfter(existingEndDate);
    }

    private String formatDateRange(LocalDateTime startTime, LocalDateTime endTime) {
        if (startTime == null || endTime == null) {
            return "unknown date range";
        }

        LocalDate startDate = startTime.toLocalDate();
        LocalDate endDate = endTime.toLocalDate();

        if (startDate.equals(endDate)) {
            return startDate.format(DateTimeFormatter.ofPattern("dd MMM yyyy"));
        } else {
            return startDate.format(DateTimeFormatter.ofPattern("dd MMM yyyy")) +
                    " to " + endDate.format(DateTimeFormatter.ofPattern("dd MMM yyyy"));
        }
    }

    public void validateNoTimeOverlaps(Integer empId, LocalDateTime requestDate, LocalDateTime requestCheckIn, LocalDateTime requestCheckOut) {
        if (requestCheckIn == null || requestCheckOut == null) {
            return; // Skip validation if times are not provided
        }

        LocalDate requestDateOnly = requestDate.toLocalDate();

        // Get existing requests for the same date
        List<EmployeeAttendanceRequest> existingRequests = employeeAttendanceRequestRepository
                .findByEmpIdAndStatusIn(empId, List.of(AppConstants.STATUS_APPROVED, AppConstants.STATUS_PENDING));

        for (EmployeeAttendanceRequest existingRequest : existingRequests) {
            LocalDate existingDate = existingRequest.getDate().toLocalDate();

            // Only check requests on the same date
            if (!requestDateOnly.equals(existingDate)) {
                continue;
            }

            // Check if existing request has time information
            if (existingRequest.getStartTime() != null && existingRequest.getEndTime() != null) {
                LocalDateTime existingStart = existingRequest.getStartTime();
                LocalDateTime existingEnd = existingRequest.getEndTime();

                // Check for time overlap
                if (hasTimeOverlap(requestCheckIn, requestCheckOut, existingStart, existingEnd)) {
                    log.error("Time overlap detected for employee {} on date {}. New request: {} to {}, Existing request: {} to {} (Type: {})",
                            empId, requestDateOnly, requestCheckIn.toLocalTime(), requestCheckOut.toLocalTime(),
                            existingStart.toLocalTime(), existingEnd.toLocalTime(), existingRequest.getType());

                    throw new BusinessException(String.format(
                            "Time overlap detected on %s. You already have a %s request from %s to %s that conflicts with your new request time (%s to %s). " +
                                    "Please choose a different time or cancel the existing request first.",
                            requestDateOnly.format(DateTimeFormatter.ofPattern("dd MMM yyyy")),
                            existingRequest.getType(),
                            existingStart.toLocalTime().format(DateTimeFormatter.ofPattern("HH:mm")),
                            existingEnd.toLocalTime().format(DateTimeFormatter.ofPattern("HH:mm")),
                            requestCheckIn.toLocalTime().format(DateTimeFormatter.ofPattern("HH:mm")),
                            requestCheckOut.toLocalTime().format(DateTimeFormatter.ofPattern("HH:mm"))
                    ));
                }
            }
        }
    }
    private boolean hasTimeOverlap(LocalDateTime start1, LocalDateTime end1, LocalDateTime start2, LocalDateTime end2) {
        // Convert to time for comparison (ignoring date part)
        LocalTime timeStart1 = start1.toLocalTime();
        LocalTime timeEnd1 = end1.toLocalTime();
        LocalTime timeStart2 = start2.toLocalTime();
        LocalTime timeEnd2 = end2.toLocalTime();

        // Check if the time ranges overlap
        // Two time ranges overlap if: start1 < end2 AND start2 < end1
        return timeStart1.isBefore(timeEnd2) && timeStart2.isBefore(timeEnd1);
    }
    public void validateLeaveDateRangeOverlaps(Integer empId, List<LocalDateTime> leaveDates) {
        if (leaveDates == null || leaveDates.isEmpty()) {
            return;
        }
        // Get all existing approved/pending requests for the employee
        List<EmployeeAttendanceRequest> existingRequests = employeeAttendanceRequestRepository
                .findByEmpIdAndStatusIn(empId, List.of(AppConstants.STATUS_APPROVED, AppConstants.STATUS_PENDING));

        if (existingRequests.isEmpty()) {
            return; // No existing requests to check against
        }
        // Get the leave date range (start and end dates)
        List<LocalDateTime> sortedDates = new ArrayList<>(leaveDates);
        sortedDates.sort(LocalDateTime::compareTo);
        LocalDate leaveStartDate = sortedDates.get(0).toLocalDate();
        LocalDate leaveEndDate = sortedDates.get(sortedDates.size() - 1).toLocalDate();

        // Check each existing request against the leave date range
        for (EmployeeAttendanceRequest existingRequest : existingRequests) {
            LocalDate existingDate = existingRequest.getDate().toLocalDate();
            // Check if existing request date falls within the leave date range
            if (!existingDate.isBefore(leaveStartDate) && !existingDate.isAfter(leaveEndDate)) {
                log.error("Leave date range overlap detected for employee {}. Leave period: {} to {}, Existing request: ID={}, Type={}, Date={}",
                        empId, leaveStartDate, leaveEndDate, existingRequest.getId(), existingRequest.getType(), existingDate);

                throw new BusinessException(String.format(
                        "Leave already exists for the dates from %s to %s. Please choose a different date range.",
                        leaveStartDate.format(DateTimeFormatter.ofPattern("dd MMM yyyy")),
                        leaveEndDate.format(DateTimeFormatter.ofPattern("dd MMM yyyy"))
                ));
            }
        }
    }
    
    /**
     * Validates that the given dates list is not null or empty
     * 
     * @param dates The dates list to validate
     * @throws BusinessException if dates list is null or empty
     */
    protected void validateDatesList(List<LocalDateTime> dates) {
        if (dates == null || dates.isEmpty()) {
            throw new BusinessException("No dates provided in request");
        }
    }

    public void  validatePayrollProcessingDates(List<LocalDateTime> requestDates) {
        if (requestDates == null || requestDates.isEmpty()) {
            return;
        }
        try {
            Integer empId = JwtContext.getInstance().getUserId();
            EmployeeBasicDetail employee = userCacheService.getUserById(empId);
            BigDecimal endDay = attendanceMetadataService.getPayrollProcessingEndDay(employee.getDepartmentId());
            
            int payrollEndDay = endDay.intValue();
            LocalDateTime currentDate = LocalDateTime.now();
            LocalDateTime cutoffDate = calculatePayrollCutoffDate(currentDate, payrollEndDay);
            
            // Early exit if all dates are after cutoff
            boolean allDatesValid = requestDates.stream()
                .noneMatch(date -> date.toLocalDate().isBefore(cutoffDate.toLocalDate()));
                
            if (allDatesValid) {
                log.debug("All {} dates passed payroll validation (cutoff: {})", 
                         requestDates.size(), cutoffDate);
                return;
            }
            throw new BusinessException("Cannot apply as the payroll processing date is passed");
        } catch (Exception e) {
            log.error("Error during payroll processing date validation for {} dates", requestDates.size(), e);
            throw new BusinessException("Cannot apply as the payroll processing date is passed");
        }
    }

    private LocalDateTime calculatePayrollCutoffDate(LocalDateTime currentDate, int payrollEndDay) {
        LocalDate currentMonth = currentDate.toLocalDate();
        
        // If current date is past payroll end day, cutoff is payroll end day of current month
        if (currentDate.getDayOfMonth() > payrollEndDay) {
            return LocalDateTime.of(currentMonth.withDayOfMonth(payrollEndDay), LocalTime.MAX);
        } else {
            // If current date is before payroll end day, cutoff is payroll end day of previous month
            LocalDate previousMonth = currentMonth.minusMonths(1);
            return LocalDateTime.of(previousMonth.withDayOfMonth(payrollEndDay), LocalTime.MAX);
        }
    }
}
