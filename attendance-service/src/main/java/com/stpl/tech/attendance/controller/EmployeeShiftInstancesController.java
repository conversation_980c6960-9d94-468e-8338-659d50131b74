package com.stpl.tech.attendance.controller;

import com.stpl.tech.attendance.constants.ApiConstants;
import com.stpl.tech.attendance.dto.EmployeeShiftInstancesDTO;
import com.stpl.tech.attendance.dto.EmployeeShiftInstanceDetailDTO;
import com.stpl.tech.attendance.dto.EmployeeShiftInstancesDateResponseDTO;
import com.stpl.tech.attendance.dto.EmployeeShiftInstancesMonthAggregatedResponseDTO;
import com.stpl.tech.attendance.dto.EmployeeShiftInstancesMonthResponseDTO;
import com.stpl.tech.attendance.dto.EmployeeShiftInstancesMultiEmployeeResponseDTO;
import com.stpl.tech.attendance.dto.EmployeeShiftInstancesRangeResponseDTO;
import com.stpl.tech.attendance.dto.EmployeeShiftInstancesWeekResponseDTO;
import com.stpl.tech.attendance.dto.ShiftInstanceRecreationRequestDTO;
import com.stpl.tech.attendance.dto.ShiftInstanceRecreationResponseDTO;
import com.stpl.tech.attendance.entity.RosteringEntity.RosteringConstants;
import com.stpl.tech.attendance.model.response.ApiResponse;
import com.stpl.tech.attendance.service.EmployeeShiftSchedulerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping(ApiConstants.Paths.EMPLOYEE_SHIFT_INSTANCES)
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Employee Shift Instances", description = "Employee shift instances management APIs")
public class EmployeeShiftInstancesController extends BaseController {
    
    private final EmployeeShiftSchedulerService employeeShiftSchedulerService;
    
    /**
     * Manually recreate shift instances for specific date range and employees
     */
    @PostMapping("/recreate")
    @Operation(summary = "Recreate shift instances", description = "Manually recreate shift instances for specific date range and employees")
    public ResponseEntity<ApiResponse<ShiftInstanceRecreationResponseDTO>> recreateShiftInstances(
            @RequestBody ShiftInstanceRecreationRequestDTO request) {
        log.info("Received shift instance recreation request: {}", request);
        
        try {
            ShiftInstanceRecreationResponseDTO response = employeeShiftSchedulerService.recreateShiftInstances(request);
            log.info("Shift instances recreated successfully: {}", response);
            return success(response);
        } catch (Exception e) {
            log.error("Error recreating shift instances", e);
            return ResponseEntity.badRequest().body(
                ApiResponse.<ShiftInstanceRecreationResponseDTO>builder()
                    .success(false)
                    .error(ApiResponse.ErrorResponse.builder()
                        .code(RosteringConstants.ERROR_RECREATION_FAILED)
                        .message("Error: " + e.getMessage())
                        .build())
                    .build()
            );
        }
    }
    
    /**
     * Get shift instances for a specific employee within a date range
     */
    @GetMapping("/employee/{empId}")
    @Operation(summary = "Get employee shift instances", description = "Get shift instances for a specific employee within a date range")
    public ResponseEntity<ApiResponse<EmployeeShiftInstancesRangeResponseDTO>> getShiftInstancesForEmployee(
            @PathVariable Integer empId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        log.info("Getting shift instances for employee {} from {} to {}", empId, startDate, endDate);
        
        try {
            EmployeeShiftInstancesRangeResponseDTO response = employeeShiftSchedulerService
                .getShiftInstancesForEmployee(empId, startDate, endDate);
            log.info("Retrieved {} shift instances for employee {}", response.getTotalInstances(), empId);
            return success(response);
        } catch (Exception e) {
            log.error("Error getting shift instances for employee {}", empId, e);
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * Get shift instances for multiple employees within a date range
     */
    @PostMapping("/employees")
    @Operation(summary = "Get multiple employees shift instances", description = "Get shift instances for multiple employees within a date range")
    public ResponseEntity<ApiResponse<EmployeeShiftInstancesMultiEmployeeResponseDTO>> getShiftInstancesForEmployees(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestBody List<Integer> empIds) {
        log.info("Getting shift instances for {} employees from {} to {}", empIds.size(), startDate, endDate);
        
        try {
            EmployeeShiftInstancesMultiEmployeeResponseDTO response = employeeShiftSchedulerService
                .getShiftInstancesForEmployees(empIds, startDate, endDate);
            log.info("Retrieved {} shift instances for {} employees", response.getTotalInstances(), response.getTotalEmployees());
            return success(response);
        } catch (Exception e) {
            log.error("Error getting shift instances for employees", e);
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * Get shift instances for a specific date
     */
    @GetMapping("/date/{businessDate}")
    @Operation(summary = "Get shift instances for date", description = "Get shift instances for a specific date")
    public ResponseEntity<ApiResponse<EmployeeShiftInstancesDateResponseDTO>> getShiftInstancesForDate(
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate businessDate) {
        log.info("Getting shift instances for date {}", businessDate);
        
        try {
            EmployeeShiftInstancesDateResponseDTO response = employeeShiftSchedulerService
                .getShiftInstancesForDate(businessDate);
            log.info("Retrieved {} shift instances for date {}", response.getTotalInstances(), businessDate);
            return success(response);
        } catch (Exception e) {
            log.error("Error getting shift instances for date {}", businessDate, e);
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * Get current week shift instances
     */
    @GetMapping("/current-week")
    @Operation(summary = "Get current week shift instances", description = "Get shift instances for the current week")
    public ResponseEntity<ApiResponse<EmployeeShiftInstancesWeekResponseDTO>> getCurrentWeekShiftInstances(
            @RequestParam(required = false) Integer empId) {
        log.info("Getting current week shift instances for employee: {}", empId);
        
        try {
            EmployeeShiftInstancesWeekResponseDTO response = employeeShiftSchedulerService
                .getCurrentWeekShiftInstances(empId);
            log.info("Retrieved {} current week shift instances", response.getTotalInstances());
            return success(response);
        } catch (Exception e) {
            log.error("Error getting current week shift instances", e);
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * Get next week shift instances
     */
    @GetMapping("/next-week")
    @Operation(summary = "Get next week shift instances", description = "Get shift instances for the next week")
    public ResponseEntity<ApiResponse<EmployeeShiftInstancesWeekResponseDTO>> getNextWeekShiftInstances(
            @RequestParam(required = false) Integer empId) {
        log.info("Getting next week shift instances for employee: {}", empId);
        
        try {
            EmployeeShiftInstancesWeekResponseDTO response = employeeShiftSchedulerService
                .getNextWeekShiftInstances(empId);
            log.info("Retrieved {} next week shift instances", response.getTotalInstances());
            return success(response);
        } catch (Exception e) {
            log.error("Error getting next week shift instances", e);
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * Get employee shift instances by month with detailed attendance information
     */
    @GetMapping("/employee/{empId}/month")
    @Operation(summary = "Get employee shift instances by month", 
              description = "Get shift instances for a specific employee by month. For current month, returns data till current date. For past months, returns all data for that month.")
    public ResponseEntity<ApiResponse<EmployeeShiftInstancesMonthResponseDTO>> getEmployeeShiftInstancesByMonth(
            @PathVariable Integer empId,
            @RequestParam int year,
            @RequestParam int month) {
        log.info("Getting shift instances for employee {} for month {}/{}", empId, month, year);
        
        try {
            EmployeeShiftInstancesMonthResponseDTO response = employeeShiftSchedulerService
                .getEmployeeShiftInstancesByMonth(empId, year, month);
            log.info("Retrieved {} shift instances for employee {} for month {}/{}", 
                    response.getTotalInstances(), empId, month, year);
            return success(response);
        } catch (Exception e) {
            log.error("Error getting shift instances for employee {} for month {}/{}", empId, month, year, e);
            return ResponseEntity.badRequest().body(
                ApiResponse.<EmployeeShiftInstancesMonthResponseDTO>builder()
                    .success(false)
                    .error(ApiResponse.ErrorResponse.builder()
                        .code(RosteringConstants.ERROR_FETCH_FAILED)
                        .message("Error: " + e.getMessage())
                        .build())
                    .build()
            );
        }
    }
    
    /**
     * Get aggregated employee shift instances by month with summary statistics
     */
    @GetMapping("/employee/{empId}/month/aggregated")
    @Operation(summary = "Get aggregated employee shift instances by month", 
              description = "Get aggregated shift instances for a specific employee by month with summary statistics including total hours, on-time days, and attendance percentages.")
    public ResponseEntity<ApiResponse<EmployeeShiftInstancesMonthAggregatedResponseDTO>> getEmployeeShiftInstancesAggregatedByMonth(
            @PathVariable Integer empId,
            @RequestParam int year,
            @RequestParam int month) {
        log.info("Getting aggregated shift instances for employee {} for month {}/{}", empId, month, year);
        
        try {
            EmployeeShiftInstancesMonthAggregatedResponseDTO response = employeeShiftSchedulerService
                .getEmployeeShiftInstancesAggregatedByMonth(empId, year, month);
            log.info("Retrieved aggregated data for employee {} for month {}/{} with {} total days", 
                    empId, month, year, response.getTotalDays());
            return success(response);
        } catch (Exception e) {
            log.error("Error getting aggregated shift instances for employee {} for month {}/{}", empId, month, year, e);
            return ResponseEntity.badRequest().body(
                ApiResponse.<EmployeeShiftInstancesMonthAggregatedResponseDTO>builder()
                    .success(false)
                    .error(ApiResponse.ErrorResponse.builder()
                        .code(RosteringConstants.ERROR_FETCH_FAILED)
                        .message("Error: " + e.getMessage())
                        .build())
                    .build()
            );
        }
    }
} 