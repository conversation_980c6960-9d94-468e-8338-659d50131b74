package com.stpl.tech.attendance.service.impl;

import com.stpl.tech.attendance.entity.EmpAttendanceBalanceData;
import com.stpl.tech.attendance.exception.BusinessException;
import com.stpl.tech.attendance.repository.EmpAttendanceBalanceDataRepository;
import com.stpl.tech.attendance.service.LeaveBalanceService;
import com.stpl.tech.attendance.service.LeaveBalanceLoggingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.orm.ObjectOptimisticLockingFailureException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Implementation of LeaveBalanceService for managing employee leave balances
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LeaveBalanceServiceImpl implements LeaveBalanceService {

    private final EmpAttendanceBalanceDataRepository empAttendanceBalanceDataRepository;
    private final LeaveBalanceLoggingService leaveBalanceLoggingService;

    @Override
    public EmpAttendanceBalanceData getAttendanceBalanceBalance(Integer empId) {
        if (empId == null) {
            throw new IllegalArgumentException("Employee ID cannot be null");
        }
        
        return empAttendanceBalanceDataRepository.findByEmpId(empId).orElse(null);
    }

    @Override
    public void validateLeaveBalance(Integer empId, String leaveType, BigDecimal requestedCount) {
        if (empId == null) {
            throw new IllegalArgumentException("Employee ID cannot be null");
        }
        
        if (leaveType == null || leaveType.trim().isEmpty()) {
            throw new IllegalArgumentException("Leave type cannot be null or empty");
        }
        
        if (requestedCount == null || requestedCount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("Requested count must be positive");
        }

        BigDecimal availableCount = getAvailableLeaveCount(empId, leaveType);
        
        if (availableCount == null || availableCount.compareTo(requestedCount) < 0) {
            throw new BusinessException("Insufficient leave balance. Available: " + availableCount + ", Requested: " + requestedCount);
        }
    }

    @Override
    public BigDecimal getAvailableLeaveCount(Integer empId, String leaveType) {
        if (empId == null) {
            throw new IllegalArgumentException("Employee ID cannot be null");
        }
        
        if (leaveType == null || leaveType.trim().isEmpty()) {
            throw new IllegalArgumentException("Leave type cannot be null or empty");
        }

        EmpAttendanceBalanceData leaveData = empAttendanceBalanceDataRepository.findByEmpId(empId).orElse(null);
        if (leaveData == null) {
           // then put in db for that employee with counts 0
            EmpAttendanceBalanceData newLeaveData = EmpAttendanceBalanceData.builder()
                    .empId(empId)
                    .leaveCount(BigDecimal.ZERO)
                    .compOffCount(BigDecimal.ZERO)
                    .lwpCount(BigDecimal.ZERO)
                    .build();
            empAttendanceBalanceDataRepository.save(newLeaveData);
            return BigDecimal.ZERO;
        }

        return switch (leaveType.toUpperCase()) {
            case "LEAVE" -> leaveData.getLeaveCount();
            case "COMP_OFF" -> leaveData.getCompOffCount();
            case "LWP" -> leaveData.getLwpCount();
            default -> {
                log.warn("Unknown leave type: {}", leaveType);
                yield null;
            }
        };
    }

    @Override
    @Transactional
    public void updateLeaveBalance(Integer empId, String leaveType, BigDecimal count) {
        if (empId == null) {
            throw new IllegalArgumentException("Employee ID cannot be null");
        }
        
        if (leaveType == null || leaveType.trim().isEmpty()) {
            throw new IllegalArgumentException("Leave type cannot be null or empty");
        }
        
        if (count == null || count.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("Count must be non-null and non-negative");
        }

        // 1. Fetch with proper error handling
        EmpAttendanceBalanceData leaveData = empAttendanceBalanceDataRepository.findByEmpId(empId)
            .orElseThrow(() -> new BusinessException("Leave data not found for employee: " + empId));
        
        // 2. Store original version and values for logging
        Integer originalVersion = leaveData.getVersion();
        BigDecimal oldCount = getCurrentLeaveCount(leaveData, leaveType);
        
        // 3. Update the specific leave type count
        switch (leaveType.toUpperCase()) {
            case "LEAVE" -> leaveData.setLeaveCount(count);
            case "COMP_OFF" -> leaveData.setCompOffCount(count);
            case "LWP" -> leaveData.setLwpCount(count);
            default -> throw new IllegalArgumentException("Invalid leave type: " + leaveType);
        }

        // 4. Update metadata
        leaveData.setUpdatedOn(LocalDateTime.now());
        leaveData.setUpdatedBy(empId.toString());

        try {
            // 5. Save with version handling
            EmpAttendanceBalanceData savedData = empAttendanceBalanceDataRepository.save(leaveData);
            log.info("Updated leave balance for employee {}. Type: {}, Old Count: {}, New Count: {}, Version: {} -> {}", 
                    empId, leaveType, oldCount, count, originalVersion, savedData.getVersion());
            
            // 6. Log the change
            logLeaveBalanceChange(leaveData.getId(), leaveType, oldCount, count, empId.toString());
            
        } catch (ObjectOptimisticLockingFailureException e) {
            log.error("Concurrent modification detected for employee {} leave data. Version conflict: expected {}, but was modified by another transaction", 
                    empId, originalVersion);
            throw new BusinessException("Leave data was modified by another user. Please refresh and try again.");
        }
    }
    
    /**
     * Get the current leave count for a specific leave type from the leave data
     * @param leaveData The leave data entity
     * @param leaveType The type of leave
     * @return The current count for the leave type
     */
    private BigDecimal getCurrentLeaveCount(EmpAttendanceBalanceData leaveData, String leaveType) {
        return switch (leaveType.toUpperCase()) {
            case "LEAVE" -> leaveData.getLeaveCount();
            case "COMP_OFF" -> leaveData.getCompOffCount();
            case "LWP" -> leaveData.getLwpCount();
            default -> BigDecimal.ZERO;
        };
    }
    
    /**
     * Log the leave balance change based on whether it's a deduction or restoration
     * @param empLeaveDataId The employee leave data ID
     * @param leaveType The type of leave
     * @param oldCount The old count
     * @param newCount The new count
     * @param updatedBy The user who performed the update
     */
    private void logLeaveBalanceChange(Long empLeaveDataId, String leaveType, BigDecimal oldCount, BigDecimal newCount, String updatedBy) {
        if (oldCount == null) oldCount = BigDecimal.ZERO;
        if (newCount == null) newCount = BigDecimal.ZERO;
        
        // Use the new logging method that includes old and new count values
        leaveBalanceLoggingService.logLeaveBalanceChange(empLeaveDataId, leaveType, oldCount, newCount, updatedBy);
    }
} 