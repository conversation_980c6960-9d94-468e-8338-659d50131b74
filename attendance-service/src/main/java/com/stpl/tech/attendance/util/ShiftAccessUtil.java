package com.stpl.tech.attendance.util;

import com.stpl.tech.attendance.dto.RosteringDto.ShiftAccessConfig;
import com.stpl.tech.attendance.service.MetadataService;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Set;

@Component
public class ShiftAccessUtil {

    @Autowired
    private MetadataService metadataService;
    // Role identifiers
    private static final String ADMIN_EMPLOYEE = "ADMIN_EMPLOYEE";
    private static final String CAFE_DEPARTMENT = "ADMIN_DEPARTMENT";
    private static final String CAFE_MANAGE_DESIGNATION = "MANAGER_DESIGNATION";

    // Predefined configs
    private static final ShiftAccessConfig ADMIN_ACCESS =
            new ShiftAccessConfig(true, true, true, true, true, true,true);

    private static final ShiftAccessConfig PARTIAL_ACCESS =
            new ShiftAccessConfig(false, true, true, true, true,true,true);

    private static final ShiftAccessConfig EMPLOYEE_ACCESS =
            new ShiftAccessConfig(false, false, false, false, true,false,false);

    private static final ShiftAccessConfig AM_DAM_ACCESS =
            new ShiftAccessConfig(true, true, true, false, true,true,true);

    // Configurable rule map
    private static final Map<String, Set<Integer>> ROLE_MAP = Map.of(
            ADMIN_EMPLOYEE, Set.of(140199),// for admin access
            CAFE_DEPARTMENT, Set.of(101,114), // cafe or bcx tech
            CAFE_MANAGE_DESIGNATION, Set.of(1003) // cafe manager
    );

    public ShiftAccessConfig getAccessConfig(EmployeeBasicDetail employee) {
        if (employee == null) return EMPLOYEE_ACCESS;

        Integer empId = employee.getId();
        Integer deptId = employee.getDepartmentId();
        Integer desigId = employee.getDesignationId();

        Boolean isManagedUnits = isManagedUnits(employee);

        if (ROLE_MAP.get(ADMIN_EMPLOYEE).contains(empId)) {
            return ADMIN_ACCESS;
        }

        if (desigId != null && ROLE_MAP.get(CAFE_MANAGE_DESIGNATION).contains(desigId)) {
            return PARTIAL_ACCESS;
        }

        if(deptId != null && ROLE_MAP.get(CAFE_DEPARTMENT).contains(deptId) && isManagedUnits){
            return AM_DAM_ACCESS;
        }

        return EMPLOYEE_ACCESS;
    }

    private Boolean isManagedUnits(EmployeeBasicDetail employee) {
        return !metadataService.getManagedUnitIds(employee.getId()).isEmpty();
    }

}
