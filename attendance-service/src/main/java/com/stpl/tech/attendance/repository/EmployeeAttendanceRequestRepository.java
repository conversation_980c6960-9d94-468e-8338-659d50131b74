package com.stpl.tech.attendance.repository;

import com.stpl.tech.attendance.entity.EmployeeAttendanceRequest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import com.stpl.tech.attendance.entity.EmployeeAttendanceRequestDetail;

@Repository
public interface EmployeeAttendanceRequestRepository extends JpaRepository<EmployeeAttendanceRequest, Long> {
    
    /**
     * Find all attendance records for a specific employee (excluding WEEK_OFF)
     * @param empId The employee ID
     * @return List of attendance records
     */
    @Query("SELECT ea FROM EmployeeAttendanceRequest ea WHERE ea.empId = :empId AND ea.type != 'WEEK_OFF'")
    List<EmployeeAttendanceRequest> findByEmpId(@Param("empId") Integer empId);
    
    /**
     * Find attendance records for a specific employee and date
     * @param empId The employee ID
     * @param date The date
     * @return List of attendance records for the specific date
     */
    @Query("SELECT ea FROM EmployeeAttendanceRequest ea WHERE ea.empId = :empId AND CAST(ea.date AS date) = :date")
    List<EmployeeAttendanceRequest> findByEmpIdAndDate(@Param("empId") Integer empId, @Param("date") LocalDate date);

    @Query("SELECT ea FROM EmployeeAttendanceRequest ea WHERE ea.empId = :empId AND CAST(ea.date AS date) BETWEEN :startDate AND :endDate AND ea.status = :status")
    List<EmployeeAttendanceRequest> findByEmpIdAndDateBetweenAndStatus(@Param("empId") Integer empId, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate, @Param("status") String status);

    @Query("SELECT ea FROM EmployeeAttendanceRequest ea WHERE ea.empId = :empId AND ea.status = :status AND CAST(ea.date AS date) = :date")
    List<EmployeeAttendanceRequest> findByEmpIdAndStatusAndDate(@Param("empId") Integer empId, @Param("status") String status, @Param("date") LocalDate date);
    
    /**
     * Find attendance records for a specific employee within a date range
     * @param empId The employee ID
     * @param startDate The start date
     * @param endDate The end date
     * @return List of attendance records within the date range
     */
    @Query("SELECT ea FROM EmployeeAttendanceRequest ea WHERE ea.empId = :empId AND CAST(ea.date AS date) BETWEEN :startDate AND :endDate")
    List<EmployeeAttendanceRequest> findByEmpIdAndDateBetween(@Param("empId") Integer empId, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);
    
    /**
     * Find attendance records by status
     * @param status The status to search for
     * @return List of attendance records with the specified status
     */
    List<EmployeeAttendanceRequest> findByStatus(String status);
    
    /**
     * Find attendance records by type
     * @param type The type to search for
     * @return List of attendance records with the specified type
     */
    List<EmployeeAttendanceRequest> findByType(String type);

    
    /**
     * Find attendance records for a specific date
     * @param date The date
     * @return List of attendance records for the specific date
     */
    @Query("SELECT ea FROM EmployeeAttendanceRequest ea WHERE CAST(ea.date AS date) = :date")
    List<EmployeeAttendanceRequest> findByDate(@Param("date") LocalDate date);
    
    /**
     * Find attendance records for a specific employee, date, and type
     * @param empId The employee ID
     * @param date The date
     * @param type The type
     * @return Optional attendance record
     */
    @Query("SELECT ea FROM EmployeeAttendanceRequest ea WHERE ea.empId = :empId AND CAST(ea.date AS date) = :date AND ea.type = :type")
    Optional<EmployeeAttendanceRequest> findByEmpIdAndDateAndType(@Param("empId") Integer empId, @Param("date") LocalDate date, @Param("type") String type);
    
    /**
     * Check if attendance record exists for employee, date, and type
     * @param empId The employee ID
     * @param date The date
     * @param type The type
     * @return true if record exists, false otherwise
     */
    @Query("SELECT COUNT(ea) > 0 FROM EmployeeAttendanceRequest ea WHERE ea.empId = :empId AND ea.date = :date AND ea.type = :type AND ea.status = :status")
    boolean existsByEmpIdAndDateAndTypeAndStatus(@Param("empId") Integer empId, @Param("date") LocalDateTime date, @Param("type") String type, @Param("status") String status);
    
    /**
     * Count attendance records for a specific employee and date
     * @param empId The employee ID
     * @param date The date
     * @return Count of attendance records
     */
    long countByEmpIdAndDateAndStatus(@Param("empId") Integer empId, @Param("date") LocalDateTime date, @Param("status") String status);
    
    /**
     * Find attendance records for multiple employees on a specific date
     * @param empIds List of employee IDs
     * @param date The date
     * @return List of attendance records
     */
    @Query("SELECT ea FROM EmployeeAttendanceRequest ea WHERE ea.empId IN :empIds AND CAST(ea.date AS date) = :date")
    List<EmployeeAttendanceRequest> findByEmpIdInAndDate(@Param("empIds") List<Integer> empIds, @Param("date") LocalDate date);

    /**
     * Find attendance records for a specific employee with multiple statuses
     * @param empId The employee ID
     * @param statuses List of statuses to search for
     * @return List of attendance records with the specified statuses
     */
    List<EmployeeAttendanceRequest> findByEmpIdAndStatusIn(Integer empId, List<String> statuses);

    List<EmployeeAttendanceRequest> findByEmpIdAndStatusInAndTypeIn(Integer empId, List<String> statuses, List<String> types);

    /**
     * Find all pending attendance requests
     * @param status The status to filter by (typically "PENDING")
     * @return List of pending attendance records
     */
    List<EmployeeAttendanceRequest> findByStatusOrderByCreationTimeDesc(String status);
    
    /**
     * Find pending attendance requests for specific employees
     * @param empIds List of employee IDs
     * @param status The status to filter by
     * @return List of pending attendance records for the specified employees
     */
    @Query("SELECT ea FROM EmployeeAttendanceRequest ea WHERE ea.empId IN :empIds AND ea.status = :status ORDER BY ea.creationTime DESC")
    List<EmployeeAttendanceRequest> findByEmpIdInAndStatusOrderByCreationTimeDesc(@Param("empIds") List<Integer> empIds, @Param("status") String status);

    @Query("SELECT ea FROM EmployeeAttendanceRequest ea " +
            "WHERE ea.empId = :empId AND ea.status = :status " +
            "AND :date BETWEEN FUNCTION('DATE', ea.startTime) AND FUNCTION('DATE', ea.endTime)")
    List<EmployeeAttendanceRequest> findByEmpIdAndStatusAndDateBetweenStartAndEnd(
            @Param("empId") Integer empId,
            @Param("status") String status,
            @Param("date") LocalDate date
    );

    EmployeeAttendanceRequest findByEmpIdAndDateAndType(Integer empId, LocalDateTime date, String type);


    @Query("SELECT ea FROM EmployeeAttendanceRequest ea WHERE ea.empId = :empId ")
    Page<EmployeeAttendanceRequest> findByEmpIdWithPagination(
        @Param("empId") Integer empId,
        Pageable pageable
    );

    /**
     * Find attendance records with leave details for LEAVE type requests
     * @param empId The employee ID
     * @param pageable Pagination and sorting information
     * @return Page of attendance records with leave details
     */
    @Query("SELECT ea, " +
           "CASE WHEN ea.type = 'LEAVE' OR ea.type = 'COMP_OFF' THEN " +
           "  (SELECT COALESCE(SUM(CASE WHEN ead.requestType = 'FULL DAY' THEN 1.0 " +
           "                            WHEN ead.requestType IN ('FIRST HALF', 'SECOND HALF') THEN 0.5 " +
           "                            ELSE 0.0 END), 0.0) " +
           "   FROM EmployeeAttendanceRequestDetail ead " +
           "   WHERE ead.attendanceReqId = ea.id) " +
           "ELSE 0.0 END as leaveDayCount " +
           "FROM EmployeeAttendanceRequest ea " +
           "WHERE ea.empId = :empId AND ea.type != 'WEEK_OFF'")
    Page<Object[]> findByEmpIdWithLeaveDetails(
        @Param("empId") Integer empId,
        Pageable pageable
    );

    /**
     * Find leave details for a specific attendance request
     * @param attendanceReqId The attendance request ID
     * @return List of leave details
     */
    @Query("SELECT ead FROM EmployeeAttendanceRequestDetail ead WHERE ead.attendanceReqId = :attendanceReqId ORDER BY ead.date")
    List<EmployeeAttendanceRequestDetail> findLeaveDetailsByRequestId(@Param("attendanceReqId") Long attendanceReqId);

}