package com.stpl.tech.attendance.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Configuration
@RefreshScope
@Data
@PropertySource("classpath:env.properties")
public class EnvironmentProperties {

    @Autowired
    private Environment env;
    
    @Value("${default.employee.image}")
    private String defaultEmployeeImage;

    @Value("${employee.image.url:https://d1nqp92n3q8zl7.cloudfront.net/}")
    private String employeeImageUrl;

    // Registration liveliness properties
    @Value("${attendance.registration.isLivelinessCheck:false}")
    private boolean attendanceRegistrationIsLivelinessCheck;

    @Value("${attendance.registration.startAngle:0}")
    private Integer attendanceRegistrationStartAngle;

    @Value("${attendance.registration.endAngle:30}")
    private Integer attendanceRegistrationEndAngle;

    @Value("${attendance.registration.angleChangeInterval:4500}")
    private Integer attendanceRegistrationAngleChangeInterval;

    @Value("${attendance.registration.angleChangeThreshold:10}")
    private Integer attendanceRegistrationAngleChangeThreshold;

    @Value("${attendance.registration.livelinessChallenges:BLINK}")
    private String attendanceRegistrationLivelinessChallenges;

    // Punch-in liveliness properties
    @Value("${attendance.punchin.isLivelinessCheck:false}")
    private boolean attendancePunchinIsLivelinessCheck;

    @Value("${attendance.punchin.startAngle:0}")
    private Integer attendancePunchinStartAngle;

    @Value("${attendance.punchin.endAngle:30}")
    private Integer attendancePunchinEndAngle;

    @Value("${attendance.punchin.angleChangeInterval:4500}")
    private Integer attendancePunchinAngleChangeInterval;

    @Value("${attendance.punchin.angleChangeThreshold:10}")
    private Integer attendancePunchinAngleChangeThreshold;

    @Value("${attendance.punchin.livelinessChallenges:BLINK}")
    private String attendancePunchinLivelinessChallenges;

    // Admin contact properties
    @Value("${attendance.app.admin.contact:9599598307}")
    private String attendanceAdminDefaultContact;

    @Value("${attendance.app.admin.email:<EMAIL>}")
    private String attendanceAdminDefaultEmail;

    // Registration threshold properties
    @Value("${attendance.registration.rotationThreshold:10.0}")
    private Double attendanceRegistrationRotationThreshold;

    @Value("${attendance.registration.brightnessThreshold:75.0}")
    private Double attendanceRegistrationBrightnessThreshold;

    @Value("${attendance.registration.contrastThreshold:75.0}")
    private Double attendanceRegistrationContrastThreshold;

    // Punch-in threshold properties
    @Value("${attendance.punchin.rotationThreshold:10.0}")
    private Double attendancePunchinRotationThreshold;

    @Value("${attendance.punchin.brightnessThreshold:75.0}")
    private Double attendancePunchinBrightnessThreshold;

    @Value("${attendance.punchin.contrastThreshold:75.0}")
    private Double attendancePunchinContrastThreshold;

    // Registration multi-frame properties
    @Value("${attendance.registration.isMultiFrameMode:false}")
    private boolean attendanceRegistrationIsMultiFrameMode;

    @Value("${attendance.registration.frameTimeLimit:5000}")
    private Integer attendanceRegistrationFrameTimeLimit;

    @Value("${attendance.registration.framesList:}")
    private String attendanceRegistrationFramesList;

    @Value("${attendance.registration.goodLightThreshold:75.0}")
    private Double attendanceRegistrationGoodLightThreshold;

    @Value("${attendance.registration.blurThreshold:50.0}")
    private Double attendanceRegistrationBlurThreshold;

    @Value("${attendance.registration.faceSizeThreshold:100.0}")
    private Double attendanceRegistrationFaceSizeThreshold;

    @Value("${attendance.registration.deltaThreshold:0.1}")
    private Double attendanceRegistrationDeltaThreshold;

    @Value("${attendance.registration.maxDeltaThreshold:0.5}")
    private Double attendanceRegistrationMaxDeltaThreshold;

    // Punch-in multi-frame properties
    @Value("${attendance.punchin.isMultiFrameMode:false}")
    private boolean attendancePunchinIsMultiFrameMode;

    @Value("${attendance.punchin.frameTimeLimit:5000}")
    private Integer attendancePunchinFrameTimeLimit;

    @Value("${attendance.punchin.framesList:}")
    private String attendancePunchinFramesList;

    @Value("${attendance.punchin.goodLightThreshold:75.0}")
    private Double attendancePunchinGoodLightThreshold;

    @Value("${attendance.punchin.blurThreshold:50.0}")
    private Double attendancePunchinBlurThreshold;

    @Value("${attendance.punchin.faceSizeThreshold:100.0}")
    private Double attendancePunchinFaceSizeThreshold;

    @Value("${attendance.punchin.deltaThreshold:0.1}")
    private Double attendancePunchinDeltaThreshold;

    @Value("${attendance.punchin.maxDeltaThreshold:0.5}")
    private Double attendancePunchinMaxDeltaThreshold;




    @Value("${spring.servlet.multipart.max-file-size:2097152}")
    private long maxFileSize;


    public String getDefaultEmployeeImage() {
        return defaultEmployeeImage;
    }

    public String getEmployeeImageUrl(){
        return employeeImageUrl;
    }

    // Registration liveliness properties
    public boolean isLivelinessCheckEnabledForRegistration() {
        return attendanceRegistrationIsLivelinessCheck;
    }

    public Integer getStartAngleForAttendanceRegistration() {
        return attendanceRegistrationStartAngle;
    }

    public Integer getEndAngleForAttendanceRegistration() {
        return attendanceRegistrationEndAngle;
    }

    public Integer getAngleChangeIntervalForAttendanceRegistration() {
        return attendanceRegistrationAngleChangeInterval;
    }

    public Integer getAngleChangeThresholdForAttendanceRegistration() {
        return attendanceRegistrationAngleChangeThreshold;
    }

    public List<String> getLivelinessChallengesForAttendanceRegistration() {
        return Arrays.asList(attendanceRegistrationLivelinessChallenges.split(","));
    }

    // Punch-in liveliness properties
    public boolean isLivelinessCheckEnabledForPunchIn() {
        return attendancePunchinIsLivelinessCheck;
    }

    public Integer getStartAngleForAttendancePunchIn() {
        return attendancePunchinStartAngle;
    }

    public Integer getEndAngleForAttendancePunchIn() {
        return attendancePunchinEndAngle;
    }

    public Integer getAngleChangeIntervalForAttendancePunchIn() {
        return attendancePunchinAngleChangeInterval;
    }

    public Integer getAngleChangeThresholdForAttendancePunchIn() {
        return attendancePunchinAngleChangeThreshold;
    }

    public List<String> getLivelinessChallengesForAttendancePunchIn() {
        return Arrays.asList(attendancePunchinLivelinessChallenges.split(","));
    }

    // Admin contact properties
    public String getAttendanceAdminDefaultContact() {
        return attendanceAdminDefaultContact;
    }

    public String getAttendanceAdminDefaultEmail() {
        return attendanceAdminDefaultEmail;
    }

    // Registration threshold getters
    public Double getAttendanceRegistrationRotationThreshold() {
        return Double.valueOf(env.getProperty("attendance.registration.rotationThreshold", "10.0"));
    }

    public Double getAttendanceRegistrationBrightnessThreshold() {
        return Double.valueOf(env.getProperty("attendance.registration.brightnessThreshold", "75.0"));
    }

    public Double getAttendanceRegistrationContrastThreshold() {
        return Double.valueOf(env.getProperty("attendance.registration.contrastThreshold", "75.0"));
    }

    // Punch-in threshold getters
    public Double getAttendancePunchInRotationThreshold() {
        return Double.valueOf(env.getProperty("attendance.punchin.rotationThreshold", "10.0"));
    }

    public Double getAttendancePunchInBrightnessThreshold() {
        return Double.valueOf(env.getProperty("attendance.punchin.brightnessThreshold", "75.0"));
    }

    public Double getAttendancePunchInContrastThreshold() {
        return Double.valueOf(env.getProperty("attendance.punchin.contrastThreshold", "75.0"));
    }

    // Registration multi-frame getters
    public boolean isMultiFrameModeForAttendanceRegistration() {
        return Boolean.valueOf(env.getProperty("attendance.registration.isMultiFrameMode", "false"));
    }

    public Integer getFrameTimeLimitForAttendanceRegistration() {
        return Integer.valueOf(env.getProperty("attendance.registration.frameTimeLimit", "5000"));
    }

    public List<String> getFramesListForAttendanceRegistration() {
        String rawValue = env.getProperty("attendance.registration.framesList", "");
        return Arrays.stream(rawValue.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toList());
    }

    public Double getGoodLightThresholdForAttendanceRegistration() {
        return Double.valueOf(env.getProperty("attendance.registration.goodLightThreshold", "75.0"));
    }

    public Double getBlurThresholdForAttendanceRegistration() {
        return Double.valueOf(env.getProperty("attendance.registration.blurThreshold", "50.0"));
    }

    public Double getFaceSizeThresholdForAttendanceRegistration() {
        return Double.valueOf(env.getProperty("attendance.registration.faceSizeThreshold", "100.0"));
    }

    public Double getDeltaThresholdForAttendanceRegistration() {
        return Double.valueOf(env.getProperty("attendance.registration.deltaThreshold", "0.1"));
    }

    public Double getMaxDeltaThresholdForAttendanceRegistration() {
        return Double.valueOf(env.getProperty("attendance.registration.maxDeltaThreshold", "0.5"));
    }

    // Punch-in multi-frame getters
    public boolean isMultiFrameModeForAttendancePunchIn() {
        return Boolean.valueOf(env.getProperty("attendance.punchin.isMultiFrameMode", "false"));
    }

    public Integer getFrameTimeLimitForAttendancePunchIn() {
        return Integer.valueOf(env.getProperty("attendance.punchin.frameTimeLimit", "5000"));
    }

    public List<String> getFramesListForAttendancePunchIn() {
        String rawValue = env.getProperty("attendance.punchin.framesList", "");
        return Arrays.stream(rawValue.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toList());
    }

    public Double getGoodLightThresholdForAttendancePunchIn() {
        return Double.valueOf(env.getProperty("attendance.punchin.goodLightThreshold", "75.0"));
    }

    public Double getBlurThresholdForAttendancePunchIn() {
        return Double.valueOf(env.getProperty("attendance.punchin.blurThreshold", "50.0"));
    }

    public Double getFaceSizeThresholdForAttendancePunchIn() {
        return Double.valueOf(env.getProperty("attendance.punchin.faceSizeThreshold", "100.0"));
    }

    public Double getDeltaThresholdForAttendancePunchIn() {
        return Double.valueOf(env.getProperty("attendance.punchin.deltaThreshold", "0.1"));
    }

    public Double getMaxDeltaThresholdForAttendancePunchIn() {
        return Double.valueOf(env.getProperty("attendance.punchin.maxDeltaThreshold", "0.5"));
    }

    public List<Integer> lucidAttendanceSyncUnits(){
        List<Integer> unitIds = new ArrayList<>();
        String[] unitIdArray = env.getProperty("lucid.attendance.sync.units","ALL").split(",");
        if(unitIdArray[0].equalsIgnoreCase("ALL")){
            return unitIds;
        }
        for (String unitId : unitIdArray) {
            unitIds.add(Integer.parseInt(unitId));
        }
        return unitIds;
    }

} 