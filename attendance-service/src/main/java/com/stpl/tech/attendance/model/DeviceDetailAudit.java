package com.stpl.tech.attendance.model;

import lombok.Builder;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;

@Data
@Builder
@Document(collection = "device_detail_audits")
public class DeviceDetailAudit {
    @Id
    private String id;
    private String deviceId;
    private String oldUnitId;
    private String newUnitId;
    private String action;
    private LocalDateTime timestamp;
} 