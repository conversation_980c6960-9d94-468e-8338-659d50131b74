package com.stpl.tech.attendance.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransferCancellationDTO {
    @NotBlank(message = "Cancelled by is required")
    private String cancelledBy;
    
    private String reason;
} 