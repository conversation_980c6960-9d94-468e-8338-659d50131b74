package com.stpl.tech.attendance.service;

import com.stpl.tech.attendance.dto.AttendanceSyncMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;

@Slf4j
@Service
@RequiredArgsConstructor
public class AttendanceSyncProducerService {

    private final KafkaTemplate<String, AttendanceSyncMessage> kafkaTemplate;
    
    @Value("${kafka.topic.attendance-sync}")
    private String attendanceSyncTopic;

    public void sendAttendanceSyncMessage(AttendanceSyncMessage message) {
        log.info("Sending attendance sync message for record: {}", message.getAttendanceRecordId());
        
        CompletableFuture<SendResult<String, AttendanceSyncMessage>> future = 
            kafkaTemplate.send(attendanceSyncTopic, message.getAttendanceRecordId().toString(), message);
        
        future.whenComplete((result, ex) -> {
            if (ex == null) {
                log.info("Attendance sync message sent successfully for record: {}, partition: {}, offset: {}", 
                    message.getAttendanceRecordId(), result.getRecordMetadata().partition(), 
                    result.getRecordMetadata().offset());
            } else {
                log.error("Failed to send attendance sync message for record: {}", 
                    message.getAttendanceRecordId(), ex);
            }
        });
    }
} 