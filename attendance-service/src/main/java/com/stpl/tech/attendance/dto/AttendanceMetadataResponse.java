package com.stpl.tech.attendance.dto;

import com.stpl.tech.attendance.enums.AttendanceAttributeType;
import com.stpl.tech.attendance.enums.LeaveCreditCycle;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * DTO for attendance metadata response
 * Contains the resolved value for a specific attribute type
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AttendanceMetadataResponse implements Serializable {
    
    private Integer deptId;
    private AttendanceAttributeType attributeCode;
    private String attributeType;
    private String attributeValue;
    private Boolean booleanValue;
    private BigDecimal numericValue;
    private LeaveCreditCycle cycleValue;
    private String weekendDaysValue;
    private boolean isDefaultValue;
    
    /**
     * Create response for boolean type attributes
     */
    public static AttendanceMetadataResponse forBoolean(
            Integer deptId, 
            AttendanceAttributeType attributeCode, 
            Boolean value, 
            boolean isDefault) {
        return AttendanceMetadataResponse.builder()
                .deptId(deptId)
                .attributeCode(attributeCode)
                .attributeType("BOOLEAN")
                .attributeValue(String.valueOf(value))
                .booleanValue(value)
                .isDefaultValue(isDefault)
                .build();
    }
    
    /**
     * Create response for numeric type attributes
     */
    public static AttendanceMetadataResponse forNumeric(
            Integer deptId, 
            AttendanceAttributeType attributeCode, 
            BigDecimal value, 
            boolean isDefault) {
        return AttendanceMetadataResponse.builder()
                .deptId(deptId)
                .attributeCode(attributeCode)
                .attributeType("NUMERIC")
                .attributeValue(value.toString())
                .numericValue(value)
                .isDefaultValue(isDefault)
                .build();
    }
    
    /**
     * Create response for cycle type attributes
     */
    public static AttendanceMetadataResponse forCycle(
            Integer deptId, 
            AttendanceAttributeType attributeCode, 
            LeaveCreditCycle value, 
            boolean isDefault) {
        return AttendanceMetadataResponse.builder()
                .deptId(deptId)
                .attributeCode(attributeCode)
                .attributeType("CYCLE")
                .attributeValue(value.name())
                .cycleValue(value)
                .isDefaultValue(isDefault)
                .build();
    }
    
    /**
     * Create response for string type attributes
     */
    public static AttendanceMetadataResponse forString(
            Integer deptId, 
            AttendanceAttributeType attributeCode, 
            String value, 
            boolean isDefault) {
        return AttendanceMetadataResponse.builder()
                .deptId(deptId)
                .attributeCode(attributeCode)
                .attributeType("STRING")
                .attributeValue(value)
                .isDefaultValue(isDefault)
                .build();
    }
    
    /**
     * Create response for weekend days type attributes
     */
    public static AttendanceMetadataResponse forWeekendDays(
            Integer deptId, 
            AttendanceAttributeType attributeCode, 
            String value, 
            boolean isDefault) {
        return AttendanceMetadataResponse.builder()
                .deptId(deptId)
                .attributeCode(attributeCode)
                .attributeType("WEEKEND_DAYS")
                .attributeValue(value)
                .weekendDaysValue(value)
                .isDefaultValue(isDefault)
                .build();
    }
}


