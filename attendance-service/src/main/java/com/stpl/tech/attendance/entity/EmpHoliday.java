package com.stpl.tech.attendance.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Entity representing the EMP_HOLIDAYS table
 * Stores system-wide holidays with financial year grouping
 */
@Entity
@Table(name = "EMP_HOLIDAYS")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmpHoliday {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "HOLIDAY_ID")
    private Long id;
    
    @Column(name = "FINANCIAL_YEAR", nullable = false, length = 10)
    private String financialYear;
    
    @Column(name = "HOLIDAY_NAME", nullable = false, length = 100)
    private String holidayName;
    
    @Column(name = "HOLIDAY_DATE", nullable = false)
    private LocalDate holidayDate;
    
    @Column(name = "IS_FULL_DAY", nullable = false)
    private Boolean isFullDay;
    
    @Column(name = "CREATED_BY")
    private String createdBy;
    
    @Column(name = "UPDATED_BY")
    private String updatedBy;
    
    @Column(name = "CREATED_AT")
    private LocalDateTime createdAt;
    
    @Column(name = "UPDATED_AT")
    private LocalDateTime updatedAt;
    
    /**
     * Check if this is a full day holiday
     * @return true if full day, false if half day
     */
    public boolean isFullDayHoliday() {
        return Boolean.TRUE.equals(isFullDay);
    }
    
    /**
     * Check if this is a half day holiday
     * @return true if half day, false if full day
     */
    public boolean isHalfDayHoliday() {
        return Boolean.FALSE.equals(isFullDay);
    }
    
    /**
     * Get display text for holiday type
     * @return "Full Day" or "Half Day"
     */
    public String getHolidayTypeDisplay() {
        return isFullDayHoliday() ? "Full Day" : "Half Day";
    }
}
