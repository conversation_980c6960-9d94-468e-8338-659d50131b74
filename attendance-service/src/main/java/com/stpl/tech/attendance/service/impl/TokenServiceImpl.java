package com.stpl.tech.attendance.service.impl;

import com.stpl.tech.attendance.service.TokenService;
import com.stpl.tech.master.core.external.acl.service.TokenDao;
import com.stpl.tech.util.AppConstants;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.JwtBuilder;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import javax.crypto.spec.SecretKeySpec;
import javax.xml.bind.DatatypeConverter;
import java.security.Key;
import java.util.Date;
import java.util.Map;

@Log4j2
@Service
public class TokenServiceImpl<T extends TokenDao> implements TokenService<T>  {



    public TokenServiceImpl() {

    }


    public String createToken(T object, long ttlMillis, String passPhraseKey) {
        SignatureAlgorithm signatureAlgorithm = SignatureAlgorithm.HS256;
        long nowMillis = System.currentTimeMillis();
        Date now = new Date(nowMillis);

        byte[] apiKeySecretBytes = DatatypeConverter.parseBase64Binary(passPhraseKey);
        Key signingKey = new SecretKeySpec(apiKeySecretBytes, signatureAlgorithm.getJcaName());
        Map<String, Object> authClaims = object.createClaims();

        JwtBuilder builder = Jwts.builder()
                .setClaims(authClaims)
                .setIssuedAt(now)
                .signWith(signatureAlgorithm, signingKey);

        if (ttlMillis >= 0) {
            long expMillis = nowMillis + ttlMillis;
            Date exp = new Date(expMillis);
            builder.setExpiration(exp);
        }

        return builder.compact();
    }


    @Override
    public void parseToken(T object, String jwt) {

            parseToken(object, jwt, AppConstants.PASSPHRASE_KEY);
    }



    public void parseToken(T object, String jwt, String key) {

            Claims claims = Jwts.parser()
                    .setSigningKey(DatatypeConverter.parseBase64Binary(key))
                    .parseClaimsJws(jwt)
                    .getBody();
            object.parseClaims(claims);
    }

 /*   public Claims parseToken(String token) {
        return Jwts.parser()
                .setSigningKey(DatatypeConverter.parseBase64Binary(AppConstants.PASSPHRASE_KEY))
                .parseClaimsJws(token)
                .getBody();
    }

    public boolean isTokenExpired(String token) {
        try {
            Claims claims = parseToken(token);
            return claims.getExpiration().before(new Date());
        } catch (ExpiredJwtException e) {
            return true;
        }
    }*/


} 