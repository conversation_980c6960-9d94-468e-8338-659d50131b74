package com.stpl.tech.attendance.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmployeeShiftInstancesMonthAggregatedResponseDTO {
    private Integer empId;
    private int year;
    private int month;
    private BigDecimal totalIdealWorkHours;
    private BigDecimal totalActualWorkHours;
    private int totalOnTimeDays;
    private int totalDays;
    private int totalCompletedDays;
    private int totalAbsentDays;
    private int totalPartialDays;
    private BigDecimal onTimePercentage;
    private BigDecimal attendancePercentage;
} 