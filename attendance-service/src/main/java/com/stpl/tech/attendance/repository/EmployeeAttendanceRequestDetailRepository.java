package com.stpl.tech.attendance.repository;

import com.stpl.tech.attendance.entity.EmployeeAttendanceRequestDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EmployeeAttendanceRequestDetailRepository extends JpaRepository<EmployeeAttendanceRequestDetail, Long> {
    
    /**
     * Find all detail records for a specific attendance request
     * @param attendanceReqId The ID of the attendance request
     * @return List of detail records
     */
    List<EmployeeAttendanceRequestDetail> findByAttendanceReqIdOrderByDateAsc(Long attendanceReqId);
    
    /**
     * Find detail records by attendance request ID and type
     * @param attendanceReqId The ID of the attendance request
     * @param type The type (LEAVE, COMP_OFF, LWP)
     * @return List of detail records
     */
    List<EmployeeAttendanceRequestDetail> findByAttendanceReqIdAndTypeOrderByDateAsc(Long attendanceReqId, String type);
    
    /**
     * Find detail records by attendance request ID and request type
     * @param attendanceReqId The ID of the attendance request
     * @param requestType The request type (FULL_DAY, FIRST_HALF, SECOND_HALF)
     * @return List of detail records
     */
    List<EmployeeAttendanceRequestDetail> findByAttendanceReqIdAndRequestTypeOrderByDateAsc(Long attendanceReqId, String requestType);
    
    /**
     * Count detail records for a specific attendance request
     * @param attendanceReqId The ID of the attendance request
     * @return Count of detail records
     */
    long countByAttendanceReqId(Long attendanceReqId);
    
    /**
     * Delete all detail records for a specific attendance request
     * @param attendanceReqId The ID of the attendance request
     */
    void deleteByAttendanceReqId(Long attendanceReqId);
    
    /**
     * Custom query to get leave details for leave deduction calculation
     * @param attendanceReqId The ID of the attendance request
     * @return List of detail records with type and request type for leave deduction
     */
    @Query("SELECT d FROM EmployeeAttendanceRequestDetail d WHERE d.attendanceReqId = :attendanceReqId AND d.type IN ('LEAVE', 'COMP_OFF', 'LWP') ORDER BY d.date ASC")
    List<EmployeeAttendanceRequestDetail> findLeaveDetailsForDeduction(@Param("attendanceReqId") Long attendanceReqId);
}
