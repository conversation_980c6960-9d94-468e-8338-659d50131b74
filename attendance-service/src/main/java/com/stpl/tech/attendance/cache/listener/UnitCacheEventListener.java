package com.stpl.tech.attendance.cache.listener;

import com.hazelcast.core.EntryEvent;
import com.hazelcast.map.listener.EntryAddedListener;
import com.hazelcast.map.listener.EntryRemovedListener;
import com.hazelcast.map.listener.EntryUpdatedListener;
import com.stpl.tech.attendance.cache.service.UnitCacheService;
import com.stpl.tech.master.domain.model.Unit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Event listener for Hazelcast UNITS_CACHE to trigger Spring cache eviction
 * when unit data is refreshed in another service.
 * 
 * This listener immediately evicts the Spring @Cacheable cache whenever
 * units cache is modified in Hazelcast.
 */
@Slf4j
@Component
public class UnitCacheEventListener implements 
    EntryAddedListener<Integer, Unit>,
    EntryUpdatedListener<Integer, Unit>,
    EntryRemovedListener<Integer, Unit> {

    private final UnitCacheService unitCacheService;

    @Autowired
    public UnitCacheEventListener(UnitCacheService unitCacheService) {
        this.unitCacheService = unitCacheService;
    }

    @Override
    public void entryAdded(EntryEvent<Integer, Unit> event) {
        log.info("Unit cache entry added: unitId={}, unitName={}", 
                event.getKey(), event.getValue() != null ? event.getValue().getName() : "null");
        evictSpringCache("ENTRY_ADDED", event.getKey());
    }

    @Override
    public void entryUpdated(EntryEvent<Integer, Unit> event) {
        log.info("Unit cache entry updated: unitId={}, unitName={}", 
                event.getKey(), event.getValue() != null ? event.getValue().getName() : "null");
        evictSpringCache("ENTRY_UPDATED", event.getKey());
    }

    @Override
    public void entryRemoved(EntryEvent<Integer, Unit> event) {
        log.info("Unit cache entry removed: unitId={}", event.getKey());
        evictSpringCache("ENTRY_REMOVED", event.getKey());
    }

    /**
     * Evict Spring cache when Hazelcast units cache changes
     * @param eventType Type of event (ENTRY_ADDED, ENTRY_UPDATED, ENTRY_REMOVED)
     * @param unitId The unit ID that was affected
     */
    private void evictSpringCache(String eventType, Integer unitId) {
        try {
            // Immediately evict the Spring cache to ensure fresh data on next access
            //unitCacheService.evictUnitsCache();
            //unitCacheService.evictAllUnitBasicDetail();

            log.info("Successfully evicted Spring cache for units due to {} event for unitId: {}", 
                    eventType, unitId);
            
        } catch (Exception e) {
            log.error("Failed to evict Spring cache for event {} and unitId {}", 
                    eventType, unitId, e);
        }
    }
}
