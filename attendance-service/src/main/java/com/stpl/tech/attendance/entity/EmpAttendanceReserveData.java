package com.stpl.tech.attendance.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Entity representing employee leave detail data
 * Maps to EMP_ATTENDANCE_RESERVE_DATA table
 * Tracks leave balances and reserved counts for different leave types
 */
@Entity
@Table(name = "EMP_ATTENDANCE_RESERVE_DATA")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmpAttendanceReserveData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;

    @Column(name = "EMP_ATTENDANCE_BALANCE_ID", nullable = false)
    private Long empAttendanceBalanceId;

    @Column(name = "TYPE", length = 45, nullable = false)
    private String type; // LEAVE, COMP_OFF, LWP

    @Column(name = "RESERVED_COUNT", precision = 10, scale = 2)
    private BigDecimal reservedCount;

    @Column(name = "UPDATED_ON", nullable = false)
    private LocalDateTime updatedOn;

    @Column(name = "UPDATED_BY", length = 45, nullable = false)
    private String updatedBy;

    @Version
    @Column(name = "VERSION")
    private Integer version;

    @PrePersist
    protected void onCreate() {
        updatedOn = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedOn = LocalDateTime.now();
    }
}
