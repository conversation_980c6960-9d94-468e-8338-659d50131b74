package com.stpl.tech.attendance.service;

import com.stpl.tech.attendance.dto.EmployeeTransferMetadataDTO;
import com.stpl.tech.attendance.dto.TransferCancellationDTO;
import com.stpl.tech.attendance.dto.TransferRequestDTO;
import com.stpl.tech.attendance.enums.ApprovalStatus;
import com.stpl.tech.attendance.model.ApproverInfo;
import com.stpl.tech.attendance.model.TransferHistory;
import com.stpl.tech.attendance.model.TransferRequest;
import com.stpl.tech.attendance.model.TransferRequestStatus;
import com.stpl.tech.attendance.model.TransferStatistics;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

public interface TransferService {
    TransferRequest createTransfer(TransferRequestDTO request);
    TransferRequest getTransferRequest(String transferId);
    TransferRequest saveTransfer(TransferRequest request);
    TransferRequest cancelTransfer(String transferId, TransferCancellationDTO request);
    Page<TransferRequest> getEmployeeTransfers(String empId, Pageable pageable);
    Page<TransferRequest> getUnitTransfers(String unitId, Pageable pageable);
    List<TransferHistory> getTransferHistory(String transferId);
    TransferStatistics getUnitTransferStatistics(String unitId);
    void handleTransferApproval(String transferId, ApprovalStatus status);
    List<ApproverInfo> getApprovers(String sourceUnitId , String destinationUnitId , Integer empId);
    
    /**
     * Get the latest transfer status for an employee
     * @param empId The employee ID
     * @return Optional containing the latest transfer status, or empty if no transfer found
     */
    Optional<TransferRequestStatus> getLatestTransferStatus(String empId);

    /**
     * Get employee transfer metadata including attendance eligible units and transfer reasons
     * @param empId The employee ID
     * @return EmployeeTransferMetadataDTO containing transfer metadata
     */
    EmployeeTransferMetadataDTO getEmployeeTransferMetadata(String empId);
} 