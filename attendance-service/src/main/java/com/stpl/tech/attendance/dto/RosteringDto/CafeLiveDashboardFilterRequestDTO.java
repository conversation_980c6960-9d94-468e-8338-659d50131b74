package com.stpl.tech.attendance.dto.RosteringDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CafeLiveDashboardFilterRequestDTO {
    private List<Integer> cafeIds; // Unit IDs to filter by
    private List<String> cityNames; // City names to filter by
} 