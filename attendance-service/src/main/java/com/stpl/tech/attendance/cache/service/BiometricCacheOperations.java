package com.stpl.tech.attendance.cache.service;

import com.stpl.tech.attendance.dto.BiometricRegistrationDTO;

/**
 * Interface for biometric cache operations that use Spring cache annotations.
 * This service is separate from BiometricCache to avoid Spring proxy issues
 * when calling @CachePut methods from within the same class.
 */
public interface BiometricCacheOperations {

    /**
     * Updates the cache with new biometric registration data using @CachePut.
     * This method will always update the cache regardless of whether the key exists.
     *
     * @param empId the employee ID
     * @return the updated BiometricRegistrationDTO, or null if not found
     */
    BiometricRegistrationDTO updateBiometricRegistration(String empId);

    /**
     * Removes a biometric registration from cache using @CacheEvict.
     *
     * @param empId the employee ID
     */
    void removeBiometricRegistration(String empId);
} 