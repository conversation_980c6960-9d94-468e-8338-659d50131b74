package com.stpl.tech.attendance.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "DAILY_ATTENDANCE_SUMMARY_LOGS")
public class DailyAttendanceSummaryLogs {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "DAILY_ATTENDANCE_SUMMARY_ID", nullable = false)
    private Long dailyAttendanceSummaryId;
    
    @Column(name = "FIRST_CHECK_IN")
    private LocalDateTime firstCheckIn;
    
    @Column(name = "LAST_CHECK_OUT")
    private LocalDateTime lastCheckOut;
    
    @Column(name = "STATUS", length = 45)
    private String status;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "ACTION_STATUS", nullable = false)
    private ActionStatus actionStatus;

    @Column(name = "ACTION_TYPE", nullable = false)
    private String actionType;
    
    @Column(name = "UPDATED_BY", nullable = false)
    private String updatedBy;
    
    @Column(name = "UPDATION_TIME", nullable = false)
    private LocalDateTime updationTime;
    
    @CreationTimestamp
    @Column(name = "CREATED_AT", updatable = false)
    private LocalDateTime createdAt;
    
    public enum ActionStatus {
        CREATED, CANCELLED
    }
}
