package com.stpl.tech.attendance.approval.service;

import java.util.List;
import java.util.Map;

public interface ApproverAssignmentService {
    /**
     * Get the approver ID for a given role and context
     * @param role The role identifier (e.g., "manager", "hr", "director")
     * @param context Additional context for role assignment (e.g., department, location)
     * @return The approver ID
     */
    String getApproverId(String role, Map<String, Object> context);
    
    /**
     * Get all possible approvers for a given role
     * @param role The role identifier
     * @param context Additional context for role assignment
     * @return List of possible approver IDs
     */
    List<String> getPossibleApprovers(String role, Map<String, Object> context);
    
    /**
     * Check if a user can approve in a given role
     * @param userId The user ID to check
     * @param role The role identifier
     * @param context Additional context for role assignment
     * @return true if the user can approve in this role
     */
    boolean canApprove(String userId, String role, Map<String, Object> context);
} 