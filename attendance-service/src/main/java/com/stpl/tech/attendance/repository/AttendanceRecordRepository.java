package com.stpl.tech.attendance.repository;

import com.stpl.tech.attendance.entity.AttendanceRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface AttendanceRecordRepository extends JpaRepository<AttendanceRecord, Long> {
    
    @Query("SELECT ar FROM AttendanceRecord ar WHERE ar.employeeId = :employeeId AND ar.punchTime BETWEEN :startTime AND :endTime ORDER BY ar.punchTime DESC")
    List<AttendanceRecord> findByEmployeeIdAndTimeRange(
        @Param("employeeId") Integer employeeId,
        @Param("startTime") LocalDateTime startTime,
        @Param("endTime") LocalDateTime endTime
    );

    @Query(value = "SELECT * FROM ATTENDANCE_RECORDS ar WHERE ar.EMPLOYEE_ID = :employeeId AND ar.PUNCH_TIME BETWEEN " +
            ":startTime AND :endTime ORDER BY ar.PUNCH_TIME DESC LIMIT 1", nativeQuery = true)
    AttendanceRecord findLastAttendanceByEmployeeIdAndTimeRange(
        @Param("employeeId") Integer employeeId,
        @Param("startTime") LocalDateTime startTime,
        @Param("endTime") LocalDateTime endTime
    );

    @Query("SELECT COUNT(ar) FROM AttendanceRecord ar WHERE ar.employeeId = :employeeId AND DATE(ar.punchTime) = :date")
    int countPunchesByEmployeeIdAndDate(
        @Param("employeeId") Integer employeeId,
        @Param("date") LocalDate date
    );

    @Query("SELECT ar FROM AttendanceRecord ar WHERE ar.employeeId = :employeeId AND DATE(ar.punchTime) = :date ORDER BY ar.punchTime")
    List<AttendanceRecord> findByEmployeeIdAndDate(
        @Param("employeeId") Integer employeeId,
        @Param("date") LocalDate date
    );

    @Query("SELECT ar FROM AttendanceRecord ar WHERE ar.employeeId = :employeeId AND MONTH(ar.punchTime) = :month AND YEAR(ar.punchTime) = :year ORDER BY ar.punchTime")
    List<AttendanceRecord> findByEmployeeIdAndMonthYear(
        @Param("employeeId") Integer employeeId,
        @Param("month") Integer month,
        @Param("year") Integer year
    );

    @Query("SELECT ar FROM AttendanceRecord ar WHERE ar.employeeId = :employeeId AND YEAR(ar.punchTime) = :year ORDER BY ar.punchTime")
    List<AttendanceRecord> findByEmployeeIdAndYear(
        @Param("employeeId") Integer employeeId,
        @Param("year") Integer year
    );

    @Query("SELECT ar FROM AttendanceRecord ar WHERE ar.employeeId = :employeeId AND DATE(ar.punchTime) BETWEEN :fromDate AND :toDate ORDER BY ar.punchTime")
    List<AttendanceRecord> findByEmployeeIdAndDateRange(
        @Param("employeeId") Integer employeeId,
        @Param("fromDate") LocalDate fromDate,
        @Param("toDate") LocalDate toDate
    );

    @Query("SELECT ar FROM AttendanceRecord ar WHERE ar.employeeId IN :employeeIds AND DATE(ar.punchTime) = :date ORDER BY ar.employeeId, ar.punchTime")
    List<AttendanceRecord> findByEmployeeIdInAndDate(
        @Param("employeeIds") List<Integer> employeeIds,
        @Param("date") LocalDate date
    );
} 