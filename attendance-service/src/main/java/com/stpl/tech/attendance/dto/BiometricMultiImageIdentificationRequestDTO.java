package com.stpl.tech.attendance.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BiometricMultiImageIdentificationRequestDTO {
    private String model_id;
    private Parameters parameters;
    private InputDataRequest input_data;
    @Builder.Default
    private Map<String, Object> processing_options = new HashMap<>();

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Parameters {
        private Metadata metadata;
        private Integer unitId;
        private String requestId;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Metadata {
        Integer unitId;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class InputDataRequest {
        private List<InputData> data;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class InputData {
        private String id;  // Unique identifier for each image
        private String data;      // Base64 encoded image
        private String imageType; // Type of image (PROCESSED, CROPPED, etc.)
    }
} 