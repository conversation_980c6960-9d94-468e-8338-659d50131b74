/*
package com.stpl.tech.attendance.approval.dto;

import com.stpl.tech.attendance.approval.entity.ApprovalStep;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApprovalStepDTO {
    private Long id;
    private Integer stepNumber;
    private String approverId;
    private Integer processDefinitionVersion;
    private ApprovalStep.Status status;
    private String comments;
    private LocalDateTime createdDate;
    private LocalDateTime processedDate;
    private String processedBy;

    public static ApprovalStepDTO fromEntity(ApprovalStep step) {
        return ApprovalStepDTO.builder()
                .id(step.getId())
                .stepNumber(step.getStepNumber())
                .approverId(step.getApproverId())
                .status(step.getStatus())
                .comments(step.getComments())
                .createdDate(step.getCreatedDate())
                .processedDate(step.getProcessedDate())
                .processedBy(step.getProcessedBy())
                .build();
    }
} */
