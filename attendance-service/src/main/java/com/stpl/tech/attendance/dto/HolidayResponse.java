package com.stpl.tech.attendance.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * DTO for holiday response
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HolidayResponse implements Serializable {
    
    private Long holidayId;
    private String financialYear;
    private String holidayName;
    private LocalDate holidayDate;
    private Boolean isFullDay;
    private String holidayTypeDisplay;
    

}
