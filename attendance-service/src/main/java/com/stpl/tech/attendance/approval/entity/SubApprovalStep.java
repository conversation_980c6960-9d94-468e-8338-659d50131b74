package com.stpl.tech.attendance.approval.entity;

import com.stpl.tech.attendance.enums.ApprovalStepStatus;
import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonManagedReference;

@Entity
@Table(name = "SUB_APPROVAL_STEP")
@Data
public class SubApprovalStep {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "SUB_STEP_ID")
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "STEP_ID", nullable = false)
    @JsonManagedReference
    private ApprovalStep approvalStep;

    @Column(name = "APPROVER_ID", nullable = false)
    private Long approverId;


    @Column(name = "STATUS", nullable = false)
    @Enumerated(EnumType.STRING)
    private ApprovalStepStatus status;

    @Column(name = "ACTION_DATE")
    private LocalDateTime actionDate;

    @Column(name = "REMARKS")
    private String remarks;

    @Column(name = "SUB_TASK_ID", nullable = false)
    private String subTaskId;

    @Column(name = "CREATED_DATE", nullable = false)
    private LocalDateTime createdDate;

    @Column(name = "CREATED_BY", nullable = false)
    private String createdBy;

    @Column(name = "UPDATED_DATE", nullable = false)
    private LocalDateTime updatedDate;

    @Column(name = "UPDATED_BY", nullable = false)
    private String updatedBy;

} 