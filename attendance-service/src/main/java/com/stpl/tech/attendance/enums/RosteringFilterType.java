package com.stpl.tech.attendance.enums;

import lombok.Getter;
import lombok.AllArgsConstructor;

/**
 * Enum for rostering filter types with metadata
 */
@Getter
@AllArgsConstructor
public enum RosteringFilterType {
    UNIT("unitIds", "Units", "INTEGER", "IN"),
    CITY("cityNames", "Cities", "STRING", "IN"),
    REGION("regions", "Regions", "STRING", "IN"),
    DESIGNATION("designations", "Designations", "STRING", "IN"),
    SHIFT("shiftIds", "Shifts", "INTEGER", "IN"),
    EMPLOYEE("employeeIds", "Employees", "INTEGER", "IN"),
    DATE("date", "Date", "DATE", "EQUALS"),
    DATE_RANGE("dateRange", "Date Range", "DATE", "BETWEEN"),
    STATUS("status", "Status", "STRING", "EQUALS");

    private final String key;
    private final String displayName;
    private final String dataType;
    private final String defaultOperator;
} 