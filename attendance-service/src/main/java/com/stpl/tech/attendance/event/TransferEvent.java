package com.stpl.tech.attendance.event;

import com.stpl.tech.attendance.model.TransferRequest;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public class TransferEvent {
    private final TransferRequest transferRequest;
    private final TransferEventType eventType;
    
    public enum TransferEventType {
        CREATED,
        APPROVED,
        REJECTED,
        CANCELLED
    }
} 