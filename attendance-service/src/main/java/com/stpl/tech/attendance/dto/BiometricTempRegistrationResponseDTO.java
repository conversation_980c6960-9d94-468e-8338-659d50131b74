package com.stpl.tech.attendance.dto;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class BiometricTempRegistrationResponseDTO {
    private String status;
    private Double processing_time;
    private Result result;
    private String error;

    @Data
    @Builder
    public static class Result {
        private String message;
        private Integer employee_id;
        private String session_id;
        private Double confidence;
        private String verification_status;
        private Boolean temp_registration_removed;
        private Boolean matched_existing_face;
        private Boolean is_temp_registration;
        private String embedding_type;
    }
} 