package com.stpl.tech.attendance.dto;

import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CancelRequestRequest {
    
    @NotNull(message = "Request ID is required")
    private Long requestId;
    
    /**
     * Flag to control whether cancellation should go through approval workflow
     * If true: creates approval request for cancellation
     * If false: handles cancellation directly (current behavior)
     */
    @Nullable
    private Boolean requireApproval = false;
}
