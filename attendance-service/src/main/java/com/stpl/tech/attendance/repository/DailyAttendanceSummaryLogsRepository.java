package com.stpl.tech.attendance.repository;

import com.stpl.tech.attendance.entity.DailyAttendanceSummaryLogs;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface DailyAttendanceSummaryLogsRepository extends JpaRepository<DailyAttendanceSummaryLogs, Long> {
    
    /**
     * Find logs by daily attendance summary ID
     */
    List<DailyAttendanceSummaryLogs> findByDailyAttendanceSummaryIdOrderByCreatedAtDesc(Long dailyAttendanceSummaryId);
    
    /**
     * Find the most recent log entry for a specific summary ID
     */
    @Query("SELECT l FROM DailyAttendanceSummaryLogs l WHERE l.dailyAttendanceSummaryId = :summaryId ORDER BY l.createdAt DESC, l.id DESC")
    Optional<DailyAttendanceSummaryLogs> findMostRecentBySummaryId(@Param("summaryId") Long summaryId);

    DailyAttendanceSummaryLogs findFirstByDailyAttendanceSummaryIdOrderByCreatedAtDescIdDesc(Long summaryId);

    /**
     * Alternative method to find the most recent log entry using subquery approach
     * This ensures unique results even when timestamps are identical
     */
    @Query("SELECT l FROM DailyAttendanceSummaryLogs l WHERE l.dailyAttendanceSummaryId = :summaryId " +
           "AND l.id = (SELECT MAX(l2.id) FROM DailyAttendanceSummaryLogs l2 " +
           "WHERE l2.dailyAttendanceSummaryId = :summaryId)")
    Optional<DailyAttendanceSummaryLogs> findMostRecentBySummaryIdAlternative(@Param("summaryId") Long summaryId);
}
