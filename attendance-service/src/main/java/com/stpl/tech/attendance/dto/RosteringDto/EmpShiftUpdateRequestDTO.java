package com.stpl.tech.attendance.dto.RosteringDto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.stpl.tech.attendance.config.JacksonConfig;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmpShiftUpdateRequestDTO {

    @NotNull(message = "Employee ID is required")
    private Integer empId;           // ← Single employee

    @NotNull(message = "Shift ID is required")
    private Integer shiftId;         // ← Single shift

    @NotNull(message = "Unit ID is required")
    private Integer unitId;          // ← Unit ID where employee is assigned

    @NotNull(message = "Business from date is required")
    @JsonFormat(pattern = JacksonConfig.BUSINESS_DATETIME_PATTERN, timezone = JacksonConfig.BUSINESS_TIMEZONE)
    private LocalDateTime businessFrom;

    private LocalDateTime businessTo;

    private Boolean updateUpcomingShifts;

    private LocalDateTime expectedArrivalTime;

    private String updatedBy;

}
