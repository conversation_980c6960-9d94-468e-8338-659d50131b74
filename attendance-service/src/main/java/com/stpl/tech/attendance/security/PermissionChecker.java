package com.stpl.tech.attendance.security;

import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import lombok.RequiredArgsConstructor;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Component
@RequiredArgsConstructor
public class PermissionChecker {
    
    private final JdbcTemplate jdbcTemplate;
    private final HazelcastInstance hazelcastInstance;

    public boolean hasPermission(String username, String actionCode) {
        // Try to get from cache first
        IMap<String, Set<String>> permissionsCache = hazelcastInstance.getMap("user-permissions-cache");
        Set<String> userPermissions = permissionsCache.get(username);

        if (userPermissions == null) {
            // Cache miss, load from database
            userPermissions = loadUserPermissions(username);
            // Store in cache with TTL
            permissionsCache.put(username, userPermissions, 1, TimeUnit.HOURS);
        }

        return userPermissions.contains(actionCode);
    }

    private Set<String> loadUserPermissions(String username) {
        String query = """
            SELECT DISTINCT ad.ACTION_CODE
            FROM USER_ROLE_DATA urd
            JOIN ROLE_ACTION_MAPPING ram ON urd.ROLE_ID = ram.ROLE_ID
            JOIN ACTION_DETAIL ad ON ram.ACTION_DETAIL_ID = ad.ACTION_DETAIL_ID
            WHERE urd.ROLE_STATUS = 'ACTIVE'
            AND ram.MAPPING_STATUS = 'ACTIVE'
            AND ad.ACTION_STATUS = 'ACTIVE'
            AND urd.APPLICATION_ID = ?
            """;

        return new HashSet<>(jdbcTemplate.queryForList(query, String.class, getApplicationId()));
    }

    private Integer getApplicationId() {
        // Return your application ID
        return 1; // Replace with your actual application ID
    }
} 