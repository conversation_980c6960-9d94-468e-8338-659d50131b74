package com.stpl.tech.attendance.controller;

import com.stpl.tech.attendance.config.DatabaseConfig;
import com.stpl.tech.attendance.model.response.ApiResponse;
import com.zaxxer.hikari.HikariDataSource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

/**
 * Controller for monitoring database connection pool status
 */
@RestController
@RequestMapping("/api/v1/database")
@RequiredArgsConstructor
@Slf4j
public class DatabaseMonitoringController extends BaseController {

    private final DataSource dataSource;
    private final DatabaseConfig databaseConfig;

    /**
     * Get connection pool statistics
     */
    @GetMapping("/pool-stats")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getPoolStats() {
        log.info("Retrieving database connection pool statistics");
        
        Map<String, Object> stats = new HashMap<>();
        
        if (dataSource instanceof HikariDataSource) {
            HikariDataSource hikariDataSource = (HikariDataSource) dataSource;
            
            stats.put("poolName", hikariDataSource.getPoolName());
            stats.put("activeConnections", hikariDataSource.getHikariPoolMXBean().getActiveConnections());
            stats.put("idleConnections", hikariDataSource.getHikariPoolMXBean().getIdleConnections());
            stats.put("totalConnections", hikariDataSource.getHikariPoolMXBean().getTotalConnections());
            stats.put("maximumPoolSize", hikariDataSource.getMaximumPoolSize());
            stats.put("minimumIdle", hikariDataSource.getMinimumIdle());
            stats.put("connectionTimeout", hikariDataSource.getConnectionTimeout());
            stats.put("idleTimeout", hikariDataSource.getIdleTimeout());
            stats.put("maxLifetime", hikariDataSource.getMaxLifetime());
            stats.put("leakDetectionThreshold", hikariDataSource.getLeakDetectionThreshold());
            
            // Calculate utilization percentage
            double utilizationPercentage = (double) hikariDataSource.getHikariPoolMXBean().getActiveConnections() / 
                                        hikariDataSource.getMaximumPoolSize() * 100;
            stats.put("utilizationPercentage", Math.round(utilizationPercentage * 100.0) / 100.0);
            
            log.info("Pool stats retrieved: {}", stats);
        } else {
            stats.put("error", "DataSource is not HikariDataSource");
            log.warn("DataSource is not HikariDataSource, cannot retrieve pool statistics");
        }
        
        return success(stats);
    }

    /**
     * Get connection pool status summary
     */
    @GetMapping("/pool-status")
    public ResponseEntity<ApiResponse<String>> getPoolStatus() {
        log.info("Retrieving database connection pool status summary");
        
        String status = databaseConfig.getPoolStats(dataSource);
        log.info("Pool status: {}", status);
        
        return success(status);
    }

    /**
     * Test database connectivity
     */
    @GetMapping("/health")
    public ResponseEntity<ApiResponse<Map<String, Object>>> testDatabaseConnection() {
        log.info("Testing database connectivity");
        
        Map<String, Object> health = new HashMap<>();
        boolean isHealthy = false;
        String message = "Database connection failed";
        
        try {
            // Test connection by executing a simple query
            dataSource.getConnection().close();
            isHealthy = true;
            message = "Database connection is healthy";
            log.info("Database connectivity test passed");
        } catch (Exception e) {
            log.error("Database connectivity test failed", e);
            message = "Database connection failed: " + e.getMessage();
        }
        
        health.put("healthy", isHealthy);
        health.put("message", message);
        health.put("timestamp", System.currentTimeMillis());
        
        return success(health);
    }
} 