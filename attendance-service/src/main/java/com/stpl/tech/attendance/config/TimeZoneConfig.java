package com.stpl.tech.attendance.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Bean;
import javax.annotation.PostConstruct;
import java.util.TimeZone;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Configuration
public class TimeZoneConfig {

    private static final String IST_TIMEZONE = "Asia/Kolkata";

    @PostConstruct
    public void init() {
        try {
            // Set default timezone to IST
            TimeZone.setDefault(TimeZone.getTimeZone(IST_TIMEZONE));
            log.info("Successfully set default timezone to {}", IST_TIMEZONE);
            
            // Log current timezone for verification
            TimeZone currentTimeZone = TimeZone.getDefault();
            log.info("Current default timezone: {} (offset: {} hours)", 
                    currentTimeZone.getID(), 
                    currentTimeZone.getRawOffset() / (1000 * 60 * 60));
        } catch (Exception e) {
            log.error("Failed to set default timezone to {}: {}", IST_TIMEZONE, e.getMessage(), e);
            throw e;
        }
    }
} 