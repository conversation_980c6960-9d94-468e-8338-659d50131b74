package com.stpl.tech.attendance.service.RosteringService.impl;

import com.stpl.tech.attendance.cache.service.EmployeeShiftDataCacheService;
import com.stpl.tech.attendance.cache.service.UnitCacheService;
import com.stpl.tech.attendance.cache.service.UserCacheService;
import com.stpl.tech.attendance.context.JwtContext;
import com.stpl.tech.attendance.domain.EmpShiftMappingFinder;
import com.stpl.tech.attendance.domain.EmpShiftMappingList;
import com.stpl.tech.attendance.domain.ShiftCoveragePlan;
import com.stpl.tech.attendance.domain.ShiftCoveragePlanFinder;
import com.stpl.tech.attendance.domain.ShiftCoveragePlanList;
import com.stpl.tech.attendance.dto.EmployeeMetadataDTO;
import com.stpl.tech.attendance.dto.RosteringDto.CafeShiftDataDTO;
import com.stpl.tech.attendance.dto.RosteringDto.EmpShiftMappingDTO;
import com.stpl.tech.attendance.dto.RosteringDto.EmpShiftUpdateRequestDTO;
import com.stpl.tech.attendance.dto.RosteringDto.EmployeeShiftDataResponseDTO;
import com.stpl.tech.attendance.dto.RosteringDto.FilterValueOptionDTO;
import com.stpl.tech.attendance.dto.RosteringDto.GenericFilterMetadataDTO;
import com.stpl.tech.attendance.dto.RosteringDto.GenericFilterRequestDTO;
import com.stpl.tech.attendance.dto.RosteringDto.HierarchyEmployeeDTO;
import com.stpl.tech.attendance.dto.RosteringDto.HierarchyEmployeesResponseDTO;
import com.stpl.tech.attendance.dto.RosteringDto.RosterLiveDashboardResponseDTO;
import com.stpl.tech.attendance.dto.RosteringDto.RosteringMetadataResponseDTO;
import com.stpl.tech.attendance.dto.RosteringDto.ShiftAccessConfig;
import com.stpl.tech.attendance.dto.RosteringDto.ShiftCafeMappingDTO;
import com.stpl.tech.attendance.dto.RosteringDto.ShiftCafeMappingRequestDTO;
import com.stpl.tech.attendance.dto.RosteringDto.ShiftCafeMappingResponseDTO;
import com.stpl.tech.attendance.dto.RosteringDto.ShiftEmployeesResponseDTO;
import com.stpl.tech.attendance.dto.RosteringDto.ShiftRequestDTO;
import com.stpl.tech.attendance.dto.RosteringDto.ShiftResponseDTO;
import com.stpl.tech.attendance.dto.UnitEligibilityDTO;
import com.stpl.tech.attendance.entity.EmpShiftOverride;
import com.stpl.tech.attendance.entity.EmployeeShiftInstances;
import com.stpl.tech.attendance.entity.RosteringEntity.EmpShiftMapping;
import com.stpl.tech.attendance.entity.RosteringEntity.RosteringConstants;
import com.stpl.tech.attendance.entity.RosteringEntity.Shift;
import com.stpl.tech.attendance.entity.RosteringEntity.ShiftCafeMapping;
import com.stpl.tech.attendance.enums.RosteringFilterType;
import com.stpl.tech.attendance.exception.BusinessException;
import com.stpl.tech.attendance.exception.CafeLiveDashboardException;
import com.stpl.tech.attendance.exception.FilterValidationException;
import com.stpl.tech.attendance.notification.service.NotificationService;
import com.stpl.tech.attendance.repository.EmpShiftOverrideRepository;
import com.stpl.tech.attendance.repository.EmployeeShiftInstancesRepository;
import com.stpl.tech.attendance.repository.EmployeeShiftUnitAttendanceRepository;
import com.stpl.tech.attendance.repository.RosteringRepository.EmpShiftMappingRepository;
import com.stpl.tech.attendance.repository.RosteringRepository.ShiftCafeMappingRepository;
import com.stpl.tech.attendance.repository.RosteringRepository.ShiftCoveragePlanRepository;
import com.stpl.tech.attendance.repository.RosteringRepository.ShiftRepository;
import com.stpl.tech.attendance.service.EmpUnitShiftInstanceAnalyticsService;
import com.stpl.tech.attendance.service.EmployeeShiftInstanceRecreationService;
import com.stpl.tech.attendance.service.MetadataService;
import com.stpl.tech.attendance.service.RosteringService.ReladomoService;
import com.stpl.tech.attendance.service.RosteringService.RosteringService;
import com.stpl.tech.attendance.service.UnitResolutionService;
import com.stpl.tech.attendance.util.AttendanceDateUtil;
import com.stpl.tech.attendance.util.RosteringNotificationUtil;
import com.stpl.tech.attendance.util.ShiftAccessUtil;
import com.stpl.tech.attendance.util.ShiftHelper;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.StopWatch;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;


@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class RosteringServiceImpl implements RosteringService {

    @Autowired
    @Lazy
    private MetadataService metadataService;

    private final ShiftRepository shiftRepository;
    private final EmpShiftMappingRepository empShiftMappingRepository;
    private final ShiftCafeMappingRepository shiftCafeMappingRepository;
    private final ShiftCoveragePlanRepository shiftCoveragePlanRepository;
    private final EmpShiftOverrideRepository empShiftOverrideRepository;
    private final EmployeeShiftInstancesRepository employeeShiftInstancesRepository;
    private final EmpUnitShiftInstanceAnalyticsService empUnitShiftInstanceAnalyticsService;

    private final UserCacheService userCacheService;
    private final UnitCacheService unitCacheService;
    private final ReladomoService reladomoService;
    private final AttendanceDateUtil attendanceDateUtil;
    private final UnitResolutionService unitResolutionService;
    private final EmployeeShiftDataCacheService employeeShiftDataCacheService;
    private final ShiftHelper shiftHelper;
    private final EmployeeShiftInstanceRecreationService employeeShiftInstanceRecreationService;
    private final RosteringNotificationUtil rosteringNotificationUtil;
    private final ShiftAccessUtil shiftAccessUtil;
    @Override
    @Transactional(readOnly = true)
    public RosteringMetadataResponseDTO getRosteringMetadata(Integer employeeId, Integer unitId) {
        log.info("Getting rostering metadata for employeeId: {}, unitId: {}", employeeId, unitId);

        try {
            //TODO
            // Get employee details to check permissions
            EmployeeBasicDetail employee = userCacheService.getUserById(employeeId);
            // Use utility method to determine shift access based on employee metadata
            ShiftAccessConfig accessConfig = shiftAccessUtil.getAccessConfig(employee);
            // Build metadata response based on access configuration
            return RosteringMetadataResponseDTO.builder()
                .allShiftManagement(accessConfig.isAllShiftManagement())
                .unitShiftManagement(accessConfig.isUnitShiftManagement())
                .liveDashboardView(accessConfig.isLiveDashboardView())
                .shiftDashboardView(accessConfig.isShiftDashboardView())
                .employeeDashboardView(accessConfig.isEmployeeDashboardView())
                .analyticsDashboardView(accessConfig.isAnalyticsDashboardView())
                .hierarchyView(accessConfig.isHierarchyView())
                .build();

        } catch (Exception e) {
            log.error("Error getting rostering metadata for employeeId: {}, unitId: {}", employeeId, unitId, e);
            return buildDefaultMetadata();
        }
    }

    @Override
    @Transactional(readOnly = true, timeout = 30)
    public RosterLiveDashboardResponseDTO getCafeLiveDashboard(Integer userId) {
    log.info("Getting cafe live dashboard data for userId: {}", userId);

    try {
        // Get employee details
        EmployeeBasicDetail employee = userCacheService.getUserById(userId);
        if (employee == null) {
            log.warn("Employee with ID {} not found", userId);
            return buildEmptyCafeLiveDashboardResponse(null);
        }

        // Get all employees under this user's hierarchy using MetadataService
        List<EmployeeMetadataDTO> hierarchyEmployees = metadataService.getEmployeeHierarchy(userId);
        log.info("Found {} employees under userId {} hierarchy", hierarchyEmployees.size(), userId);

        if (hierarchyEmployees.isEmpty()) {
            log.info("No employees found under userId {} hierarchy", userId);
            return buildEmptyCafeLiveDashboardResponse(null);
        }

        // Extract employee IDs from hierarchy
        Set<Integer> employeeIds = hierarchyEmployees.stream()
                .map(EmployeeMetadataDTO::getEmpId)
                .collect(Collectors.toSet());

        // Get shift mappings and build response
        EmpShiftMappingList allEmpShiftMappings = EmpShiftMappingFinder.findMany(
                EmpShiftMappingFinder.all());

        // Filter to only include mappings for employees in hierarchy
        EmpShiftMappingList filteredMappings = filterMappingsByEmployeeIds(allEmpShiftMappings, employeeIds);
        log.info("Found {} total shift mappings for {} employees in hierarchy", filteredMappings.size(), employeeIds.size());

        // Group shift mappings by shift ID to get unique shifts
        Map<Integer, List<com.stpl.tech.attendance.domain.EmpShiftMapping>> shiftMappingsMap = allEmpShiftMappings.stream()
                .collect(Collectors.groupingBy(com.stpl.tech.attendance.domain.EmpShiftMapping::getShiftId));

        // Build shifts list and calculate total
        List<RosterLiveDashboardResponseDTO.ShiftInfoDTO> shifts = buildShiftInfoList(shiftMappingsMap);
        int totalActual = calculateTotalEmployees(shifts);

        // Build dashboard view
        RosterLiveDashboardResponseDTO.DashboardViewDTO dashboardView = buildDashboardView(shifts, totalActual,attendanceDateUtil.getCurrentAttendanceDate());

        log.info("Returning cafe live dashboard with {} shifts and {} total employees in hierarchy", shifts.size(), totalActual);
        return RosterLiveDashboardResponseDTO.builder()
                .dashboardView(dashboardView)
                .build();

    } catch (Exception e) {
        log.error("Error getting cafe live dashboard data for userId: {}", userId, e);
        throw new BusinessException("Failed to get cafe live dashboard data", e);
    }
}

    @Override
    @Transactional(readOnly = true, timeout = 30)
    public RosterLiveDashboardResponseDTO getRosterLiveDashboardWithFilters(Integer userId, GenericFilterRequestDTO filterRequest) {
    log.info("Getting cafe live dashboard data with filters for userId: {}, filters: {}", userId, filterRequest);

    try {
        // Get employee details
        EmployeeBasicDetail employee = userCacheService.getUserById(userId);
        if (employee == null) {
            log.warn("Employee with ID {} not found", userId);
            return buildEmptyCafeLiveDashboardResponse(null);
        }


        // Get all managed unit IDs
        List<Integer> managedUnitIds = metadataService.getManagedUnitIds(userId);
        log.info("Found {} managed units for userId {}", managedUnitIds.size(), userId);

        if (managedUnitIds.isEmpty()) {
            log.info("No managed units found for userId {}", userId);
            return buildEmptyCafeLiveDashboardResponse(null);
        }

        // Apply filters to get filtered unit IDs using common method
        List<Integer> filteredUnitIds = empUnitShiftInstanceAnalyticsService.getManagedUnitIdsWithFilters(userId,managedUnitIds ,filterRequest);
        log.info("After filtering, found {} units out of {} managed units", filteredUnitIds.size(), managedUnitIds.size());

        if (filteredUnitIds.isEmpty()) {
            log.info("No units found after applying filters for userId {}", userId);
            GenericFilterMetadataDTO filterMetadata = buildGenericFilterMetadata(managedUnitIds, filterRequest);
            return buildEmptyCafeLiveDashboardResponse(filterMetadata);
        }

        // Calculate current attendance date (4 AM to 3:59 AM next day)
        LocalDate currentAttendanceDate = attendanceDateUtil.getCurrentAttendanceDate();
        LocalDateTime currentTime = LocalDateTime.now();

        log.info("Current attendance date: {}, current time: {}", currentAttendanceDate, currentTime);

        // Get shift instances directly by filtered unit IDs on current attendance date
        List<EmployeeShiftInstances> shiftInstances = employeeShiftInstancesRepository
            .findByUnitIdsAndBusinessDate(filteredUnitIds, currentAttendanceDate);

        log.info("Found {} shift instances for {} filtered units on date {}",
                shiftInstances.size(), filteredUnitIds.size(), currentAttendanceDate);

        if (shiftInstances.isEmpty()) {
            log.info("No shift instances found for filtered units on date {}", currentAttendanceDate);
            GenericFilterMetadataDTO filterMetadata = buildGenericFilterMetadata(managedUnitIds, filterRequest);
            return buildEmptyCafeLiveDashboardResponse(filterMetadata);
        }

        // Calculate ideal and actual counts using common methods
        int idealCount = empUnitShiftInstanceAnalyticsService.getIdealEmployeeCount(filteredUnitIds, currentAttendanceDate, currentTime);
        int actualCount = empUnitShiftInstanceAnalyticsService.getActualEmployeeCount(filteredUnitIds, currentAttendanceDate ,currentTime);

        // Build shifts list from unique shifts in shift instances
        List<RosterLiveDashboardResponseDTO.ShiftInfoDTO> shifts = buildShiftInfoListFromInstances(shiftInstances, currentAttendanceDate);

        // Build dashboard view
        RosterLiveDashboardResponseDTO.DashboardViewDTO dashboardView = buildDashboardViewWithCounts(shifts, actualCount, idealCount, currentAttendanceDate);

        log.info("Returning cafe live dashboard with {} shifts, {} actual employees, {} ideal employees",
                shifts.size(), actualCount, idealCount);

        // Build filter metadata with applied filter flags
        GenericFilterMetadataDTO filterMetadata = buildGenericFilterMetadata(managedUnitIds, filterRequest);

        return RosterLiveDashboardResponseDTO.builder()
                .dashboardView(dashboardView)
                .filterMetadata(filterMetadata)
                .build();

    } catch (CafeLiveDashboardException | FilterValidationException e) {
        log.error("Error getting cafe live dashboard data with filters for userId: {}", userId, e);
        throw e;
    } catch (Exception e) {
        log.error("Error getting cafe live dashboard data with filters for userId: {}", userId, e);
        throw new CafeLiveDashboardException("CAFE_DASHBOARD_ERROR", "Failed to get cafe live dashboard data with filters", e);
    }
}

    @Override
    @Transactional(readOnly = true)
    public ShiftEmployeesResponseDTO getShiftEmployeesForUser(Timestamp date, Integer userId, GenericFilterRequestDTO filterRequest) {
        log.info("Getting shift employees for date: {}, userId: {}, filters: {}", date, userId, filterRequest);

        try {
            // Get employee details
            EmployeeBasicDetail employee = userCacheService.getUserById(userId);
            if (employee == null) {
                log.warn("Employee with ID {} not found", userId);
                return buildEmptyShiftEmployeesResponse();
            }

            // Get all managed unit IDs
            List<Integer> managedUnitIds = metadataService.getManagedUnitIds(userId);
            log.info("Found {} managed units for userId {}", managedUnitIds.size(), userId);

            if (managedUnitIds.isEmpty()) {
                log.info("No managed units found for userId {}", userId);
                return buildEmptyShiftEmployeesResponse();
            }

            // Apply filters to get filtered unit IDs
            List<Integer> filteredUnitIds = applyGenericFiltersToUnits(managedUnitIds, filterRequest);
            log.info("After filtering, found {} units out of {} managed units", filteredUnitIds.size(), managedUnitIds.size());

            if (filteredUnitIds.isEmpty()) {
                log.info("No units found after applying filters for userId {}", userId);
                return buildEmptyShiftEmployeesResponse();
            }

            // Convert date to LocalDate using attendance date logic
            LocalDate businessDate = date != null ?
                attendanceDateUtil.getAttendanceDateForTimestamp(date) :
                attendanceDateUtil.getCurrentAttendanceDate();

            log.info("Business date: {}", businessDate);

            // Get shift instances directly by shift IDs, filtered unit IDs, and business date
//            List<EmployeeShiftInstances> shiftInstances = employeeShiftInstancesRepository
//                .findByShiftIdsAndUnitIdsAndBusinessDateAndInstanceStatus(shiftIds, filteredUnitIds, RosteringConstants.ACTIVE, businessDate);

            List<EmployeeShiftInstances> shiftInstances = employeeShiftInstancesRepository
                    .findByUnitIdsAndBusinessDate(filteredUnitIds, businessDate);
            log.info("Found {} shift instances for {} filtered units on date {}",
                    shiftInstances.size(), filteredUnitIds.size(), businessDate);

            if (shiftInstances.isEmpty()) {
                log.info("No shift instances found for the specified criteria");
                return buildEmptyShiftEmployeesResponse();
            }

            // Build shifts list from unique shifts in shift instances
            List<ShiftEmployeesResponseDTO.ShiftDTO> shifts = buildShiftEmployeesListFromInstances(shiftInstances, businessDate);

            // Build filter metadata with applied filter flags
            GenericFilterMetadataDTO filterMetadata = buildGenericFilterMetadata(managedUnitIds, filterRequest);

            log.info("Returning {} shifts with employee assignments", shifts.size());
            return ShiftEmployeesResponseDTO.builder()
                .shifts(shifts)
                .filterMetadata(filterMetadata)
                .build();

        } catch (Exception e) {
            log.error("Error getting shift employees for userId: {}, shiftIds: {}", userId, e);
            throw new BusinessException("Failed to get shift employees", e);
        }
    }

@Override
@Transactional(readOnly = true)
public EmployeeShiftDataResponseDTO getEmpUpcomingShiftData(Integer empId, LocalDate startDate, LocalDate endDate) {
    log.info("Getting employee shift data for empId: {}, startDate: {}, endDate: {}", empId, startDate, endDate);

    try {
        // Convert timestamps to LocalDate
        LocalDate startLocalDate = startDate != null ? startDate : LocalDate.now();
        LocalDate endLocalDate = endDate != null ? endDate: startLocalDate.plusDays(7);
        log.info("Date range: {} to {}", startLocalDate, endLocalDate);

        List<EmployeeShiftDataResponseDTO.EmployeeShiftDTO> shifts = new ArrayList<>();
        // First: Try to find data in emp_shift_instances for the date range
        List<EmployeeShiftInstances> shiftInstances = employeeShiftInstancesRepository
            .findByEmpIdAndBusinessDateBetweenAndInstanceStatus(empId, startLocalDate, endLocalDate, RosteringConstants.ACTIVE);

        LocalDateTime currentTime = LocalDateTime.now();
        if (!shiftInstances.isEmpty() && shiftInstances.get(0).getExpectedStartTime().isBefore(currentTime)) {
            log.info("First shift has already started. Skipping today's data.");
            shiftInstances = shiftInstances.stream()
                    .filter(shift -> !shift.getExpectedStartTime().toLocalDate().isEqual(LocalDate.now()))
                    .toList();
        }

        log.info("Found {} shift instances for empId {} in date range", shiftInstances.size(), empId);

        // Convert instances to DTOs and add to result
        for (EmployeeShiftInstances instance : shiftInstances) {
            // Filter out shifts that have already started based on current time
            if (RosteringConstants.ACTIVE.equals(instance.getInstanceStatus()) &&
                instance.getExpectedStartTime().isAfter(currentTime)) {
                Shift shift = shiftRepository.findById(instance.getShiftId()).orElse(null);
                if (shift != null) {
                    // Get unit name from unit cache
                    String unitName = null;
                    if (instance.getUnitId() != null) {
                        UnitBasicDetail unit = unitCacheService.getUnitBasicDetail(instance.getUnitId());
                        unitName = unit != null ? unit.getName() : null;
                    }

                    EmployeeShiftDataResponseDTO.EmployeeShiftDTO shiftDto =
                            EmployeeShiftDataResponseDTO.EmployeeShiftDTO.builder()
                                    .shiftId(shift.getShiftId())
                                    .shiftName(shift.getShiftName())
                                    .startTime(instance.getExpectedStartTime())
                                    .endTime(instance.getExpectedEndTime())
                                    .date(instance.getExpectedStartTime())
                                    .unitId(instance.getUnitId())
                                    .unitName(unitName)
                                    .build();
                    shifts.add(shiftDto);

                    // Cache this data
                    employeeShiftDataCacheService.cacheEmployeeShiftData(empId, instance.getBusinessDate(), shiftDto);
                }
            }
        }

        // Check if we have data for all dates in the range
        long expectedDays = java.time.temporal.ChronoUnit.DAYS.between(startLocalDate, endLocalDate) + 1;
        long foundDays = shifts.size();

        log.info("Expected {} days, found {} days", expectedDays, foundDays);

        // If we have gaps, fill them by checking in order: override -> mapping -> universal
        if (foundDays < expectedDays) {
            log.info("Filling gaps for {} missing days", expectedDays - foundDays);

            for (LocalDate date = startLocalDate; !date.isAfter(endLocalDate); date = date.plusDays(1)) {
                // Check if we already have data for this date
                LocalDate finalDate = date;
                boolean hasDataForDate = shifts.stream()
                    .anyMatch(shift -> shift.getDate().toLocalDate().equals(finalDate));

                if (!hasDataForDate) {
                    // Try to get cached data first
                    EmployeeShiftDataResponseDTO.EmployeeShiftDTO cachedData =
                        employeeShiftDataCacheService.getCachedEmployeeShiftData(empId, date);

                    if (cachedData != null) {
                        // Apply time-based filtering to cached data as well
                        if (cachedData.getStartTime().isAfter(currentTime)) {
                            shifts.add(cachedData);
                            log.debug("Added cached data for employee {} on date {}", empId, date);
                        } else {
                            log.debug("Filtered out cached data for employee {} on date {} as shift has already started", empId, date);
                        }
                    } else {
                        // Calculate shift data for this date
                        EmployeeShiftDataResponseDTO.EmployeeShiftDTO calculatedData =
                            calculateShiftDataForDate(empId, date);

                        if (calculatedData != null) {
                            // Apply time-based filtering to calculated data
                            if (calculatedData.getStartTime().isAfter(currentTime)) {
                                shifts.add(calculatedData);

                                // Cache this calculated data
                                employeeShiftDataCacheService.cacheEmployeeShiftData(empId, date, calculatedData);
                                log.debug("Calculated and cached shift data for employee {} on date {}", empId, date);
                            } else {
                                log.debug("Filtered out calculated data for employee {} on date {} as shift has already started", empId, date);
                            }
                        }
                    }
                }
            }
        }

        // Sort by date
        shifts.sort((a, b) -> a.getDate().compareTo(b.getDate()));

        log.info("Returning {} shifts for employee {} (filtered out shifts that have already started)", shifts.size(), empId);
        return EmployeeShiftDataResponseDTO.builder()
                .employeeId(empId)
                .shifts(shifts)
                .build();

    } catch (Exception e) {
        log.error("Error getting employee shift data for empId: {}", empId, e);
        throw new BusinessException("Failed to get employee shift data", e);
    }
}


    @Override
    @Transactional(readOnly = true)
    public CafeShiftDataDTO getCafeShiftData(Integer employeeId, Integer unitId) {
        log.info("Getting cafe shift data for employeeId: {}, unitId: {}", employeeId, unitId);
        try {
            // Validate unitId is provided
            if (unitId == null) {
                log.warn("UnitId is null, returning empty result");
                return new CafeShiftDataDTO();
            }

            // Get unit details
            UnitBasicDetail unit = unitCacheService.getUnitBasicDetail(unitId);
            if (unit == null) {
                log.warn("Unit with ID {} not found", unitId);
                return new CafeShiftDataDTO();
            }

            // Build shift details for this unit
            List<CafeShiftDataDTO.ShiftDetailDTO> shiftDetails = new ArrayList<>();

            // Always add universal shift for every unit (no mapping required)
            Shift universalShift = shiftHelper.getDefaultUniversalShift();
            if (universalShift != null) {
                // Get ideal counts for universal shift from any unit mapping
                Map<String, Integer> dayWiseIdealCount = new HashMap<>();
                boolean allDaySame = false;
                Integer idealCount = null;

                try {
                    // Get all shift cafe mappings for universal shift across all units
                    List<ShiftCafeMapping> allUniversalShiftMappings = shiftCafeMappingRepository.findByShiftIdAndStatus(universalShift.getShiftId(), RosteringConstants.ACTIVE);

                    for (ShiftCafeMapping mapping : allUniversalShiftMappings) {
                        Integer mappingId = mapping.getShiftCafeMappingId();

                        // Get ideal counts from ShiftCoveragePlan using Reladomo
                        Timestamp infinityTimestamp = java.sql.Timestamp.valueOf(RosteringConstants.INFINITY_TIME);

                        try {
                            ShiftCoveragePlanList existingPlans = ShiftCoveragePlanFinder.findMany(
                                    ShiftCoveragePlanFinder.shiftCafeMappingId().eq(mappingId).
                                            and(ShiftCoveragePlanFinder.processingDate().eq(infinityTimestamp)));

                            // Build dayWiseIdealCount map for this shift
                            for (ShiftCoveragePlan plan : existingPlans) {
                                dayWiseIdealCount.put(plan.getDay(), plan.getIdealCount());
                            }

                            log.debug("Found {} ideal count entries for universal shift mappingId: {} using Reladomo: {}",
                                    dayWiseIdealCount.size(), mappingId, dayWiseIdealCount);

                            // If we found data, break (use the first mapping with data)
                            if (!dayWiseIdealCount.isEmpty()) {
                                break;
                            }

                        } catch (Exception reladomoException) {
                            log.warn("Reladomo not available, using fallback for universal shift mappingId: {}",
                                    mappingId, reladomoException);

                            // Fallback: Use direct database query
                            try {
                                List<com.stpl.tech.attendance.entity.RosteringEntity.ShiftCoveragePlan> plans =
                                    shiftCoveragePlanRepository.findByShiftCafeMappingIdAndStatus(mappingId, RosteringConstants.ACTIVE);

                                for (com.stpl.tech.attendance.entity.RosteringEntity.ShiftCoveragePlan plan : plans) {
                                    dayWiseIdealCount.put(plan.getDay(), plan.getIdealCount());
                                }

                                log.debug("Found {} ideal count entries for universal shift mappingId: {} using fallback: {}",
                                        dayWiseIdealCount.size(), mappingId, dayWiseIdealCount);

                                // If we found data, break (use the first mapping with data)
                                if (!dayWiseIdealCount.isEmpty()) {
                                    break;
                                }

                            } catch (Exception fallbackException) {
                                log.warn("Fallback also failed for universal shift mappingId: {}",
                                        mappingId, fallbackException);
                            }
                        }
                    }

                    // Determine if all days have the same count
                    if (!dayWiseIdealCount.isEmpty()) {
                        Set<Integer> uniqueCounts = new HashSet<>(dayWiseIdealCount.values());
                        if (uniqueCounts.size() == 1) {
                            // All days have the same count - set allDaySame=true and empty dayWiseIdealCount
                            allDaySame = true;
                            idealCount = uniqueCounts.iterator().next();
                            dayWiseIdealCount = new HashMap<>(); // Empty map when allDaySame=true
                        } else {
                            // Different counts for different days
                            allDaySame = false;
                            idealCount = null;
                        }
                    } else {
                        // Set empty map if no ideal counts found
                        allDaySame = false;
                        idealCount = null;
                    }

                } catch (Exception e) {
                    log.warn("Error getting ideal counts for universal shift", e);
                    // Continue with empty ideal counts
                }

                shiftDetails.add(
                    CafeShiftDataDTO.ShiftDetailDTO.builder()
                        .cafeShiftMappingId(null) // Universal shift doesn't have a mapping ID for this unit
                        .shiftId(universalShift.getShiftId())
                        .shiftName(universalShift.getShiftName())
                        .startTime(universalShift.getStartTime())
                        .endTime(universalShift.getEndTime())
                        .creationTime(universalShift.getCreationTime())
                        .createdBy(universalShift.getCreatedBy())
                        .dayWiseIdealCount(dayWiseIdealCount)
                        .idealCount(idealCount)
                        .allDaySame(allDaySame)
                        .build()
                );
            }

            // Get all active shift mappings for this unit
            List<ShiftCafeMapping> shiftCafeMappings = shiftCafeMappingRepository.findByUnitIdAndStatus(unitId, RosteringConstants.ACTIVE);
            if (shiftCafeMappings != null && !shiftCafeMappings.isEmpty()) {
                // Collect all unique shiftIds for this unit
                List<Integer> shiftIds = shiftCafeMappings.stream()
                        .map(ShiftCafeMapping::getShiftId)
                        .distinct()
                        .toList();

                // Add mapped shifts (excluding universal shift if it's already added above)
                for (Integer shiftId : shiftIds) {
                    // Skip if this is the universal shift (already added above)
                    if (universalShift != null && universalShift.getShiftId().equals(shiftId)) {
                        log.debug("Skipping universal shift as it's already added for unitId: {}", unitId);
                        continue;
                    }

                    Shift shift = shiftRepository.findById(shiftId).orElse(null);
                    if (shift == null) {
                        log.warn("Shift with ID {} not found, skipping", shiftId);
                        continue;
                    }

                    // Use the specific mapping for this unit
                    Integer shiftCafeMappingId = shiftCafeMappings.stream()
                        .filter(mapping -> mapping.getShiftId().equals(shiftId))
                        .findFirst()
                        .map(ShiftCafeMapping::getShiftCafeMappingId)
                        .orElse(null);

                    log.debug("Found shiftCafeMappingId: {} for shiftId: {} in unitId: {}", shiftCafeMappingId, shiftId, unitId);

                    // Get ideal counts for this shift
                    Map<String, Integer> dayWiseIdealCount = new HashMap<>();
                    boolean allDaySame = false;
                    Integer idealCount = null;

                    // Get coverage plans for THIS specific mapping
                    if (shiftCafeMappingId != null) {
                        List<com.stpl.tech.attendance.entity.RosteringEntity.ShiftCoveragePlan> plans =
                            shiftCoveragePlanRepository.findByShiftCafeMappingIdAndStatus(shiftCafeMappingId, RosteringConstants.ACTIVE);

                        for (com.stpl.tech.attendance.entity.RosteringEntity.ShiftCoveragePlan plan : plans) {
                            dayWiseIdealCount.put(plan.getDay(), plan.getIdealCount());
                        }
                    }

                    shiftDetails.add(
                        CafeShiftDataDTO.ShiftDetailDTO.builder()
                            .cafeShiftMappingId(shiftCafeMappingId)
                            .shiftId(shift.getShiftId())
                            .shiftName(shift.getShiftName())
                            .startTime(shift.getStartTime())
                            .endTime(shift.getEndTime())
                            .creationTime(shift.getCreationTime())
                            .createdBy(shift.getCreatedBy())
                            .dayWiseIdealCount(dayWiseIdealCount)
                            .idealCount(idealCount)
                            .allDaySame(allDaySame)
                            .build()
                    );
                }
            }

            log.info("Returning cafe shift data for unitId: {} with {} shifts (including universal shift)", unitId, shiftDetails.size());
           return CafeShiftDataDTO.builder()
                   .unitId(unit.getId())
                   .unitName(unit.getName())
                   .shifts(shiftDetails)
                   .build();

        } catch (Exception e) {
            log.error("Error getting cafe shift data for unitId: {}", unitId, e);
            throw new BusinessException("Failed to get cafe shift data", e);
        }
    }

    @NotNull
    private static List<Map<String, Object>> getEmpList(Integer employeeId, Map<Integer, EmployeeBasicDetail> allEmployees) {
        List<Map<String, Object>> result = new ArrayList<>();
        for (EmployeeBasicDetail emp : allEmployees.values()) {
            if (employeeId != null && employeeId.equals(emp.getReportingManagerId())) {
                Map<String, Object> empMap = new java.util.HashMap<>();
                empMap.put("id", emp.getId());
                empMap.put("name", emp.getName());
                empMap.put("email", emp.getEmailId());
                empMap.put("role", emp.getDesignation());
                result.add(empMap);
            }
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = false)
    public ShiftResponseDTO createShift(ShiftRequestDTO shiftRequestDTO, String createdBy) {
        log.info("Creating new shift: {}", shiftRequestDTO.getShiftName());
//        if (shiftRequestDTO.getStartTime().toLocalDate().isEqual(shiftRequestDTO.getEndTime().toLocalDate()) && shiftRequestDTO.getStartTime().isAfter(shiftRequestDTO.getEndTime())) {
//            throw new BusinessException("Start time cannot be after end time on the same day");
//        }
        // validate shift_name,startTime,endTime form db
        LocalDateTime startTime = shiftRequestDTO.getStartTime().toLocalDateTime();
        LocalDateTime endTime = shiftRequestDTO.getEndTime().toLocalDateTime();
        if(shiftRepository.existsByShiftNameIgnoreCaseAndStartTimeAndEndTimeAndStatus(shiftRequestDTO.getShiftName(),startTime,endTime)){
            throw new BusinessException("Shift with name '" + shiftRequestDTO.getShiftName() + "' and startTime '" + shiftRequestDTO.getStartTime() + "' and endTime '" + shiftRequestDTO.getEndTime() + "' already exists");
        }else if(shiftRepository.findByShiftNameAndStatus(shiftRequestDTO.getShiftName(), RosteringConstants.ACTIVE).isPresent()){
            throw new BusinessException("Shift with name'" + shiftRequestDTO.getShiftName() + "' already exists");
        }
        try{
            // Use status from request payload, default to ACTIVE if not provided
//            String status = shiftRequestDTO.getStatus() != null ? shiftRequestDTO.getStatus() : RosteringConstants.SHIFT_STATUS_ACTIVE;
            Shift shift = Shift.builder()
                    .shiftName(shiftRequestDTO.getShiftName())
                    .startTime(startTime)
                    .endTime(endTime)
                    .status(RosteringConstants.ACTIVE)
                    .createdBy(createdBy)
                    .creationTime(LocalDateTime.now())
                    .updatedBy(createdBy)
                    .updationTime(LocalDateTime.now())
                    .build();
             shiftRepository.save(shift);
            return convertToShiftResponseDTO(shift,"Shift created successfully");
        } catch (Exception e) {
            log.error("Error creating shift", e);
            throw new BusinessException("Failed to create shift", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = false)
    public ShiftResponseDTO updateShift( ShiftRequestDTO shiftRequestDTO, String updatedBy) {
        log.info("Updating shift with ID: {}", shiftRequestDTO.getShiftId());
        try {
            // Find the shift first
            Shift shift = shiftRepository.findById(shiftRequestDTO.getShiftId())
                .orElseThrow(() -> new BusinessException("Shift not found"));



            // Check if shift can be deactivated
            if(shiftRequestDTO.getStatus().equals(RosteringConstants.IN_ACTIVE)){
                List<ShiftCafeMapping> shiftCafeMapping = shiftCafeMappingRepository.findByShiftIdAndStatus(shiftRequestDTO.getShiftId(),RosteringConstants.ACTIVE);
                EmpShiftMappingList empShiftMapping = EmpShiftMappingFinder.findMany(
                        EmpShiftMappingFinder.shiftId().eq(shiftRequestDTO.getShiftId())
                                .and(EmpShiftMappingFinder.status().eq(RosteringConstants.ACTIVE)));
                if(!shiftCafeMapping.isEmpty() || !empShiftMapping.isEmpty()){
                    throw new BusinessException("Shift is mapped to employees and units, cannot be deleted");
                }
            }

            // Update shift properties
            LocalDateTime startTime = shiftRequestDTO.getStartTime().toLocalDateTime();
            LocalDateTime endTime = shiftRequestDTO.getEndTime().toLocalDateTime();

            shift.setShiftName(shiftRequestDTO.getShiftName());
            shift.setStartTime(startTime);
            shift.setEndTime(endTime);

            // Update status if provided in request, otherwise keep existing status
            if (shiftRequestDTO.getStatus() != null) {
                shift.setStatus(shiftRequestDTO.getStatus());
            }
            shift.setUpdatedBy(updatedBy);
            shift = shiftRepository.save(shift);

            return convertToShiftResponseDTO(shift, "Shift updated successfully");

        } catch (BusinessException e) {
            // Re-throw BusinessException as-is since it's already properly handled
            throw e;
        } catch (Exception e) {
            log.error("Error updating shift", e);
            throw new BusinessException("Failed to update shift: " + e.getMessage());
        }
    }

    // Helper methods for CafeLiveDashboard

    /**
     * Build empty cafe live dashboard response when no data is available
     * @return Empty dashboard response with default values
     */
    private RosterLiveDashboardResponseDTO buildEmptyCafeLiveDashboardResponse(GenericFilterMetadataDTO filterMetadataDTO) {
        return RosterLiveDashboardResponseDTO.builder()
                .dashboardView(RosterLiveDashboardResponseDTO.DashboardViewDTO.builder()
                        .date(RosterLiveDashboardResponseDTO.DateRangeDTO.builder()
                                .date(null)
                                .build())
                        .cafeDashboard(RosterLiveDashboardResponseDTO.CafeDashboardStatsDTO.builder()
                                .actual(RosteringConstants.DEFAULT_ACTUAL)
                                .ideal(RosteringConstants.DEFAULT_IDEAL)
                                .build())
                        .shifts(new ArrayList<>())
                        .build())
                .filterMetadata(Objects.nonNull(filterMetadataDTO) ? filterMetadataDTO :  GenericFilterMetadataDTO.builder()
                        .availableFilters(new ArrayList<>())
                        .appliedFilters(GenericFilterMetadataDTO.FilterAppliedFlags.builder()
                                .appliedFilters(new HashMap<>())
                                .anyFilterApplied(false)
                                .build())
                        .build())
                .build();
    }

    /**
     * Filter shift mappings to only include those for employees in the given hierarchy
     * @param allMappings All shift mappings
     * @param employeeIds Set of employee IDs in hierarchy
     * @return Filtered mappings
     */
    private EmpShiftMappingList filterMappingsByEmployeeIds(EmpShiftMappingList allMappings, Set<Integer> employeeIds) {
        // TODO: Fix Reladomo dependency - temporarily returning empty list
        // EmpShiftMappingList filteredMappings = new EmpShiftMappingList();
        // for (com.stpl.tech.attendance.domain.EmpShiftMapping mapping : allMappings) {
        //     if (employeeIds.contains(mapping.getEmpId())) {
        //         filteredMappings.add(mapping);
        //     }
        // }
        // return filteredMappings;
        return new EmpShiftMappingList();
    }

    /**
     * Build list of shift info DTOs from shift mappings
     * @param shiftMappingsMap Map of shift ID to list of mappings
     * @return List of shift info DTOs
     */
    private List<RosterLiveDashboardResponseDTO.ShiftInfoDTO> buildShiftInfoList(
            Map<Integer, List<com.stpl.tech.attendance.domain.EmpShiftMapping>> shiftMappingsMap) {
        List<RosterLiveDashboardResponseDTO.ShiftInfoDTO> shifts = new ArrayList<>();

        for (Map.Entry<Integer, List<com.stpl.tech.attendance.domain.EmpShiftMapping>> entry : shiftMappingsMap.entrySet()) {
            Integer shiftId = entry.getKey();
            List<com.stpl.tech.attendance.domain.EmpShiftMapping> mappings = entry.getValue();

            Shift shift = shiftRepository.findById(shiftId).orElse(null);
            if (shift != null) {
                // Count employees in hierarchy for this shift
                int employeeCount = mappings.size();

                RosterLiveDashboardResponseDTO.ShiftInfoDTO shiftInfo =
                        RosterLiveDashboardResponseDTO.ShiftInfoDTO.builder()
                                .shiftId(shift.getShiftId())
                                .startTime(shift.getStartTime())
                                .endTime(shift.getEndTime())
                                .numberOfEmployees(employeeCount)
                                .shiftName(shift.getShiftName())
                                .build();
                shifts.add(shiftInfo);
            }
        }
        return shifts;
    }

    /**
     * Calculate total number of employees across all shifts
     * @param shifts List of shift info DTOs
     * @return Total employee count
     */
    private int calculateTotalEmployees(List<RosterLiveDashboardResponseDTO.ShiftInfoDTO> shifts) {
        return shifts.stream()
                .mapToInt(RosterLiveDashboardResponseDTO.ShiftInfoDTO::getNumberOfEmployees)
                .sum();
    }

    /**
     * Build dashboard view with current attendance date and cafe dashboard stats
     * @param shifts List of shift info DTOs
     * @param totalActual Total actual employee count
     * @param currentAttendanceDate Current attendance date
     * @return Dashboard view DTO
     */
    private RosterLiveDashboardResponseDTO.DashboardViewDTO buildDashboardView(
            List<RosterLiveDashboardResponseDTO.ShiftInfoDTO> shifts, int totalActual, LocalDate currentAttendanceDate) {
        return RosterLiveDashboardResponseDTO.DashboardViewDTO.builder()
                .date(RosterLiveDashboardResponseDTO.DateRangeDTO.builder()
                        .date(currentAttendanceDate.atStartOfDay())
                        .build())
                .cafeDashboard(RosterLiveDashboardResponseDTO.CafeDashboardStatsDTO.builder()
                        .actual(RosteringConstants.DEFAULT_IDEAL)
                        .ideal(totalActual)
                        .build())
                .shifts(shifts)
                .build();
    }

    @Override
    @Transactional(readOnly = true)
    public List<com.stpl.tech.master.domain.model.UnitBasicDetail> getManagedUnits(Integer userId) {
        log.info("Getting managed units for userId: {}", userId);

        try {
            // Get unit IDs managed by the user using MetadataService
            List<Integer> managedUnitIds = metadataService.getManagedUnitIds(userId);

            // Convert unit IDs to UnitBasicDetail objects
            List<com.stpl.tech.master.domain.model.UnitBasicDetail> units = managedUnitIds.stream()
                    .map(unitCacheService::getUnitBasicDetail)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            log.info("Found {} managed units for userId: {}", units.size(), userId);
            return units;

        } catch (Exception e) {
            log.error("Error getting managed units for userId: {}", userId, e);
            throw new BusinessException("Failed to get managed units", e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<ShiftResponseDTO> getAllShifts() {
        try {
            List<Shift> shifts = shiftRepository.findByStatus(RosteringConstants.ACTIVE);
            log.info("Found {} active shifts", shifts.size());
            return shifts.stream()
                .map(shift -> {
                    return convertToShiftResponseDTO(shift, "Active shifts returned successfully");
                })
                .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("Error getting all shifts", e);
            throw new BusinessException("Failed to get all shifts", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = false)
    public ShiftCafeMappingResponseDTO createShiftCafeMapping(ShiftCafeMappingRequestDTO request) {
        log.info("Creating shift cafe mapping for shiftId: {}, unitId: {}", request.getShiftId(), request.getUnitId());
        String createdBy = JwtContext.getInstance().getUserId().toString();
        try {
            // Check if mapping already exists
            if (shiftCafeMappingRepository.existsByShiftIdAndUnitIdAndStatus(request.getShiftId(), request.getUnitId(), RosteringConstants.ACTIVE)) {
                log.info("Shift cafe mapping already exists for shiftId: {}, unitId: {}", request.getShiftId(), request.getUnitId());
                return ShiftCafeMappingResponseDTO.builder()
                .shiftCafeMappingId(null)
                .shiftId(request.getShiftId())
                .unitId(request.getUnitId())
                .successMessage("Shift cafe mapping already exists")
                .build();
            }

            ShiftCafeMapping mapping = ShiftCafeMapping.builder()
                .shiftId(request.getShiftId())
                .unitId(request.getUnitId())
                .status(RosteringConstants.ACTIVE)
                .createdBy(createdBy)
                .updatedBy(createdBy)
                .build();

            mapping = shiftCafeMappingRepository.save(mapping);
            // update shift coverage plan using reladomo
            reladomoService.updateShiftCoveragePlan(request);

            log.info("Created shift cafe mapping with ID: {}", mapping.getShiftCafeMappingId());
            return ShiftCafeMappingResponseDTO.builder()
                .shiftCafeMappingId(mapping.getShiftCafeMappingId())
                .shiftId(mapping.getShiftId())
                .unitId(mapping.getUnitId())
                .successMessage("Shift cafe mapping created successfully")
                .build();

        } catch (Exception e) {
            log.error("Error creating shift cafe mapping", e);
            throw new BusinessException("Failed to create shift cafe mapping", e);
        }
    }

    @Transactional(rollbackFor = Exception.class, readOnly = false)
    @Override
    public ShiftCafeMappingResponseDTO updateShiftCafeMapping(ShiftCafeMappingRequestDTO request) {
        log.info("Updating shift cafe mapping for shiftId: {}, unitId: {}", request.getShiftId(), request.getUnitId());
        String createdBy = JwtContext.getInstance().getUserId().toString();
        try {
            String successMessage = "Shift cafe mapping updated successfully";
            ShiftCafeMapping mapping = shiftCafeMappingRepository
                .findByShiftIdAndUnitIdAndStatusUpdate(request.getShiftId(), request.getUnitId(), RosteringConstants.ACTIVE);

            if(mapping == null){
                throw new BusinessException("Shift cafe mapping not found");
            }
            EmpShiftMappingList allEmpShiftMappings = EmpShiftMappingFinder.findMany(
                    EmpShiftMappingFinder.all().and(EmpShiftMappingFinder.shiftId().eq(request.getShiftId())));
            if(!allEmpShiftMappings.isEmpty() && request.getAction().equals(RosteringConstants.CAFE_SHIFT_DELETE)
                    && request.getShiftId() == 4){
                throw new BusinessException("Shift is mapped to employees or this is the universal shift, cannot be deleted");
            }
            mapping.setUpdatedBy(createdBy);
            mapping.setUpdationTime(LocalDateTime.now());
            mapping.setUnitId(request.getUnitId());
            mapping.setShiftId(request.getShiftId());
            mapping.setStatus(request.getAction().equals(RosteringConstants.CAFE_SHIFT_DELETE)
                    ? RosteringConstants.IN_ACTIVE
                    : RosteringConstants.ACTIVE);
            shiftCafeMappingRepository.save(mapping);

            // Only update shift coverage plan if NOT deleting
            if (!request.getAction().equals(RosteringConstants.CAFE_SHIFT_DELETE)) {
                // updating to SHIFT_COVERGARE_PLAN TABLE using reladomo
                reladomoService.updateShiftCoveragePlan(request);
            }

            return ShiftCafeMappingResponseDTO.builder()
                    .shiftCafeMappingId(mapping.getShiftCafeMappingId())
                    .shiftId(mapping.getShiftId())
                    .unitId(mapping.getUnitId())
                    .successMessage(successMessage)
                .build();
        } catch (BusinessException e) {
            // Re-throw BusinessException as-is since it's already properly handled
            throw e;
        } catch (Exception e) {
            log.error("Error updating shift cafe mapping", e);
            throw new BusinessException("Failed to update shift cafe mapping: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = false)
    public void createEmpShiftMappingOverriding(EmpShiftUpdateRequestDTO request) {
        log.info("Creating employee shift override for empId: {}, shiftId: {}, unitId: {}, startDate: {}, endDate: {}",
                request.getEmpId(), request.getShiftId(), request.getUnitId(), request.getBusinessFrom(), request.getBusinessTo());

        try {
            request.setUpdatedBy(String.valueOf(JwtContext.getInstance().getUserId()));
            // Validate request
            validateEmpShiftOverrideRequest(request);

            // Validate employee unit assignment
            LocalDate businessDate = request.getBusinessFrom().toLocalDate();
            validateEmployeeUnitAssignment(request.getEmpId(), request.getUnitId(), businessDate);

            // Convert LocalDateTime to LocalDate for the override table
            LocalDate startDate = request.getBusinessFrom().toLocalDate();
            LocalDate endDate = request.getBusinessTo().toLocalDate();

            String createdBy = JwtContext.getInstance().getUserId().toString();

            // Check for overlapping overrides
            List<EmpShiftOverride> overlappingOverrides = empShiftOverrideRepository.findOverlappingOverrides(
                    request.getEmpId(), RosteringConstants.ACTIVE, startDate, endDate);

            if (!overlappingOverrides.isEmpty()) {
                log.warn("Found {} overlapping overrides for empId: {} in date range {} to {}",
                        overlappingOverrides.size(), request.getEmpId(), startDate, endDate);

                // Option 1: Inactivate existing overrides
                for (EmpShiftOverride existingOverride : overlappingOverrides) {
                    existingOverride.setStatus(RosteringConstants.IN_ACTIVE);
                    existingOverride.setUpdatedBy(createdBy);
                    existingOverride.setUpdatedAt(LocalDateTime.now());
                    empShiftOverrideRepository.save(existingOverride);
                    log.info("Inactivated existing override with ID: {}", existingOverride.getId());
                }
            }

            // Create new override
            EmpShiftOverride override = EmpShiftOverride.builder()
                    .empId(request.getEmpId())
                    .shiftId(request.getShiftId())
                    .startDate(startDate)
                    .endDate(endDate)
                    .unitId(request.getUnitId())
                    .status(RosteringConstants.ACTIVE)
                    .createdBy(createdBy)
                    .updatedBy(createdBy)
                    .build();

            EmpShiftOverride savedOverride = empShiftOverrideRepository.save(override);

            log.info("Successfully created employee shift override with ID: {} for empId: {}, shiftId: {}, unitId: {}, date range: {} to {}",
                    savedOverride.getId(), request.getEmpId(), request.getShiftId(), request.getUnitId(), startDate, endDate);

            // Invalidate cache for affected dates
            employeeShiftInstanceRecreationService.invalidateCacheForDateRange(request.getEmpId(), startDate, endDate);

            // Trigger recreation of shift instances from effective date
            triggerShiftInstanceRecreation(request.getEmpId(), startDate, request.getUpdatedBy());

            // Send notification after successful creation
            rosteringNotificationUtil.sendRosteringNotification(request, RosteringConstants.OVERRIDE_CREATED);

        } catch (Exception e) {
            log.error("Error creating employee shift override for empId: {}, shiftId: {}, unitId: {}",
                    request.getEmpId(), request.getShiftId(), request.getUnitId(), e);
            throw new BusinessException("Failed to create employee shift override", e);
        }
    }

    /**
     * Validate employee shift override request
     * @param request Employee shift update request to validate
     * @throws IllegalArgumentException if validation fails
     */
    private void validateEmpShiftOverrideRequest(EmpShiftUpdateRequestDTO request) {
        if (request == null) {
            throw new IllegalArgumentException("Request cannot be null");
        }

        if (request.getEmpId() == null) {
            throw new IllegalArgumentException("Employee ID is required");
        }

        if (request.getShiftId() == null) {
            throw new IllegalArgumentException("Shift ID is required");
        }

        if (request.getUnitId() == null) {
            throw new IllegalArgumentException("Unit ID is required");
        }

        if (request.getBusinessFrom() == null) {
            throw new IllegalArgumentException("Start date is required");
        }

        if (request.getBusinessTo() == null) {
            throw new IllegalArgumentException("End date is required");
        }

        // Validate that shift has not already started for the business date
        validateShiftStartTime(request);

        if (request.getBusinessFrom().isAfter(request.getBusinessTo())) {
            throw new IllegalArgumentException("Start date cannot be after end date");
        }

        if (request.getBusinessFrom().isAfter(request.getBusinessTo().plusDays(365))) {
            throw new IllegalArgumentException("Date range cannot exceed 1 year");
        }

        if (request.getEmpId() <= 0) {
            throw new IllegalArgumentException("Employee ID must be a positive integer");
        }

        if (request.getShiftId() <= 0) {
            throw new IllegalArgumentException("Shift ID must be a positive integer");
        }

        if (request.getUnitId() <= 0) {
            throw new IllegalArgumentException("Unit ID must be a positive integer");
        }

        log.debug("Override request validation passed for employee {}, shift {}, and unit {}",
                 request.getEmpId(), request.getShiftId(), request.getUnitId());
    }

    /**
     * Validate that the shift has not already started for the given business date
     * @param request The shift update request
     */
    private void validateShiftStartTime(EmpShiftUpdateRequestDTO request) {
        try {
            // Get shift details from repository
            Shift shift = shiftRepository.findById(request.getShiftId())
                    .orElseThrow(() -> new IllegalArgumentException("Shift not found with ID: " + request.getShiftId()));

            // Get the business date (date part only)
            LocalDate businessDate = request.getBusinessFrom().toLocalDate();
            
            // Create the actual shift start time for the business date
            LocalDateTime actualShiftStartTime = businessDate.atTime(shift.getStartTime().toLocalTime());
            
            // Get current time
            LocalDateTime currentTime = LocalDateTime.now();
            
            // Check if shift has already started
            if (currentTime.isAfter(actualShiftStartTime)) {
                String errorMessage = String.format(
                    "Shift '%s' (%s - %s) has already started for %s. Cannot update shift mapping. Please select a different shift or date.",
                    shift.getShiftName(),
                    shift.getStartTime().toLocalTime().format(java.time.format.DateTimeFormatter.ofPattern("hh:mm a")),
                    shift.getEndTime().toLocalTime().format(java.time.format.DateTimeFormatter.ofPattern("hh:mm a")),
                    businessDate.format(java.time.format.DateTimeFormatter.ofPattern("dd MMM yyyy"))
                );
                throw new BusinessException(errorMessage);
            }
            
            log.debug("Shift start time validation passed for shift {} on date {}", 
                     shift.getShiftName(), businessDate);
                     
        } catch (BusinessException e) {
            // Re-throw BusinessException as is
            throw e;
        } catch (Exception e) {
            log.error("Error validating shift start time for shiftId: {}", request.getShiftId(), e);
            throw new BusinessException("Error validating shift start time", e);
        }
    }

    /**
     * Validate that employee belongs to the specified unit based on emp shift mapping first, then emp eligibility mapping
     * @param empId Employee ID
     * @param unitId Unit ID
     * @param businessDate Business date for validation
     * @throws IllegalArgumentException if validation fails
     */
    private void validateEmployeeUnitAssignment(Integer empId, Integer unitId, LocalDate businessDate) {
        if (empId == null || unitId == null) {
            throw new IllegalArgumentException("Employee ID and Unit ID are required for validation");
        }

//        // Get the actual unit ID for this employee on the business date
//        Integer actualUnitId = unitResolutionService.getUnitIdForEmployee(empId, businessDate);
//
//        if (!unitId.equals(actualUnitId)) {
//            throw new IllegalArgumentException(
//                    String.format("Employee %d is assigned to unit %d on date %s, but requested unit is %d",
//                            empId, actualUnitId, businessDate, unitId));
//        }

        log.debug("Validated employee {} assignment to unit {} for date {}", empId, unitId, businessDate);
    }

    @Override
    public void deleteEmpShiftMapping(Integer mappingId) {
        log.info("Deleting employee shift mapping with ID: {}", mappingId);

        try {
            EmpShiftMapping mapping = empShiftMappingRepository.findById(mappingId)
                .orElseThrow(() -> new RuntimeException("Employee shift mapping not found"));

            mapping.setStatus(RosteringConstants.IN_ACTIVE);
            empShiftMappingRepository.save(mapping);

        } catch (Exception e) {
            log.error("Error deleting employee shift mapping", e);
            throw new BusinessException("Failed to delete employee shift mapping", e);
        }
    }

    private Shift convertToShiftDTO(com.stpl.tech.attendance.entity.RosteringEntity.Shift shift) {
        return Shift.builder()
            .shiftId(shift.getShiftId())
            .shiftName(shift.getShiftName())
            .startTime(shift.getStartTime())
            .endTime(shift.getEndTime())
            .status(shift.getStatus())
            .createdBy(shift.getCreatedBy())
            .creationTime(shift.getCreationTime())
            .updatedBy(shift.getUpdatedBy())
            .updationTime(shift.getUpdationTime())
            .build();
    }

    private ShiftResponseDTO convertToShiftResponseDTO(com.stpl.tech.attendance.entity.RosteringEntity.Shift shift ,String successMessage) {
        return ShiftResponseDTO.builder()
            .shiftId(shift.getShiftId()) // Auto-generated from DB
            .shiftName(shift.getShiftName())
            .startTime(shift.getStartTime())
            .endTime(shift.getEndTime())
            .status(shift.getStatus())
            .dayWiseIdealCount(new HashMap<>()) // Initialize with empty map, will be populated by caller
            .idealCount(null) // Initialize as null, will be set by caller
            .allDaySame(false) // Initialize as false, will be set by caller
            .successMessage(successMessage)
            .creationTime(LocalDateTime.now())
            .build();
    }

    private ShiftCafeMappingDTO convertToShiftCafeMappingDTO(ShiftCafeMapping mapping) {
        String shiftName = null;
        String unitName = null;

        if (mapping.getShift() != null) {
            shiftName = mapping.getShift().getShiftName();
        }

        UnitBasicDetail unit = unitCacheService.getUnitBasicDetail(mapping.getUnitId());
        if (unit != null) {
            unitName = unit.getName();
        }

        return ShiftCafeMappingDTO.builder()
            .shiftCafeMappingId(mapping.getShiftCafeMappingId())
            .shiftId(mapping.getShiftId())
            .shiftName(shiftName)
            .unitId(mapping.getUnitId())
            .unitName(unitName)
            .status(mapping.getStatus())
            .createdBy(mapping.getCreatedBy())
            .creationTime(mapping.getCreationTime())
            .updatedBy(mapping.getUpdatedBy())
            .updationTime(mapping.getUpdationTime())
            .build();
    }

    private EmpShiftMappingDTO convertToEmpShiftMappingDTO(EmpShiftMapping mapping) {
        String shiftName = null;
        String empName = null;
        String empCode = null;
        String unitName = null;

        if (mapping.getShift() != null) {
            shiftName = mapping.getShift().getShiftName();
        }

        EmployeeBasicDetail emp = userCacheService.getUserById(mapping.getEmpId());
        if (emp != null) {
            empName = emp.getName();
            empCode = emp.getEmployeeCode();
        }

        UnitBasicDetail unit = unitCacheService.getUnitBasicDetail(mapping.getUnitId());
        if (unit != null) {
            unitName = unit.getName();
        }

        return EmpShiftMappingDTO.builder()
            .id(mapping.getId())
            .shiftId(mapping.getShiftId())
            .shiftName(shiftName)
            .empId(mapping.getEmpId())
            .empName(empName)
            .empCode(empCode)
            .unitId(mapping.getUnitId())
            .unitName(unitName)
            .expectedStartDate(mapping.getExpectedStartDate())
            .expectedEndDate(mapping.getExpectedEndDate())
            .processingFrom(mapping.getProcessingFrom())
            .processingTo(mapping.getProcessingTo())
            .businessFrom(mapping.getBusinessFrom())
            .businessTo(mapping.getBusinessTo())
            .status(mapping.getStatus())
            .createdBy(mapping.getCreatedBy())
            .creationTime(mapping.getCreationTime())
            .updatedBy(mapping.getUpdatedBy())
            .updationTime(mapping.getUpdationTime())
            .build();
    }

    private HierarchyEmployeeDTO convertToHierarchyEmployeeDTO(EmployeeBasicDetail emp, int level) {
        String reportingManagerName = null;
        if (emp.getReportingManagerId() != null) {
            EmployeeBasicDetail manager = userCacheService.getUserById(emp.getReportingManagerId());
            if (manager != null) {
                reportingManagerName = manager.getName();
            }
        }

        return HierarchyEmployeeDTO.builder()
            .empId(emp.getId())
            .empName(emp.getName())
            .empCode(emp.getEmployeeCode())
            .designation(emp.getDesignation())
            .contactNumber(emp.getContactNumber())
            .emailId(emp.getEmailId())
            .status(emp.getStatus().name())
            .reportingManagerId(emp.getReportingManagerId())
            .reportingManagerName(reportingManagerName)
            .departmentId(emp.getDepartmentId())
            .departmentName(emp.getDepartmentName())
            .level(level)
            .subordinates(new ArrayList<>())
            .build();
    }

    /**
     * Build default metadata response when there are errors or missing data
     * @return Default metadata with all flags set to false
     */
    private RosteringMetadataResponseDTO buildDefaultMetadata() {
        return RosteringMetadataResponseDTO.builder()
            .allShiftManagement(false)
            .unitShiftManagement(false)
            .liveDashboardView(false)
            .shiftDashboardView(false)
            .employeeDashboardView(false)
            .build();
    }

    // Note: Old filter methods have been replaced with generic filter methods
    // The following methods are kept for backward compatibility if needed:
    // - applyFiltersToUnits (old version)
    // - validateFilterRequest (old version)
    // - buildFilterMetadata (old version)



    /**
     * Build shift info list from employee shift instances
     * @param shiftInstances List of employee shift instances
     * @param currentAttendanceDate Current attendance date
     * @return List of shift info DTOs
     */
    private List<RosterLiveDashboardResponseDTO.ShiftInfoDTO> buildShiftInfoListFromInstances(List<EmployeeShiftInstances> shiftInstances, LocalDate currentAttendanceDate) {
        // Group by shift ID to get unique shifts
        Map<Integer, List<EmployeeShiftInstances>> shiftInstancesMap = shiftInstances.stream()
            .collect(Collectors.groupingBy(EmployeeShiftInstances::getShiftId));

        List<RosterLiveDashboardResponseDTO.ShiftInfoDTO> shifts = new ArrayList<>();

        for (Map.Entry<Integer, List<EmployeeShiftInstances>> entry : shiftInstancesMap.entrySet()) {
            Integer shiftId = entry.getKey();
            List<EmployeeShiftInstances> instances = entry.getValue();

            Shift shift = shiftRepository.findById(shiftId).orElse(null);
            if (shift != null) {
                // Count employees for this shift
                int employeeCount = instances.size();

                // Create shift times using attendance date + shift times
                LocalDateTime startTime = currentAttendanceDate.atTime(shift.getStartTime().toLocalTime());
                LocalDateTime endTime = currentAttendanceDate.atTime(shift.getEndTime().toLocalTime());

                RosterLiveDashboardResponseDTO.ShiftInfoDTO shiftInfo =
                        RosterLiveDashboardResponseDTO.ShiftInfoDTO.builder()
                                .shiftId(shift.getShiftId())
                                .startTime(startTime)
                                .endTime(endTime)
                                .numberOfEmployees(employeeCount)
                                .shiftName(shift.getShiftName())
                                .build();
                shifts.add(shiftInfo);
            }
        }
        return shifts;
    }

    /**
     * Build dashboard view with actual and ideal counts
     * @param shifts List of shift info DTOs
     * @param actualCount Actual employee count
     * @param idealCount Ideal employee count
     * @param currentAttendanceDate Current attendance date
     * @return Dashboard view DTO
     */
    private RosterLiveDashboardResponseDTO.DashboardViewDTO buildDashboardViewWithCounts(
            List<RosterLiveDashboardResponseDTO.ShiftInfoDTO> shifts, int actualCount, int idealCount, LocalDate currentAttendanceDate) {
        return RosterLiveDashboardResponseDTO.DashboardViewDTO.builder()
                .date(RosterLiveDashboardResponseDTO.DateRangeDTO.builder()
                        .date(currentAttendanceDate.atStartOfDay())
                        .build())
                .cafeDashboard(RosterLiveDashboardResponseDTO.CafeDashboardStatsDTO.builder()
                        .actual(actualCount)
                        .ideal(idealCount)
                        .build())
                .shifts(shifts)
                .build();
    }

    // Helper methods for ShiftEmployeesForUser

    /**
     * Build empty shift employees response when no data is available
     * @return Empty response with default values
     */
    private ShiftEmployeesResponseDTO buildEmptyShiftEmployeesResponse() {
        return ShiftEmployeesResponseDTO.builder()
                .shifts(new ArrayList<>())
                .filterMetadata(GenericFilterMetadataDTO.builder()
                        .availableFilters(new ArrayList<>())
                        .appliedFilters(GenericFilterMetadataDTO.FilterAppliedFlags.builder()
                                .appliedFilters(new HashMap<>())
                                .anyFilterApplied(false)
                                .build())
                        .build())
                .build();
    }

    /**
     * Apply generic filters to units
     * @param managedUnitIds List of managed unit IDs
     * @param filterRequest Generic filter request
     * @return List of filtered unit IDs
     */
    private List<Integer> applyGenericFiltersToUnits(List<Integer> managedUnitIds, GenericFilterRequestDTO filterRequest) {
        if (filterRequest == null || filterRequest.getFilters() == null || filterRequest.getFilters().isEmpty()) {
            // No filters applied, return all managed unit IDs
            return managedUnitIds;
        }

        return managedUnitIds.stream()
            .filter(unitId -> {
                UnitBasicDetail unit = unitCacheService.getUnitBasicDetail(unitId);
                if (unit == null) {
                    log.warn("Unit not found for unitId: {}", unitId);
                    return false;
                }

                boolean includeUnit = true;

                // Apply unit filter if specified
                GenericFilterRequestDTO.FilterValue unitFilter = filterRequest.getFilters().get(RosteringConstants.FILTER_KEY_UNIT_IDS);
                if (unitFilter != null && "IN".equalsIgnoreCase(unitFilter.getOperator())) {
                    List<Integer> unitIds = unitFilter.getValues().stream()
                        .map(Integer::parseInt)
                        .collect(Collectors.toList());
                    if (!unitIds.contains(unitId)) {
                        includeUnit = false;
                    }
                }

                // Apply city filter if specified
                if (includeUnit) {
                    GenericFilterRequestDTO.FilterValue cityFilter = filterRequest.getFilters().get(RosteringConstants.FILTER_KEY_CITY_NAMES);
                    if (cityFilter != null && "IN".equalsIgnoreCase(cityFilter.getOperator())) {
                        if (unit.getCity() == null || !cityFilter.getValues().contains(unit.getCity())) {
                            includeUnit = false;
                        }
                    }
                }

                return includeUnit;
            })
            .collect(Collectors.toList());
    }

    /**
     * Build shift employees list from employee shift instances
     * @param shiftInstances List of employee shift instances
     * @param businessDate Business date
     * @return List of shift DTOs with employee metadata
     */
    private List<ShiftEmployeesResponseDTO.ShiftDTO> buildShiftEmployeesListFromInstances(List<EmployeeShiftInstances> shiftInstances, LocalDate businessDate) {
        // Group by shift ID to get unique shifts
        Map<Integer, List<EmployeeShiftInstances>> shiftInstancesMap = shiftInstances.stream()
            .collect(Collectors.groupingBy(EmployeeShiftInstances::getShiftId));

        List<ShiftEmployeesResponseDTO.ShiftDTO> shifts = new ArrayList<>();

        for (Map.Entry<Integer, List<EmployeeShiftInstances>> entry : shiftInstancesMap.entrySet()) {
            Integer shiftId = entry.getKey();
            List<EmployeeShiftInstances> instances = entry.getValue();

            Shift shift = shiftRepository.findById(shiftId).orElse(null);
            if (shift != null) {
                // Collect employee metadata for this shift
                List<EmployeeMetadataDTO> employeeMetadataList = new ArrayList<>();
                Integer unitId = unitResolutionService.getUnitIdForEmployee(instances.get(0).getEmpId(), businessDate);
                String unitName = unitCacheService.getUnitBasicDetail(unitId).getName();
                for (EmployeeShiftInstances instance : instances) {
                    EmployeeBasicDetail emp = userCacheService.getUserById(instance.getEmpId());
                    if (emp != null) {
                        // Create employee metadata DTO
                        EmployeeMetadataDTO empMetadata = EmployeeMetadataDTO.builder()
                            .empId(emp.getId())
                            .empName(emp.getName())
                            .empCode(emp.getEmployeeCode())
                            .designation(emp.getDesignation())
                            .status(emp.getStatus().name())
                            .unitId(unitId)
                            .unitName(unitName)
                            .registrationImageUrl(emp.getImagekey())
                            .build();

                        employeeMetadataList.add(empMetadata);
                        log.debug("Added employee {} (ID: {}) to shift {} response", emp.getName(), emp.getId(), shiftId);
                    } else {
                        log.warn("Employee with ID {} not found in cache", instance.getEmpId());
                    }
                }

                // Create shift times using business date + shift times
                LocalDateTime startTime = businessDate.atTime(shift.getStartTime().toLocalTime());
                LocalDateTime endTime = businessDate.atTime(shift.getEndTime().toLocalTime());

                shifts.add(
                    ShiftEmployeesResponseDTO.ShiftDTO.builder()
                        .shiftId(shift.getShiftId())
                        .shiftName(shift.getShiftName())
                        .startTime(startTime)
                        .endTime(endTime)
                        .employeeMetadata(employeeMetadataList)
                        .build()
                );
            }
        }
        return shifts;
    }

    /**
     * Build generic filter metadata with applied filter flags
     * @param managedUnitIds List of managed unit IDs
     * @param filterRequest Generic filter request
     * @return Filter metadata DTO
     */
    private GenericFilterMetadataDTO buildGenericFilterMetadata(List<Integer> managedUnitIds, GenericFilterRequestDTO filterRequest) {
        Map<Integer, UnitBasicDetail> unitMap = unitCacheService.getAllUnitBasicDetailMap();
        // Get unit details for managed units (original unfiltered list)
        List<UnitBasicDetail> managedUnits = managedUnitIds.stream()
            .map(unitMap::get)
            .filter(Objects::nonNull)
            .toList();

        // Build available filters
        List<GenericFilterMetadataDTO.FilterOption> availableFilters = new ArrayList<>();

        // Unit filter
        List<FilterValueOptionDTO> unitValueOptions = managedUnits.stream()
            .map(unit -> FilterValueOptionDTO.builder()
                .value(String.valueOf(unit.getId()))
                .displayName(unit.getName())
                .build())
            .collect(Collectors.toList());

        availableFilters.add(buildFilterOptionFromEnum(
            RosteringFilterType.UNIT,
            unitValueOptions
        ));

        // City filter
        List<String> cities = managedUnits.stream()
            .map(UnitBasicDetail::getCity)
            .filter(Objects::nonNull)
            .distinct()
            .sorted()
            .collect(Collectors.toList());

        availableFilters.add(buildFilterOptionFromEnum(
            RosteringFilterType.CITY,
            buildValueOptionsFromList(cities)
        ));

        // Determine which filters were applied
        Map<String, Boolean> appliedFilters = new HashMap<>();
        boolean anyFilterApplied = false;

        if (filterRequest != null && filterRequest.getFilters() != null) {
            appliedFilters.put(RosteringConstants.FILTER_KEY_UNIT_IDS, filterRequest.getFilters().containsKey(RosteringConstants.FILTER_KEY_UNIT_IDS));
            appliedFilters.put(RosteringConstants.FILTER_KEY_CITY_NAMES, filterRequest.getFilters().containsKey(RosteringConstants.FILTER_KEY_CITY_NAMES));
            anyFilterApplied = filterRequest.getFilters().values().stream().anyMatch(Objects::nonNull);
        } else {
            appliedFilters.put(RosteringConstants.FILTER_KEY_UNIT_IDS, false);
            appliedFilters.put(RosteringConstants.FILTER_KEY_CITY_NAMES, false);
        }

        return GenericFilterMetadataDTO.builder()
            .availableFilters(availableFilters)
            .appliedFilters(GenericFilterMetadataDTO.FilterAppliedFlags.builder()
                .appliedFilters(appliedFilters)
                .anyFilterApplied(anyFilterApplied)
                .build())
            .build();
    }

    /**
     * Build filter option from RosteringFilterType enum
     * @param filterType The filter type enum
     * @param valueOptions List of value options with linked values and display names
     * @return Filter option DTO
     */
    private GenericFilterMetadataDTO.FilterOption buildFilterOptionFromEnum(
            RosteringFilterType filterType,
            List<FilterValueOptionDTO> valueOptions) {
        return GenericFilterMetadataDTO.FilterOption.builder()
            .filterKey(filterType.getKey())
            .filterName(filterType.getDisplayName())
            .dataType(filterType.getDataType())
            .valueOptions(valueOptions)
            .operator(filterType.getDefaultOperator())
            .build();
    }

    /**
     * Build value options from a map of values to display names
     * @param valueDisplayMap Map of values to display names
     * @return List of FilterValueOptionDTO
     */
    private List<FilterValueOptionDTO> buildValueOptionsFromMap(Map<String, String> valueDisplayMap) {
        return valueDisplayMap.entrySet().stream()
            .map(entry -> FilterValueOptionDTO.builder()
                .value(entry.getKey())
                .displayName(entry.getValue())
                .build())
            .collect(Collectors.toList());
    }

    /**
     * Build value options from a list where values and display names are the same
     * @param values List of values (used for both value and display name)
     * @return List of FilterValueOptionDTO
     */
    private List<FilterValueOptionDTO> buildValueOptionsFromList(List<String> values) {
        return values.stream()
            .map(value -> FilterValueOptionDTO.builder()
                .value(value)
                .displayName(value)
                .build())
            .collect(Collectors.toList());
    }

    /**
     * Calculate shift data for a specific date by checking override -> mapping -> universal shift
     * @param empId Employee ID
     * @param date Business date
     * @return Shift data DTO or null if no shift found
     */
    private EmployeeShiftDataResponseDTO.EmployeeShiftDTO calculateShiftDataForDate(Integer empId, LocalDate date) {
        log.debug("Calculating shift data for employee {} on date {}", empId, date);

        Shift shift = shiftHelper.getShiftForEmployee(empId, date);
        if (shift != null) {
            return buildShiftDataDTO(shift, date, empId);
        }

        return null;
    }

    /**
     * Trigger recreation of shift instances from effective date
     * @param empId Employee ID
     * @param effectiveDate Effective date from which to recreate instances
     * @param updatedBy User who made the change
     */
    private void triggerShiftInstanceRecreation(Integer empId, LocalDate effectiveDate, String updatedBy) {
        try {
            log.info("Triggering shift instance recreation for employee {} from effective date {}", empId, effectiveDate);
            employeeShiftInstanceRecreationService.recreateShiftInstancesFromEffectiveDate(empId, effectiveDate, updatedBy);

        } catch (Exception e) {
            log.error("Error triggering shift instance recreation for employee {} from effective date {}",
                     empId, effectiveDate, e);
            // Don't throw exception as this is a side effect
        }
    }

    /**
     * Build shift data DTO from shift and date
     * For universal shifts, use expected arrival time logic
     * @param shift Shift
     * @param date Business date
     * @param empId Employee ID (needed for universal shift unit ID logic)
     * @return Shift data DTO
     */
    private EmployeeShiftDataResponseDTO.EmployeeShiftDTO buildShiftDataDTO(Shift shift, LocalDate date, Integer empId) {
        LocalDateTime shiftStartDateTime;
        LocalDateTime shiftEndDateTime;
        Integer unitId = null;
        String unitName = null;

        // Check if this is a universal shift and use appropriate calculation
        if (shiftHelper.isUniversalShift(shift)) {
            // For universal shifts, we need employee ID to get expected arrival time
            // Since we don't have empId here, we'll use the regular shift times
            // The actual universal shift logic will be applied in the shift instance creation
            shiftStartDateTime = date.atTime(shift.getStartTime().toLocalTime());
            shiftEndDateTime = date.atTime(shift.getEndTime().toLocalTime());

            // For universal shift fallback, get employee's current unit assignment (latest unit mapped with attendance eligibility)
            unitId = unitResolutionService.getUnitIdForEmployee(empId, date);
            if (unitId == null) {
                log.warn("Unit ID not found for employee {} on date {}, using null unit ID for universal shift", empId, date);
            } else {
                log.debug("Using current unit assignment {} for universal shift fallback for employee {}", unitId, empId);
                // Get unit name from unit cache
                UnitBasicDetail unit = unitCacheService.getUnitBasicDetail(unitId);
                unitName = unit != null ? unit.getName() : null;
            }
        } else {
            shiftStartDateTime = date.atTime(shift.getStartTime().toLocalTime());
            shiftEndDateTime = date.atTime(shift.getEndTime().toLocalTime());

            // For regular shifts, get unit ID from employee's current unit assignment
            unitId = unitResolutionService.getUnitIdForEmployee(empId, date);
            if (unitId == null) {
                log.warn("Unit ID not found for employee {} on date {}, using null unit ID", empId, date);
            } else {
                // Get unit name from unit cache
                UnitBasicDetail unit = unitCacheService.getUnitBasicDetail(unitId);
                unitName = unit != null ? unit.getName() : null;
            }
        }

        return EmployeeShiftDataResponseDTO.EmployeeShiftDTO.builder()
                .shiftId(shift.getShiftId())
                .shiftName(shift.getShiftName())
                .startTime(shiftStartDateTime)
                .endTime(shiftEndDateTime)
                .date(shiftStartDateTime)
                .unitId(unitId)
                .unitName(unitName)
                .build();
    }



    @Override
    @Transactional(readOnly = true)
    public HierarchyEmployeesResponseDTO getHierarchyEmployeesWithFilters(Integer userId, GenericFilterRequestDTO filterRequest,
                                                                          String searchTerm, int page, int size) {
        log.info("Getting hierarchy employees with filters for userId: {}, searchTerm: {}, page: {}, size: {}, filters: {}",
                userId, searchTerm, page, size, filterRequest);

        try {
            Map<Integer, Unit> unitMap = unitCacheService.getAllUnitCache();
            // Get managed units for the user
            List<Integer> managedUnitIds = metadataService.getManagedUnitIds(userId);
            log.info("Found {} managed units for userId {}", managedUnitIds.size(), userId);

            if (managedUnitIds.isEmpty()) {
                log.info("No managed units found for userId {}", userId);
                return buildEmptyHierarchyEmployeesResponse(page, size);
            }

            // Get current business date
            LocalDate currentDate = LocalDate.now();

            // Get employees who are actually assigned to shifts in managed units
            Set<Integer> unitEmployeeIds = unitResolutionService.getEmployeesForUnits(managedUnitIds, currentDate);
            log.info("Found {} employees assigned to shifts in {} managed units for userId {}",
                    unitEmployeeIds.size(), managedUnitIds.size(), userId);

            if (unitEmployeeIds.isEmpty()) {
                log.info("No employees found assigned to shifts in managed units for userId {}", userId);
                return buildEmptyHierarchyEmployeesResponse(page, size);
            }
            Map<Integer, EmployeeBasicDetail> employeeMap = userCacheService.getAllUserCache();
            // Convert employee IDs to EmployeeMetadataDTO objects
            List<EmployeeMetadataDTO> allHierarchyEmployees = unitEmployeeIds.stream()
                    .map(empId -> {
                        EmployeeBasicDetail employee = employeeMap.get(empId);
                        if (employee != null) {
                            return mapToEmployeeMetadataDTO(employee);
                        }
                        return null;
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            log.info("Converted {} employee IDs to EmployeeMetadataDTO objects", allHierarchyEmployees.size());

            // Apply filters to employees
            List<EmployeeMetadataDTO> filteredEmployees = applyEmployeeFilters(allHierarchyEmployees, filterRequest);
            log.info("After filtering, found {} employees out of {} total employees",
                    filteredEmployees.size(), allHierarchyEmployees.size());


            // Apply search and pagination using the helper method
            Page<EmployeeMetadataDTO> employeePage = metadataService.applySearchAndPagination(filteredEmployees, searchTerm, page, size);

            // Set unitId and unitName for each employee based on their actual assignment
            for (EmployeeMetadataDTO employee : employeePage.getContent()) {
                Integer employeeUnitId = unitResolutionService.getUnitIdForEmployee(employee.getEmpId(), currentDate);
                employee.setUnitId(employeeUnitId);

                if (employeeUnitId != null) {
                    Unit unit = unitMap.get(employeeUnitId);
                    employee.setUnitName(unit != null ? unit.getName() : null);
                }
            }

            // Build filter metadata
            GenericFilterMetadataDTO filterMetadata = buildEmployeeFilterMetadata(allHierarchyEmployees, filterRequest,managedUnitIds);

            log.info("Returning {} employees with filter metadata", employeePage.getContent().size());
            return HierarchyEmployeesResponseDTO.builder()
                .employees(employeePage)
                .filterMetadata(filterMetadata)
                .build();

        } catch (Exception e) {
            log.error("Error getting hierarchy employees with filters for userId: {}", userId, e);
            throw new BusinessException("Failed to get hierarchy employees with filters", e);
        }
    }

    // Helper method to convert EmployeeBasicDetail to EmployeeMetadataDTO
    private EmployeeMetadataDTO mapToEmployeeMetadataDTO(EmployeeBasicDetail employee) {
        return EmployeeMetadataDTO.builder()
            .empId(employee.getId())
            .empCode(employee.getEmployeeCode())
            .empName(employee.getName())
            .designation(employee.getDesignation())
            .companyId(employee.getCompanyId())
            .status(employee.getStatus().name())
                .registrationImageUrl(employee.getImagekey())
            .build();
    }

    /**
     * Apply filters to employee list
     * @param employees List of employees to filter
     * @param filterRequest Filter request
     * @return Filtered list of employees
     */
    private List<EmployeeMetadataDTO> applyEmployeeFilters(List<EmployeeMetadataDTO> employees, GenericFilterRequestDTO filterRequest) {
        if (filterRequest == null || filterRequest.getFilters() == null || filterRequest.getFilters().isEmpty()) {
            // No filters applied, return all employees
            return employees;
        }

        // Pre-fetch all employee eligible units in batch to avoid multiple queries
        Map<String, List<UnitEligibilityDTO>> employeeEligibleUnitsMap;
        boolean needsUnitData = filterRequest.getFilters().containsKey(RosteringConstants.FILTER_KEY_CITY_NAMES) ||
                               filterRequest.getFilters().containsKey(RosteringConstants.FILTER_KEY_REGIONS) ||
                               filterRequest.getFilters().containsKey(RosteringConstants.FILTER_KEY_UNIT_IDS);
        
        if (needsUnitData) {
            List<String> empIds = employees.stream()
                .map(emp -> String.valueOf(emp.getEmpId()))
                .collect(Collectors.toList());
            employeeEligibleUnitsMap = metadataService.getEmployeeAttendanceEligibleUnitsBatch(empIds);
        } else {
            employeeEligibleUnitsMap = new HashMap<>();
        }

        return employees.stream()
            .filter(employee -> {
                boolean includeEmployee = true;
                // Apply designation filter if specified
                GenericFilterRequestDTO.FilterValue designationFilter = filterRequest.getFilters().get(RosteringConstants.FILTER_KEY_DESIGNATIONS);
                if (designationFilter != null && "IN".equalsIgnoreCase(designationFilter.getOperator())) {
                    if (employee.getDesignation() == null || !designationFilter.getValues().contains(employee.getDesignation())) {
                        includeEmployee = false;
                    }
                }

                // Apply city filter if specified
                if (includeEmployee) {
                    GenericFilterRequestDTO.FilterValue cityFilter = filterRequest.getFilters().get(RosteringConstants.FILTER_KEY_CITY_NAMES);
                    if (cityFilter != null && "IN".equalsIgnoreCase(cityFilter.getOperator())) {
                        String employeeCity = getEmployeeCityFromBatch(employee.getEmpId(), employeeEligibleUnitsMap);
                        if (employeeCity == null || !cityFilter.getValues().contains(employeeCity)) {
                            includeEmployee = false;
                        }
                    }
                }

                // Apply region filter if specified
                if (includeEmployee) {
                    GenericFilterRequestDTO.FilterValue regionFilter = filterRequest.getFilters().get(RosteringConstants.FILTER_KEY_REGIONS);
                    if (regionFilter != null && "IN".equalsIgnoreCase(regionFilter.getOperator())) {
                        String employeeRegion = getEmployeeRegionFromBatch(employee.getEmpId(), employeeEligibleUnitsMap);
                        if (employeeRegion == null || !regionFilter.getValues().contains(employeeRegion)) {
                            includeEmployee = false;
                        }
                    }
                }

                // Apply unit filter if specified
                if (includeEmployee) {
                    GenericFilterRequestDTO.FilterValue unitFilter = filterRequest.getFilters().get(RosteringConstants.FILTER_KEY_UNIT_IDS);
                    if (unitFilter != null && "IN".equalsIgnoreCase(unitFilter.getOperator())) {
                        List<Integer> employeeUnits = getEmployeeUnitsFromBatch(employee.getEmpId(), employeeEligibleUnitsMap);
                        List<Integer> filterUnitIds = unitFilter.getValues().stream()
                            .map(Integer::parseInt)
                            .toList();

                        // Check if employee has any of the specified units
                        boolean hasMatchingUnit = employeeUnits.stream()
                            .anyMatch(filterUnitIds::contains);

                        if (!hasMatchingUnit) {
                            includeEmployee = false;
                        }
                    }
                }

                return includeEmployee;
            })
            .collect(Collectors.toList());
    }

    /**
     * Get city for an employee based on their unit assignment
     * @param empId Employee ID
     * @return City name or null if not found
     */
    private String getEmployeeCity(Integer empId) {
        try {
            // Get employee's attendance eligible units
            List<UnitEligibilityDTO> eligibleUnits = metadataService.getEmployeeAttendanceEligibleUnits(String.valueOf(empId));

            if (!eligibleUnits.isEmpty()) {
                // Get the first unit's city (assuming employee is assigned to one primary unit)
                Integer unitId = eligibleUnits.get(0).getUnitId();
                UnitBasicDetail unit = unitCacheService.getUnitBasicDetail(unitId);
                if (unit != null) {
                    return unit.getCity();
                }
            }

            return null;
        } catch (Exception e) {
            log.warn("Error getting city for employee {}: {}", empId, e.getMessage());
            return null;
        }
    }

    /**
     * Get region for an employee based on their unit assignment
     * @param empId Employee ID
     * @return Region name or null if not found
     */
    private String getEmployeeRegion(Integer empId) {
        try {
            // Get employee's attendance eligible units
            List<UnitEligibilityDTO> eligibleUnits = metadataService.getEmployeeAttendanceEligibleUnits(String.valueOf(empId));

            if (!eligibleUnits.isEmpty()) {
                // Get the first unit's region (assuming employee is assigned to one primary unit)
                Integer unitId = eligibleUnits.get(0).getUnitId();
                UnitBasicDetail unit = unitCacheService.getUnitBasicDetail(unitId);
                if (unit != null) {
                    return unit.getUnitZone();
                }
            }

            return null;
        } catch (Exception e) {
            log.warn("Error getting region for employee {}: {}", empId, e.getMessage());
            return null;
        }
    }

    /**
     * Get units for an employee based on their attendance eligibility
     * @param empId Employee ID
     * @return List of unit IDs or empty list if not found
     */
    private List<Integer> getEmployeeUnits(Integer empId) {
        try {
            // Get employee's attendance eligible units
            List<UnitEligibilityDTO> eligibleUnits = metadataService.getEmployeeAttendanceEligibleUnits(String.valueOf(empId));

            return eligibleUnits.stream()
                .map(UnitEligibilityDTO::getUnitId)
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.warn("Error getting units for employee {}: {}", empId, e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * Get city for an employee from batch data
     * @param empId Employee ID
     * @param employeeEligibleUnitsMap Batch data map
     * @return City name or null if not found
     */
    private String getEmployeeCityFromBatch(Integer empId, Map<String, List<UnitEligibilityDTO>> employeeEligibleUnitsMap) {
        try {
            List<UnitEligibilityDTO> eligibleUnits = employeeEligibleUnitsMap.get(String.valueOf(empId));
            if (eligibleUnits != null && !eligibleUnits.isEmpty()) {
                // Get the first unit's city (assuming employee is assigned to one primary unit)
                Integer unitId = eligibleUnits.get(0).getUnitId();
                UnitBasicDetail unit = unitCacheService.getUnitBasicDetail(unitId);
                if (unit != null) {
                    return unit.getCity();
                }
            }
            return null;
        } catch (Exception e) {
            log.warn("Error getting city for employee {} from batch: {}", empId, e.getMessage());
            return null;
        }
    }

    /**
     * Get region for an employee from batch data
     * @param empId Employee ID
     * @param employeeEligibleUnitsMap Batch data map
     * @return Region name or null if not found
     */
    private String getEmployeeRegionFromBatch(Integer empId, Map<String, List<UnitEligibilityDTO>> employeeEligibleUnitsMap) {
        try {
            List<UnitEligibilityDTO> eligibleUnits = employeeEligibleUnitsMap.get(String.valueOf(empId));
            if (eligibleUnits != null && !eligibleUnits.isEmpty()) {
                // Get the first unit's region (assuming employee is assigned to one primary unit)
                Integer unitId = eligibleUnits.get(0).getUnitId();
                UnitBasicDetail unit = unitCacheService.getUnitBasicDetail(unitId);
                if (unit != null) {
                    return unit.getRegion();
                }
            }
            return null;
        } catch (Exception e) {
            log.warn("Error getting region for employee {} from batch: {}", empId, e.getMessage());
            return null;
        }
    }

    /**
     * Get units for an employee from batch data
     * @param empId Employee ID
     * @param employeeEligibleUnitsMap Batch data map
     * @return List of unit IDs
     */
    private List<Integer> getEmployeeUnitsFromBatch(Integer empId, Map<String, List<UnitEligibilityDTO>> employeeEligibleUnitsMap) {
        try {
            List<UnitEligibilityDTO> eligibleUnits = employeeEligibleUnitsMap.get(String.valueOf(empId));
            if (eligibleUnits != null) {
                return eligibleUnits.stream()
                    .map(UnitEligibilityDTO::getUnitId)
                    .collect(Collectors.toList());
            }
            return new ArrayList<>();
        } catch (Exception e) {
            log.warn("Error getting units for employee {} from batch: {}", empId, e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * Build employee filter metadata with available filter options
     * @param employees List of employees (unfiltered)
     * @param filterRequest Filter request
     * @return Filter metadata DTO
     */
    private GenericFilterMetadataDTO buildEmployeeFilterMetadata(List<EmployeeMetadataDTO> employees, GenericFilterRequestDTO filterRequest
    ,List<Integer> managedUnitIds) {
        // Build available filters
        List<GenericFilterMetadataDTO.FilterOption> availableFilters = new ArrayList<>();

        // Designation filter
        List<String> designations = employees.stream()
            .map(EmployeeMetadataDTO::getDesignation)
            .filter(Objects::nonNull)
            .distinct()
            .sorted()
            .collect(Collectors.toList());

        availableFilters.add(buildFilterOptionFromEnum(
            RosteringFilterType.DESIGNATION,
            buildValueOptionsFromList(designations)
        ));
         StopWatch stopWatch = new StopWatch();
        // Pre-fetch all employee eligible units in batch ONCE to avoid multiple database calls
        stopWatch.start();
//        List<String> empIds = employees.stream()
//            .map(emp -> String.valueOf(emp.getEmpId()))
//            .collect(Collectors.toList());
        Map<Integer,UnitBasicDetail> unitMap = unitCacheService.getAllUnitBasicDetailMap();
        List<UnitEligibilityDTO> eligibilityDTOS = managedUnitIds.stream().filter(id -> unitMap.containsKey(id)).map(uid -> {
            UnitBasicDetail unitDetail = unitMap.get(uid);
            return UnitEligibilityDTO.builder()
                .unitId(uid)
                .unitName(unitDetail.getName())
                .unitCode(unitDetail.getReferenceName())
                .build();
        }).collect(Collectors.toList());
        //Map<String, List<UnitEligibilityDTO>> employeeEligibleUnitsMap = metadataService.getEmployeeAttendanceEligibleUnitsBatch(empIds,managedUnitIds);
        log.info("Time taken to fetch employee eligible units in batch: {} ms", stopWatch.getTime());
        stopWatch.stop();
        stopWatch.reset();
        // City filter - get cities from employee units using batch data
        stopWatch.start();
        List<String> cities = getAvailableCitiesForEmployeesFromBatch(employees, eligibilityDTOS,unitMap);
        availableFilters.add(buildFilterOptionFromEnum(
            RosteringFilterType.CITY,
            buildValueOptionsFromList(cities)
        ));
        log.info("Time taken to fetch cities for employees from batch: {} ms", stopWatch.getTime());
         stopWatch.stop();
         stopWatch.reset();
         stopWatch.start();
        // Region filter - get regions from employee units using batch data
        List<String> regions = getAvailableRegionsForEmployeesFromBatch(employees, eligibilityDTOS,unitMap);
        availableFilters.add(buildFilterOptionFromEnum(
            RosteringFilterType.REGION,
            buildValueOptionsFromList(regions)
        ));
        log.info("Time taken to fetch regions for employees from batch: {} ms", stopWatch.getTime());
        stopWatch.stop();
        stopWatch.reset();
        stopWatch.start();
        // Unit filter - get units from employee units with unit names using batch data
        List<FilterValueOptionDTO> unitValueOptions = getAvailableUnitsWithNamesForEmployeesFromBatch(employees, eligibilityDTOS);
        availableFilters.add(buildFilterOptionFromEnum(
            RosteringFilterType.UNIT,
            unitValueOptions
        ));
        log.info("Time taken to fetch units for employees from batch: {} ms", stopWatch.getTime());

        // Determine which filters were applied
        Map<String, Boolean> appliedFilters = new HashMap<>();
        boolean anyFilterApplied = false;

        if (filterRequest != null && filterRequest.getFilters() != null) {
            appliedFilters.put(RosteringConstants.FILTER_KEY_DESIGNATIONS, filterRequest.getFilters().containsKey(RosteringConstants.FILTER_KEY_DESIGNATIONS));
            appliedFilters.put(RosteringConstants.FILTER_KEY_CITY_NAMES, filterRequest.getFilters().containsKey(RosteringConstants.FILTER_KEY_CITY_NAMES));
            appliedFilters.put(RosteringConstants.FILTER_KEY_REGIONS, filterRequest.getFilters().containsKey(RosteringConstants.FILTER_KEY_REGIONS));
            appliedFilters.put(RosteringConstants.FILTER_KEY_UNIT_IDS, filterRequest.getFilters().containsKey(RosteringConstants.FILTER_KEY_UNIT_IDS));
            anyFilterApplied = filterRequest.getFilters().values().stream().anyMatch(Objects::nonNull);
        } else {
            appliedFilters.put(RosteringConstants.FILTER_KEY_DESIGNATIONS, false);
            appliedFilters.put(RosteringConstants.FILTER_KEY_CITY_NAMES, false);
            appliedFilters.put(RosteringConstants.FILTER_KEY_REGIONS, false);
            appliedFilters.put(RosteringConstants.FILTER_KEY_UNIT_IDS, false);
        }

        return GenericFilterMetadataDTO.builder()
            .availableFilters(availableFilters)
            .appliedFilters(GenericFilterMetadataDTO.FilterAppliedFlags.builder()
                .appliedFilters(appliedFilters)
                .anyFilterApplied(anyFilterApplied)
                .build())
            .build();
    }

    /**
     * Get available cities for employees using pre-fetched batch data
     * @param employees List of employees
     * @param employeeEligibleUnitsMap Pre-fetched batch data
     * @return List of available cities
     */
    private List<String> getAvailableCitiesForEmployeesFromBatch(List<EmployeeMetadataDTO> employees,
                                                                 List<UnitEligibilityDTO> employeeEligibleUnitsMap
    ,Map<Integer,UnitBasicDetail> unitMap) {
        Set<String> cities = new HashSet<>();
        employeeEligibleUnitsMap.stream().forEach(unitEligibilityDTO -> {
            cities.add(unitMap.get(unitEligibilityDTO.getUnitId()).getCity());
        });
        return cities.stream()
            .sorted()
            .collect(Collectors.toList());

    }

    /**
     * Get available regions for employees using pre-fetched batch data
     * @param employees List of employees
     * @param employeeEligibleUnitsMap Pre-fetched batch data
     * @return List of available regions
     */
    private List<String> getAvailableRegionsForEmployeesFromBatch(List<EmployeeMetadataDTO> employees,List<UnitEligibilityDTO> employeeEligibleUnitsMap
     , Map<Integer,UnitBasicDetail> unitMap) {
        Set<String> regions = new HashSet<>();
        employeeEligibleUnitsMap.stream().forEach(unitEligibilityDTO -> {
            regions.add(unitMap.get(unitEligibilityDTO.getUnitId()).getUnitZone());
        });


        return regions.stream()
            .sorted()
            .collect(Collectors.toList());
    }

    /**
     * Get available units with names for employees using pre-fetched batch data
     * @param employees List of employees
     * @param employeeEligibleUnitsMap Pre-fetched batch data
     * @return List of FilterValueOptionDTO with unit IDs and names
     */
    private List<FilterValueOptionDTO> getAvailableUnitsWithNamesForEmployeesFromBatch(List<EmployeeMetadataDTO> employees, List<UnitEligibilityDTO> employeeEligibleUnitsMap) {
        Set<Integer> unitIds = new HashSet<>();

//        for (EmployeeMetadataDTO employee : employees) {
//            List<Integer> empUnits = getEmployeeUnitsFromBatch(employee.getEmpId(), employeeEligibleUnitsMap);
//            if (!empUnits.isEmpty()) {
//                unitIds.addAll(empUnits);
//            }
//        }
//        employeeEligibleUnitsMap.stream().forEach(unitEligibilityDTO -> {
//            unitIds.add(unitEligibilityDTO.getUnitId());
//        });

        return employeeEligibleUnitsMap.stream()
            .map(unit -> {
                String unitName = unit.getUnitName();
                return FilterValueOptionDTO.builder()
                    .value(String.valueOf(unit.getUnitId()))
                    .displayName(unitName)
                    .build();
            }).sorted(Comparator.comparing(FilterValueOptionDTO::getDisplayName))
            .collect(Collectors.toList());
    }

    /**
     * Build empty hierarchy employees response
     * @param page Page number
     * @param size Page size
     * @return Empty response
     */
    private HierarchyEmployeesResponseDTO buildEmptyHierarchyEmployeesResponse(int page, int size) {
        Page<EmployeeMetadataDTO> emptyPage = new PageImpl<>(new ArrayList<>(), PageRequest.of(page, size), 0);
        return HierarchyEmployeesResponseDTO.builder()
            .employees(emptyPage)
            .filterMetadata(GenericFilterMetadataDTO.builder()
                .availableFilters(new ArrayList<>())
                .appliedFilters(GenericFilterMetadataDTO.FilterAppliedFlags.builder()
                    .appliedFilters(new HashMap<>())
                    .anyFilterApplied(false)
                    .build())
                .build())
            .build();
    }


}
