package com.stpl.tech.attendance.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShiftInstanceRecreationResponseDTO {
    private String operationId;
    private LocalDateTime processedAt;
    private Integer totalEmployees;
    private Integer totalInstancesCreated;
    private Integer totalInstancesDeleted;
    private Integer totalInstancesSkipped;
    private List<String> errors;
    private String status; // SUCCESS, PARTIAL_SUCCESS, FAILED
    private String message;
} 