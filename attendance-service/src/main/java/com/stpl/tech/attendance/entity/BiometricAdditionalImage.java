package com.stpl.tech.attendance.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Entity to store additional images for biometric registration
 * Linked to BiometricRegistration via biometricRegistrationId
 */
@Entity
@Table(name = "BIOMETRIC_ADDITIONAL_IMAGES")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BiometricAdditionalImage implements Serializable {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * Reference to the biometric registration
     */
    @Column(name = "biometric_registration_id", nullable = false)
    private Long biometricRegistrationId;
    
    /**
     * Type of the image (e.g., "left", "center", "right", "profile")
     */
    @Column(name = "image_type", nullable = false, length = 50)
    private String imageType;
    
    /**
     * Base64 encoded image data
     */
    @Column(name = "base64_image", columnDefinition = "TEXT", nullable = false)
    private String base64Image;
    
    /**
     * Optional metadata as JSON string
     */
    @Column(name = "metadata", columnDefinition = "TEXT")
    private String metadata;

}
