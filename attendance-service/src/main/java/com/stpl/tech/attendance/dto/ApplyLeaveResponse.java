package com.stpl.tech.attendance.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApplyLeaveResponse {
    
    /**
     * Success or failure message
     */
    private String message;
    
    /**
     * Whether the operation was successful
     */
    private boolean success;
    
    /**
     * List of created entries with date and ID
     */
    private List<CreatedEntry> createdEntries;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CreatedEntry {
        /**
         * Date of the entry
         */
        private LocalDateTime date;
        
        /**
         * Created entry ID
         */
        private Long entryId;

        private String comment;
    }
} 