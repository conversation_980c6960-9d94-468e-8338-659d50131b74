package com.stpl.tech.attendance.cache.service;

import com.hazelcast.map.IMap;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.domain.model.Designation;
import com.stpl.tech.master.domain.model.Employee;

public interface UserCacheService {
    /**
     * Retrieves all users from the Hazelcast cache
     * @return IMap containing all users
     * @throws DataNotFoundInHazelCastException if cache is not found
     */
    IMap<Integer, EmployeeBasicDetail> getAllUserCache();

    /**
     * Retrieves a specific user by their ID from the Hazelcast cache
     * @param userId the ID of the user to retrieve
     * @return User object
     * @throws DataNotFoundInHazelCastException if user is not found
     */
    EmployeeBasicDetail getUserById(Integer userId);

    IMap<Integer, Designation> getAllDesignations();

    void evictUserCache(Integer userId);

} 