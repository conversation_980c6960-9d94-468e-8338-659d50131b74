package com.stpl.tech.attendance.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmployeeShiftInstancesRangeResponseDTO {
    private Integer empId;
    private LocalDate startDate;
    private LocalDate endDate;
    private List<EmployeeShiftInstancesDTO> shiftInstances;
    private int totalInstances;
} 