package com.stpl.tech.attendance.service;

import com.stpl.tech.attendance.constants.AppConstants;

import java.util.Arrays;
import java.util.List;


public class ShiftInstanceTypeUtilityService {
    
    /**
     * Get list of types based on scenario
     * @param scenario The scenario name
     * @return List of type strings
     */
    public  static List<String> getTypesByScenario(String scenario) {
        return switch (scenario.toUpperCase()) {
            case "WORKING_DAYS" -> getWorkingDaysTypes();
            case "NON_WORKING_DAYS" -> getNonWorkingDaysTypes();
            case "LEAVE_DAYS" -> getLeaveDaysTypes();
            case "ALL_TYPES" -> getAllTypes();
            case "EXCLUDE_LEAVE_WEEKOFF_COMPOFF" -> getExcludeLeaveWeekOffCompOffTypes();
            default -> getAllTypes(); // Default to all types
        };
    }
    
    /**
     * Get types for regular working days (excludes leave, week off, comp off)
     */
    public static List<String> getWorkingDaysTypes() {
        return Arrays.asList("NORMAL", "OD", "WFH", "REGULARISATION");
    }
    
    /**
     * Get types for non-working days
     */
    public static List<String> getNonWorkingDaysTypes() {
        return Arrays.asList(AppConstants.WEEK_OFF, AppConstants.LEAVE,
                           AppConstants.COMP_OFF, AppConstants.LWP);
    }
    
    /**
     * Get types for leave-related days
     */
    public  static List<String> getLeaveDaysTypes() {
        return Arrays.asList(AppConstants.LEAVE, AppConstants.COMP_OFF, AppConstants.LWP);
    }
    
    /**
     * Get all available types
     */
    public static List<String> getAllTypes() {
        return Arrays.asList("NORMAL", AppConstants.WEEK_OFF, AppConstants.LEAVE,
                           AppConstants.COMP_OFF, AppConstants.LWP, AppConstants.OD,
                           AppConstants.WFH, AppConstants.REGULARISATION);
    }
    
    /**
     * Get types excluding leave, week off, and comp off (your first scenario)
     */
    public static List<String> getExcludeLeaveWeekOffCompOffTypes() {
        return Arrays.asList("NORMAL", AppConstants.OD, AppConstants.WFH,
                           AppConstants.REGULARISATION);
    }
    
    /**
     * Get types for a specific category
     */
    public static List<String> getTypesByCategory(String category) {
        return switch (category.toUpperCase()) {
            case "ATTENDANCE" -> Arrays.asList("NORMAL", AppConstants.OD, AppConstants.WFH,
                                             AppConstants.REGULARISATION);
            case "LEAVE" -> getLeaveDaysTypes();
            case "WEEK_OFF" -> Arrays.asList(AppConstants.WEEK_OFF);
            case "SPECIAL" -> Arrays.asList(AppConstants.COMP_OFF, AppConstants.LWP);
            default -> getAllTypes();
        };
    }
}
