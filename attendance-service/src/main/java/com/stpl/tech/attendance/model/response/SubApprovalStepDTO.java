package com.stpl.tech.attendance.model.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.stpl.tech.attendance.approval.entity.SubApprovalStep;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SubApprovalStepDTO {
    private Long id;
    private Long approverId;
    private String status;
    private LocalDateTime createdDate;
    private String remarks;

    public static SubApprovalStepDTO fromEntity(SubApprovalStep subStep) {
        SubApprovalStepDTO dto = new SubApprovalStepDTO();
        dto.setId(subStep.getId());
        dto.setApproverId(subStep.getApproverId());
        dto.setStatus(subStep.getStatus().name());
        dto.setCreatedDate(subStep.getCreatedDate());
        dto.setRemarks(subStep.getRemarks());
        return dto;
    }
} 