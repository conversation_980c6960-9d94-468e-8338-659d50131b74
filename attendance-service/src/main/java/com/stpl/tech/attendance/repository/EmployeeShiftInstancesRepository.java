package com.stpl.tech.attendance.repository;

import com.stpl.tech.attendance.entity.AttendanceStatus;
import com.stpl.tech.attendance.entity.EmployeeShiftInstances;
import com.stpl.tech.attendance.enums.EmployeeShiftScheduleStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface EmployeeShiftInstancesRepository extends JpaRepository<EmployeeShiftInstances, Long> {

    Optional<EmployeeShiftInstances> findByEmpIdAndBusinessDateAndInstanceStatus(Integer empId,
                                                                                 LocalDate businessDate , String status);
    
    boolean existsByEmpIdAndBusinessDateAndInstanceStatus(Integer empId, LocalDate businessDate , String status);
    
    /**
     * Find the latest inactive instance for employee and business date
     * This is used to get the most recent inactive instance when carrying forward actual data
     */
    @Query("SELECT esi FROM EmployeeShiftInstances esi " +
           "WHERE esi.empId = :empId AND esi.businessDate = :businessDate AND esi.instanceStatus = 'INACTIVE' " +
           "ORDER BY esi.updatedAt DESC, esi.createdAt DESC " +
           "LIMIT 1")
    Optional<EmployeeShiftInstances> findLatestInactiveInstanceByEmpIdAndBusinessDate(
        @Param("empId") Integer empId, 
        @Param("businessDate") LocalDate businessDate);

    /**
     * Find active shift instances for employee within date range
     */
    List<EmployeeShiftInstances> findByEmpIdAndBusinessDateBetweenAndInstanceStatus(
        Integer empId, LocalDate startDate, LocalDate endDate, String instanceStatus);

    List<EmployeeShiftInstances> findByBusinessDate(LocalDate businessDate);


    
    @Query("SELECT esi FROM EmployeeShiftInstances esi WHERE esi.empId IN :empIds " +
           "AND esi.businessDate BETWEEN :startDate AND :endDate AND esi.instanceStatus = 'ACTIVE' " +
           "ORDER BY esi.empId, esi.businessDate")
    List<EmployeeShiftInstances> findByEmpIdsAndDateRange(
        @Param("empIds") List<Integer> empIds, 
        @Param("startDate") LocalDate startDate, 
        @Param("endDate") LocalDate endDate);



    List<EmployeeShiftInstances> findByBusinessDateBetweenAndInstanceStatus(LocalDate startDate,
                                                                            LocalDate endDate , String instanceStatus);



    /**
     * Find all shift instances by unit ID and business date
     */
    List<EmployeeShiftInstances> findByUnitIdAndBusinessDateAndInstanceStatus(Integer unitId,
                                                                              LocalDate businessDate , String instanceStatus);

    /**
     * Find all shift instances by multiple unit IDs and business date
     */
    @Query("SELECT esi FROM EmployeeShiftInstances esi WHERE esi.unitId IN :unitIds " +
           "AND esi.businessDate = :businessDate AND esi.instanceStatus = 'ACTIVE' " +
           "ORDER BY esi.unitId, esi.empId")
    List<EmployeeShiftInstances> findByUnitIdsAndBusinessDate(
        @Param("unitIds") List<Integer> unitIds, 
        @Param("businessDate") LocalDate businessDate);


    /**
     * Find active shift instances for multiple units where current time is between expected start and end time
     */
    @Query("SELECT esi FROM EmployeeShiftInstances esi WHERE esi.unitId IN :unitIds " +
           "AND esi.businessDate = :businessDate " +
           "AND :currentTime BETWEEN esi.expectedStartTime AND esi.expectedEndTime AND esi.instanceStatus = 'ACTIVE' " +
           "ORDER BY esi.unitId, esi.empId")
    List<EmployeeShiftInstances> findActiveShiftInstancesByUnitIdsAndDate(
        @Param("unitIds") List<Integer> unitIds,
        @Param("businessDate") LocalDate businessDate,
        @Param("currentTime") LocalDateTime currentTime);





    /**
     * Find shift instances by shift IDs, unit IDs, and business date
     */
    @Query("SELECT esi FROM EmployeeShiftInstances esi WHERE esi.shiftId IN :shiftIds " +
           "AND esi.unitId IN :unitIds " +
           "AND esi.businessDate = :businessDate " +
           "AND esi.instanceStatus = :instanceStatus " +
           "ORDER BY esi.shiftId, esi.unitId, esi.empId")
    List<EmployeeShiftInstances> findByShiftIdsAndUnitIdsAndBusinessDateAndInstanceStatus(
        @Param("shiftIds") List<Integer> shiftIds,
        @Param("unitIds") List<Integer> unitIds,
        @Param("instanceStatus") String instanceStatus,
        @Param("businessDate") LocalDate businessDate);

   
} 