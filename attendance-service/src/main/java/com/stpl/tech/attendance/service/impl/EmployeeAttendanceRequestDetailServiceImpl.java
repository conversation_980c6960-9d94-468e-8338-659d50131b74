package com.stpl.tech.attendance.service.impl;

import com.stpl.tech.attendance.entity.EmployeeAttendanceRequestDetail;
import com.stpl.tech.attendance.repository.EmployeeAttendanceRequestDetailRepository;
import com.stpl.tech.attendance.service.EmployeeAttendanceRequestDetailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class EmployeeAttendanceRequestDetailServiceImpl implements EmployeeAttendanceRequestDetailService {
    
    private final EmployeeAttendanceRequestDetailRepository employeeAttendanceRequestDetailRepository;

    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = false, propagation = org.springframework.transaction.annotation.Propagation.REQUIRED)
    public void createLeaveDetailRecords(Long attendanceReqId,
                                         Map<LocalDateTime, String> leaveDates,
                                         String leaveType, String userId) {
        try {
            List<EmployeeAttendanceRequestDetail> detailRecords = new ArrayList<>();
            for (Map.Entry<LocalDateTime, String> entry : leaveDates.entrySet()) {
                LocalDateTime date = entry.getKey();
                String requestType = entry.getValue();
                
                EmployeeAttendanceRequestDetail detail = EmployeeAttendanceRequestDetail.builder()
                        .attendanceReqId(attendanceReqId)
                        .date(date)
                        .type(leaveType)
                        .requestType(requestType)
                        .build();
                detailRecords.add(detail);
            }
            // Save all detail records in batch
            List<EmployeeAttendanceRequestDetail> savedRecords = employeeAttendanceRequestDetailRepository.saveAll(detailRecords);
            
            log.info("Successfully created {} detail records for attendance request ID: {}", 
                    savedRecords.size(), attendanceReqId);

        } catch (Exception e) {
            log.error("Failed to create leave detail records for attendance request ID: {}", attendanceReqId, e);
            throw new RuntimeException("Failed to create leave detail records: " + e.getMessage(), e);
        }
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<EmployeeAttendanceRequestDetail> getDetailsByAttendanceRequestId(Long attendanceReqId) {
        return employeeAttendanceRequestDetailRepository.findByAttendanceReqIdOrderByDateAsc(attendanceReqId);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<EmployeeAttendanceRequestDetail> getLeaveDetailsForDeduction(Long attendanceReqId) {
        return employeeAttendanceRequestDetailRepository.findLeaveDetailsForDeduction(attendanceReqId);
    }
    
    @Override
    public BigDecimal calculateLeaveDeduction(List<EmployeeAttendanceRequestDetail> detailRecords) {
        BigDecimal totalDeduction = BigDecimal.ZERO;
        
        for (EmployeeAttendanceRequestDetail detail : detailRecords) {
            String requestType = detail.getRequestType();
            if (requestType != null) {
                switch (requestType.toUpperCase()) {
                    case "FULL_DAY":
                        totalDeduction = totalDeduction.add(BigDecimal.ONE);
                        break;
                    case "FIRST_HALF":
                    case "SECOND_HALF":
                        totalDeduction = totalDeduction.add(new BigDecimal("0.5"));
                        break;
                    default:
                        // Default to full day if requestType is not recognized
                        log.warn("Unknown requestType '{}' for detail record ID {}. Defaulting to full day.", 
                                requestType, detail.getId());
                        totalDeduction = totalDeduction.add(BigDecimal.ONE);
                        break;
                }
            } else {
                // Default to full day if requestType is null
                log.warn("requestType is null for detail record ID {}. Defaulting to full day.", detail.getId());
                totalDeduction = totalDeduction.add(BigDecimal.ONE);
            }
        }
        
        log.debug("Calculated total leave deduction: {} days from {} detail records", totalDeduction, detailRecords.size());
        return totalDeduction;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = false, propagation = org.springframework.transaction.annotation.Propagation.REQUIRED)
    public void deleteDetailsByAttendanceRequestId(Long attendanceReqId) {
        try {
            employeeAttendanceRequestDetailRepository.deleteByAttendanceReqId(attendanceReqId);
            log.info("Successfully deleted all detail records for attendance request ID: {}", attendanceReqId);
        } catch (Exception e) {
            log.error("Failed to delete detail records for attendance request ID: {}", attendanceReqId, e);
            throw new RuntimeException("Failed to delete detail records: " + e.getMessage(), e);
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = false, propagation = org.springframework.transaction.annotation.Propagation.REQUIRED)
    public EmployeeAttendanceRequestDetail updateDetail(EmployeeAttendanceRequestDetail detail) {
        try {
            EmployeeAttendanceRequestDetail updatedDetail = employeeAttendanceRequestDetailRepository.save(detail);
            log.info("Successfully updated detail record ID: {}", updatedDetail.getId());
            return updatedDetail;
        } catch (Exception e) {
            log.error("Failed to update detail record ID: {}", detail.getId(), e);
            throw new RuntimeException("Failed to update detail record: " + e.getMessage(), e);
        }
    }
}
