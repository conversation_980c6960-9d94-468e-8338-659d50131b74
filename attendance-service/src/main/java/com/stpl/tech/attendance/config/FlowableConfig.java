package com.stpl.tech.attendance.config;

import org.flowable.engine.ProcessEngine;
import org.flowable.engine.ProcessEngineConfiguration;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.ManagementService;
import org.flowable.spring.SpringProcessEngineConfiguration;
import org.flowable.common.engine.impl.history.HistoryLevel;
import org.flowable.spring.boot.EngineConfigurationConfigurer;
import org.flowable.idm.engine.IdmEngine;
import org.flowable.idm.engine.IdmEngineConfiguration;
import org.flowable.idm.spring.SpringIdmEngineConfiguration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.FileSystemResource;
import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;

import javax.sql.DataSource;
import java.io.IOException;
import java.io.InputStream;
import org.springframework.beans.factory.annotation.Value;

@Slf4j
@Configuration
public class FlowableConfig implements EngineConfigurationConfigurer<SpringProcessEngineConfiguration> {

    @Value("${flowable.async-executor-activate:true}")
    private boolean asyncExecutorActivate;

    @Autowired
    private ApplicationContext applicationContext;

    @Override
    public void configure(SpringProcessEngineConfiguration engineConfiguration) {
        engineConfiguration.setActivityFontName("Arial");
        engineConfiguration.setLabelFontName("Arial");
        engineConfiguration.setAnnotationFontName("Arial");
        engineConfiguration.setAsyncExecutorActivate(asyncExecutorActivate);
        engineConfiguration.setDatabaseSchemaUpdate(ProcessEngineConfiguration.DB_SCHEMA_UPDATE_FALSE);
        engineConfiguration.setHistoryLevel(HistoryLevel.AUDIT);
        
        // Configure async executor
        engineConfiguration.setAsyncExecutorDefaultAsyncJobAcquireWaitTime(10000);
        engineConfiguration.setAsyncExecutorDefaultTimerJobAcquireWaitTime(10000);
        engineConfiguration.setAsyncExecutorDefaultQueueSizeFullWaitTime(10000);
    }

    @Bean
    public TaskExecutor flowableTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("flowable-");
        executor.initialize();
        return executor;
    }

    @Bean
    public ProcessEngine processEngine(DataSource dataSource, PlatformTransactionManager transactionManager) {
        SpringProcessEngineConfiguration config = new SpringProcessEngineConfiguration();
        config.setDataSource(dataSource);
        config.setTransactionManager(transactionManager);
        config.setDatabaseSchemaUpdate(ProcessEngineConfiguration.DB_SCHEMA_UPDATE_FALSE);
        config.setAsyncExecutorActivate(true);
        config.setHistoryLevel(HistoryLevel.AUDIT);
        config.setApplicationContext(applicationContext);
        
        // Configure async executor
        config.setAsyncExecutorDefaultAsyncJobAcquireWaitTime(10000);
        config.setAsyncExecutorDefaultTimerJobAcquireWaitTime(10000);
        config.setAsyncExecutorDefaultQueueSizeFullWaitTime(10000);
        
        ProcessEngine processEngine = config.buildProcessEngine();
        
        // Manually deploy the process definition
        try {
            RepositoryService repositoryService = processEngine.getRepositoryService();
            
            // First, check if the process is already deployed
            long count = repositoryService.createProcessDefinitionQuery()
                .processDefinitionKey("attendance-approval")
                .count();
                
            if (count == 0) {
                // Deploy the process if not already deployed
                repositoryService.createDeployment()
                    .addClasspathResource("processes/attendance-approval.bpmn20.xml")
                    .name("Attendance Approval Process")
                    .deploy();
                log.info("Deployed attendance-approval process definition");
            }
        } catch (Exception e) {
            log.error("Error deploying process definition", e);
        }
        
        return processEngine;
    }

    @Bean
    public IdmEngine idmEngine(DataSource dataSource, PlatformTransactionManager transactionManager) {
        SpringIdmEngineConfiguration config = new SpringIdmEngineConfiguration();
        config.setDataSource(dataSource);
        config.setTransactionManager(transactionManager);
        config.setDatabaseSchemaUpdate(IdmEngineConfiguration.DB_SCHEMA_UPDATE_FALSE);
        return config.buildIdmEngine();
    }

    @Bean
    public RuntimeService runtimeService(ProcessEngine processEngine) {
        return processEngine.getRuntimeService();
    }

    @Bean
    public TaskService taskService(ProcessEngine processEngine) {
        return processEngine.getTaskService();
    }

    @Bean
    public HistoryService historyService(ProcessEngine processEngine) {
        return processEngine.getHistoryService();
    }

    @Bean
    public RepositoryService repositoryService(ProcessEngine processEngine) {
        return processEngine.getRepositoryService();
    }

    @Bean
    public ManagementService managementService(ProcessEngine processEngine) {
        return processEngine.getManagementService();
    }

    @Bean
    public RepositoryService deployProcesses(RepositoryService repositoryService) throws IOException {
        // Get all BPMN files from the processes directory
        Resource[] resources = new PathMatchingResourcePatternResolver()
            .getResources("classpath:processes/*.bpmn20.xml");
            
        // Deploy each process
        for (Resource resource : resources) {
            String processName = resource.getFilename();
            log.info("Deploying process: {}", processName);
            repositoryService.createDeployment()
                .addInputStream(processName, resource.getInputStream())
                .name(processName)
                .deploy();
        }
        
        return repositoryService;
    }
} 