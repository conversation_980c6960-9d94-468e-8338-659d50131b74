package com.stpl.tech.attendance.cache.service;

import com.hazelcast.map.IMap;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;

import java.util.List;
import java.util.Map;

public interface UnitCacheService {
    /**
     * Retrieves all units from the Hazelcast cache
     * @return IMap containing all units
     * @throws DataNotFoundInHazelCastException if cache is not found
     */
    Map<Integer, Unit> getAllUnitCache();

    Map<Integer, UnitBasicDetail> getAllUnitBasicDetailMap();

    /**
     * Retrieves a specific unit by its ID from the Hazelcast cache
     * @param unitId the ID of the unit to retrieve
     * @return Unit object
     * @throws DataNotFoundInHazelCastException if unit is not found
     */
    Unit getUnitById(Integer unitId);

    /**
     * Get unit basic details by ID from cache
     * @param unitId The unit ID to fetch details for
     * @return UnitBasicDetail object if found, null otherwise
     */
    UnitBasicDetail getUnitBasicDetail(Integer unitId);

    List<UnitBasicDetail> getAllUnitBasicDetail();

    /**
     * Get all units that belong to a specific city
     * @param city The city name to search for
     * @return List of unit IDs that belong to the city
     */
    List<Integer> getUnitsByCity(String city);

    /**
     * Get all units that belong to a specific region/zone
     * @param region The region/zone name to search for
     * @return List of unit IDs that belong to the region
     */
    List<Integer> getUnitsByRegion(String region);

    void evictUnitsCache();

    void evictAllUnitBasicDetail();

    /**
     * Refreshes local maps from Hazelcast cache
     * This method should be called when the main cache is updated
     */
    void refreshLocalMaps();
} 