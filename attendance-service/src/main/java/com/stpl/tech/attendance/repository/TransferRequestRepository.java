package com.stpl.tech.attendance.repository;

import com.stpl.tech.attendance.model.TransferRequest;
import com.stpl.tech.attendance.model.TransferRequestStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface TransferRequestRepository extends JpaRepository<TransferRequest, String> {
    Page<TransferRequest> findByEmpId(String empId, Pageable pageable);
    Page<TransferRequest> findBySourceUnitIdOrDestinationUnitId(String sourceUnitId, String destinationUnitId, Pageable pageable);
    
    // Find pending transfer requests for an employee
    List<TransferRequest> findByEmpIdAndStatus(String empId, TransferRequestStatus status);
    
    // Find the latest transfer request for an employee
    @Query("SELECT tr FROM TransferRequest tr WHERE tr.empId = :empId ORDER BY tr.createdDate DESC")
    List<TransferRequest> findLatestTransferByEmpId(@Param("empId") String empId, Pageable pageable);
    
    // Find the most recent transfer request for an employee (returns only one result)
    @Query("SELECT tr FROM TransferRequest tr WHERE tr.empId = :empId ORDER BY tr.createdDate DESC LIMIT 1")
    Optional<TransferRequest> findMostRecentTransferByEmpId(@Param("empId") String empId);
} 