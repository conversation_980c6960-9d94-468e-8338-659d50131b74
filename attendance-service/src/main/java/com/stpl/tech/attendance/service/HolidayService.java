package com.stpl.tech.attendance.service;

import com.stpl.tech.attendance.dto.HolidayResponse;
import com.stpl.tech.attendance.entity.EmpHoliday;

import java.time.LocalDate;
import java.util.List;

/**
 * Service interface for holiday management operations
 */
public interface HolidayService {
    
    /**
     * Get all holidays for a specific financial year
     * @param financialYear Financial year (e.g., "2024-25")
     * @return List of holidays for the financial year
     */
    List<HolidayResponse> getHolidaysByFinancialYear(String financialYear);
    
    /**
     * Get holidays for a specific date range
     * @param startDate Start date
     * @param endDate End date
     * @return List of holidays in the date range
     */
    List<HolidayResponse> getHolidaysByDateRange(LocalDate startDate, LocalDate endDate);
    
    /**
     * Get holidays for the current financial year
     * @return List of holidays for current financial year
     */
    List<HolidayResponse> getCurrentFinancialYearHolidays();
    
    /**
     * Check if a specific date is a holiday
     * @param date Date to check
     * @return true if the date is a holiday
     */
    boolean isHoliday(LocalDate date);
    
    /**
     * Get holiday information for a specific date
     * @param date Date to check
     * @return Holiday response or null if not a holiday
     */
    HolidayResponse getHolidayByDate(LocalDate date);
    
    /**
     * Create or update a holiday
     * @param holiday Holiday entity to save
     * @return Saved holiday entity
     */
    EmpHoliday saveHoliday(EmpHoliday holiday);
    
    /**
     * Delete a holiday by ID
     * @param holidayId Holiday ID to delete
     */
    void deleteHoliday(Long holidayId);
    
    /**
     * Get all available financial years
     * @return List of distinct financial years
     */
    List<String> getAllFinancialYears();
    
    /**
     * Get holidays by name pattern
     * @param namePattern Holiday name pattern
     * @return List of holidays matching the pattern
     */
    List<HolidayResponse> getHolidaysByNamePattern(String namePattern);
    
    /**
     * Refresh cache for a specific financial year
     * @param financialYear Financial year to refresh
     */
    void refreshFinancialYearCache(String financialYear);
    
    /**
     * Evict all holiday cache entries
     */
    void evictAllCache();
    
    /**
     * Evict cache for a specific financial year
     * @param financialYear Financial year
     */
    void evictFinancialYearCache(String financialYear);
}
