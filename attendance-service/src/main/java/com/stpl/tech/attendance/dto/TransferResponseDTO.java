package com.stpl.tech.attendance.dto;

import com.stpl.tech.attendance.model.TransferRequestStatus;
import com.stpl.tech.attendance.model.TransferType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransferResponseDTO {
    private String transferRequestId;
    private String empId;
    private String sourceUnitId;
    private String destinationUnitId;
    private TransferType transferType;
    private LocalDate startDate;
    private LocalDate endDate;
    private String reason;
    private String comment;
    private TransferRequestStatus status;
    private String approvalRequestId;
    private LocalDateTime createdDate;
    private String createdBy;
} 