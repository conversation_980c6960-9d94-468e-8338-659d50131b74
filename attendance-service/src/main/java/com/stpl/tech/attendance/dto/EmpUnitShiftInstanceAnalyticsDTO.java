package com.stpl.tech.attendance.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmpUnitShiftInstanceAnalyticsDTO {
    
    private Integer managerId;
    private DateAndTimeRange dateAndTimeRange;
    private AttendanceSummary summary;
    private List<EmployeeDetail> idealEmployees;
    private List<EmployeeDetail> actualEmployees;
    private List<EmployeeDetail> absentEmployees;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DateAndTimeRange {
        private LocalDate date;
        private LocalTime startTime;
        private LocalTime endTime;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AttendanceSummary {
        private Long totalIdealEmployees;
        private Long totalActualEmployees;
        private Double attendanceRate;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EmployeeDetail {
        private Integer empId;
        private String empName;
        private String empCode;
        private String designation;
        private String imageUrl;
        private Integer unitId;
        private String unitName;
        private String expectedStartTime;
        private String expectedEndTime;
        private String actualStartTime;
        private String actualEndTime;
        private Integer actualUnitId;
        private String actualUnitName;
    }
} 