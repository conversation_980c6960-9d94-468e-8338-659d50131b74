package com.stpl.tech.attendance.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UnitAttendanceAnalyticsDTO implements Serializable {
    private Integer unitId;
    private String unitName;
    private String unitCode;
    private LocalDate businessDate;
    private Integer idealEmployeeCount;
    private Integer actualEmployeeCount;
    private Integer onTimeEmployeeCount;
    private Integer onLeaveEmployeeCount; // TODO: Implement leave logic later
    private BigDecimal totalIdealWorkingHours;
    private BigDecimal totalActualWorkingHours;
    private BigDecimal attendancePercentage;
    private BigDecimal onTimePercentage;
    private BigDecimal workingHoursEfficiency;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ManagerAnalyticsResponseDTO implements Serializable{
        private Integer managerId;
        private String managerName;
        private LocalDate businessDate;
        private List<UnitAttendanceAnalyticsDTO> unitAnalytics;
        private UnitAttendanceAnalyticsDTO totalAnalytics;
    }
} 