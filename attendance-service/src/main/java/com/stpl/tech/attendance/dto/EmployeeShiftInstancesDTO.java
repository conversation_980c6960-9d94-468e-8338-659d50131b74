package com.stpl.tech.attendance.dto;

import com.stpl.tech.attendance.entity.AttendanceStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmployeeShiftInstancesDTO {
    private Long id;
    private Integer empId;
    private String empName;
    private String empCode;
    private LocalDate businessDate;
    private Integer shiftId;
    private String shiftName;
    private Integer unitId;
    private String unitName;
    private LocalDateTime expectedStartTime;
    private LocalDateTime expectedEndTime;
    private BigDecimal idealHours;
    private LocalDateTime actualStartTime;
    private LocalDateTime actualEndTime;
    private BigDecimal actualHours;
    private AttendanceStatus status;
    private String type;
    private Boolean isOverride;
    private String overrideReason;
    private String createdBy;
    private LocalDateTime createdAt;
    private String updatedBy;
    private LocalDateTime updatedAt;
} 