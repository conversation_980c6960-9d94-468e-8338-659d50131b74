package com.stpl.tech.attendance.cache.service.impl;

import com.google.common.base.Stopwatch;
import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import com.stpl.tech.attendance.cache.constants.CacheConstants;
import com.stpl.tech.attendance.cache.exception.DataNotFoundInHazelCastException;
import com.stpl.tech.attendance.cache.service.UserCacheService;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.domain.model.Designation;
import com.stpl.tech.master.domain.model.Employee;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserCacheServiceImpl implements UserCacheService {

    private final HazelcastInstance instance;

    @Override
    public IMap<Integer, EmployeeBasicDetail> getAllUserCache() {
        log.info("Fetching all users detail");
        Stopwatch watch = Stopwatch.createUnstarted();
        watch.start();
        IMap<Integer, EmployeeBasicDetail> allUserDataMap = instance.getMap(CacheConstants.EMPLOYEES_CACHE);
        if (Objects.isNull(allUserDataMap)) {
            throw new DataNotFoundInHazelCastException("User details not found");
        }
        log.info("Loaded User Cache in {} ms", watch.stop().elapsed(TimeUnit.MILLISECONDS));
        return allUserDataMap;
    }

    @Override
    @Cacheable(value = "userCache", key = "#userId")
    public EmployeeBasicDetail getUserById(Integer userId) {
        log.info("Fetching user basic details for user id {}", userId);
        IMap<Integer, EmployeeBasicDetail> userMap = getAllUserCache();
        Stopwatch watch = Stopwatch.createUnstarted();
        watch.start();
        EmployeeBasicDetail userData = userMap.get(userId);
        log.info("Loaded User Cache For User Id : {} in {} ms", userId, watch.stop().elapsed(TimeUnit.MILLISECONDS));

        if (Objects.isNull(userData)) {
            throw new DataNotFoundInHazelCastException("Data not found for user id " + userId);
        }
        return userData;
    }

    @Override
    @CacheEvict(value = "userCache", key = "#userId")
    public void evictUserCache(Integer userId) {
        log.info("Evicting user cache for user id {}", userId);
    }

    @Override
    public IMap<Integer, Designation> getAllDesignations() {
        log.info("Fetching All Designation Details");
        Stopwatch watch = Stopwatch.createUnstarted();
        watch.start();
        IMap<Integer, Designation> designationMap = instance.getMap(CacheConstants.DESIGNATIONS_CACHE);
        if (Objects.isNull(designationMap)) {
            throw new DataNotFoundInHazelCastException("Designations Not Found");
        }
        log.info("Loaded Designations Cache in {} ms", watch.stop().elapsed(TimeUnit.MILLISECONDS));
        return designationMap;
    }

} 