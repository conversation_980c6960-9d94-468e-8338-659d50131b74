package com.stpl.tech.attendance.controller;

import com.stpl.tech.attendance.constants.ApiConstants;
import com.stpl.tech.attendance.dto.AttendanceConfigDTO;
import com.stpl.tech.attendance.dto.DevicePairingRequest;
import com.stpl.tech.attendance.dto.DevicePairingResponse;
import com.stpl.tech.attendance.dto.EmployeeDetailsDTO;
import com.stpl.tech.attendance.dto.EmployeeMetadataDTO;
import com.stpl.tech.attendance.dto.UnitEligibilityDTO;
import com.stpl.tech.attendance.model.response.ApiResponse;
import com.stpl.tech.attendance.service.MetadataService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping(ApiConstants.Paths.METADATA)
@RequiredArgsConstructor
@Tag(name = "Metadata", description = "Metadata management APIs")
public class MetadataController extends BaseController {

    private final MetadataService metadataService;

    @GetMapping("/employees")
    @Operation(summary = "Get employee metadata with pagination and search")
    public ResponseEntity<ApiResponse<List<EmployeeMetadataDTO>>> getEmployeeMetadata(
            @RequestParam(required = false) String searchTerm,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        Page<EmployeeMetadataDTO> pageResult = metadataService.getEmployeeMetadata(searchTerm, page, size);
        List<EmployeeMetadataDTO> responses = pageResult.getContent().stream()
                .collect(Collectors.toList());
        return success(responses, pageResult);
    }

    @GetMapping("/employees/with-biometric")
    @Operation(summary = "Get employees with registered biometrics")
    public ResponseEntity<ApiResponse<List<EmployeeMetadataDTO>>> getEmployeesWithBiometric(
            @RequestParam(required = false) String searchTerm,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        Page<EmployeeMetadataDTO> pageResult = metadataService.getEmployeesWithBiometric(searchTerm, page, size);
        List<EmployeeMetadataDTO> responses = pageResult.getContent().stream()
                .collect(Collectors.toList());
        return success(responses, pageResult);
    }

    @GetMapping("/employees/manager/{empId}")
    @Operation(summary = "Get employee metadata for a manager's team")
    public ResponseEntity<ApiResponse<List<EmployeeMetadataDTO>>> getManagerTeamMetadata(
            @PathVariable Integer empId,
            @RequestParam(required = false) String searchTerm,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        Page<EmployeeMetadataDTO> pageResult = metadataService.getManagerTeamMetadata(empId, searchTerm, page, size);
        List<EmployeeMetadataDTO> responses = pageResult.getContent().stream()
                .collect(Collectors.toList());
        return success(responses, pageResult);
    }

    @GetMapping("/employees/{empId}/attendance-eligible-units")
    @Operation(summary = "Get all units for which an employee has attendance eligibility")
    public ResponseEntity<ApiResponse<List<UnitEligibilityDTO>>> getEmployeeAttendanceEligibleUnits(
            @PathVariable String empId) {
        List<UnitEligibilityDTO> eligibleUnits = metadataService.getEmployeeAttendanceEligibleUnits(empId);
        return success(eligibleUnits);
    }

    @GetMapping("/employees/{empId}/details")
    @Operation(summary = "Get employee details including biometric status and approval eligibility")
    public ResponseEntity<ApiResponse<EmployeeDetailsDTO>> getEmployeeDetails(
            @PathVariable String empId) {
        EmployeeDetailsDTO employeeDetails = metadataService.getEmployeeDetails(empId);
        return success(employeeDetails);
    }

    @PostMapping("/devices/pair")
    @Operation(summary = "Pair a device with an employee")
    public ResponseEntity<ApiResponse<DevicePairingResponse>> pairDevice(
            @Valid @RequestBody DevicePairingRequest request) {
        DevicePairingResponse response = metadataService.pairDevice(request);
        return success(response);
    }

    @GetMapping("/devices/{deviceId}/validate-pairing")
    @Operation(summary = "Validate if a device is paired with a unit")
    public ResponseEntity<ApiResponse<Boolean>> validateDevicePairing(
            @PathVariable String deviceId,
            @RequestParam Long unitId) {
        boolean isPaired = metadataService.isDevicePairedWithUnit(deviceId, unitId);
        return success(isPaired);
    }

    @GetMapping("/attendance-config")
    @Operation(summary = "Get attendance configuration properties for UI")
    public ResponseEntity<ApiResponse<AttendanceConfigDTO>> getAttendanceConfig() {
        AttendanceConfigDTO config = metadataService.getAttendanceConfig();
        return success(config);
    }

}