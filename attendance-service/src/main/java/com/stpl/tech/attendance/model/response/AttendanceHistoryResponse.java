package com.stpl.tech.attendance.model.response;

import com.stpl.tech.attendance.dto.EmployeeInfoDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AttendanceHistoryResponse {
    private Long requestId;
    private String requestType;
    private String status;
    private LocalDateTime requestDate;
    private LocalDateTime lastUpdatedDate;
    private EmployeeInfoDTO employeeInfo;
    private EmployeeInfoDTO createdBy;
    private EmployeeInfoDTO updatedBy;
    private String reason;
    private String comments;
    private String documents;
    private String fromDate;
    private String toDate;
    private String approvers;
}
