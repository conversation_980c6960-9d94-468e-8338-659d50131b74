package com.stpl.tech.attendance.domain;

import java.sql.Timestamp;

/**
 * Concrete implementation of ShiftCoveragePlan for Reladomo with unitemporal data
 * This class extends the generated abstract class and provides the concrete implementation
 */
public class ShiftCoveragePlan extends ShiftCoveragePlanAbstract {


    public ShiftCoveragePlan(Timestamp processingDate) {
        super(processingDate);
    }

    public void setShiftCafeMappingId(Integer shiftCafeMappingId) {
        super.setShiftCafeMappingId(shiftCafeMappingId);
    }

    public void setDay(String day) {
        super.setDay(day);
    }

    public void setIdealCount(Integer idealCount) {
        super.setIdealCount(idealCount);
    }

    public void setStatus(String status) {
        super.setStatus(status);
    }

    public void setCreatedBy(String createdBy) {
        super.setCreatedBy(createdBy);
    }

    public void setCreationTime(Timestamp creationTime) {
        super.setCreationTime(creationTime);
    }

    public void setUpdatedBy(String updatedBy) {
        super.setUpdatedBy(updatedBy);
    }

    public void setUpdationTime(Timestamp updationTime) {
        super.setUpdationTime(updationTime);
    }

    public void setId(Integer id) {
        super.setId(id);
    }


}