package com.stpl.tech.attendance.controller;

import com.stpl.tech.attendance.constants.ApiConstants;
import com.stpl.tech.attendance.dto.EmployeeTransferMetadataDTO;
import com.stpl.tech.attendance.dto.TransferCancellationDTO;
import com.stpl.tech.attendance.dto.TransferRequestDTO;
import com.stpl.tech.attendance.dto.TransferResponseDTO;
import com.stpl.tech.attendance.model.TransferHistory;
import com.stpl.tech.attendance.model.TransferRequest;
import com.stpl.tech.attendance.model.TransferStatistics;
import com.stpl.tech.attendance.model.response.ApiResponse;
import com.stpl.tech.attendance.service.TransferService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping(ApiConstants.Paths.TRANSFER)
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Transfer Management", description = "APIs for managing employee transfers")
public class TransferController extends BaseController {
    private final TransferService transferService;

    @PostMapping
    @Operation(summary = "Create a new transfer request")
    public ResponseEntity<ApiResponse<TransferResponseDTO>> createTransfer(@Valid @RequestBody TransferRequestDTO request) {
        log.info("Creating transfer request for employee: {}", request.getEmpId());
        TransferRequest transfer = transferService.createTransfer(request);
        log.info("Transfer request created successfully: {}", transfer);
        return success(mapToResponseDTO(transfer));
    }

    @GetMapping("/{transferId}")
    @Operation(summary = "Get transfer request by ID")
    public ResponseEntity<ApiResponse<TransferResponseDTO>> getTransfer(@PathVariable String transferId) {
        log.info("Getting transfer request: {}", transferId);
        TransferRequest transfer = transferService.getTransferRequest(transferId);
        log.info("Transfer request retrieved successfully: {}", transfer);
        return success(mapToResponseDTO(transfer));
    }

    @GetMapping("/employee/{empId}/metadata")
    @Operation(summary = "Get employee transfer metadata including attendance eligible units and transfer reasons")
    public ResponseEntity<ApiResponse<EmployeeTransferMetadataDTO>> getEmployeeTransferMetadata(
            @PathVariable String empId) {
        log.info("Getting transfer metadata for employee: {}", empId);
        EmployeeTransferMetadataDTO metadata = transferService.getEmployeeTransferMetadata(empId);
        log.info("Transfer metadata retrieved successfully for employee: {}", empId);
        return success(metadata);
    }

//    @PostMapping("/{transferId}/approve")
//    @Operation(summary = "Approve a transfer request")
//    public ResponseEntity<TransferResponseDTO> approveTransfer(@PathVariable String transferId) {
//        log.info("Approving transfer request: {}", transferId);
//        TransferRequest transfer = transferService.approveTransfer(transferId);
//        log.info("Transfer request approved successfully: {}", transfer);
//        return success(mapToResponseDTO(transfer));
//    }
//
//    @PostMapping("/{transferId}/reject")
//    @Operation(summary = "Reject a transfer request")
//    public ResponseEntity<ResponseDTO<TransferResponseDTO>> rejectTransfer(@PathVariable String transferId) {
//        log.info("Rejecting transfer request: {}", transferId);
//        TransferRequest transfer = transferService.rejectTransfer(transferId);
//        log.info("Transfer request rejected successfully: {}", transfer);
//        return success(mapToResponseDTO(transfer), "Transfer request rejected successfully");
//    }

    @PostMapping("/{transferId}/cancel")
    @Operation(summary = "Cancel a transfer request")
    public ResponseEntity<ApiResponse<TransferResponseDTO>> cancelTransfer(
            @PathVariable String transferId,
            @Valid @RequestBody TransferCancellationDTO request) {
        log.info("Cancelling transfer request: {}", transferId);
        TransferRequest transfer = transferService.cancelTransfer(transferId, request);
        return success(mapToResponseDTO(transfer));
    }

//    @GetMapping("/employee/{empId}")
//    @Operation(summary = "Get all transfers for an employee")
//    public ResponseEntity<ApiResponse<List<TransferResponseDTO>>> getEmployeeTransfers(
//            @PathVariable String empId,
//            Pageable pageable) {
//        log.info("Getting transfers for employee: {}", empId);
//        Page<TransferRequest> transfers = transferService.getEmployeeTransfers(empId, pageable);
//        return ResponseEntity.ok(transfers.map(this::mapToResponseDTO));
//    }
//
//    @GetMapping("/unit/{unitId}")
//    @Operation(summary = "Get all transfers for a unit")
//    public ResponseEntity<Page<TransferResponseDTO>> getUnitTransfers(
//            @PathVariable String unitId,
//            Pageable pageable) {
//        log.info("Getting transfers for unit: {}", unitId);
//        Page<TransferRequest> transfers = transferService.getUnitTransfers(unitId, pageable);
//        return ResponseEntity.ok(transfers.map(this::mapToResponseDTO));
//    }
//
//    @GetMapping("/{transferId}/history")
//    @Operation(summary = "Get transfer history")
//    public ResponseEntity<List<TransferHistory>> getTransferHistory(@PathVariable String transferId) {
//        log.info("Getting transfer history for request: {}", transferId);
//        List<TransferHistory> history = transferService.getTransferHistory(transferId);
//        return ResponseEntity.ok(history);
//    }
//
//    @GetMapping("/unit/{unitId}/statistics")
//    @Operation(summary = "Get transfer statistics for a unit")
//    public ResponseEntity<TransferStatistics> getUnitTransferStatistics(@PathVariable String unitId) {
//        log.info("Getting transfer statistics for unit: {}", unitId);
//        TransferStatistics statistics = transferService.getUnitTransferStatistics(unitId);
//        return ResponseEntity.ok(statistics);
//    }

    private TransferResponseDTO mapToResponseDTO(TransferRequest transfer) {
        return TransferResponseDTO.builder()
                .transferRequestId(transfer.getTransferRequestId().toString())
                .empId(transfer.getEmpId())
                .sourceUnitId(transfer.getSourceUnitId())
                .destinationUnitId(transfer.getDestinationUnitId())
                .transferType(transfer.getTransferType())
                .startDate(transfer.getStartDate())
                .endDate(transfer.getEndDate())
                .reason(transfer.getReason())
                .comment(transfer.getComment())
                .status(transfer.getStatus())
                .approvalRequestId(transfer.getApprovalRequestId() != null ? transfer.getApprovalRequestId().toString() : null)
                .createdDate(transfer.getCreatedDate())
                .createdBy(transfer.getCreatedBy())
                .build();
    }
} 