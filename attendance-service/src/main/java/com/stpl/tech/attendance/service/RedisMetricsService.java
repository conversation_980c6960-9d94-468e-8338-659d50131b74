package com.stpl.tech.attendance.service;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class RedisMetricsService {
    
    private final MeterRegistry meterRegistry;
    private final Counter cacheHits;
    private final Counter cacheMisses;
    private final Timer cacheOperations;
    
    // Detailed cache operation metrics
    private final Counter cacheGetCounter;
    private final Counter cacheSetCounter;
    private final Counter cacheDeleteCounter;
    private final Counter cacheExpireCounter;
    private final Timer cacheGetTimer;
    private final Timer cacheSetTimer;
    private final Timer cacheDeleteTimer;
    private final Timer cacheExpireTimer;
    
    // Cache error metrics
    private final Counter cacheErrorCounter;
    private final Counter cacheTimeoutCounter;
    private final Counter cacheConnectionErrorCounter;
    
    public RedisMetricsService(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        
        // Initialize basic cache metrics
        this.cacheHits = Counter.builder("redis.cache.hits")
            .description("Number of cache hits")
            .register(meterRegistry);
            
        this.cacheMisses = Counter.builder("redis.cache.misses")
            .description("Number of cache misses")
            .register(meterRegistry);
            
        this.cacheOperations = Timer.builder("redis.cache.operations")
            .description("Cache operation timing")
            .register(meterRegistry);
            
        // Initialize detailed cache operation metrics
        this.cacheGetCounter = Counter.builder("redis.cache.get.total")
            .description("Number of cache get operations")
            .register(meterRegistry);
            
        this.cacheSetCounter = Counter.builder("redis.cache.set.total")
            .description("Number of cache set operations")
            .register(meterRegistry);
            
        this.cacheDeleteCounter = Counter.builder("redis.cache.delete.total")
            .description("Number of cache delete operations")
            .register(meterRegistry);
            
        this.cacheExpireCounter = Counter.builder("redis.cache.expire.total")
            .description("Number of cache expire operations")
            .register(meterRegistry);
            
        this.cacheGetTimer = Timer.builder("redis.cache.get.duration")
            .description("Time taken for cache get operations")
            .register(meterRegistry);
            
        this.cacheSetTimer = Timer.builder("redis.cache.set.duration")
            .description("Time taken for cache set operations")
            .register(meterRegistry);
            
        this.cacheDeleteTimer = Timer.builder("redis.cache.delete.duration")
            .description("Time taken for cache delete operations")
            .register(meterRegistry);
            
        this.cacheExpireTimer = Timer.builder("redis.cache.expire.duration")
            .description("Time taken for cache expire operations")
            .register(meterRegistry);
            
        // Initialize cache error metrics
        this.cacheErrorCounter = Counter.builder("redis.cache.errors.total")
            .description("Total number of cache errors")
            .register(meterRegistry);
            
        this.cacheTimeoutCounter = Counter.builder("redis.cache.timeout.total")
            .description("Total number of cache timeouts")
            .register(meterRegistry);
            
        this.cacheConnectionErrorCounter = Counter.builder("redis.cache.connection.errors.total")
            .description("Total number of cache connection errors")
            .register(meterRegistry);
    }
    
    public void recordCacheHit() {
        cacheHits.increment();
        log.debug("Cache hit recorded");
    }
    
    public void recordCacheMiss() {
        cacheMisses.increment();
        log.debug("Cache miss recorded");
    }
    
    public Timer.Sample startOperation() {
        return Timer.start(meterRegistry);
    }
    
    public void recordOperation(Timer.Sample sample, String operation) {
        sample.stop(cacheOperations);
        log.debug("Cache operation '{}' completed", operation);
    }
    
    // Detailed cache operation methods
    public void recordCacheGet() {
        cacheGetCounter.increment();
    }
    
    public void recordCacheSet() {
        cacheSetCounter.increment();
    }
    
    public void recordCacheDelete() {
        cacheDeleteCounter.increment();
    }
    
    public void recordCacheExpire() {
        cacheExpireCounter.increment();
    }
    
    public Timer.Sample startCacheGetTimer() {
        return Timer.start(meterRegistry);
    }
    
    public Timer.Sample startCacheSetTimer() {
        return Timer.start(meterRegistry);
    }
    
    public Timer.Sample startCacheDeleteTimer() {
        return Timer.start(meterRegistry);
    }
    
    public Timer.Sample startCacheExpireTimer() {
        return Timer.start(meterRegistry);
    }
    
    public void recordCacheGetDuration(Timer.Sample sample) {
        sample.stop(cacheGetTimer);
    }
    
    public void recordCacheSetDuration(Timer.Sample sample) {
        sample.stop(cacheSetTimer);
    }
    
    public void recordCacheDeleteDuration(Timer.Sample sample) {
        sample.stop(cacheDeleteTimer);
    }
    
    public void recordCacheExpireDuration(Timer.Sample sample) {
        sample.stop(cacheExpireTimer);
    }
    
    // Cache error methods
    public void recordCacheError() {
        cacheErrorCounter.increment();
    }
    
    public void recordCacheTimeout() {
        cacheTimeoutCounter.increment();
    }
    
    public void recordCacheConnectionError() {
        cacheConnectionErrorCounter.increment();
    }
} 