package com.stpl.tech.attendance.service.impl;

import com.stpl.tech.attendance.cache.service.ACLCache;
import com.stpl.tech.attendance.cache.service.PreAuthenticatedApiCache;
import com.stpl.tech.attendance.service.ACLService;
import com.stpl.tech.attendance.util.ACLUtil;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Log4j2
@Service
public class ACLServiceImpl implements ACLService {

    @Autowired
    private ACLCache aclCache;

    @Autowired
    private PreAuthenticatedApiCache preAuthenticatedApiCache;
    
    @Autowired
    private ACLUtil aclUtil;

    @Override
    public Boolean checkPermission(String requestURI, String requestMethod, String sessionKey) {
        return aclUtil.checkPermission(aclCache.getPermissions(sessionKey).get(sessionKey), requestURI,
                requestMethod);
    }

    @Override
    public boolean isPreAuthenticated(String requestUrl) {
        return aclUtil.hasPermission(preAuthenticatedApiCache.getPreAuthenticatedAPIs(),
                requestUrl);
    }
} 