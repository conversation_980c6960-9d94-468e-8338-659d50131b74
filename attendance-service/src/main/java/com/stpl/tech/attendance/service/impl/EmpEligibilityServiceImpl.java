package com.stpl.tech.attendance.service.impl;

import com.stpl.tech.attendance.cache.service.UnitCacheService;
import com.stpl.tech.attendance.entity.EmpEligibilityMapping;
import com.stpl.tech.attendance.dto.CacheRefreshResult;
import com.stpl.tech.attendance.enums.EligibilityType;
import com.stpl.tech.attendance.enums.MappingStatus;
import com.stpl.tech.attendance.enums.MappingType;
import com.stpl.tech.attendance.exception.TransferErrorCode;
import com.stpl.tech.attendance.exception.TransferException;
import com.stpl.tech.attendance.model.ApproverInfo;
import com.stpl.tech.attendance.model.TransferType;
import com.stpl.tech.attendance.repository.EmpEligibilityMappingRepository;
import com.stpl.tech.attendance.service.EmpEligibilityService;
import com.stpl.tech.attendance.util.DateTimeUtil;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.util.AppConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;
import java.util.ArrayList;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class EmpEligibilityServiceImpl implements EmpEligibilityService {

    private final EmpEligibilityMappingRepository empEligibilityMappingRepository;
    private final UnitCacheService unitCacheService;

    @Autowired
    @Lazy
    private EmpEligibilityService self;

    @Override
    public boolean isEligibleForAttendance(String empId, String unitId) {
        log.info("Checking attendance eligibility for employee: {} at unit: {}",
            empId, unitId);

        // Get unit details from cache
        UnitBasicDetail unitDetail = unitCacheService.getUnitBasicDetail(Integer.parseInt(unitId));
        if (unitDetail == null) {
            log.warn("Unit not found with ID: {}", unitId);
            return false;
        }

        String unitZone = unitDetail.getUnitZone();
        String location = unitDetail.getLocationCode();
        LocalDate today = LocalDate.now();

        // Get all active mappings for the employee for today
        List<EmpEligibilityMapping> employeeMappings = empEligibilityMappingRepository
            .findActiveMappingsForDate(empId, EligibilityType.ATTENDANCE, MappingStatus.ACTIVE, today);

        if (employeeMappings.isEmpty()) {
            log.debug("No active attendance eligibility mappings found for employee: {} on date: {}", empId, today);
            return false;
        }

        // Check eligibility using a single query for each type
        boolean isEligibleForUnit = employeeMappings.stream()
            .anyMatch(mapping -> mapping.getMappingType() == MappingType.UNIT 
                && mapping.getValue().equals(unitId) && isMappingActive(mapping,today));

        boolean isEligibleForZone = employeeMappings.stream()
            .anyMatch(mapping -> mapping.getMappingType() == MappingType.REGION 
                && mapping.getValue().equals(unitZone) && isMappingActive(mapping,today));

        boolean isEligibleForCity = employeeMappings.stream()
            .anyMatch(mapping -> mapping.getMappingType() == MappingType.CITY 
                && mapping.getValue().equals(location) && isMappingActive(mapping,today) );

        boolean isEligible = isEligibleForUnit || isEligibleForZone || isEligibleForCity;

        if (!isEligible) {
            log.debug("Employee {} is not eligible for attendance at unit: {} (zone: {}, city: {}) on date: {}",
                empId, unitId, unitZone, location, today);
        }

        return isEligible;
    }

    @Override
    public List<ApproverInfo> getApprovers(EligibilityType eligibilityType, Integer unitId) {
        log.info("Getting approvers for eligibility type: {} and unit: {}", eligibilityType, unitId);

        // Get unit details from cache
        UnitBasicDetail unitDetail = unitCacheService.getUnitBasicDetail(unitId);
        if (unitDetail == null) {
            log.warn("Unit not found with ID: {}", unitId);
            return List.of();
        }

        String unitZone = unitDetail.getUnitZone();

        // Get all active mappings for the unit, zone, and city
        List<EmpEligibilityMapping> approverMappings = empEligibilityMappingRepository
            .findByValueAndMappingTypeAndEligibilityTypeAndStatus(
                unitId.toString(), MappingType.UNIT, eligibilityType, MappingStatus.ACTIVE);

        approverMappings.addAll(empEligibilityMappingRepository
            .findByValueAndMappingTypeAndEligibilityTypeAndStatus(
                unitZone, MappingType.REGION, eligibilityType, MappingStatus.ACTIVE));

        approverMappings.addAll(empEligibilityMappingRepository
            .findByValueAndMappingTypeAndEligibilityTypeAndStatus(
                unitDetail.getCity(), MappingType.CITY, eligibilityType, MappingStatus.ACTIVE));

        Set<String> approvers = approverMappings.stream()
            .map(EmpEligibilityMapping::getEmpId)
            .collect(Collectors.toSet());
        if(unitDetail.getCafeManagerId() != null){
            approvers.add(unitDetail.getCafeManagerId().toString());
        }
        if(unitDetail.getUnitManagerId() != null){
            approvers.add(unitDetail.getUnitManagerId().toString());
        }
        return approvers.stream()
            .map(approverId -> new ApproverInfo(Integer.parseInt(approverId), true))
            .collect(Collectors.toList());
    }


    private boolean isMappingActive(EmpEligibilityMapping mapping, LocalDate currentDate) {
        // Check start date: mapping is active if start date is null, before current date, or equal to current date
        boolean startDateValid = mapping.getStartDate() == null ||
                mapping.getStartDate().isBefore(currentDate) ||
                mapping.getStartDate().isEqual(currentDate);

        // Check end date: mapping is active if end date is null or after current date
        boolean endDateValid = mapping.getEndDate() == null ||
                mapping.getEndDate().isAfter(currentDate);

        return startDateValid && endDateValid;
    }

    @Override
    public boolean createAttendanceEligibilityMapping(String empId, String unitId) {
        log.info("Creating attendance eligibility mapping for employee: {} in unit: {}", empId, unitId);
            
        // Check if eligibility mapping already exists
        boolean exists = isEligibleForAttendance(empId, unitId);
        
        if (!exists) {
            // Create new eligibility mapping
            EmpEligibilityMapping mapping = EmpEligibilityMapping.builder()
                .empId(empId)
                .value(unitId)
                .mappingType(MappingType.UNIT)
                .eligibilityType(EligibilityType.ATTENDANCE)
                .status(MappingStatus.ACTIVE)
                .createdBy(String.valueOf(AppConstants.SYSTEM_EMPLOYEE_ID))
                .updatedBy(String.valueOf(AppConstants.SYSTEM_EMPLOYEE_ID))
                .createdAt(DateTimeUtil.now())
                .updatedAt(DateTimeUtil.now())
                .build();

            empEligibilityMappingRepository.save(mapping);
            log.info("Created attendance eligibility mapping for employee: {} in unit: {}", empId, unitId);
            return true;
        } else {
            log.info("Attendance eligibility mapping already exists for employee: {} in unit: {}", empId, unitId);
            return false;
        }
    }

    @Override
    public void updateEligibilityMapping(String empId, String sourceUnitId, String destinationUnitId,
                                         TransferType transferType, LocalDate startDate, LocalDate endDate) {
        log.info("Updating eligibility mapping for employee: {} from unit: {} to unit: {} with transfer type: {}", 
            empId, sourceUnitId, destinationUnitId, transferType);

        // Get unit details from cache
        UnitBasicDetail sourceUnitDetail = sourceUnitId == null ? null : unitCacheService.getUnitBasicDetail(Integer.parseInt(sourceUnitId));
        UnitBasicDetail destUnitDetail = unitCacheService.getUnitBasicDetail(Integer.parseInt(destinationUnitId));
        
        if ((sourceUnitDetail == null && transferType != TransferType.TEMPORARY )|| destUnitDetail == null) {
            log.warn("Unit not found. Source Unit: {}, Destination Unit: {}", sourceUnitId, destinationUnitId);
            return;
        }

        // Check if mapping already exists for the destination unit
        List<EmpEligibilityMapping> existingDestinationMappings = empEligibilityMappingRepository
                .findByEmpIdAndValueAndMappingTypeAndEligibilityTypeAndStatus(
                        empId, destinationUnitId, MappingType.UNIT, EligibilityType.ATTENDANCE, MappingStatus.ACTIVE);

        if (transferType == TransferType.PERMANENT) {
            // For permanent transfers, check if permanent mapping already exists
            boolean permanentMappingExists = existingDestinationMappings.stream()
                    .anyMatch(mapping -> mapping.getStartDate() != null && mapping.getEndDate() == null);
            
            if (permanentMappingExists) {
                log.info("Permanent eligibility mapping already exists for employee {} in unit {}. Skipping creation.", 
                         empId, destinationUnitId);
                return;
            }
            
            if (startDate == null) {
                log.error("Start date is required for permanent transfers");
                throw new TransferException(TransferErrorCode.INVALID_START_DATE);
            }
            
            // Deactivate source unit mappings if they exist
            if (sourceUnitId != null) {
                List<EmpEligibilityMapping> sourceUnitMappings = empEligibilityMappingRepository
                        .findByEmpIdAndValueAndMappingTypeAndEligibilityTypeAndStatus(
                                empId, sourceUnitId, MappingType.UNIT, EligibilityType.ATTENDANCE, MappingStatus.ACTIVE);

                sourceUnitMappings.forEach(mapping -> {
                    mapping.setUpdatedBy(String.valueOf(AppConstants.SYSTEM_EMPLOYEE_ID));
                    mapping.setUpdatedAt(DateTimeUtil.now());
                    mapping.setEndDate(startDate);
                });
                empEligibilityMappingRepository.saveAll(sourceUnitMappings);
            }
            
            // Create permanent mapping for destination unit
            EmpEligibilityMapping mapping = EmpEligibilityMapping.builder()
                .empId(empId)
                .value(destinationUnitId)
                .mappingType(MappingType.UNIT)
                .eligibilityType(EligibilityType.ATTENDANCE)
                .status(MappingStatus.ACTIVE)
                .startDate(startDate)
                .endDate(null) // No end date for permanent mapping
                .createdBy(String.valueOf(AppConstants.SYSTEM_EMPLOYEE_ID))
                .updatedBy(String.valueOf(AppConstants.SYSTEM_EMPLOYEE_ID))
                .createdAt(DateTimeUtil.now())
                .updatedAt(DateTimeUtil.now())
                .build();
            empEligibilityMappingRepository.save(mapping);
            
        } else if (transferType == TransferType.TEMPORARY) {
            // For temporary transfers, check for overlapping date ranges
            List<EmpEligibilityMapping> existingTemporaryMappings = empEligibilityMappingRepository
                .findByEmpIdAndValueAndMappingTypeAndEligibilityTypeAndStatus(empId, destinationUnitId, MappingType.UNIT, EligibilityType.ATTENDANCE, MappingStatus.ACTIVE);
            
            if (!existingTemporaryMappings.isEmpty()) {
                // Check for overlapping date ranges
                boolean hasOverlap = existingTemporaryMappings.stream()
                    .anyMatch(existing -> {
                        LocalDate existingStart = existing.getStartDate();
                        LocalDate existingEnd = existing.getEndDate();
                        
                        // Check if dates overlap
                        return !(startDate.isAfter(existingEnd) || endDate.isBefore(existingStart));
                    });
                
                if (hasOverlap) {
                    log.info("Found overlapping temporary mapping for employee: {} in unit: {}. " +
                            "Creating new mapping with adjusted dates to avoid overlap.", empId, destinationUnitId);
                    
                    // Find the non-overlapping period by adjusting start/end dates
                    LocalDate adjustedStartDate = startDate;
                    LocalDate adjustedEndDate = endDate;
                    
                    for (EmpEligibilityMapping existing : existingTemporaryMappings) {
                        LocalDate existingStart = existing.getStartDate();
                        LocalDate existingEnd = existing.getEndDate();
                        
                        // If new mapping overlaps with existing, adjust the dates
                        if (!(startDate.isAfter(existingEnd) || endDate.isBefore(existingStart))) {
                            // Overlap detected, adjust dates
                            if (startDate.isBefore(existingStart) && endDate.isAfter(existingStart)) {
                                // New mapping starts before existing but overlaps
                                adjustedEndDate = existingStart.minusDays(1);
                            } else if (startDate.isBefore(existingEnd) && endDate.isAfter(existingEnd)) {
                                // New mapping ends after existing but overlaps
                                adjustedStartDate = existingEnd.plusDays(1);
                            } else if (startDate.isBefore(existingStart) && endDate.isAfter(existingEnd)) {
                                // New mapping completely encompasses existing
                                // Split into two mappings: before and after existing
                                if (startDate.isBefore(existingStart)) {
                                    // Create mapping before existing
                                    EmpEligibilityMapping beforeMapping = EmpEligibilityMapping.builder()
                                        .empId(empId)
                                        .value(destinationUnitId)
                                        .mappingType(MappingType.UNIT)
                                        .eligibilityType(EligibilityType.ATTENDANCE)
                                        .status(MappingStatus.ACTIVE)
                                        .startDate(startDate)
                                        .endDate(existingStart.minusDays(1))
                                        .createdBy(String.valueOf(AppConstants.SYSTEM_EMPLOYEE_ID))
                                        .updatedBy(String.valueOf(AppConstants.SYSTEM_EMPLOYEE_ID))
                                        .createdAt(DateTimeUtil.now())
                                        .updatedAt(DateTimeUtil.now())
                                        .build();
                                    empEligibilityMappingRepository.save(beforeMapping);
                                    log.info("Created temporary mapping before existing: {} to {}", 
                                        startDate, existingStart.minusDays(1));
                                }
                                
                                if (endDate.isAfter(existingEnd)) {
                                    // Create mapping after existing
                                    adjustedStartDate = existingEnd.plusDays(1);
                                    adjustedEndDate = endDate;
                                } else {
                                    // No mapping needed after existing
                                    return;
                                }
                            }
                        }
                    }
                    
                    // Create the adjusted mapping if there's a valid date range
                    if (adjustedStartDate.isBefore(adjustedEndDate) || adjustedStartDate.isEqual(adjustedEndDate)) {
                        EmpEligibilityMapping newMapping = EmpEligibilityMapping.builder()
                            .empId(empId)
                            .value(destinationUnitId)
                            .mappingType(MappingType.UNIT)
                            .eligibilityType(EligibilityType.ATTENDANCE)
                            .status(MappingStatus.ACTIVE)
                            .startDate(adjustedStartDate)
                            .endDate(adjustedEndDate)
                            .createdBy(String.valueOf(AppConstants.SYSTEM_EMPLOYEE_ID))
                            .updatedBy(String.valueOf(AppConstants.SYSTEM_EMPLOYEE_ID))
                            .createdAt(DateTimeUtil.now())
                            .updatedAt(DateTimeUtil.now())
                            .build();
                        empEligibilityMappingRepository.save(newMapping);
                        log.info("Created adjusted temporary mapping: {} to {} for employee: {} in unit: {}", 
                            adjustedStartDate, adjustedEndDate, empId, destinationUnitId);
                    } else {
                        log.info("No valid date range available for temporary mapping after adjusting for overlaps. " +
                                "Employee: {} in unit: {}", empId, destinationUnitId);
                    }
                    return;
                }
            }
            
            // No overlaps found, create normal temporary mapping
            EmpEligibilityMapping newMapping = EmpEligibilityMapping.builder()
                .empId(empId)
                .value(destinationUnitId)
                .mappingType(MappingType.UNIT)
                .eligibilityType(EligibilityType.ATTENDANCE)
                .status(MappingStatus.ACTIVE)
                .startDate(startDate)
                .endDate(endDate)
                .createdBy(String.valueOf(AppConstants.SYSTEM_EMPLOYEE_ID))
                .updatedBy(String.valueOf(AppConstants.SYSTEM_EMPLOYEE_ID))
                .createdAt(DateTimeUtil.now())
                .updatedAt(DateTimeUtil.now())
                .build();
            empEligibilityMappingRepository.save(newMapping);
            log.info("Created temporary eligibility mapping: {} to {} for employee: {} in unit: {}", 
                startDate, endDate, empId, destinationUnitId);
        }

        log.info("Successfully updated eligibility mapping for employee: {} from unit: {} to unit: {}", 
            empId, sourceUnitId, destinationUnitId);
    }

    @Override
    @Cacheable(value = "eligibilityUnits", key = "#empId")
    public java.util.List<Integer> getUnitsFromEligibilityMappings(Integer empId) {
        log.debug("Getting units from eligibility mappings for employee: {}", empId);
        
        // Get active eligibility mappings for the employee
        List<EmpEligibilityMapping> activeMappings = getActiveEligibilityMappings(empId);
        
        // Process mappings and find matching units
        java.util.Set<Integer> eligibleUnits = new java.util.HashSet<>();
        for (EmpEligibilityMapping mapping : activeMappings) {
            if (mapping.getMappingType() == MappingType.CITY || mapping.getMappingType() == MappingType.REGION) {
                java.util.Set<Integer> unitsForMapping = getUnitsForEligibilityMapping(mapping);
                eligibleUnits.addAll(unitsForMapping);
            }
        }
        
        log.debug("Found {} units from eligibility mappings for employee: {}", eligibleUnits.size(), empId);
        return new java.util.ArrayList<>(eligibleUnits);
    }

    /**
     * Get active eligibility mappings for an employee
     */
    private List<EmpEligibilityMapping> getActiveEligibilityMappings(Integer empId) {
        log.debug("Getting active eligibility mappings for employee: {}", empId);
        
        // Get all attendance eligibility mappings for the employee
        List<EmpEligibilityMapping> employeeEligibilityMappings = empEligibilityMappingRepository
                .findByEmpIdAndEligibilityTypeAndStatus(
                        empId.toString(),
                        EligibilityType.ATTENDANCE,
                        MappingStatus.ACTIVE
                );

        LocalDate currentDate = LocalDate.now();
        
        // Filter active mappings for current date
        List<EmpEligibilityMapping> activeMappings = employeeEligibilityMappings.stream()
                .filter(mapping -> isMappingActive(mapping, currentDate))
                .toList();
        
        log.debug("Found {} active eligibility mappings for employee: {}", activeMappings.size(), empId);
        return activeMappings;
    }

    /**
     * Get units that match a specific eligibility mapping (CITY or REGION)
     */
    private java.util.Set<Integer> getUnitsForEligibilityMapping(EmpEligibilityMapping mapping) {
        log.debug("Getting units for eligibility mapping: {} (type: {}, value: {})", 
                mapping.getId(), mapping.getMappingType(), mapping.getValue());
        
        java.util.Set<Integer> matchingUnits = new java.util.HashSet<>();
        String mappingValue = mapping.getValue();
        
        // Get all units and check if they match the mapping
        unitCacheService.getAllUnitCache().values().forEach(unit -> {
            UnitBasicDetail unitDetail = unitCacheService.getUnitBasicDetail(unit.getId());
            if (unitDetail != null && isUnitMatchingMapping(unitDetail, mapping, mappingValue)) {
                matchingUnits.add(unit.getId());
                log.debug("Added unit {} to matching units for {} mapping: {}", 
                        unit.getId(), mapping.getMappingType(), mappingValue);
            }
        });
        
        log.debug("Found {} units matching {} mapping: {}", 
                matchingUnits.size(), mapping.getMappingType(), mappingValue);
        return matchingUnits;
    }

    /**
     * Check if a unit matches the given eligibility mapping
     */
    private boolean isUnitMatchingMapping(UnitBasicDetail unitDetail, EmpEligibilityMapping mapping, String mappingValue) {
        if (mapping.getMappingType() == MappingType.CITY) {
            // Check if unit's city matches the mapping value
            return mappingValue.equals(unitDetail.getCity());
        } else if (mapping.getMappingType() == MappingType.REGION) {
            // Check if unit's zone/region matches the mapping value
            return mappingValue.equals(unitDetail.getUnitZone());
        }
//        }else if(mapping.getMappingType() == MappingType.UNIT){
//            return mappingValue.equalsIgnoreCase(String.valueOf(unitDetail.getId()));
//        }
        return false;
    }

    @Override
    @Cacheable(value = "unitEmployees", key = "#unitId")
    public java.util.List<Integer> getEmployeesForUnit(Integer unitId) {
        log.debug("Getting employees for unit: {}", unitId);
        
        // Get unit details to check city and region
        UnitBasicDetail unitDetail = unitCacheService.getUnitBasicDetail(unitId);
        if (unitDetail == null) {
            log.warn("Unit not found with ID: {}", unitId);
            return new java.util.ArrayList<>();
        }
        
        java.util.Set<Integer> eligibleEmployees = new java.util.HashSet<>();
        LocalDate currentDate = LocalDate.now();
        
        // 1. Find employees directly mapped to this unit
        List<EmpEligibilityMapping> unitMappings = empEligibilityMappingRepository
            .findActiveMappingsForValueAndDate(
                unitId.toString(), 
                MappingType.UNIT, 
                EligibilityType.ATTENDANCE, 
                MappingStatus.ACTIVE, 
                currentDate
            );
        
        for (EmpEligibilityMapping mapping : unitMappings) {
            try {
                eligibleEmployees.add(Integer.parseInt(mapping.getEmpId()));
            } catch (NumberFormatException e) {
                log.warn("Invalid employee ID format: {}", mapping.getEmpId());
            }
        }
        
        // 2. Find employees mapped to this unit's city
        String unitCity = unitDetail.getLocationCode();
        if (unitCity != null && !unitCity.trim().isEmpty()) {
            List<EmpEligibilityMapping> cityMappings = empEligibilityMappingRepository
                .findActiveMappingsForValueAndDate(
                    unitCity, 
                    MappingType.CITY, 
                    EligibilityType.ATTENDANCE, 
                    MappingStatus.ACTIVE, 
                    currentDate
                );
            
            for (EmpEligibilityMapping mapping : cityMappings) {
                try {
                    eligibleEmployees.add(Integer.parseInt(mapping.getEmpId()));
                } catch (NumberFormatException e) {
                    log.warn("Invalid employee ID format: {}", mapping.getEmpId());
                }
            }
        }
        
        // 3. Find employees mapped to this unit's region/zone
        String unitZone = unitDetail.getUnitZone();
        if (unitZone != null && !unitZone.trim().isEmpty()) {
            List<EmpEligibilityMapping> regionMappings = empEligibilityMappingRepository
                .findActiveMappingsForValueAndDate(
                    unitZone, 
                    MappingType.REGION, 
                    EligibilityType.ATTENDANCE, 
                    MappingStatus.ACTIVE, 
                    currentDate
                );
            
            for (EmpEligibilityMapping mapping : regionMappings) {
                try {
                    eligibleEmployees.add(Integer.parseInt(mapping.getEmpId()));
                } catch (NumberFormatException e) {
                    log.warn("Invalid employee ID format: {}", mapping.getEmpId());
                }
            }
        }
        
        log.debug("Found {} employees eligible for unit: {}", eligibleEmployees.size(), unitId);
        return new java.util.ArrayList<>(eligibleEmployees);
    }

    /**
     * Evict cache for a specific unit when eligibility mappings change
     * @param unitId Unit ID
     */
    @CacheEvict(value = "unitEmployees", key = "#unitId")
    public void evictUnitEmployeesCache(Integer unitId) {
        log.debug("Evicting unit employees cache for unit: {}", unitId);
    }

    /**
     * Evict all unit employees cache entries
     */
    @Override
    @CacheEvict(value = "unitEmployees", allEntries = true)
    public void evictAllUnitEmployeesCache() {
        log.debug("Evicting all unit employees cache");
    }

    /**
     * Scheduled task to invalidate unit employees cache daily at 1:00 AM
     * This ensures fresh data is loaded each day
     */
//    @Scheduled(cron = "0 0 1 * * ?") // Run daily at 1:00 AM
//    @CacheEvict(value = "unitEmployees", allEntries = true)
//    public void scheduledUnitEmployeesCacheInvalidation() {
//        log.info("Scheduled daily cache invalidation for unit employees cache at {}", LocalDateTime.now());
//    }

    /**
     * Evict the eligibility units cache for a specific employee
     * @param empId Employee ID
     */
    @CacheEvict(value = "eligibilityUnits", key = "#empId")
    public void evictEligibilityUnitsCache(Integer empId) {
        log.debug("Evicting eligibility units cache for employee: {}", empId);
    }

    /**
     * Update unit employees cache for a specific unit
     *
     * @param unitId Unit ID
     */
    @CachePut(value = "unitEmployees", key = "#unitId")
    public void updateUnitEmployeesCache(Integer unitId) {
        log.debug("Updating unit employees cache for unit: {}", unitId);
        self.getEmployeesForUnit(unitId);
    }

    /**
     * Refresh unit employees cache for a specific unit
     * This method is specifically called after biometric registration approval
     * to ensure the cache is updated with the new employee eligibility
     *
     * @param unitId Unit ID
     */
    public void refreshUnitEmployeesCache(Integer unitId) {
        log.info("Refreshing unit employees cache for unit: {} after biometric registration approval", unitId);
        try {
            // First evict the existing cache
            self.evictUnitEmployeesCache(unitId);

            // Then update with fresh data
            updateUnitEmployeesCache(unitId);

            log.info("Successfully refreshed unit employees cache for unit: {}", unitId);
        } catch (Exception e) {
            log.error("Error refreshing unit employees cache for unit: {}", unitId, e);
        }
    }

    /**
     * Update all unit employees cache entries
     */
    public void updateAllUnitEmployeesCache() {
        log.info("Updating all unit employees cache entries");
        try {
            // Get all units from cache
            List<UnitBasicDetail> allUnits = unitCacheService.getAllUnitBasicDetail();
            if (allUnits == null || allUnits.isEmpty()) {
                log.warn("No units found in cache, skipping cache update");
                return;
            }

            int updatedCount = 0;
            for (UnitBasicDetail unit : allUnits) {
                try {
                    updateUnitEmployeesCache(unit.getId());
                    updatedCount++;
                } catch (Exception e) {
                    log.error("Error updating cache for unit: {}", unit.getId(), e);
                }
            }
            log.info("Successfully updated cache for {} units", updatedCount);
        } catch (Exception e) {
            log.error("Error updating all unit employees cache", e);
        }
    }

    /**
     * Initialize unit employees cache after application is ready
     * This runs after all beans are properly initialized
     */
    @EventListener(ApplicationReadyEvent.class)
    public void initializeUnitEmployeesCacheOnStartup() {
        unitCacheService.refreshLocalMaps();
        log.info("Initializing unit employees cache after application startup");
        try {
            // Get all active units from cache
            List<UnitBasicDetail> allUnits = unitCacheService.getAllUnitBasicDetail();
            if (allUnits == null || allUnits.isEmpty()) {
                log.warn("No units found in cache, skipping initialization");
                return;
            }

            // Filter only active units
            List<UnitBasicDetail> activeUnits = allUnits.stream()
                .filter(unit -> "ACTIVE".equalsIgnoreCase(unit.getStatus().value()))
                .collect(Collectors.toList());

            if (activeUnits.isEmpty()) {
                log.warn("No active units found, skipping cache initialization");
                return;
            }

            log.info("Found {} active units, initializing cache for {} units", 
                    allUnits.size(), activeUnits.size());

            int initializedCount = 0;
            for (UnitBasicDetail unit : activeUnits) {
                try {
                    // This will populate the cache for each active unit
                    self.getEmployeesForUnit(unit.getId());
                    initializedCount++;
                } catch (Exception e) {
                    log.error("Error initializing cache for unit: {}", unit.getId(), e);
                }
            }
            log.info("Successfully initialized cache for {} active units", initializedCount);
        } catch (Exception e) {
            log.error("Error during cache initialization", e);
        }
    }

    /**
     * Scheduled task to refresh unit employees cache every 2 hours
     * This ensures the cache stays up-to-date with any changes in employee eligibility
     */
    @Scheduled(fixedRate = 2 * 60 * 60 * 1000) // 2 hours in milliseconds
    public void scheduledRefreshUnitEmployeesCache() {
        log.info("Starting scheduled refresh of unit employees cache");
        try {
            // Get all units from cache
            List<UnitBasicDetail> allUnits = unitCacheService.getAllUnitBasicDetail();
            if (allUnits == null || allUnits.isEmpty()) {
                log.warn("No units found in cache, skipping scheduled refresh");
                return;
            }

            int refreshedCount = 0;
            for (UnitBasicDetail unit : allUnits) {
                try {
                    log.debug("Refreshing cache for unit: {}", unit.getId());
                    // First evict the existing cache for this unit
                    self.evictUnitEmployeesCache(unit.getId());

                    // Then update with fresh data
                    updateUnitEmployeesCache(unit.getId());

                    refreshedCount++;
                    log.debug("Successfully refreshed cache for unit: {}", unit.getId());
                } catch (Exception e) {
                    log.error("Error refreshing cache for unit: {}", unit.getId(), e);
                }
            }
            log.info("Successfully completed scheduled refresh of unit employees cache for {} units", refreshedCount);
        } catch (Exception e) {
            log.error("Error during scheduled refresh of unit employees cache", e);
        }
    }

    /**
     * Manual method to refresh all unit employees cache
     * This can be called via API or other services
     *
     * @return CacheRefreshResult containing refresh statistics
     */
    public CacheRefreshResult manuallyRefreshAllUnitEmployeesCache() {
        log.info("Starting manual refresh of all unit employees cache");
        long startTime = System.currentTimeMillis();

        try {
            // Get all units from cache
            List<UnitBasicDetail> allUnits = unitCacheService.getAllUnitBasicDetail();
            if (allUnits == null || allUnits.isEmpty()) {
                log.warn("No units found in cache, skipping manual refresh");
                return CacheRefreshResult.builder()
                    .success(false)
                    .message("No units found in cache")
                    .totalUnits(0)
                    .refreshedUnits(0)
                    .failedUnits(0)
                    .durationMs(0)
                    .build();
            }

            int totalUnits = allUnits.size();
            int refreshedCount = 0;
            int failedCount = 0;
            List<String> failedUnits = new ArrayList<>();

            for (UnitBasicDetail unit : allUnits) {
                try {
                    log.debug("Manually refreshing cache for unit: {}", unit.getId());
                    // First evict the existing cache for this unit
                    self.evictUnitEmployeesCache(unit.getId());

                    // Then update with fresh data
                    updateUnitEmployeesCache(unit.getId());

                    refreshedCount++;
                    log.debug("Successfully manually refreshed cache for unit: {}", unit.getId());
                } catch (Exception e) {
                    failedCount++;
                    failedUnits.add("Unit " + unit.getId() + ": " + e.getMessage());
                    log.error("Error manually refreshing cache for unit: {}", unit.getId(), e);
                }
            }

            long duration = System.currentTimeMillis() - startTime;

            CacheRefreshResult result = CacheRefreshResult.builder()
                .success(true)
                .message("Manual cache refresh completed")
                .totalUnits(totalUnits)
                .refreshedUnits(refreshedCount)
                .failedUnits(failedCount)
                .failedUnitDetails(failedUnits)
                .durationMs(duration)
                .build();

            log.info("Successfully completed manual refresh of unit employees cache. Total: {}, Refreshed: {}, Failed: {}, Duration: {}ms",
                    totalUnits, refreshedCount, failedCount, duration);

            return result;

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("Error during manual refresh of unit employees cache", e);

            return CacheRefreshResult.builder()
                .success(false)
                .message("Error during manual cache refresh: " + e.getMessage())
                .totalUnits(0)
                .refreshedUnits(0)
                .failedUnits(0)
                .durationMs(duration)
                .build();
        }
    }


} 