package com.stpl.tech.attendance.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Entity
@Table(name = "APPLICATION_INSTALLATION_DETAIL")
public class ApplicationInstallationData {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "INSTALLATION_ID")
    private Integer installationId;

    @Column(name = "UNIT_ID")
    private Integer unitId;

    @Column(name = "MACHINE_ID")
    private String machineId;

    @Column(name = "OS_VERSION")
    private String osVersion;

    @Column(name = "CREATED_DATE")
    private LocalDateTime createdDate;

    @Column(name = "CREATED_BY")
    private String createdBy;

    @Column(name = "UPDATED_DATE")
    private LocalDateTime updatedDate;

    @Column(name = "UPDATED_BY")
    private String updatedBy;

    @Column(name = "DELETED_DATE")
    private LocalDateTime deletedDate;

    @Column(name = "DELETED_BY")
    private String deletedBy;

    @Column(name = "STATUS")
    private String status;
} 