package com.stpl.tech.attendance.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.stpl.tech.attendance.approval.entity.ApprovalRequest;
import com.stpl.tech.attendance.approval.service.ApprovalEngineService;
import com.stpl.tech.attendance.cache.service.UserCacheService;
import com.stpl.tech.attendance.config.EnvironmentProperties;
import com.stpl.tech.attendance.context.BiometricContext;
import com.stpl.tech.attendance.context.JwtContext;
import com.stpl.tech.attendance.dto.BiometricAdditionalImageDTO;
import com.stpl.tech.attendance.dto.BiometricDeregistrationResponse;
import com.stpl.tech.attendance.dto.BiometricFaceActivationRequestDTO;
import com.stpl.tech.attendance.dto.BiometricIdentificationRequestDTO;
import com.stpl.tech.attendance.dto.BiometricIdentificationResponseDTO;
import com.stpl.tech.attendance.dto.BiometricRegistrationRequestDTO;
import com.stpl.tech.attendance.dto.BiometricRegistrationResponseDTO;
import com.stpl.tech.attendance.dto.BiometricTempRegistrationRequestDTO;
import com.stpl.tech.attendance.dto.BiometricTempRegistrationResponseDTO;
import com.stpl.tech.attendance.entity.BiometricRegistration;
import com.stpl.tech.attendance.entity.BiometricAdditionalImage;
import com.stpl.tech.attendance.enums.ApprovalStatus;
import com.stpl.tech.attendance.enums.ApprovalType;
import com.stpl.tech.attendance.enums.AttendanceErrorCode;
import com.stpl.tech.attendance.enums.BiometricErrorCode;
import com.stpl.tech.attendance.enums.BiometricStatus;
import com.stpl.tech.attendance.enums.EligibilityType;
import com.stpl.tech.attendance.exception.AttendanceException;
import com.stpl.tech.attendance.exception.BiometricRegistrationException;
import com.stpl.tech.attendance.model.ApproverInfo;
import com.stpl.tech.attendance.model.request.BiometricRegistrationRequest;
import com.stpl.tech.attendance.model.request.BiometricTempVerificationRequest;
import com.stpl.tech.attendance.model.request.BiometricVerificationRequest;
import com.stpl.tech.attendance.model.response.BiometricRegistrationActionResponse;
import com.stpl.tech.attendance.model.response.BiometricRegistrationResponse;
import com.stpl.tech.attendance.repository.BiometricRegistrationRepository;
import com.stpl.tech.attendance.repository.BiometricAdditionalImageRepository;
import com.stpl.tech.attendance.service.BiometricRegistrationService;
import com.stpl.tech.attendance.service.BiometricService;
import com.stpl.tech.attendance.service.EmpEligibilityService;
import com.stpl.tech.attendance.service.S3Service;
import com.stpl.tech.attendance.util.CloudFrontToBase64;
import com.stpl.tech.attendance.util.DateTimeUtil;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.util.AppConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.connection.ReactiveGeoCommands;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import com.stpl.tech.attendance.notification.dto.NotificationRequest;
import com.stpl.tech.attendance.notification.entity.Notification;

@Slf4j
@Service
@RequiredArgsConstructor
public class BiometricRegistrationServiceImpl implements BiometricRegistrationService {

    private final BiometricRegistrationRepository biometricRegistrationRepository;
    private final BiometricAdditionalImageRepository biometricAdditionalImageRepository;
    private final ApprovalEngineService approvalEngineService;
    private final S3Service s3Service;
    private final BiometricService biometricService;
    private final UserCacheService userCacheService;
    private final EmpEligibilityService empEligibilityService;
    private final EnvironmentProperties environmentProperties;
    private final ObjectMapper objectMapper;
    private final AsyncOperationService asyncOperationService;

    @Override
    @Transactional
    public BiometricRegistrationActionResponse registerBiometric(BiometricRegistrationRequest request) {
        long startTime = System.currentTimeMillis();
        log.info("Starting biometric registration for employee: {}", request.getEmpId());


            // Validate request
            long validationStart = System.currentTimeMillis();
            EmployeeBasicDetail employee = validateRegistrationRequest(request);
            log.info("Employee validation completed in {} ms for employee: {}",
                    System.currentTimeMillis() - validationStart, request.getEmpId());

            // Create registration record
            long registrationStart = System.currentTimeMillis();
            BiometricRegistration registration = createRegistrationRecord(request);
            log.info("Registration record creation completed in {} ms for employee: {}",
                    System.currentTimeMillis() - registrationStart, request.getEmpId());

            // Create and start approval workflow
            long approvalStart = System.currentTimeMillis();
            ApprovalRequest approvalRequest = createApprovalRequest(request, registration);
            log.info("Approval workflow preparation completed in {} ms for employee: {}",
                    System.currentTimeMillis() - approvalStart, request.getEmpId());

            // Start approval request creation asynchronously
            //approvalRequest.setCreatedBy(JwtContext.getInstance().getUserId().toString());
            long asyncStart = System.currentTimeMillis();
            approvalEngineService.createRequest(approvalRequest);
            log.info("Approval request creation completed in {} ms for employee: {}",
                    System.currentTimeMillis() - asyncStart, request.getEmpId());
//            CompletableFuture.runAsync(() -> {
//                long asyncStart = System.currentTimeMillis();
//                try {
//                    log.info("Starting approval request creation for employee: {}", request.getEmpId());
//                    approvalEngineService.createRequest(approvalRequest);
//                    log.info("Approval request creation completed in {} ms for employee: {}",
//                            System.currentTimeMillis() - asyncStart, request.getEmpId());
//                } catch (Exception e) {
//                    log.error("Error creating approval request for employee: {} after {} ms",
//                            request.getEmpId(), System.currentTimeMillis() - asyncStart, e);
//                }
//            });

        // Register with biometric service
        BiometricRegistrationRequestDTO registrationRequest = BiometricRegistrationRequestDTO.builder()
                .model_id("face_registration")
                .parameters(BiometricRegistrationRequestDTO.Parameters.builder()
                        .employeeId(Integer.parseInt(registration.getEmpId()))
                        .metadata(BiometricRegistrationRequestDTO.Metadata.builder()
                                .employeeId(Integer.parseInt(registration.getEmpId()))
                                .unitId(Integer.parseInt(registration.getUnitId()))
                                .imageUrl(registration.getImageUrl())
                                .build())
                        .build())
                .input_data(BiometricRegistrationRequestDTO.InputData.builder().data(request.getBase64Image())
                        .build())
                .extra_reg(buildExtraRegData(request.getAdditionalImages()))
                .build();
        log.info("request : {}", new Gson().toJson(registrationRequest));
        BiometricRegistrationResponseDTO biometricResponse = biometricService.registerFace(registrationRequest);
        log.info("response : {} ", new Gson().toJson(biometricResponse));
        if (biometricResponse == null || biometricResponse.getStatus() == null || !biometricResponse.getStatus().equals("success")) {
            throw new BiometricRegistrationException(BiometricErrorCode.REGISTRATION_PROCESSING_ERROR);
        }
        registration.setBiometricId(biometricResponse.getResult().getBiometricId());
        registration.setBiometricUserId(biometricResponse.getResult().getUserId());

         registration = biometricRegistrationRepository.save(registration);
        biometricService.handleRegistrationStatusChange(registration.getEmpId(), registration.getStatus());

        log.info("Total biometric registration completed in {} ms for employee: {}",
                    System.currentTimeMillis() - startTime, request.getEmpId());

        // Send notification to approvers after biometric registration is completed
        try {
            // Get the approval request to find approvers
                // Get approvers for this biometric registration request
                List<ApproverInfo> approvers = empEligibilityService.getApprovers(EligibilityType.APPROVAL, Integer.valueOf(request.getUnitId()));

                if (approvers != null && !approvers.isEmpty()) {
                    NotificationRequest notificationRequest = NotificationRequest.builder()
                            .type(Notification.NotificationType.APPROVAL_REQUEST)
                            .title("Biometric Registration Completed - Approval Required")
                            .message(employee.getName() + " has completed biometric registration and requires approval.")
                            .priority(Notification.Priority.MEDIUM)
                            .metadata(new HashMap<>())
                            .recipientIds(approvers.stream().map(approver -> approver.getId().toString()).collect(Collectors.toList()))
                            .requesterId(request.getEmpId())
                            .build();

                    // Send notification asynchronously
                    asyncOperationService.sendNotificationAsync(notificationRequest);
                    log.info("Sent biometric registration completion notification to {} approvers for employee: {}", approvers.size(), request.getEmpId());
                }
        } catch (Exception e) {
            log.warn("Failed to send biometric registration completion notification for employee: {}", request.getEmpId(), e);
            // Don't throw exception to avoid affecting the main registration process
        }

        return buildRegistrationResponse(request, employee, registration, approvalRequest);

    }

    /**
     * Validates the registration request and returns employee details
     */
    private EmployeeBasicDetail validateRegistrationRequest(BiometricRegistrationRequest request) {
        // Validate employee exists
        EmployeeBasicDetail employee = userCacheService.getUserById(Integer.valueOf(request.getEmpId()));
        if (employee == null) {
            throw new BiometricRegistrationException(BiometricErrorCode.EMPLOYEE_NOT_FOUND,
                    "Employee not found with ID: " + request.getEmpId());
        }
        if (!employee.getStatus().name().equals(AppConstants.ACTIVE)) {
            throw new AttendanceException(AttendanceErrorCode.INACTIVE_EMPLOYEE);
        }

        // Check if employee already has a biometric registration
        if (biometricRegistrationRepository.existsByEmpIdAndStatusIn(
                request.getEmpId(),
                List.of(BiometricStatus.APPROVED, BiometricStatus.PENDING))) {
            throw new BiometricRegistrationException(BiometricErrorCode.PENDING_REGISTRATION,
                    "Registration already submitted (" + request.getEmpId() + ")");
        }

       /* // Check if face already exists for any employee
        try {
            Integer existingEmpId = getEmployeeIdFromImage(request.getBase64Image(), Integer.parseInt(request.getUnitId()));
            if (existingEmpId != null) {
                throw new BiometricRegistrationException(BiometricErrorCode.FACE_ALREADY_EXISTS,
                        "Face already registered for employee ID: " + existingEmpId);
            }
        } catch (BiometricRegistrationException e) {
            // If no face match found, continue with registration
            if (!e.getErrorCode().equals(BiometricErrorCode.REGISTRATION_PROCESSING_ERROR)) {
                throw e;
            }
        }*/

        return employee;
    }

    /**
     * Creates a new biometric registration record
     */
    private BiometricRegistration createRegistrationRecord(BiometricRegistrationRequest request) {
        // Create registration record
        BiometricRegistration registration = new BiometricRegistration();
        registration.setEmpId(request.getEmpId());
        registration.setUnitId(JwtContext.getInstance().getUnitId().toString());
        registration.setDeviceId(request.getDeviceId());
        registration.setLatitude(request.getLatitude());
        registration.setLongitude(request.getLongitude());
        registration.setStatus(BiometricStatus.PENDING);
        registration.setCreatedBy(JwtContext.getInstance().getUserId().toString());

        // Upload images asynchronously
        CompletableFuture<String> processedImageFuture = asyncOperationService.uploadImageAsync(
                request.getBase64Image(),
                "biometric/" + request.getEmpId() + "/" + System.currentTimeMillis() + ".jpg"
        );

        CompletableFuture<String> originalImageFuture = null;
        if (request.getOriginalImage() != null) {
            originalImageFuture = asyncOperationService.uploadImageAsync(
                    request.getOriginalImage(),
                    "biometric/original/" + request.getEmpId() + "/" + System.currentTimeMillis() + ".jpg"
            );
        }

        // Wait for uploads to complete and set URLs
        try {
            String processedImageKey = processedImageFuture.get();
            registration.setImageUrl(s3Service.getCloudfrontUrl(processedImageKey));

            if (originalImageFuture != null) {
                String originalImageKey = originalImageFuture.get();
                registration.setOriginalImageUrl(s3Service.getCloudfrontUrl(originalImageKey));
            } else {
                registration.setOriginalImageUrl(registration.getImageUrl());
            }
        } catch (Exception e) {
            log.error("Failed to complete image uploads: {}", e.getMessage());
            throw new BiometricRegistrationException(BiometricErrorCode.REGISTRATION_PROCESSING_ERROR,
                    "Failed to complete image uploads: " + e.getMessage());
        }

        // Save registration and invalidate cache
        registration = biometricRegistrationRepository.save(registration);
        //biometricService.handleRegistrationStatusChange(registration.getEmpId(), registration.getStatus());

        return registration;
    }

    /**
     * Creates and starts the approval workflow
     */
    private ApprovalRequest createApprovalRequest(BiometricRegistrationRequest request, BiometricRegistration registration) {
        // Get approvers from eligibility mapping
        List<ApproverInfo> approvers = empEligibilityService.getApprovers(
                EligibilityType.APPROVAL,
                Integer.valueOf(registration.getUnitId())
        );

        if (approvers.isEmpty()) {
            throw new BiometricRegistrationException(BiometricErrorCode.APPROVAL_METADATA_ERROR,
                    "No approvers found for biometric registration");
        }

        // Create approval request
        return buildApprovalRequest(request, registration, approvers);
    }

    /**
     * Builds the approval request object
     */
    private ApprovalRequest buildApprovalRequest(
            BiometricRegistrationRequest request,
            BiometricRegistration registration,
            List<ApproverInfo> approvers
    ) {
        try {
            Map<String, String> metadata = Map.of(
                    "biometricRegistrationId", registration.getId().toString(),
                    "biometricId", "csscs",
                    "imageUrl", registration.getOriginalImageUrl(),
                    "croppedImageUrl" , registration.getImageUrl(),
                    "approvers", approvers.stream().map(approver -> approver.getId().toString()).collect(Collectors.joining(","))
            );

            LocalDateTime now = DateTimeUtil.now();
            return ApprovalRequest.builder()
                    .requestType(ApprovalType.BIOMETRIC_REGISTRATION)
                    .requesterId(Long.parseLong(request.getEmpId()))
                    .status(ApprovalStatus.PENDING)
                    .currentStep(1)
                    .totalSteps(1)
                    .requestDate(now)
                    .createdDate(now)
                    .createdBy(JwtContext.getInstance().getUserId().toString())
                    .referenceId(registration.getId())
                    .unitId(Long.valueOf(JwtContext.getInstance().getUnitId()))
                    .metadata(objectMapper.writeValueAsString(metadata))
                    .build();
        } catch (JsonProcessingException e) {
            throw new BiometricRegistrationException(BiometricErrorCode.APPROVAL_METADATA_ERROR);
        }
    }

    /**
     * Builds the registration response
     */
    private BiometricRegistrationActionResponse buildRegistrationResponse(
            BiometricRegistrationRequest request,
            EmployeeBasicDetail employee,
            BiometricRegistration registration,
            ApprovalRequest approvalRequest
    ) {
        return BiometricRegistrationActionResponse.builder()
                .empId(request.getEmpId())
                .empName(employee.getName())
                .empCode(employee.getEmployeeCode())
                .action("REGISTER")
                .status(ApprovalStatus.PENDING.name())
                .message("Biometric registration request submitted successfully")
                .actionTime(DateTimeUtil.now())
                .actionBy(request.getEmpId())
                //.requestId(approvalRequest.getId().toString())
                .imageUrl(registration.getImageUrl())
                .registrationImageUrl(employee.getImagekey())
                .biometricRegistrationId(registration.getId())
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class , readOnly = false)
    public BiometricRegistrationActionResponse deregisterBiometric(String empId) {
        log.info("Processing biometric deregistration for employee: {}", empId);

        // Validate employee exists
        EmployeeBasicDetail employee = userCacheService.getUserById(Integer.parseInt(empId));
        if (employee == null) {
            throw new BiometricRegistrationException(BiometricErrorCode.EMPLOYEE_NOT_FOUND,
                    "Employee not found with ID: " + empId);
        }

        try {
            BiometricRegistration registration = biometricRegistrationRepository
                    .findByEmpIdAndStatus(empId, BiometricStatus.APPROVED)
                    .orElseThrow(() -> new BiometricRegistrationException(BiometricErrorCode.NO_ACTIVE_REGISTRATION,
                            "No approved biometric registration found for employee"));

            // Deregister from biometric service
            try {
                BiometricDeregistrationResponse deregResponse = biometricService.deregisterFace(
                        registration.getEmpId(),
                        registration.getUnitId(),true
                );

                if (!"success".equals(deregResponse.getStatus())) {
                    throw new BiometricRegistrationException(BiometricErrorCode.DEREGISTRATION_SERVICE_ERROR,
                            "Failed to deregister from biometric service: " + deregResponse.getStatus());
                }
            } catch (Exception e) {
                throw new BiometricRegistrationException(BiometricErrorCode.DEREGISTRATION_SERVICE_ERROR,
                        "Failed to deregister from biometric service: " + e.getMessage());
            }

            // Update registration status
            registration.setStatus(BiometricStatus.DEREGISTERED);
            registration.setUpdatedBy(JwtContext.getInstance().getUserId().toString());
            registration.setBiometricId(null);
            biometricRegistrationRepository.save(registration);

            log.info("Biometric deregistration completed successfully for employee: {}", empId);
            biometricService.handleRegistrationStatusChange(registration.getEmpId(), registration.getStatus());
            return BiometricRegistrationActionResponse.builder()
                    .empId(empId)
                    .empName(employee.getName())
                    .empCode(employee.getEmployeeCode())
                    .action("DEREGISTER")
                    .status("COMPLETED")
                    .message("Biometric deregistration completed successfully")
                    .actionTime(DateTimeUtil.now())
                    .actionBy(empId)
                    .requestId(registration.getId().toString())
                    .build();
        } catch (BiometricRegistrationException e) {
            throw e;
        } catch (Exception e) {
            log.error("Failed to process biometric deregistration: ", e);
            throw new BiometricRegistrationException(BiometricErrorCode.DEREGISTRATION_PROCESSING_ERROR,
                    "Failed to process biometric deregistration: " + e.getMessage());
        }
    }

    @Override
    public BiometricRegistrationResponse getBiometricStatus(String empId) {
        BiometricRegistration registration = biometricRegistrationRepository
                .findByEmpIdAndStatusNot(empId, BiometricStatus.DEREGISTERED)
                .orElseThrow(() -> new BiometricRegistrationException(BiometricErrorCode.NO_ACTIVE_REGISTRATION));

        return mapToResponse(registration);
    }

    private BiometricRegistrationResponse mapToResponse(BiometricRegistration registration) {
        return BiometricRegistrationResponse.builder()
                .empId(registration.getEmpId())
                .unitId(registration.getUnitId())
                .deviceId(registration.getDeviceId())
                .biometricId(registration.getBiometricId())
                .biometricUserId(registration.getBiometricUserId())
                .imageUrl(registration.getImageUrl())
                .registrationImageUrl(environmentProperties.getDefaultEmployeeImage())
                .latitude(registration.getLatitude())
                .longitude(registration.getLongitude())
                .status(registration.getStatus())
                .registrationDate(registration.getCreatedAt())
                .lastUpdatedDate(registration.getUpdatedAt())
                .registeredBy(registration.getCreatedBy())
                .lastUpdatedBy(registration.getUpdatedBy())
                .build();
    }

    @Override
    @Transactional
    public void handleApprovalResponse(Long approvalRequestId, ApprovalStatus status, String approverId) throws Exception {
        log.info("Handling approval response for request: {} with status: {}", approvalRequestId, status);

        ApprovalRequest approvalRequest = validateAndGetApprovalRequest(approvalRequestId);
        BiometricRegistration registration = getBiometricRegistrationFromMetadata(approvalRequest);

        if (ApprovalStatus.APPROVED.equals(status)) {
            handleApprovedStatus(registration);
        } else if (ApprovalStatus.REJECTED.equals(status)) {
            handleRejectedStatus(registration);
        }

        updateRegistrationStatus(registration, status, approverId);
        biometricService.handleRegistrationStatusChange(registration.getEmpId(), registration.getStatus());

        log.info("Updated biometric registration status to: {} for employee: {}", status, registration.getEmpId());
    }


    private ApprovalRequest validateAndGetApprovalRequest(Long approvalRequestId) {
        ApprovalRequest approvalRequest = approvalEngineService.getRequest(approvalRequestId);
        if (approvalRequest == null) {
            throw new BiometricRegistrationException(BiometricErrorCode.NO_ACTIVE_REGISTRATION, "Approval request not found");
        }

        if (approvalRequest.getRequestType() != ApprovalType.BIOMETRIC_REGISTRATION) {
            throw new BiometricRegistrationException(BiometricErrorCode.REGISTRATION_PROCESSING_ERROR, "Invalid approval request type");
        }

        return approvalRequest;
    }

    private BiometricRegistration getBiometricRegistrationFromMetadata(ApprovalRequest approvalRequest) {
        Map<String, String> metadata;
        try {
            metadata = new ObjectMapper().readValue(approvalRequest.getMetadata(), Map.class);
        } catch (JsonProcessingException e) {
            throw new BiometricRegistrationException(BiometricErrorCode.INVALID_BIOMETRIC_DATA, "Invalid metadata format");
        }

        String biometricRegistrationId = metadata.get("biometricRegistrationId");
        return biometricRegistrationRepository.findById(Long.parseLong(biometricRegistrationId))
                .orElseThrow(() -> new BiometricRegistrationException(BiometricErrorCode.NO_ACTIVE_REGISTRATION,
                        "Biometric registration not found"));
    }

    private void handleApprovedStatus(BiometricRegistration registration) {
        try {
            BiometricFaceActivationRequestDTO activationRequest = createActivationRequest(registration);
            BiometricRegistrationResponseDTO biometricResponse = activateFace(activationRequest);
            updateRegistrationWithBiometricData(registration, biometricResponse);
        } catch (Exception e) {
            log.error("Failed to activate face for employee: {}", registration.getEmpId(), e);
            throw new BiometricRegistrationException(BiometricErrorCode.REGISTRATION_PROCESSING_ERROR,
                    "Failed to activate face: " + e.getMessage());
        }
    }

    private void handleRejectedStatus(BiometricRegistration registration) {
        try {
            rejectRegistrationRequest(registration);
        } catch (Exception e) {
            log.error("Failed to deregister face for employee: {} after rejection", registration.getEmpId(), e);
            throw new BiometricRegistrationException(BiometricErrorCode.DEREGISTRATION_PROCESSING_ERROR,
                    "Failed to process face deregistration after rejection: " + e.getMessage());
        }
    }

    private BiometricFaceActivationRequestDTO createActivationRequest(BiometricRegistration registration) {
        return BiometricFaceActivationRequestDTO.builder()
                .employeeId(Integer.parseInt(registration.getEmpId()))
                .build();
    }

    private BiometricRegistrationResponseDTO activateFace(BiometricFaceActivationRequestDTO request) {
        log.info("Activating face for employee: {}", request.getEmployeeId());
        BiometricRegistrationResponseDTO response = biometricService.activateFace(request);
        validateBiometricResponse(response);
        return response;
    }

    private void validateBiometricResponse(BiometricRegistrationResponseDTO response) {
        if (response == null || response.getStatus() == null || !response.getStatus().equals("success")) {
            throw new BiometricRegistrationException(BiometricErrorCode.REGISTRATION_PROCESSING_ERROR);
        }
    }

    private void updateRegistrationWithBiometricData(BiometricRegistration registration,
            BiometricRegistrationResponseDTO response) {
        registration.setBiometricId(response.getResult().getBiometricId());
        registration.setBiometricUserId(response.getResult().getUserId());
    }

    private void rejectRegistrationRequest(BiometricRegistration registration) {
        log.info("Deregistering face for employee: {} due to rejection", registration.getEmpId());
        BiometricDeregistrationResponse deregResponse = biometricService.deregisterFace(
                registration.getEmpId(),
                registration.getUnitId(),false
        );

        validateDeregistrationResponse(deregResponse, registration.getEmpId());
        log.info("Successfully deregistered face for employee: {} after rejection", registration.getEmpId());
    }

    private void validateDeregistrationResponse(BiometricDeregistrationResponse response, String empId) {
        if (response == null || !"success".equals(response.getStatus())) {
            log.error("Failed to deregister face for employee: {} after rejection. Response: {}",
                    empId, response);
            throw new BiometricRegistrationException(BiometricErrorCode.DEREGISTRATION_SERVICE_ERROR,
                    "Failed to deregister face after rejection: " +
                    (response != null ? response.getStatus() : "null response"));
        }
    }

    private void updateRegistrationStatus(BiometricRegistration registration, ApprovalStatus status, String approverId) {
        if (status == ApprovalStatus.APPROVED) {
            registration.setStatus(BiometricStatus.APPROVED);
        } else if (status == ApprovalStatus.REJECTED) {
            registration.setStatus(BiometricStatus.REJECTED);
        }
        registration.setUpdatedBy(approverId);
        biometricRegistrationRepository.save(registration);
    }

    @Override
    public Integer getEmployeeIdFromImage(String base64image, Integer unitId) {
        BiometricIdentificationRequestDTO request = BiometricIdentificationRequestDTO.builder()
                .model_id("attendance")
                .parameters(BiometricIdentificationRequestDTO.Parameters.builder()
                        .metadata(BiometricIdentificationRequestDTO.Metadata.builder()
                                .unitId(unitId)
                                .build())
                        .unitId(unitId)
                        .build())
                .input_data(BiometricIdentificationRequestDTO.InputData.builder()
                        .data(base64image)
                        .build())
                .processing_options(new HashMap<>())
                .build();
        BiometricIdentificationResponseDTO response = biometricService.identifyFace(request);
        BiometricContext.setContext(response.getRequestId());
        if (response == null || response.getStatus() == null || !response.getStatus().equals("success")) {
            throw new BiometricRegistrationException(BiometricErrorCode.REGISTRATION_PROCESSING_ERROR, "Failed to Identify Employee");
        }
        log.info("response :{} " , new Gson().toJson(response));
        if(!StringUtils.isEmpty(response.getResult().getStatus())
                && AppConstants.IN_PROGRESS.equalsIgnoreCase(response.getResult().getStatus())){
            throw new BiometricRegistrationException(BiometricErrorCode.BIOMETRIC_IN_PROGRESS);
        }
        return Integer.parseInt(response.getResult().getEmployeeId());
    }



    @Override
    @Transactional(rollbackFor = Exception.class , readOnly = false)
    public BiometricRegistrationActionResponse verifyAndRegisterBiometric(BiometricVerificationRequest request) {
        log.info("Processing temporary verification for employee: {}", request.getEmpId());
        BiometricTempRegistrationResponseDTO response;
        try {
            // Create temporary registration request for verification
            BiometricTempRegistrationRequestDTO tempRequest = BiometricTempRegistrationRequestDTO.builder()
                    .model_id("attendance")
                    .parameters(BiometricTempRegistrationRequestDTO.Parameters.builder()
                            .metadata(BiometricTempRegistrationRequestDTO.Metadata.builder()
                                    .employeeId(Integer.parseInt(request.getEmpId()))
                                    .build())
                            .build())
                    .input_data(BiometricTempRegistrationRequestDTO.InputData.builder()
                            .data(request.getBase64Image())
                            .build())
                    .extra_reg(buildExtraRegDataForVerify(request.getAdditionalImages()))
                    .processing_options(new HashMap<>())
                    .verification_face(request.getVerificationFace())
                    .build();

            // Call temporary registration verification
            response = biometricService.verifyTempRegistration(tempRequest);
            log.info("response : {} " , new Gson().toJson(response));
        } catch (Exception e) {
            log.error("Error during temporary verification for employee: {}", request.getEmpId(), e);
            throw new BiometricRegistrationException(BiometricErrorCode.REGISTRATION_PROCESSING_ERROR,
                    "Failed to verify and register biometric for employee: " + request.getEmpId());
        }
            // Process the response
            if ("success".equals(response.getStatus())) {

                // If verification successful and face matched, proceed with regular registration
                BiometricRegistrationRequest registrationRequest = new BiometricRegistrationRequest();
                registrationRequest.setEmpId(request.getEmpId());
                registrationRequest.setUnitId(request.getUnitId());
                registrationRequest.setBase64Image(request.getBase64Image());
                registrationRequest.setLongitude(request.getLongitude());
                registrationRequest.setLatitude(request.getLatitude());
                registrationRequest.setDeviceId(request.getDeviceId());
                registrationRequest.setOriginalImage(request.getOriginalImage());
                registrationRequest.setAdditionalImages(request.getAdditionalImages());

                BiometricRegistrationActionResponse result = registerBiometric(registrationRequest);

                // Save additional images if provided (async operation)
                if (CollectionUtils.isNotEmpty(request.getAdditionalImages())) {
                    // Start async processing of additional images using AsyncOperationService
                    asyncOperationService.saveAdditionalImagesAsync(
                        request.getAdditionalImages(),
                        result.getBiometricRegistrationId()
                    );

                    // Log the start of async processing (don't wait for completion)
                    log.info("Started async processing of {} additional images for biometric registration ID: {}",
                            request.getAdditionalImages().size(), result.getBiometricRegistrationId());
                }
                
                return result;
            }
        if(Objects.nonNull(response.getResult()) && Boolean.TRUE.equals(response.getResult().getMatched_existing_face())){
            if(Objects.isNull(response.getResult().getEmployee_id())){
                throw new BiometricRegistrationException(BiometricErrorCode.FACE_ALREADY_EXISTS);
            }
            Integer empId = response.getResult().getEmployee_id();
            List<Integer> employees = empEligibilityService.getEmployeesForUnit(Integer.valueOf(request.getUnitId()));
            log.info("employees for unit: {} are : {} " ,request.getUnitId() ,employees);
            if(employees.contains(empId)){
                throw new BiometricRegistrationException(BiometricErrorCode.FACE_ALREADY_EXISTS,
                "Face already registered With employee (" + userCacheService.getUserById(response.getResult().getEmployee_id()).getEmployeeCode() + ")");
            }else{
                log.info("Face matched with different employee in same unit. Proceeding with registration for" +
                                " matched with employee: {}  , original employee: {} ",
                         empId , request.getEmpId());
                // If verification successful and face matched, proceed with regular registration
                BiometricRegistrationRequest registrationRequest = new BiometricRegistrationRequest();
                registrationRequest.setEmpId(request.getEmpId());
                registrationRequest.setUnitId(request.getUnitId());
                registrationRequest.setBase64Image(request.getBase64Image());
                registrationRequest.setLongitude(request.getLongitude());
                registrationRequest.setLatitude(request.getLatitude());
                registrationRequest.setDeviceId(request.getDeviceId());
                registrationRequest.setOriginalImage(request.getOriginalImage());
                registrationRequest.setAdditionalImages(request.getAdditionalImages());


                return registerBiometric(registrationRequest);
            }
        }else{
            throw new BiometricRegistrationException(BiometricErrorCode.REGISTRATION_PROCESSING_ERROR);
        }

    }
    
    /**
     * Build extra_reg data for biometric service API call
     * Converts additional images to the format expected by biometric service
     * @param additionalImages List of additional images from request
     * @return List of ExtraRegData objects
     */
    private List<BiometricRegistrationRequestDTO.ExtraRegData> buildExtraRegData(List<BiometricAdditionalImageDTO> additionalImages) {
        if (CollectionUtils.isEmpty(additionalImages)) {
            return null;
        }
        
        return additionalImages.stream()
                .map(image -> BiometricRegistrationRequestDTO.ExtraRegData.builder()
                        .data(image.getBase64Image())
                        .metadata(Map.of("type", image.getType()))
                        .build())
                .toList();
    }


    private List<BiometricTempRegistrationRequestDTO.ExtraRegData> buildExtraRegDataForVerify(List<BiometricAdditionalImageDTO> additionalImages) {
        if (CollectionUtils.isEmpty(additionalImages)) {
            return null;
        }

        return additionalImages.stream()
                .map(image -> BiometricTempRegistrationRequestDTO.ExtraRegData.builder()
                        .data(image.getBase64Image())
                        .metadata(Map.of("type", image.getType()))
                        .build())
                .toList();
    }


}