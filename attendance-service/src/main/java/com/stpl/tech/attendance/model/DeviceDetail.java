package com.stpl.tech.attendance.model;

import lombok.Builder;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;

@Data
@Builder
@Document(collection = "device_details")
public class DeviceDetail {
    @Id
    private String id;
    private String deviceId;
    private String os;
    private String version;
    private String unitId;
    private LocalDateTime createdAt;
    private LocalDateTime lastUpdatedAt;
} 