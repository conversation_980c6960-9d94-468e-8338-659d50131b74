package com.stpl.tech.attendance.enums;

public enum ApprovalStepStatus {
    PENDING,        // Step is created but not yet started
    IN_PROGRESS,    // Step is active and waiting for approval
    APPROVED,       // Step is approved
    REJECTED,       // Step is rejected
    SKIPPED,        // Step is skipped (e.g., in parallel approval when enough approvals are received)
    EXPIRED,        // Step has exceeded its validity period
    CANCELLED       // Step is cancelled along with the request
} 