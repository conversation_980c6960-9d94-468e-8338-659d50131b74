package com.stpl.tech.attendance.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Getter
public class BiometricServiceEndpoints extends ServiceEndpoints {

    private static final String MODEL_SERVICE = "model-service";
    private static final String REGISTER_FACE = "register-face";
    private static final String RECOGNISE_FACE = "recognise-face";
    private static final String DE_REGISTER_FACE = "de-register-face";
    private static final String REGISTER_TEMP = "register-temp";
    private static final String UPDATE_IMG_PATH = "update-img-path";
    private static final String UPDATE_IMG_PATH_V2 = "update-img-path-v2";
    private static final String ACTIVATE_FACE = "activate-face";
    private static final String IDENTIFY_MULTIPLE_FACES = "recognise-face-v2";

    @Value("${biometric.service.base-url:http://**************:8080}")
    @Override
    public void setBaseUrl(String baseUrl) {
        super.baseUrl = baseUrl;
    }

    public String getRegistrationUrl() {
        return buildUrl(MODEL_SERVICE, REGISTER_FACE);
    }

    public String getDeRegisterFaceUrl() {
        return buildUrl(MODEL_SERVICE, DE_REGISTER_FACE);
    }

    public String getIdentifyFaceUrl() {
        return buildUrl(MODEL_SERVICE, RECOGNISE_FACE);
    }

    public String getIdentifyMultipleFacesUrl() {
        return buildUrl(MODEL_SERVICE, IDENTIFY_MULTIPLE_FACES);
    }

    public String getRegisterTempUrl() {
        return buildUrl(MODEL_SERVICE, REGISTER_TEMP);
    }

    public String getUpdateImgPathUrl() {
        return buildUrl(MODEL_SERVICE, UPDATE_IMG_PATH);
    }

    public String getUpdateImgPathV2Url() {
        return buildUrl(MODEL_SERVICE, UPDATE_IMG_PATH_V2);
    }

    public String getActivateFaceUrl() {
        return buildUrl(MODEL_SERVICE, ACTIVATE_FACE);
    }
} 