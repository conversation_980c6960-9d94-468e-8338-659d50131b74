package com.stpl.tech.attendance.config;

import com.stpl.tech.attendance.security.JwtAuthenticationFilter;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;

@Configuration
@EnableWebSecurity
@EnableMethodSecurity
@RequiredArgsConstructor
public class SecurityConfig {

    private final JwtAuthenticationFilter jwtAuthenticationFilter;

    @Value("${app.security.enabled:true}")
    private boolean securityEnabled;

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        // Always disable CSRF and frame options
        http
            .csrf(csrf -> csrf.disable())
            .headers(headers -> headers.frameOptions().disable())
            .sessionManagement(session -> session
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS));

        if (!securityEnabled) {
            // Completely disable security for testing
            http.authorizeHttpRequests(auth -> auth
                .anyRequest().permitAll()
            );
        } else {
            // Original security configuration
            http.authorizeHttpRequests(auth -> auth
                // Swagger UI and API docs
                .requestMatchers(
                    new AntPathRequestMatcher("/api/v1/swagger-ui/**"),
                    new AntPathRequestMatcher("/api/v1/api-docs/**"),
                    new AntPathRequestMatcher("/api/v1/swagger-ui.html"),
                    new AntPathRequestMatcher("/swagger-ui/**"),
                    new AntPathRequestMatcher("/api-docs/**"),
                    new AntPathRequestMatcher("/swagger-ui.html")
                ).permitAll()
                // Actuator endpoints
                .requestMatchers(
                    new AntPathRequestMatcher("/api/v1/actuator/**"),
                    new AntPathRequestMatcher("/actuator/**")
                ).permitAll()
                // Flowable UI endpoints
                .requestMatchers(
                    new AntPathRequestMatcher("/api/v1/task/**"),
                    new AntPathRequestMatcher("/api/v1/admin/**"),
                    new AntPathRequestMatcher("/api/v1/modeler/**"),
                    new AntPathRequestMatcher("/api/v1/rest/**"),
                    new AntPathRequestMatcher("/api/v1/flowable/**"),
                    new AntPathRequestMatcher("/api/v1/error"),
                    new AntPathRequestMatcher("/error"),
                    new AntPathRequestMatcher("/api/v1/task"),
                    new AntPathRequestMatcher("/api/v1/admin"),
                    new AntPathRequestMatcher("/api/v1/modeler")
                ).permitAll()
                // Auth endpoints
                .requestMatchers("/api/v1/auth/**").permitAll()
                .requestMatchers("/api/v1/public/**").permitAll()
                .anyRequest().authenticated()
            )
            .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);
        }
        
        return http.build();
    }
} 