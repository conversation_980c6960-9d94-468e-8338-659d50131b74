package com.stpl.tech.attendance.cache.service.impl;

import com.google.common.base.Stopwatch;
import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import com.stpl.tech.attendance.cache.constants.CacheConstants;
import com.stpl.tech.attendance.cache.exception.DataNotFoundInHazelCastException;
import com.stpl.tech.attendance.cache.service.UnitCacheService;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class UnitCacheServiceImpl implements UnitCacheService {
    private static final Logger LOG = LoggerFactory.getLogger(UnitCacheServiceImpl.class);

    @Autowired
    private HazelcastInstance instance;

    // Local maps to avoid cache eviction issues with IMap references
    private Map<Integer, com.stpl.tech.master.domain.model.Unit> allUnitTrimDataMap;
    private Map<Integer, UnitBasicDetail> unitsBasicDetails;

//    @PostConstruct
//    public void init() {
//        // Initialize local maps by copying data from Hazelcast cache
//        IMap<Integer, com.stpl.tech.master.domain.model.Unit> hazelcastUnitsMap = instance.getMap(CacheConstants.UNITS_CACHE);
//        IMap<Integer, UnitBasicDetail> hazelcastBasicDetailsMap = instance.getMap(CacheConstants.UNITS_BASIC_DETAILS_CACHE);
//
//        this.allUnitTrimDataMap = new HashMap<>(hazelcastUnitsMap);
//        this.unitsBasicDetails = new HashMap<>(hazelcastBasicDetailsMap);
//
//        LOG.info("Initialized unit cache maps with {} units and {} basic details",
//                allUnitTrimDataMap.size(), unitsBasicDetails.size());
//    }

    /**
     * Refreshes local maps from Hazelcast cache
     * This method should be called when the main cache is updated
     */
    public void refreshLocalMaps() {
        LOG.info("Refreshing local unit cache maps");
        IMap<Integer, com.stpl.tech.master.domain.model.Unit> hazelcastUnitsMap = instance.getMap(CacheConstants.UNITS_CACHE);
        IMap<Integer, UnitBasicDetail> hazelcastBasicDetailsMap = instance.getMap(CacheConstants.UNITS_BASIC_DETAILS_CACHE);

        this.allUnitTrimDataMap = new HashMap<>(hazelcastUnitsMap);
        this.unitsBasicDetails = new HashMap<>(hazelcastBasicDetailsMap);

        LOG.info("Refreshed local cache maps with {} units and {} basic details",
                allUnitTrimDataMap.size(), unitsBasicDetails.size());
    }

    @Override
    //@Cacheable(value = "unitsCache")
    public Map<Integer, Unit> getAllUnitCache() {
        LOG.info("Fetching all units detail");
        Stopwatch watch = Stopwatch.createUnstarted();
        watch.start();
        if (Objects.isNull(allUnitTrimDataMap) || allUnitTrimDataMap.isEmpty()) {
            throw new DataNotFoundInHazelCastException("Unit detail not found");
        }
        LOG.info("Loaded Unit Cache in {} ms", watch.stop().elapsed(TimeUnit.MILLISECONDS));
        return new HashMap<>(allUnitTrimDataMap);
    }

    @Override
    public com.stpl.tech.master.domain.model.Unit getUnitById(Integer unitId) {
        LOG.info("Fetching unit basic details for unit id {}", unitId);
        Map<Integer, com.stpl.tech.master.domain.model.Unit> unitMap = getAllUnitCache();
        Stopwatch watch = Stopwatch.createUnstarted();
        watch.start();
        com.stpl.tech.master.domain.model.Unit unitData = unitMap.get(unitId);
        LOG.info("Loaded Unit Cache For Unit Id : {} in {} ms", unitId, watch.stop().elapsed(TimeUnit.MILLISECONDS));

        if (Objects.isNull(unitData)) {
            throw new DataNotFoundInHazelCastException("Data not found for unit id " + unitId);
        }
        return unitData;
    }

    /**
     * Fetches unit basic details by unit ID from the Hazelcast cache
     * @param unitId The unit ID to fetch details for
     * @return UnitBasicDetail object if found, null otherwise
     */
    @Override
    public UnitBasicDetail getUnitBasicDetail(Integer unitId) {
        try {
            if (unitId == null) {
                LOG.warn("Attempted to fetch unit details with null unitId");
                return null;
            }

            UnitBasicDetail unitDetail = getAllUnitBasicDetailMap().get(unitId);
            
            if (unitDetail == null) {
                LOG.warn("No unit details found in cache for unitId: {}", unitId);
            } else {
                LOG.debug("Successfully retrieved unit details for unitId: {}", unitId);
            }

            return unitDetail;
        } catch (Exception e) {
            LOG.error("Error fetching unit details for unitId: {}", unitId, e);
            return null;
        }
    }

    @Override
    public List<UnitBasicDetail> getAllUnitBasicDetail() {
        try {
            return (List<UnitBasicDetail>) getAllUnitBasicDetailMap().values();
        } catch (Exception e) {
            LOG.error("Error fetching unit basic details: {}",e);
            return null;
        }
    }

    @Override
    //@Cacheable(value = "unitsBasicDetailsMap")
    public Map<Integer, UnitBasicDetail> getAllUnitBasicDetailMap() {
        if (Objects.isNull(unitsBasicDetails) || unitsBasicDetails.isEmpty()) {
            throw new DataNotFoundInHazelCastException("Unit basic details not found");
        }
        return new HashMap<>(unitsBasicDetails);
    }


    @Override
    //@CacheEvict(value = "unitsBasicDetailsMap")
    public void evictAllUnitBasicDetail() {
        LOG.info("Evicting all unit basic details cache");
        // Refresh local maps after eviction
        //refreshLocalMaps();
    }


    @Override
    //@CacheEvict(value = "unitsCache")
    public void evictUnitsCache() {
        LOG.info("Evicting unit cache");
        // Refresh local maps after eviction
        refreshLocalMaps();
    }

    @Override
    public List<Integer> getUnitsByCity(String city) {
        try {
            if (city == null || city.trim().isEmpty()) {
                LOG.warn("City parameter is null or empty");
                return List.of();
            }

            List<Integer> unitsInCity = getAllUnitBasicDetail().stream()
                    .filter(unit -> city.equalsIgnoreCase(unit.getLocationCode()))
                    .map(UnitBasicDetail::getId)
                    .toList();

            LOG.debug("Found {} units in city: {}", unitsInCity.size(), city);
            return unitsInCity;
        } catch (Exception e) {
            LOG.error("Error fetching units by city: {}", city, e);
            return List.of();
        }
    }

    @Override
    public List<Integer> getUnitsByRegion(String region) {
        try {
            if (region == null || region.trim().isEmpty()) {
                LOG.warn("Region parameter is null or empty");
                return List.of();
            }

            List<Integer> unitsInRegion = getAllUnitBasicDetail().stream()
                    .filter(unit -> region.equalsIgnoreCase(unit.getUnitZone()))
                    .map(UnitBasicDetail::getId)
                    .toList();

            LOG.debug("Found {} units in region: {}", unitsInRegion.size(), region);
            return unitsInRegion;
        } catch (Exception e) {
            LOG.error("Error fetching units by region: {}", region, e);
            return List.of();
        }
    }


}