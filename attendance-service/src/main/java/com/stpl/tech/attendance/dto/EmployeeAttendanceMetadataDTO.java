package com.stpl.tech.attendance.dto;

import com.stpl.tech.attendance.constants.AppConstants;
import com.stpl.tech.attendance.entity.EmployeeAttendanceEnum;
import com.stpl.tech.attendance.entity.EmpAttendanceBalanceData;
import com.stpl.tech.attendance.repository.EmpAttendanceReserveDataRepository;
import com.stpl.tech.attendance.service.impl.AttendanceRequestUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Arrays;
import java.util.Map;

/**
 * DTO for employee attendance metadata
 * Contains various attendance-related options and configurations for UI
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmployeeAttendanceMetadataDTO {
    
    /**
     * Available regularisation reasons as display names
     */
    private List<String> regularisationReasons;
    
    /**
     * Available day types for leave applications as display names
     */
    private List<String> dayTypes;
    
    /**
     * Available OD/WFH options as display names
     */
    private List<String> odWfhOptions;

    /**
     * Employee leave details with balance information
     * Using BigDecimal to support decimal values like 10.5 with precision
     */
    private Map<String, BigDecimal> leaveDetails;
    
    /**
     * View permissions for different attendance features
     */
    private ViewPermissionsDTO viewPermissions;

    /**
     * List of holidays for the current financial year
     */
    private List<HolidayResponse> holidays;

    private BigDecimal pendingLeave;

    private String weekOffDay;

    private Integer salaryProcessingEndDate;



    /**
     * DTO for view permissions
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ViewPermissionsDTO {
        private Boolean regularisationView;
        private Boolean applyManagerOdView;
        private Boolean applyLeaveView;
        private Boolean applyEmpOdView;
        private Boolean compOffView;
        
        // New fields for holiday and weekend permissions
        private Boolean isFixedWeekendAllowed;
        private Boolean holidaysAllowed;
        private String[] weekendDays;
    }

    public static EmployeeAttendanceMetadataDTO createAttendanceMetadata(EmpAttendanceBalanceData empAttendanceBalanceData, EmpAttendanceReserveDataRepository empAttendanceReserveDataRepository) {
        return EmployeeAttendanceMetadataDTO.builder()
                .regularisationReasons(createRegularisationReasons())
                .dayTypes(createDayTypes())
                .odWfhOptions(createOdWfhOptions())
                .leaveDetails(AttendanceRequestUtil.createLeaveDetailsFromDatabase(empAttendanceBalanceData, empAttendanceReserveDataRepository))
                .pendingLeave(empAttendanceBalanceData != null ? empAttendanceReserveDataRepository.getReservedCountByEmpAttendanceBalanceIdAndTypes(empAttendanceBalanceData.getId(), List.of("LEAVE", "LWP", "COMP_OFF")) : BigDecimal.ZERO)
                .viewPermissions(createDefaultViewPermissions())
                .holidays(createDefaultHolidays())
                .build();
    }
    
    /**
     * Create attendance metadata with department-specific permissions
     * @param empLeaveData Employee leave data
     * @param empLeaveDetailDataRepository Leave detail repository
     * @param deptId Department ID for metadata lookup
     * @param metadataService Metadata service for department-specific values
     * @return EmployeeAttendanceMetadataDTO with department-specific permissions
     */
    public static EmployeeAttendanceMetadataDTO createAttendanceMetadataWithDept(
            EmpAttendanceBalanceData empLeaveData,
            EmpAttendanceReserveDataRepository empLeaveDetailDataRepository,
            Integer deptId,
            com.stpl.tech.attendance.service.AttendanceMetadataService metadataService,
            com.stpl.tech.attendance.service.HolidayService holidayService) {

        assert empLeaveData != null;
        return EmployeeAttendanceMetadataDTO.builder()
                .regularisationReasons(createRegularisationReasons())
                .dayTypes(createDayTypes())
                .odWfhOptions(createOdWfhOptions())
                .leaveDetails(AttendanceRequestUtil.createLeaveDetailsFromDatabase(empLeaveData, empLeaveDetailDataRepository))
                .pendingLeave(empLeaveDetailDataRepository.getReservedCountByEmpAttendanceBalanceIdAndTypes(empLeaveData.getId(), AppConstants.LEAVE_TYPES))
                .viewPermissions(createViewPermissionsWithDept(deptId, metadataService))
                .weekOffDay(empLeaveData.getWeekOffDay())
                .holidays(holidayService != null ? holidayService.getCurrentFinancialYearHolidays() : createDefaultHolidays())
                .salaryProcessingEndDate(metadataService.getPayrollProcessingEndDay(deptId).intValue())
                .build();
    }
    
    /**
     * Create regularisation reasons as display names
     */
    private static List<String> createRegularisationReasons() {
        return Arrays.stream(EmployeeAttendanceEnum.RegularisationReason.values())
                .map(EmployeeAttendanceEnum.RegularisationReason::getValue)
                .toList();
    }
    
    /**
     * Create day types as display names
     */
    private static List<String> createDayTypes() {
        return Arrays.stream(EmployeeAttendanceEnum.DayType.values())
                .map(EmployeeAttendanceEnum.DayType::getValue)
                .toList();
    }
    
    /**
     * Create OD/WFH options as display names
     */
    private static List<String> createOdWfhOptions() {
        return Arrays.stream(EmployeeAttendanceEnum.AttendanceType.values())
                .map(EmployeeAttendanceEnum.AttendanceType::getValue)
                .toList();
    }


    /**
     * Create default view permissions
     */
    private static ViewPermissionsDTO createDefaultViewPermissions() {
        return ViewPermissionsDTO.builder()
                .regularisationView(true)
                .applyManagerOdView(true)
                .applyLeaveView(true)
                .applyEmpOdView(true)
                .compOffView(true)
                .isFixedWeekendAllowed(true)
                .holidaysAllowed(true)
                .weekendDays(new String[]{AppConstants.SATURDAY, AppConstants.SUNDAY})
                .build();
    }
    
    /**
     * Create view permissions with department-specific metadata
     */
    private static ViewPermissionsDTO createViewPermissionsWithDept(
            Integer deptId, 
            com.stpl.tech.attendance.service.AttendanceMetadataService metadataService) {
        
        if (metadataService == null) {
            return createDefaultViewPermissions();
        }
        
        return ViewPermissionsDTO.builder()
                .regularisationView(metadataService.isRegularisationAllowed(deptId))
                .applyManagerOdView(metadataService.isOdAllowed(deptId))
                .applyLeaveView(metadataService.isLeaveAllowed(deptId))
                .applyEmpOdView(metadataService.isOdAllowed(deptId)) // Assuming same as OD
                .compOffView(true) // Default to true for comp off
                .isFixedWeekendAllowed(metadataService.isFixedWeekendAllowed(deptId))
                .holidaysAllowed(metadataService.isHolidayAllowed(deptId))
                .weekendDays(metadataService.getWeekendDays(deptId))
                .build();
    }
    
    /**
     * Create default holidays list
     */
    private static List<HolidayResponse> createDefaultHolidays() {
        return List.of(); // Return empty list for default
    }
}