package com.stpl.tech.attendance.service.impl;

import com.stpl.tech.attendance.config.WebClientConfig;
import com.stpl.tech.attendance.exception.HttpServiceException;
import com.stpl.tech.attendance.service.HttpService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.core.publisher.Mono;

import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class HttpServiceImpl implements HttpService {

    private final WebClient defaultWebClient;
    private final WebClientConfig webClientConfig;

    // Default timeout methods
    @Override
    public <T> Mono<T> get(String url, Class<T> responseType) {
        return get(url, responseType, null, null);
    }

    @Override
    public <T> Mono<T> get(String url, Class<T> responseType, Map<String, String> headers) {
        return get(url, responseType, headers, null);
    }

    @Override
    public <T> Mono<T> get(String url, Class<T> responseType, Map<String, String> headers, Map<String, String> queryParams) {
        log.debug("Making GET request to: {}", url);
        return defaultWebClient.get()
                .uri(uriBuilder -> {
                    uriBuilder.path(url);
                    if (queryParams != null) {
                        queryParams.forEach(uriBuilder::queryParam);
                    }
                    return uriBuilder.build();
                })
                .headers(h -> addHeaders(h, headers))
                .retrieve()
                .bodyToMono(responseType)
                .doOnError(this::handleError);
    }

    @Override
    public <T, R> Mono<R> post(String url, T request, Class<R> responseType) {
        return post(url, request, responseType, null);
    }

    @Override
    public <T, R> Mono<R> post(String url, T request, Class<R> responseType, Map<String, String> headers) {
        log.debug("Making POST request to: {}", url);
        return defaultWebClient.post()
                .uri(url)
                .headers(h -> addHeaders(h, headers))
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .retrieve()
                .bodyToMono(responseType)
                .doOnError(this::handleError);
    }

    @Override
    public <T, R> Mono<R> post(String url, T request, Class<R> responseType, Map<String, String> headers,
                               Map<String, String> queryParams) {
        log.debug("Making POST request to: {} with query params", url);
        String finalUrl = buildUrlWithQueryParams(url, queryParams);
        return defaultWebClient.post()
                .uri(finalUrl)
                .headers(h -> addHeaders(h, headers))
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .retrieve()
                .bodyToMono(responseType)
                .doOnError(this::handleError);
    }

    private String buildUrlWithQueryParams(String baseUrl, Map<String, String> queryParams) {
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(baseUrl);
        if (queryParams != null) {
            queryParams.forEach(builder::queryParam);
        }
        return builder.toUriString();
    }

    @Override
    public <T> Mono<Void> put(String url, T request) {
        return put(url, request, null);
    }

    @Override
    public <T> Mono<Void> put(String url, T request, Map<String, String> headers) {
        log.debug("Making PUT request to: {}", url);
        return defaultWebClient.put()
                .uri(url)
                .headers(h -> addHeaders(h, headers))
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .retrieve()
                .toBodilessEntity()
                .then()
                .doOnError(this::handleError);
    }

    @Override
    public Mono<Void> delete(String url) {
        return delete(url, null, null);
    }

    @Override
    public Mono<Void> delete(String url, Map<String, String> headers) {
        return delete(url, headers, null);
    }

    @Override
    public Mono<Void> delete(String url, Map<String, String> headers, Map<String, String> queryParams) {
        log.debug("Making DELETE request to: {}", url);
        return defaultWebClient.delete()
                .uri(uriBuilder -> {
                    uriBuilder.path(url);
                    if (queryParams != null) {
                        queryParams.forEach(uriBuilder::queryParam);
                    }
                    return uriBuilder.build();
                })
                .headers(h -> addHeaders(h, headers))
                .retrieve()
                .toBodilessEntity()
                .then()
                .doOnError(this::handleError);
    }

    // Custom timeout methods
    @Override
    public <T> Mono<T> getWithTimeout(String url, Class<T> responseType, int timeoutMillis) {
        return getWithTimeout(url, responseType, null, null, timeoutMillis);
    }

    @Override
    public <T> Mono<T> getWithTimeout(String url, Class<T> responseType, Map<String, String> headers, int timeoutMillis) {
        return getWithTimeout(url, responseType, headers, null, timeoutMillis);
    }

    @Override
    public <T> Mono<T> getWithTimeout(String url, Class<T> responseType, Map<String, String> headers, Map<String, String> queryParams, int timeoutMillis) {
        log.debug("Making GET request with {}ms timeout to: {}", timeoutMillis, url);
        WebClient customWebClient = webClientConfig.createWebClient(timeoutMillis, timeoutMillis, timeoutMillis, timeoutMillis);
        return customWebClient.get()
                .uri(uriBuilder -> {
                    uriBuilder.path(url);
                    if (queryParams != null) {
                        queryParams.forEach(uriBuilder::queryParam);
                    }
                    return uriBuilder.build();
                })
                .headers(h -> addHeaders(h, headers))
                .retrieve()
                .bodyToMono(responseType)
                .doOnError(this::handleError);
    }

    @Override
    public <T, R> Mono<R> postWithTimeout(String url, T request, Class<R> responseType, int timeoutMillis) {
        return postWithTimeout(url, request, responseType, null, timeoutMillis);
    }

    @Override
    public <T, R> Mono<R> postWithTimeout(String url, T request, Class<R> responseType, Map<String, String> headers, int timeoutMillis) {
        log.debug("Making POST request with {}ms timeout to: {}", timeoutMillis, url);
        WebClient customWebClient = webClientConfig.createWebClient(timeoutMillis, timeoutMillis, timeoutMillis, timeoutMillis);
        return customWebClient.post()
                .uri(url)
                .headers(h -> addHeaders(h, headers))
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .retrieve()
                .bodyToMono(responseType)
                .doOnError(this::handleError);
    }

    @Override
    public <T> Mono<Void> putWithTimeout(String url, T request, int timeoutMillis) {
        return putWithTimeout(url, request, null, timeoutMillis);
    }

    @Override
    public <T> Mono<Void> putWithTimeout(String url, T request, Map<String, String> headers, int timeoutMillis) {
        log.debug("Making PUT request with {}ms timeout to: {}", timeoutMillis, url);
        WebClient customWebClient = webClientConfig.createWebClient(timeoutMillis, timeoutMillis, timeoutMillis, timeoutMillis);
        return customWebClient.put()
                .uri(url)
                .headers(h -> addHeaders(h, headers))
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .retrieve()
                .toBodilessEntity()
                .then()
                .doOnError(this::handleError);
    }

    @Override
    public Mono<Void> deleteWithTimeout(String url, int timeoutMillis) {
        return deleteWithTimeout(url, null, null, timeoutMillis);
    }

    @Override
    public Mono<Void> deleteWithTimeout(String url, Map<String, String> headers, int timeoutMillis) {
        return deleteWithTimeout(url, headers, null, timeoutMillis);
    }

    @Override
    public Mono<Void> deleteWithTimeout(String url, Map<String, String> headers, Map<String, String> queryParams, int timeoutMillis) {
        log.debug("Making DELETE request with {}ms timeout to: {}", timeoutMillis, url);
        WebClient customWebClient = webClientConfig.createWebClient(timeoutMillis, timeoutMillis, timeoutMillis, timeoutMillis);
        return customWebClient.delete()
                .uri(uriBuilder -> {
                    uriBuilder.path(url);
                    if (queryParams != null) {
                        queryParams.forEach(uriBuilder::queryParam);
                    }
                    return uriBuilder.build();
                })
                .headers(h -> addHeaders(h, headers))
                .retrieve()
                .toBodilessEntity()
                .then()
                .doOnError(this::handleError);
    }

    // No timeout methods
    @Override
    public <T> Mono<T> getWithoutTimeout(String url, Class<T> responseType) {
        return getWithoutTimeout(url, responseType, null, null);
    }

    @Override
    public <T> Mono<T> getWithoutTimeout(String url, Class<T> responseType, Map<String, String> headers) {
        return getWithoutTimeout(url, responseType, headers, null);
    }

    @Override
    public <T> Mono<T> getWithoutTimeout(String url, Class<T> responseType, Map<String, String> headers, Map<String, String> queryParams) {
        log.debug("Making GET request without timeout to: {}", url);
        WebClient noTimeoutWebClient = webClientConfig.createWebClient(0, 0, 0, 0);
        return noTimeoutWebClient.get()
                .uri(uriBuilder -> {
                    uriBuilder.path(url);
                    if (queryParams != null) {
                        queryParams.forEach(uriBuilder::queryParam);
                    }
                    return uriBuilder.build();
                })
                .headers(h -> addHeaders(h, headers))
                .retrieve()
                .bodyToMono(responseType)
                .doOnError(this::handleError);
    }

    @Override
    public <T, R> Mono<R> postWithoutTimeout(String url, T request, Class<R> responseType) {
        return postWithoutTimeout(url, request, responseType, null);
    }

    @Override
    public <T, R> Mono<R> postWithoutTimeout(String url, T request, Class<R> responseType, Map<String, String> headers) {
        log.debug("Making POST request without timeout to: {}", url);
        WebClient noTimeoutWebClient = webClientConfig.createWebClient(0, 0, 0, 0);
        return noTimeoutWebClient.post()
                .uri(url)
                .headers(h -> addHeaders(h, headers))
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .retrieve()
                .bodyToMono(responseType)
                .doOnError(this::handleError);
    }

    @Override
    public <T> Mono<Void> putWithoutTimeout(String url, T request) {
        return putWithoutTimeout(url, request, null);
    }

    @Override
    public <T> Mono<Void> putWithoutTimeout(String url, T request, Map<String, String> headers) {
        log.debug("Making PUT request without timeout to: {}", url);
        WebClient noTimeoutWebClient = webClientConfig.createWebClient(0, 0, 0, 0);
        return noTimeoutWebClient.put()
                .uri(url)
                .headers(h -> addHeaders(h, headers))
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .retrieve()
                .toBodilessEntity()
                .then()
                .doOnError(this::handleError);
    }

    @Override
    public Mono<Void> deleteWithoutTimeout(String url) {
        return deleteWithoutTimeout(url, null, null);
    }

    @Override
    public Mono<Void> deleteWithoutTimeout(String url, Map<String, String> headers) {
        return deleteWithoutTimeout(url, headers, null);
    }

    @Override
    public Mono<Void> deleteWithoutTimeout(String url, Map<String, String> headers, Map<String, String> queryParams) {
        log.debug("Making DELETE request without timeout to: {}", url);
        WebClient noTimeoutWebClient = webClientConfig.createWebClient(0, 0, 0, 0);
        return noTimeoutWebClient.delete()
                .uri(uriBuilder -> {
                    uriBuilder.path(url);
                    if (queryParams != null) {
                        queryParams.forEach(uriBuilder::queryParam);
                    }
                    return uriBuilder.build();
                })
                .headers(h -> addHeaders(h, headers))
                .retrieve()
                .toBodilessEntity()
                .then()
                .doOnError(this::handleError);
    }

    private void addHeaders(HttpHeaders headers, Map<String, String> headerMap) {
        if (headerMap != null) {
            headerMap.forEach(headers::add);
        }
    }

    private void handleError(Throwable error) {
        log.error("HTTP request failed: {}", error.getMessage());
        throw new HttpServiceException("HTTP request failed: " + error.getMessage(), error);
    }
} 