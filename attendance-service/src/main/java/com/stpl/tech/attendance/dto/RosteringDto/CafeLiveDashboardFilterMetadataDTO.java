package com.stpl.tech.attendance.dto.RosteringDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CafeLiveDashboardFilterMetadataDTO {
    private List<CafeFilterDTO> cafes;
    private List<String> cities;
    private FilterAppliedFlags appliedFilters;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CafeFilterDTO {
        private Integer cafeId;
        private String cafeName;
        private String cityName;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FilterAppliedFlags {
        private Boolean cafeFilterApplied;
        private Boolean cityFilterApplied;
    }
} 