package com.stpl.tech.attendance.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Table(name = "TRANSFER_REQUEST")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransferRequest implements Serializable {
    private static final long serialVersionUID = 1L;
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "TRANSFER_REQUEST_ID")
    private Long transferRequestId;
    
    @Column(name = "EMP_ID", nullable = false)
    private String empId;
    
    @Column(name = "SOURCE_UNIT_ID", nullable = false)
    private String sourceUnitId;
    
    @Column(name = "DESTINATION_UNIT_ID", nullable = false)
    private String destinationUnitId;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "TRANSFER_TYPE", nullable = false)
    private TransferType transferType;
    
    @Column(name = "START_DATE", nullable = false)
    private LocalDate startDate;
    
    @Column(name = "END_DATE")
    private LocalDate endDate;
    
    @Column(name = "REASON")
    private String reason;
    
    @Column(name = "COMMENT")
    private String comment;
    
    @Column(name = "INITIATOR_ID", nullable = false)
    private String initiatorId;

    @Column(name = "APPROVAL_REQUEST_ID")
    private Long approvalRequestId;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "REQUEST_STATUS", nullable = false)
    private TransferRequestStatus status;
    
    @Column(name = "TRANSFER_REQUEST_STATUS", nullable = false)
    private String transferRequestStatus;
    
    @Column(name = "CREATED_DATE", nullable = false)
    private LocalDateTime createdDate;
    
    @Column(name = "CREATED_BY", nullable = false)
    private String createdBy;
    
    @Column(name = "UPDATED_DATE", nullable = false)
    private LocalDateTime updatedDate;
    
    @Column(name = "UPDATED_BY", nullable = false)
    private String updatedBy;

} 