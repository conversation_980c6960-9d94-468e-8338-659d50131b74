package com.stpl.tech.attendance.util;

import com.stpl.tech.attendance.dto.RosteringDto.EmpShiftUpdateRequestDTO;
import com.stpl.tech.attendance.entity.RosteringEntity.RosteringConstants;
import com.stpl.tech.attendance.entity.RosteringEntity.Shift;
import com.stpl.tech.attendance.notification.dto.RosteringNotificationRequest;
import com.stpl.tech.attendance.notification.entity.Notification;
import com.stpl.tech.attendance.notification.service.NotificationService;
import com.stpl.tech.attendance.repository.RosteringRepository.ShiftRepository;
import com.stpl.tech.attendance.cache.service.UnitCacheService;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * Utility class for shared rostering notification functionality
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RosteringNotificationUtil {
    
    private final NotificationService notificationService;
    private final ShiftRepository shiftRepository;
    private final UnitCacheService unitCacheService;

    /**
     * Send rostering notification with specified action
     * @param request The employee shift update request
     * @param action The action type (e.g., OVERRIDE_CREATED, SHIFT_ASSIGNED)
     */
    @Async
    public void sendRosteringNotification(EmpShiftUpdateRequestDTO request, String action) {
        try {
            Shift shift = shiftRepository.findById(request.getShiftId()).orElse(null);
            UnitBasicDetail unit = unitCacheService.getUnitBasicDetail(request.getUnitId());
            
            RosteringNotificationRequest notificationRequest = RosteringNotificationRequest.builder()
                .empId(request.getEmpId())
                .shiftId(request.getShiftId())
                .shiftName(shift != null ? shift.getShiftName() : "Unknown Shift")
                .unitId(request.getUnitId())
                .unitName(unit != null ? unit.getName() : "Unknown Unit")
                .effectiveFrom(request.getBusinessFrom())
                .effectiveTo(request.getBusinessTo())
                .action(action)
                .updatedBy(request.getUpdatedBy())
                .build();
            
            // Send appropriate notification based on action
            if (RosteringConstants.OVERRIDE_CREATED.equals(action)) {
                notificationService.sendRosteringNotification(notificationRequest, Notification.NotificationType.ROSTERING_TEMP_SHIFT_ASSIGNED, RosteringConstants.TEMPORARY_SHIFT_ASSIGNED, "You've been assigned a temporary shift");
                log.info("Sent rostering override notification for empId: {}", request.getEmpId());
            } else if (RosteringConstants.SHIFT_ASSIGNED.equals(action)) {
                notificationService.sendRosteringNotification(notificationRequest, Notification.NotificationType.ROSTERING_SHIFT_ASSIGNED, RosteringConstants.SHIFT_ASSIGNED_NOTIFICATION, "You have been assigned to");
                log.info("Sent rostering shift assigned notification for empId: {}", request.getEmpId());
            } else {
                log.warn("Unknown action type: {} for empId: {}", action, request.getEmpId());
            }
        } catch (Exception e) {
            log.warn("Failed to send rostering notification for empId: {} with action: {}", request.getEmpId(), action, e);
        }
    }
} 