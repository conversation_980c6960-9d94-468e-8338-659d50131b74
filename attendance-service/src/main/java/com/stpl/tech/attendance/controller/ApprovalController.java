package com.stpl.tech.attendance.controller;


import com.stpl.tech.attendance.cache.service.UnitCacheService;
import com.stpl.tech.attendance.cache.service.UserCacheService;
import com.stpl.tech.attendance.config.EnvironmentProperties;
import com.stpl.tech.attendance.constants.ApiConstants;
import com.stpl.tech.attendance.approval.dto.ApprovalDecision;
import com.stpl.tech.attendance.approval.entity.ApprovalRequest;
import com.stpl.tech.attendance.approval.entity.ApprovalStep;
import com.stpl.tech.attendance.approval.service.ApprovalEngineService;

import com.stpl.tech.attendance.dto.EmployeeInfoDTO;
import com.stpl.tech.attendance.enums.ApprovalStatus;
import com.stpl.tech.attendance.enums.ApprovalType;
import com.stpl.tech.attendance.model.request.ApprovalActionRequest;
import com.stpl.tech.attendance.model.request.ApprovalRequestFilter;
import com.stpl.tech.attendance.model.request.BulkApprovalActionRequest;
import com.stpl.tech.attendance.model.response.ApiResponse;
import com.stpl.tech.attendance.model.response.ApprovalListResponse;
import com.stpl.tech.attendance.model.response.ApprovalRequestResponse;
import com.stpl.tech.attendance.service.UnitResolutionService;
import com.stpl.tech.attendance.service.impl.EmployeeSearchService;
import com.stpl.tech.attendance.service.EmployeeAttendanceApprovalService;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.EnumSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import com.stpl.tech.attendance.entity.EmployeeAttendanceRequest;

@Tag(name = "Approvals", description = "Approval management APIs")
@Slf4j
@RestController
@RequestMapping(ApiConstants.Paths.APPROVALS)
@RequiredArgsConstructor
public class ApprovalController extends BaseController {

    private final ApprovalEngineService approvalEngineService;
    private final UserCacheService userCacheService;
    private final UnitCacheService unitCacheService;
    private final EnvironmentProperties properties;
    private final EmployeeSearchService employeeSearchService;
    private final EmployeeAttendanceApprovalService employeeAttendanceApprovalService;
    private final UnitResolutionService unitResolutionService;

    @Operation(summary = "Get pending approval requests")
    @PostMapping("/pending/{employeeId}")
    public ResponseEntity<ApiResponse<ApprovalListResponse>> getPendingApprovals(
            @PathVariable String employeeId,
            @RequestParam(required = false) String searchTerm,
            @RequestParam(defaultValue = "0") int pageNumber,
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestBody ApprovalRequestFilter approvalRequestFilter) {

        // Extracting the Employee IDs which match the search term
        List<EmployeeBasicDetail> employeeBasicDetails =  employeeSearchService.searchEmployees(searchTerm);
        List<Long> requesterIds = new ArrayList<>();
        if (Objects.nonNull(searchTerm) && !searchTerm.trim().isEmpty()) {
            requesterIds = employeeBasicDetails.stream()
                    .map(e -> (long) e.getId())
                    .toList();
        } else {
            requesterIds = null;
        }

        Pageable pageable = PageRequest.of(pageNumber, pageSize, Sort.by(Sort.Direction.DESC, "CREATED_DATE"));
        List<ApprovalRequest> pendingApprovalsList = new ArrayList<>(approvalEngineService.getPendingApprovals(employeeId, requesterIds, approvalRequestFilter));
        
        // Sort the list by creation time in descending order
        pendingApprovalsList.sort((a, b) -> b.getCreatedDate().compareTo(a.getCreatedDate()));
        
        // Convert List to Page
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), pendingApprovalsList.size());
        
        List<ApprovalRequest> pageContent = start < pendingApprovalsList.size() 
            ? pendingApprovalsList.subList(start, end) 
            : new ArrayList<>();
            
        Page<ApprovalRequest> pendingApprovals = new PageImpl<>(
            pageContent, 
            pageable, 
            pendingApprovalsList.size()
        );
        
        List<ApprovalRequestResponse> approvalResponses = pendingApprovals.getContent().stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());

        Set<String> uniqueStatuses = Arrays.stream(ApprovalStatus.values()).map(Enum::name).collect(Collectors.toSet());

        Set<String> uniqueRequestTypes = Arrays.stream(ApprovalType.values()).map(Enum::name).collect(Collectors.toSet());

//        Set<String> uniqueDesignations = approvalResponses.stream()
//                .map(response -> response.getEmployeeInfo().getEmployeeDesignation())
//                .collect(Collectors.toSet());

        Set<String> uniqueDesignations = approvalEngineService.getUniqueDesignations();
        
        ApprovalListResponse response = ApprovalListResponse.builder()
                .approvals(approvalResponses)
                .uniqueStatuses(uniqueStatuses)
                .uniqueRequestTypes(uniqueRequestTypes)
                .uniqueDesignations(uniqueDesignations)
                .build();
        
        return success(response, pendingApprovals);
    }

    @Operation(summary = "Get completed approval requests")
    @PostMapping("/completed/{employeeId}")
    public ResponseEntity<ApiResponse<ApprovalListResponse>> getCompletedApprovals(
            @PathVariable String employeeId,
            @RequestParam(required = false) String searchTerm,
            @RequestParam(defaultValue = "0") int pageNumber,
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestBody ApprovalRequestFilter approvalRequestFilter) {

        // Extracting the Employee IDs which match the search term
        List<EmployeeBasicDetail> employeeBasicDetails = employeeSearchService.searchEmployees(searchTerm);
        List<Long> requesterIds = new ArrayList<>();
        if (Objects.nonNull(searchTerm) && !searchTerm.trim().isEmpty()) {
            requesterIds = employeeBasicDetails.stream()
                    .map(e -> (long) e.getId())
                    .toList();
        } else {
            requesterIds = null;
        }

        // Create pageable with sorting by creation time in descending order
        Pageable pageable = PageRequest.of(pageNumber, pageSize, Sort.by(Sort.Direction.DESC, "CREATED_DATE"));
        Page<ApprovalRequest> completedApprovals = approvalEngineService.getCompletedApprovalsByApprover(
                Long.valueOf(employeeId), pageable, requesterIds, approvalRequestFilter);
        
        List<ApprovalRequestResponse> approvalResponses = completedApprovals.getContent().stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
        
        Set<String> uniqueStatuses = Arrays.stream(ApprovalStatus.values()).map(Enum::name).collect(Collectors.toSet());
        
        Set<String> uniqueRequestTypes = Arrays.stream(ApprovalType.values()).map(Enum::name).collect(Collectors.toSet());

//        Set<String> uniqueDesignations = approvalResponses.stream()
//                .map(response -> response.getEmployeeInfo().getEmployeeDesignation())
//                .collect(Collectors.toSet());

        Set<String> uniqueDesignations = approvalEngineService.getUniqueDesignations();

        ApprovalListResponse response = ApprovalListResponse.builder()
                .approvals(approvalResponses)
                .uniqueStatuses(uniqueStatuses)
                .uniqueRequestTypes(uniqueRequestTypes)
                .uniqueDesignations(uniqueDesignations)
                .build();
        
        return success(response, completedApprovals);
    }

    @Operation(summary = "Approve or reject a request")
    @PostMapping("/{requestId}/action")
    public ResponseEntity<ApiResponse<ApprovalRequestResponse>> processApprovalAction(
            @PathVariable Long requestId,
            @Valid @RequestBody ApprovalActionRequest request) {
        ApprovalDecision decision = new ApprovalDecision();
        decision.setApproverId(request.getApproverId());
        decision.setDecision(ApprovalDecision.Decision.valueOf(request.getAction()));
        decision.setComments(request.getRemarks());

        ApprovalRequest approvalRequest = approvalEngineService.processStep(
            requestId, 
            request.getStepId(), 
            decision
        );
        return success(mapToResponse(approvalRequest));
    }

//    @Operation(summary = "Get approval history")
//    @GetMapping("/{requestId}/history")
//    public ResponseEntity<ApiResponse<List<ApprovalRequestResponse>>> getApprovalHistory(
//            @PathVariable Long requestId) {
//        List<ApprovalStep> steps = approvalEngineService.getRequestSteps(requestId);
//        List<ApprovalRequestResponse> responses = steps.stream()
//                .map(step -> {
//                    ApprovalRequestResponse response = new ApprovalRequestResponse();
//                    response.setRequestId(step.getApprovalRequest().getId());
//                    response.setEmployeeInfo(getEmployeeInfo(step.getApprovalRequest().getRequesterId().intValue()));
//                    response.setRequestType(step.getApprovalRequest().getRequestType().name());
//                    response.setStatus(step.getStatus().name());
//                    response.setRequestDate(step.getCreatedDate());
//                    response.setLastUpdatedDate(step.getUpdatedDate());
//                    response.setRemarks(step.getSubSteps().stream().map
//                            (SubApprovalStep::getRemarks).collect(Collectors.joining(", ")));
//                    response.setCreatedBy(getEmployeeInfo(Integer.valueOf(step.getCreatedBy())));
//                    response.setUpdatedBy(getEmployeeInfo(step.getUpdatedBy() != null ? Integer.valueOf(step.getUpdatedBy()) : null));
//                    return response;
//                })
//                .collect(Collectors.toList());
//        return success(responses);
//    }



    @Operation(summary = "Bulk approve or reject multiple requests")
    @PostMapping("/bulk-action")
    public ResponseEntity<ApiResponse<List<ApprovalRequestResponse>>> processBulkApprovalAction(
            @Valid @RequestBody BulkApprovalActionRequest request) {
        List<ApprovalRequest> processedRequests = new ArrayList<>();
        
        for (BulkApprovalActionRequest.RequestAction action : request.getRequests()) {
            ApprovalDecision decision = new ApprovalDecision();
            decision.setApproverId(request.getApproverId().toString());
            decision.setDecision(ApprovalDecision.Decision.valueOf(action.getAction()));
            decision.setComments(action.getRemarks());

            ApprovalRequest approvalRequest = approvalEngineService.processStep(
                action.getRequestId(),
                action.getStepId(),
                decision
            );
            processedRequests.add(approvalRequest);
        }
        
        return success(processedRequests.stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList()));
    }

    @Operation(summary = "Get attendance history for an employee", 
               description = "Retrieves the attendance history (leave, OD, WFH, regularisation) for the authenticated employee based on JWT token with pagination")
    @GetMapping("/attendance/history")
    public ResponseEntity<ApiResponse<List<ApprovalRequestResponse>>> getAttendanceHistory(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            // Create pageable object with sorting by creation time in descending order
            Pageable pageable = PageRequest.of(page, size, Sort.by("creationTime").descending());
            
            // Get filtered and paginated attendance records from service
            Page<EmployeeAttendanceRequest> attendanceRequestsPage = employeeAttendanceApprovalService.getAttendanceHistory(pageable);
            
            // Map to response DTOs using new mapToAttendanceResponse method
            List<ApprovalRequestResponse> historyResponses = attendanceRequestsPage.getContent().stream()
                    .map(this::mapToAttendanceResponse)
                    .collect(Collectors.toList());
            
            // Return success response with pagination information
            return success(historyResponses, attendanceRequestsPage);
        } catch (Exception e) {
            log.error("Error getting attendance history for employee", e);
            throw new RuntimeException("Failed to get attendance history: " + e.getMessage());
        }
    }

    private EmployeeInfoDTO getEmployeeInfo(Integer empId) {
        EmployeeBasicDetail employeeBasicDetail = userCacheService.getUserById(empId);
        Integer unitId = unitResolutionService.getUnitIdForEmployee(empId, LocalDate.now());
        UnitBasicDetail unitBasicDetail = unitCacheService.getUnitBasicDetail(unitId);
        String unitName = unitBasicDetail != null ? unitBasicDetail.getName() : null;
        return EmployeeInfoDTO.builder()
                .empId(empId)
                .employeeName(employeeBasicDetail.getName())
                .employeeCode(employeeBasicDetail.getEmployeeCode())
                .employeeDesignation(employeeBasicDetail.getDesignation())
                .unitId(unitId)
                .unitName(unitName)
                .build();
    }

    private ApprovalRequestResponse mapToResponse(ApprovalRequest request) {
        EmployeeBasicDetail employeeBasicDetail  =userCacheService.getUserById(request.getRequesterId().intValue());
        UnitBasicDetail unitBasicDetail = unitCacheService.getUnitBasicDetail(request.getUnitId().intValue());
        ApprovalRequestResponse response = new ApprovalRequestResponse();
        response.setRequestId(request.getId());
        response.setEmployeeInfo(getEmployeeInfo(request.getRequesterId().intValue()));
        response.setRequestType(request.getRequestType().name());
        response.setStatus(request.getStatus().name());
        response.setRequestDate(request.getCreatedDate());
        response.setLastUpdatedDate(request.getUpdatedDate());
        // Handle createdBy - check if it's a numeric value before parsing
        response.setCreatedBy(request.getCreatedBy() != null && request.getCreatedBy().matches("\\d+") 
            ? getEmployeeInfo(Integer.valueOf(request.getCreatedBy())) 
            : null);
        
        // Handle updatedBy - check if it's a numeric value before parsing
        response.setUpdatedBy(request.getUpdatedBy() != null && request.getUpdatedBy().matches("\\d+") 
            ? getEmployeeInfo(Integer.valueOf(request.getUpdatedBy())) 
            : null);
        response.setMetadata(request.getMetadata());
        if (!EnumSet.of(
                ApprovalType.EMPLOYEE_LEAVE,
                ApprovalType.EMPLOYEE_OD,
                ApprovalType.EMPLOYEE_WFH,
                ApprovalType.EMPLOYEE_REGULARISATION,
                ApprovalType.ATTENDANCE_REQUEST_CANCELLATION
        ).contains(request.getRequestType())) {
            if(unitBasicDetail != null) {
                response.setUnitId(unitBasicDetail.getId());
                response.setUnitName(unitBasicDetail.getName());
            }
        }
//        response.setUnitId(unitBasicDetail.getId());
//        response.setUnitName(unitBasicDetail.getName());
        // Get the current step number
        ApprovalStep currentStep = approvalEngineService.getCurrentStep(request.getId());
        response.setCurrentStepNumber(currentStep.getStepNumber());
        response.setStepId(currentStep.getId());
        response.setRegistrationImageUrl(employeeBasicDetail.getImagekey());
        
        return response;
    }

    private ApprovalRequestResponse mapToAttendanceResponse(EmployeeAttendanceRequest request) {
        EmployeeBasicDetail employeeBasicDetail = userCacheService.getUserById(request.getEmpId());
        ApprovalRequestResponse response = new ApprovalRequestResponse();
        response.setRequestId(request.getId());
        response.setEmployeeInfo(getEmployeeInfo(request.getEmpId()));
        response.setRequestType(request.getType());
        response.setStatus(request.getStatus());
        response.setRequestDate(request.getCreationTime());
        response.setLastUpdatedDate(request.getUpdationTime());
        
        // Handle createdBy - check if it's a numeric value before parsing
        if (request.getCreatedBy() != null && request.getCreatedBy().matches("\\d+")) {
            response.setCreatedBy(getEmployeeInfo(Integer.valueOf(request.getCreatedBy())));
        } else {
            response.setCreatedBy(null);
        }
        
        // Handle updatedBy - check if it's a numeric value before parsing
        if (request.getUpdatedBy() != null && request.getUpdatedBy().matches("\\d+")) {
            response.setUpdatedBy(getEmployeeInfo(Integer.valueOf(request.getUpdatedBy())));
        } else {
            response.setUpdatedBy(null);
        }
        
        // Use the new metadata construction service for optimal metadata handling
        response.setMetadata(employeeAttendanceApprovalService.constructMetadata(request));
        
        // Set default values for fields not available in EmployeeAttendanceRequest
        response.setCurrentStepNumber(1); // Default step number
        response.setStepId(request.getId()); // Use request ID as step ID
        response.setRegistrationImageUrl(employeeBasicDetail != null ? employeeBasicDetail.getImagekey() : null);
        
        return response;
    }
} 