package com.stpl.tech.attendance.controller;

import com.stpl.tech.attendance.dto.EmpUnitShiftInstanceAnalyticsDTO;
import com.stpl.tech.attendance.dto.RosteringDto.GenericFilterMetadataDTO;
import com.stpl.tech.attendance.dto.RosteringDto.GenericFilterRequestDTO;
import com.stpl.tech.attendance.model.response.ApiResponse;
import com.stpl.tech.attendance.service.EmpUnitShiftInstanceAnalyticsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.LocalTime;

@Tag(name = "Employee Unit Shift Instance Analytics", description = "APIs for employee unit shift instance analytics")
@Slf4j
@RestController
@RequestMapping("/api/v1/analytics/emp-unit-shift-instance")
@RequiredArgsConstructor
public class EmpUnitShiftInstanceAnalyticsController extends BaseController {

    private final EmpUnitShiftInstanceAnalyticsService empUnitShiftInstanceAnalyticsService;

    @Operation(summary = "Get employee unit shift instance analytics for a manager")
    @PostMapping("/manager/{managerId}")
    public ResponseEntity<ApiResponse<EmpUnitShiftInstanceAnalyticsDTO>> getEmpUnitShiftInstanceAnalytics(
            @Parameter(description = "Manager's employee ID") 
            @PathVariable Integer managerId,
            
            @Parameter(description = "Date (YYYY-MM-DD)") 
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date,
            
            @Parameter(description = "Start time (HH:mm:ss)") 
            @RequestParam @DateTimeFormat(pattern = "HH:mm:ss") LocalTime startTime,
            
            @Parameter(description = "End time (HH:mm:ss)") 
            @RequestParam @DateTimeFormat(pattern = "HH:mm:ss") LocalTime endTime,
            
            @Parameter(description = "Generic filter request", required = false)
            @RequestBody(required = false) GenericFilterRequestDTO filterRequest) {
        
        log.info("Getting employee unit shift instance analytics for manager: {} on date: {} from {} to {} with filters: {}", managerId, date, startTime, endTime, filterRequest);
        
        EmpUnitShiftInstanceAnalyticsDTO analytics = empUnitShiftInstanceAnalyticsService.getEmpUnitShiftInstanceAnalytics(managerId, date, startTime, endTime, filterRequest);
        
        return success(analytics);
    }

    @Operation(summary = "Get filter metadata for employee unit shift instance analytics")
    @GetMapping("/manager/{managerId}/filters")
    public ResponseEntity<ApiResponse<GenericFilterMetadataDTO>> getFilterMetadata(
            @Parameter(description = "Manager's employee ID") 
            @PathVariable Integer managerId) {
        
        log.info("Getting filter metadata for manager: {}", managerId);
        
        GenericFilterMetadataDTO filterMetadata = empUnitShiftInstanceAnalyticsService.getFilterMetadata(managerId);
        
        return success(filterMetadata);
    }
} 