package com.stpl.tech.attendance.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO to group employee and user information for approval requests
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmployeeInfoDTO {
    private Integer empId;
    private String employeeCode;
    private String employeeName;
    private String employeeDesignation;
    private Integer unitId;
    private String unitName;
} 