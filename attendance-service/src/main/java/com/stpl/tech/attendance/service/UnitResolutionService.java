package com.stpl.tech.attendance.service;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;

/**
 * Service interface for resolving unit IDs for employees
 */
public interface UnitResolutionService {

    /**
     * Get unit ID for employee based on emp shift mapping first, then fallback to emp eligibility mapping
     * @param empId Employee ID
     * @param businessDate Business date
     * @return Unit ID, defaults to 26091 if no mapping found
     */
    Integer getUnitIdForEmployee(Integer empId, LocalDate businessDate);

    /**
     * Get unit ID for employee from emp shift mapping only
     * @param empId Employee ID
     * @param businessDate Business date
     * @return Unit ID from emp shift mapping, null if not found
     */
    Integer getUnitIdFromEmpShiftMapping(Integer empId, LocalDate businessDate);

    /**
     * Get unit ID for employee from emp eligibility mapping only
     * @param empId Employee ID
     * @param businessDate Business date
     * @return Unit ID from emp eligibility mapping, defaults to 26091 if not found
     */
    Integer getUnitIdFromEmpEligibilityMapping(Integer empId, LocalDate businessDate);

    /**
     * Get employees for a single unit with caching
     * @param unitId Unit ID
     * @param businessDate Business date
     * @return Set of employee IDs
     */
    List<Integer> getEmployeesForUnit(Integer unitId, LocalDate businessDate);

    /**
     * Evict cache for a specific unit and date when employee shift mapping changes
     * @param unitId Unit ID
     * @param businessDate Business date
     */
    void evictUnitEmployeesCache(Integer unitId, LocalDate businessDate);

    /**
     * Evict all unit employees cache entries
     */
    void evictAllUnitEmployeesCache();

    /**
     * Evict cache for a specific employee and date when employee shift mapping changes
     * @param empId Employee ID
     * @param businessDate Business date
     */
    void evictEmployeeUnitCache(Integer empId, LocalDate businessDate);

    /**
     * Evict all employee unit cache entries
     */
    void evictAllEmployeeUnitCache();

    /**
     * Evict both caches for a specific employee and date when employee shift mapping changes
     * @param empId Employee ID
     * @param businessDate Business date
     */
    void evictEmployeeCaches(Integer empId, LocalDate businessDate);

    /**
     * Evict both caches for a specific unit and date when employee shift mapping changes
     * @param unitId Unit ID
     * @param businessDate Business date
     */
    void evictUnitCaches(Integer unitId, LocalDate businessDate);

    /**
     * Evict all cache entries for both caches
     */
    void evictAllCaches();

    /**
     * Get all employees for the given unit IDs based on emp eligibility mapping
     * @param unitIds List of unit IDs
     * @param businessDate Business date
     * @return Set of employee IDs
     */
    Set<Integer> getEmployeesForUnits(List<Integer> unitIds, LocalDate businessDate);

    /**
     * Get employees for units with control over whether to include secondary mappings
     * @param unitIds List of unit IDs
     * @param businessDate Business date
     * @param includeSecondaryMappings If true, include employees from all sources. If false, only include employees in their primary unit.
     * @return Set of employee IDs
     */
    Set<Integer> getEmployeesForUnits(List<Integer> unitIds, LocalDate businessDate, boolean includeSecondaryMappings);
} 