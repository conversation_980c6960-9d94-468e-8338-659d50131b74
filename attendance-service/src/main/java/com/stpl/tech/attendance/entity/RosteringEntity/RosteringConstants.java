package com.stpl.tech.attendance.entity.RosteringEntity;

public class RosteringConstants {
    public static final String ACTIVE = "ACTIVE";
    public static final String IN_ACTIVE = "IN_ACTIVE";
    public static final String INFINITY_TIME = "9999-12-01 23:59:00.000";
    public static final Integer DEFAULT_ACTUAL = 0;
    public static final Integer DEFAULT_IDEAL = 0;
    public static final String CAFE_SHIFT_UPDATE = "UPDATE";
    public static final String CAFE_SHIFT_DELETE = "DELETE";
    // Error codes for EmployeeShiftInstancesController
    public static final String ERROR_RECREATION_FAILED = "RECREATION_FAILED";
    public static final String ERROR_FETCH_FAILED = "FETCH_FAILED";
    // Filter keys for employee filtering
    public static final String FILTER_KEY_DESIGNATIONS = "designations";
    public static final String FILTER_KEY_CITY_NAMES = "cityNames";
    public static final String FILTER_KEY_UNIT_IDS = "unitIds";
    public static final String FILTER_KEY_REGIONS = "regions";
    public static final String SHIFT_ASSIGNED = "SHIFT_ASSIGNED";
    public static final String OVERRIDE_CREATED = "OVERRIDE_CREATED";
    public static final String TEMPORARY_SHIFT_ASSIGNED = "Temporary Shift Assigned";
    public static final String SHIFT_ASSIGNED_NOTIFICATION = "Shift Assigned";
    // Day constants for shift coverage plans
    public static final String DAY_MONDAY = "MONDAY";
    public static final String DAY_TUESDAY = "TUESDAY";
    public static final String DAY_WEDNESDAY = "WEDNESDAY";
    public static final String DAY_THURSDAY = "THURSDAY";
    public static final String DAY_FRIDAY = "FRIDAY";
    public static final String DAY_SATURDAY = "SATURDAY";
    public static final String DAY_SUNDAY = "SUNDAY";
}
