package com.stpl.tech.attendance.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import jakarta.persistence.*;
import java.time.LocalDateTime;

/**
 * Generic entity to track API request processing status and metrics
 * This table tracks any request that is annotated with @TrackRequest
 */
@Entity
@Table(name = "request_tracking")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RequestTracking {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * Unique request ID from MDC context (RequestIdInterceptor)
     */
    @Column(name = "request_id", nullable = false, unique = true, length = 50)
    private String requestId;

    /**
     * Unit ID from JWT context
     */
    @Column(name = "unit_id", nullable = false)
    private Integer unitId;

    /**
     * API path (e.g., "/api/attendance/punch", "/api/employees")
     */
    @Column(name = "api_identifier", nullable = false, length = 200)
    private String apiIdentifier;

    /**
     * Business reference ID (e.g., attendance request ID, employee ID, etc.)
     * Can be updated asynchronously after main operation completes
     */
    @Column(name = "reference_id", length = 100)
    private String referenceId;

    /**
     * Request start time
     */
    @Column(name = "request_time", nullable = false)
    @CreationTimestamp
    private LocalDateTime requestTime;

    /**
     * Request completion time
     */
    @Column(name = "completion_time")
    private LocalDateTime completionTime;

    /**
     * Error message if request failed
     */
    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;

    /**
     * Error code if request failed
     */
    @Column(name = "error_code", length = 50)
    private String errorCode;

    /**
     * Last update time
     */
    @Column(name = "updated_at", nullable = false)
    @UpdateTimestamp
    private LocalDateTime updatedAt;
}
