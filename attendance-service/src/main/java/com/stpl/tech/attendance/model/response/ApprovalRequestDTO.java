package com.stpl.tech.attendance.model.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.stpl.tech.attendance.approval.entity.ApprovalRequest;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ApprovalRequestDTO {
    private Long id;
    private String requesterId;
    private String requestType;
    private String status;
    private LocalDateTime createdDate;
    private LocalDateTime updatedDate;
    private String createdBy;
    private String updatedBy;
    private String metadata;
    private String description;
    private Integer currentStep;
    private Integer totalSteps;
    private String processInstanceId;

    public static ApprovalRequestDTO fromEntity(ApprovalRequest request) {
        ApprovalRequestDTO dto = new ApprovalRequestDTO();
        dto.setId(request.getId());
        dto.setRequesterId(request.getRequesterId().toString());
        dto.setRequestType(request.getRequestType().name());
        dto.setStatus(request.getStatus().name());
        dto.setCreatedDate(request.getCreatedDate());
        dto.setUpdatedDate(request.getUpdatedDate());
        dto.setCreatedBy(request.getCreatedBy());
        dto.setUpdatedBy(request.getUpdatedBy());
        dto.setMetadata(request.getMetadata());
        dto.setDescription(request.getDescription());
        dto.setCurrentStep(request.getCurrentStep());
        dto.setTotalSteps(request.getTotalSteps());
        dto.setProcessInstanceId(request.getProcessInstanceId());
        return dto;
    }
} 