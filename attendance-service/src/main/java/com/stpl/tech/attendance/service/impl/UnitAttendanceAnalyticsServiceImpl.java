package com.stpl.tech.attendance.service.impl;

import com.stpl.tech.attendance.cache.service.UserCacheService;
import com.stpl.tech.attendance.dto.UnitAttendanceAnalyticsDTO;
import com.stpl.tech.attendance.service.MetadataService;
import com.stpl.tech.attendance.service.UnitAttendanceAnalyticsService;
import com.stpl.tech.attendance.service.UnitAttendanceAnalyticsCacheService;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class UnitAttendanceAnalyticsServiceImpl implements UnitAttendanceAnalyticsService {
    
    private final UserCacheService userCacheService;
    private final UnitAttendanceAnalyticsCacheService unitAttendanceAnalyticsCacheService;
    private final MetadataService metadataService;
    
    @Override
    public UnitAttendanceAnalyticsDTO.ManagerAnalyticsResponseDTO getManagerUnitAnalytics(Integer managerId, LocalDate businessDate) {
        log.info("Getting manager unit analytics for manager: {} on date: {}", managerId, businessDate);
        
        try {
            // Get manager details
            EmployeeBasicDetail manager = userCacheService.getUserById(managerId);
            if (manager == null) {
                throw new RuntimeException("Manager not found with ID: " + managerId);
            }
            
            // Get managed unit IDs
            List<Integer> managedUnitIds = getManagedUnitIds(managerId);
            if (managedUnitIds.isEmpty()) {
                log.warn("No units found for manager: {}", managerId);
                return buildEmptyManagerAnalytics(managerId, manager.getName(), businessDate);
            }
            
            // Get analytics for each unit
            List<UnitAttendanceAnalyticsDTO> unitAnalytics = new ArrayList<>();
            for (Integer unitId : managedUnitIds) {
                UnitAttendanceAnalyticsDTO unitAnalytic = getUnitAnalytics(unitId, businessDate);
                unitAnalytics.add(unitAnalytic);
            }
            
            // Calculate total analytics
            UnitAttendanceAnalyticsDTO totalAnalytics = calculateTotalAnalytics(unitAnalytics);
            
            return UnitAttendanceAnalyticsDTO.ManagerAnalyticsResponseDTO.builder()
                .managerId(managerId)
                .managerName(manager.getName())
                .businessDate(businessDate)
                .unitAnalytics(unitAnalytics)
                .totalAnalytics(totalAnalytics)
                .build();
                
        } catch (Exception e) {
            log.error("Error getting manager unit analytics for manager: {} on date: {}", managerId, businessDate, e);
            throw new RuntimeException("Failed to get manager unit analytics", e);
        }
    }
    
    @Override
    public UnitAttendanceAnalyticsDTO getUnitAnalytics(Integer unitId, LocalDate businessDate) {
        return unitAttendanceAnalyticsCacheService.getUnitAnalytics(unitId, businessDate);
    }
    
    @Override
    public void updateUnitAnalyticsCache(Integer unitId, LocalDate businessDate) {
        // This method is now handled by the cache service
        log.debug("Unit analytics cache update delegated to cache service for unit: {} on date: {}", unitId, businessDate);
    }
    
    @Override
    public void clearUnitAnalyticsCache() {
        unitAttendanceAnalyticsCacheService.clearUnitAttendanceAnalyticsCache();
    }
    
    /**
     * Get managed unit IDs for a manager
     */
    private List<Integer> getManagedUnitIds(Integer managerId) {
        // This method should be implemented based on your existing logic
        // For now, I'll create a placeholder - you'll need to implement this
        log.debug("Getting managed unit IDs for manager: {}", managerId);
        
        return metadataService.getManagedUnitIds(managerId);
    }
    
    /**
     * Calculate total analytics across all units
     */
    private UnitAttendanceAnalyticsDTO calculateTotalAnalytics(List<UnitAttendanceAnalyticsDTO> unitAnalytics) {
        int totalIdealEmployees = unitAnalytics.stream().mapToInt(UnitAttendanceAnalyticsDTO::getIdealEmployeeCount).sum();
        int totalActualEmployees = unitAnalytics.stream().mapToInt(UnitAttendanceAnalyticsDTO::getActualEmployeeCount).sum();
        int totalOnTimeEmployees = unitAnalytics.stream().mapToInt(UnitAttendanceAnalyticsDTO::getOnTimeEmployeeCount).sum();
        int totalOnLeaveEmployees = unitAnalytics.stream().mapToInt(UnitAttendanceAnalyticsDTO::getOnLeaveEmployeeCount).sum();
        
        BigDecimal totalIdealHours = unitAnalytics.stream()
            .map(UnitAttendanceAnalyticsDTO::getTotalIdealWorkingHours)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        BigDecimal totalActualHours = unitAnalytics.stream()
            .map(UnitAttendanceAnalyticsDTO::getTotalActualWorkingHours)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        BigDecimal totalAttendancePercentage = calculatePercentage(totalActualEmployees, totalIdealEmployees);
        BigDecimal totalOnTimePercentage = calculatePercentage(totalOnTimeEmployees, totalActualEmployees);
        BigDecimal totalWorkingHoursEfficiency = calculateWorkingHoursEfficiency(totalActualHours, totalIdealHours);
        
        return UnitAttendanceAnalyticsDTO.builder()
            .unitId(0) // 0 represents total across all units
            .unitName("Total")
            .unitCode("TOTAL")
            .businessDate(unitAnalytics.isEmpty() ? LocalDate.now() : unitAnalytics.get(0).getBusinessDate())
            .idealEmployeeCount(totalIdealEmployees)
            .actualEmployeeCount(totalActualEmployees)
            .onTimeEmployeeCount(totalOnTimeEmployees)
            .onLeaveEmployeeCount(totalOnLeaveEmployees)
            .totalIdealWorkingHours(totalIdealHours)
            .totalActualWorkingHours(totalActualHours)
            .attendancePercentage(totalAttendancePercentage)
            .onTimePercentage(totalOnTimePercentage)
            .workingHoursEfficiency(totalWorkingHoursEfficiency)
            .build();
    }
    
    /**
     * Calculate percentage
     */
    private BigDecimal calculatePercentage(int numerator, int denominator) {
        if (denominator == 0) {
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(numerator)
            .multiply(BigDecimal.valueOf(100))
            .divide(BigDecimal.valueOf(denominator), 2, RoundingMode.HALF_UP);
    }
    
    /**
     * Calculate working hours efficiency
     */
    private BigDecimal calculateWorkingHoursEfficiency(BigDecimal actualHours, BigDecimal idealHours) {
        if (idealHours.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return actualHours
            .multiply(BigDecimal.valueOf(100))
            .divide(idealHours, 2, RoundingMode.HALF_UP);
    }
    
    /**
     * Build empty manager analytics response
     */
    private UnitAttendanceAnalyticsDTO.ManagerAnalyticsResponseDTO buildEmptyManagerAnalytics(Integer managerId, String managerName, LocalDate businessDate) {
        return UnitAttendanceAnalyticsDTO.ManagerAnalyticsResponseDTO.builder()
            .managerId(managerId)
            .managerName(managerName)
            .businessDate(businessDate)
            .unitAnalytics(new ArrayList<>())
            .totalAnalytics(UnitAttendanceAnalyticsDTO.builder()
                .unitId(0)
                .unitName("Total")
                .unitCode("TOTAL")
                .businessDate(businessDate)
                .idealEmployeeCount(0)
                .actualEmployeeCount(0)
                .onTimeEmployeeCount(0)
                .onLeaveEmployeeCount(0)
                .totalIdealWorkingHours(BigDecimal.ZERO)
                .totalActualWorkingHours(BigDecimal.ZERO)
                .attendancePercentage(BigDecimal.ZERO)
                .onTimePercentage(BigDecimal.ZERO)
                .workingHoursEfficiency(BigDecimal.ZERO)
                .build())
            .build();
    }
} 