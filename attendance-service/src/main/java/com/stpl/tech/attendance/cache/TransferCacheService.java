package com.stpl.tech.attendance.cache;

import com.stpl.tech.attendance.model.TransferRequest;
import com.stpl.tech.attendance.model.TransferRequestStatus;
import com.stpl.tech.attendance.model.TransferStatistics;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class TransferCacheService {
    private final CacheManager cacheManager;
    private static final String TRANSFER_CACHE = "transferCache";
    private static final String STATISTICS_CACHE = "statisticsCache";
    private static final String TRANSFER_STATUS_CACHE = "transferStatusCache";
    
    public void cacheTransferRequest(TransferRequest request) {
        try {
            cacheManager.getCache(TRANSFER_CACHE).put(request.getTransferRequestId(), request);
            log.debug("Cached transfer request: {}", request.getTransferRequestId());
        } catch (Exception e) {
            log.error("Error caching transfer request: {}", request.getTransferRequestId(), e);
        }
    }
    
    public TransferRequest getCachedTransferRequest(String transferId) {
        try {
            return cacheManager.getCache(TRANSFER_CACHE).get(transferId, TransferRequest.class);
        } catch (Exception e) {
            log.error("Error getting cached transfer request: {}", transferId, e);
            return null;
        }
    }
    
    public void cacheStatistics(TransferStatistics statistics) {
        try {
            cacheManager.getCache(STATISTICS_CACHE).put(statistics.getUnitId(), statistics);
            log.debug("Cached statistics for unit: {}", statistics.getUnitId());
        } catch (Exception e) {
            log.error("Error caching statistics for unit: {}", statistics.getUnitId(), e);
        }
    }
    
    public TransferStatistics getCachedStatistics(String unitId) {
        try {
            return cacheManager.getCache(STATISTICS_CACHE).get(unitId, TransferStatistics.class);
        } catch (Exception e) {
            log.error("Error getting cached statistics for unit: {}", unitId, e);
            return null;
        }
    }
    
    /**
     * Cache the latest transfer status for an employee
     * @param empId The employee ID
     * @param status The transfer status (can be null if no transfer found)
     */
    public void cacheLatestTransferStatus(String empId, TransferRequestStatus status) {
        try {
            cacheManager.getCache(TRANSFER_STATUS_CACHE).put(empId, status);
            log.debug("Cached latest transfer status for employee: {} = {}", empId, status);
        } catch (Exception e) {
            log.error("Error caching latest transfer status for employee: {}", empId, e);
        }
    }
    
    /**
     * Get the cached latest transfer status for an employee
     * @param empId The employee ID
     * @return Optional containing the transfer status, or empty if not found in cache
     */
    public Optional<TransferRequestStatus> getLatestTransferStatus(String empId) {
        try {
            TransferRequestStatus status = cacheManager.getCache(TRANSFER_STATUS_CACHE).get(empId, TransferRequestStatus.class);
            return Optional.ofNullable(status);
        } catch (Exception e) {
            log.error("Error getting cached latest transfer status for employee: {}", empId, e);
            return Optional.empty();
        }
    }
    
    /**
     * Invalidate the cached transfer status for an employee
     * @param empId The employee ID
     */
    public void invalidateTransferStatus(String empId) {
        try {
            cacheManager.getCache(TRANSFER_STATUS_CACHE).evict(empId);
            log.debug("Invalidated transfer status cache for employee: {}", empId);
        } catch (Exception e) {
            log.error("Error invalidating transfer status cache for employee: {}", empId, e);
        }
    }
} 