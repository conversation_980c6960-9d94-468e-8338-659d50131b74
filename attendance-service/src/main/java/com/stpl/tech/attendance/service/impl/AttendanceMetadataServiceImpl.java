package com.stpl.tech.attendance.service.impl;

import com.stpl.tech.attendance.cache.service.AttendanceMetadataCacheService;
import com.stpl.tech.attendance.dto.AttendanceMetadataResponse;
import com.stpl.tech.attendance.entity.EmpAttendanceMetadata;
import com.stpl.tech.attendance.enums.AttendanceAttributeType;
import com.stpl.tech.attendance.enums.MappingStatus;
import com.stpl.tech.attendance.repository.EmpAttendanceMetadataRepository;
import com.stpl.tech.attendance.service.AttendanceMetadataService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import com.stpl.tech.attendance.constants.AppConstants;

/**
 * Service implementation for attendance metadata operations
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AttendanceMetadataServiceImpl implements AttendanceMetadataService {
    
    private final AttendanceMetadataCacheService cacheService;
    private final EmpAttendanceMetadataRepository metadataRepository;
    
    @Override
    public AttendanceMetadataResponse getMetadataValue(Integer deptId, AttendanceAttributeType attributeCode) {
        log.debug("Getting metadata value for Dept: {}, Attribute: {}", deptId, attributeCode);
        return cacheService.getMetadataValue(deptId, attributeCode);
    }
    
    @Override
    public Map<AttendanceAttributeType, AttendanceMetadataResponse> getAllMetadataForDept(Integer deptId) {
        log.debug("Getting all metadata for department: {}", deptId);
        return cacheService.getAllMetadataForDept(deptId);
    }
    
    @Override
    public Boolean getBooleanValue(Integer deptId, AttendanceAttributeType attributeCode) {
        log.debug("Getting boolean value for Dept: {}, Attribute: {}", deptId, attributeCode);
        AttendanceMetadataResponse response = getMetadataValue(deptId, attributeCode);
        return response != null ? response.getBooleanValue() : null;
    }
    
    @Override
    public BigDecimal getNumericValue(Integer deptId, AttendanceAttributeType attributeCode) {
        log.debug("Getting numeric value for Dept: {}, Attribute: {}", deptId, attributeCode);
        AttendanceMetadataResponse response = getMetadataValue(deptId, attributeCode);
        return response != null ? response.getNumericValue() : null;
    }
    
    @Override
    public boolean isLeaveAllowed(Integer deptId) {
        log.debug("Checking if leave is allowed for department: {}", deptId);
        Boolean value = getBooleanValue(deptId, AttendanceAttributeType.IS_LEAVE_ALLOWED);
        return Boolean.TRUE.equals(value);
    }
    
    @Override
    public boolean isOdAllowed(Integer deptId) {
        log.debug("Checking if OD is allowed for department: {}", deptId);
        Boolean value = getBooleanValue(deptId, AttendanceAttributeType.IS_OD_ALLOWED);
        return Boolean.TRUE.equals(value);
    }
    
    @Override
    public boolean isWfhAllowed(Integer deptId) {
        log.debug("Checking if WFH is allowed for department: {}", deptId);
        Boolean value = getBooleanValue(deptId, AttendanceAttributeType.IS_WFH_ALLOWED);
        return Boolean.TRUE.equals(value);
    }
    
    @Override
    public boolean isRegularisationAllowed(Integer deptId) {
        log.debug("Checking if regularisation is allowed for department: {}", deptId);
        Boolean value = getBooleanValue(deptId, AttendanceAttributeType.IS_REGULARISATION_ALLOWED);
        return Boolean.TRUE.equals(value);
    }
    
    @Override
    public String getLeaveCreditCycle(Integer deptId) {
        log.debug("Getting leave credit cycle for department: {}", deptId);
        AttendanceMetadataResponse response = getMetadataValue(deptId, AttendanceAttributeType.LEAVE_CREDIT_CYCLE);
        return response != null ? response.getAttributeValue() : null;
    }
    
    @Override
    public BigDecimal getTotalNumberOfLeaves(Integer deptId) {
        log.debug("Getting total number of leaves for department: {}", deptId);
        return getNumericValue(deptId, AttendanceAttributeType.TOTAL_NUMBER_OF_LEAVES);
    }
    
    @Override
    public BigDecimal getPayrollProcessingStartDay(Integer deptId) {
        log.debug("Getting payroll processing start day for department: {}", deptId);
        return getNumericValue(deptId, AttendanceAttributeType.PAYROLL_PROCESSING_START_DAY);
    }
    
    @Override
    public BigDecimal getPayrollProcessingEndDay(Integer deptId) {
        log.debug("Getting payroll processing end day for department: {}", deptId);
        return getNumericValue(deptId, AttendanceAttributeType.PAYROLL_PROCESSING_END_DAY);
    }
    
    @Override
    public EmpAttendanceMetadata saveMetadata(EmpAttendanceMetadata metadata) {
        log.info("Saving metadata for Dept: {}, Attribute: {}", metadata.getDeptId(), metadata.getAttributeCode());
        
        // Set audit fields if not present
        if (metadata.getCreatedAt() == null) {
            metadata.setCreatedAt(LocalDateTime.now());
        }
        metadata.setUpdatedAt(LocalDateTime.now());
        
        // Save the metadata
        EmpAttendanceMetadata savedMetadata = metadataRepository.save(metadata);
        
        // Evict cache for this department and attribute
        cacheService.evictMetadata(metadata.getDeptId(), metadata.getAttributeCode());
        
        log.info("Metadata saved successfully with ID: {}", savedMetadata.getId());
        return savedMetadata;
    }
    
    @Override
    public List<EmpAttendanceMetadata> getAllMetadataEntitiesForDept(Integer deptId) {
        log.debug("Getting all metadata entities for department: {}", deptId);
        return metadataRepository.findByDeptIdAndMappingStatus(deptId, MappingStatus.ACTIVE);
    }
    
    @Override
    public void refreshCacheForDept(Integer deptId) {
        log.info("Refreshing cache for department: {}", deptId);
        cacheService.refreshCacheForDept(deptId);
    }
    
    @Override
    public void refreshAllCache() {
        log.info("Refreshing all attendance metadata cache");
        cacheService.evictAllCache();
    }
    
    /**
     * Bulk save metadata for multiple departments
     * @param metadataList List of metadata to save
     * @return List of saved metadata
     */
    public List<EmpAttendanceMetadata> saveMetadataBulk(List<EmpAttendanceMetadata> metadataList) {
        log.info("Bulk saving {} metadata records", metadataList.size());
        
        List<EmpAttendanceMetadata> savedMetadata = metadataRepository.saveAll(metadataList);
        
        // Evict cache for all affected departments
        savedMetadata.stream()
                .map(EmpAttendanceMetadata::getDeptId)
                .distinct()
                .forEach(cacheService::evictAllMetadataForDept);
        
        log.info("Bulk metadata save completed successfully");
        return savedMetadata;
    }
    
    /**
     * Get metadata for multiple departments
     * @param deptIds List of department IDs
     * @return Map of department ID to metadata map
     */
    public Map<Integer, Map<AttendanceAttributeType, AttendanceMetadataResponse>> getMetadataForMultipleDepts(List<Integer> deptIds) {
        log.debug("Getting metadata for multiple departments: {}", deptIds);
        
        Map<Integer, Map<AttendanceAttributeType, AttendanceMetadataResponse>> result = new java.util.HashMap<>();
        
        for (Integer deptId : deptIds) {
            Map<AttendanceAttributeType, AttendanceMetadataResponse> deptMetadata = getAllMetadataForDept(deptId);
            result.put(deptId, deptMetadata);
        }
        
        return result;
    }
    
    /**
     * Check if a specific attribute is configured for a department
     * @param deptId Department ID
     * @param attributeCode Attribute code
     * @return true if configured, false otherwise
     */
    public boolean isAttributeConfigured(Integer deptId, AttendanceAttributeType attributeCode) {
        log.debug("Checking if attribute {} is configured for department: {}", attributeCode, deptId);
        AttendanceMetadataResponse response = getMetadataValue(deptId, attributeCode);
        return response != null;
    }
    
    /**
     * Get default metadata for a specific attribute
     * @param attributeCode Attribute code
     * @return Default metadata response or null if not found
     */
    @Override
    public AttendanceMetadataResponse getDefaultMetadata(AttendanceAttributeType attributeCode) {
        log.debug("Getting default metadata for attribute: {}", attributeCode);
        return cacheService.getMetadataValue(-1, attributeCode);
    }
    
    @Override
    public boolean isHolidayAllowed(Integer deptId) {
        log.debug("Checking if holidays are allowed for department: {}", deptId);
        Boolean value = getBooleanValue(deptId, AttendanceAttributeType.IS_HOLIDAY_ALLOWED);
        return Boolean.TRUE.equals(value);
    }
    
    @Override
    public String[] getWeekendDays(Integer deptId) {
        log.debug("Getting weekend days for department: {}", deptId);
        AttendanceMetadataResponse response = getMetadataValue(deptId, AttendanceAttributeType.WEEKEND_DAYS);
        if (response != null && response.getAttributeValue() != null) {
            return response.getAttributeValue().split(",");
        }
        return new String[]{AppConstants.SATURDAY, AppConstants.SUNDAY}; // Default fallback
    }
    
    @Override
    public boolean isFixedWeekendAllowed(Integer deptId) {
        log.debug("Checking if fixed weekend is allowed for department: {}", deptId);
        // For now, we'll assume fixed weekend is always allowed if weekend days are configured
        AttendanceMetadataResponse fixedWeekOff = getMetadataValue(deptId,AttendanceAttributeType.FIXED_WEEK_OFF);
        return fixedWeekOff != null && Boolean.TRUE.equals(fixedWeekOff.getBooleanValue());
    }
}


