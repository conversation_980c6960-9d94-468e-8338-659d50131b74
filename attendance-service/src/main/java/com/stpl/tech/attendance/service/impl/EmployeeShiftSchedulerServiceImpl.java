package com.stpl.tech.attendance.service.impl;

import com.stpl.tech.attendance.cache.service.UnitCacheService;
import com.stpl.tech.attendance.cache.service.UserCacheService;
import com.stpl.tech.attendance.domain.EmpShiftMapping;
import com.stpl.tech.attendance.domain.EmpShiftMappingFinder;
import com.stpl.tech.attendance.domain.EmpShiftMappingList;
import com.stpl.tech.attendance.dto.EmployeeShiftInstanceDetailDTO;
import com.stpl.tech.attendance.dto.EmployeeShiftInstancesDTO;
import com.stpl.tech.attendance.dto.EmployeeShiftInstancesDateResponseDTO;
import com.stpl.tech.attendance.dto.EmployeeShiftInstancesMonthAggregatedResponseDTO;
import com.stpl.tech.attendance.dto.EmployeeShiftInstancesMonthResponseDTO;
import com.stpl.tech.attendance.dto.EmployeeShiftInstancesMultiEmployeeResponseDTO;
import com.stpl.tech.attendance.dto.EmployeeShiftInstancesRangeResponseDTO;
import com.stpl.tech.attendance.dto.EmployeeShiftInstancesWeekResponseDTO;
import com.stpl.tech.attendance.dto.ShiftInstanceRecreationRequestDTO;
import com.stpl.tech.attendance.dto.ShiftInstanceRecreationResponseDTO;
import com.stpl.tech.attendance.entity.AttendanceStatus;
import com.stpl.tech.attendance.entity.EmpShiftOverride;
import com.stpl.tech.attendance.entity.EmployeeShiftInstances;
import com.stpl.tech.attendance.entity.RosteringEntity.RosteringConstants;
import com.stpl.tech.attendance.entity.RosteringEntity.Shift;
import com.stpl.tech.attendance.repository.EmpShiftOverrideRepository;
import com.stpl.tech.attendance.repository.EmployeeShiftInstancesRepository;
import com.stpl.tech.attendance.repository.RosteringRepository.ShiftRepository;
import com.stpl.tech.attendance.service.EmployeeShiftSchedulerService;
import com.stpl.tech.attendance.service.UnitResolutionService;
import com.stpl.tech.attendance.util.ShiftHelper;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.attendance.constants.AppConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.DayOfWeek;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class EmployeeShiftSchedulerServiceImpl implements EmployeeShiftSchedulerService {
    
    private final UserCacheService userCacheService;
    private final UnitCacheService unitCacheService;
    private final EmployeeShiftInstancesRepository employeeShiftInstancesRepository;
    private final EmpShiftOverrideRepository empShiftOverrideRepository;
    private final ShiftRepository shiftRepository;
    private final UnitResolutionService unitResolutionService;
    private final ShiftHelper shiftHelper;
    
    /**
     * Cron job to create weekly shift schedules every Sunday
     */
    @Override
    @Scheduled(cron = "0 0 2 * * SUN") // Every Sunday at 2 AM
    @Transactional
    public void createWeeklyShiftSchedules() {
        log.info("Starting weekly shift schedule creation");
        
        LocalDate weekStart = LocalDate.now().with(TemporalAdjusters.nextOrSame(DayOfWeek.MONDAY));
        LocalDate weekEnd = weekStart.plusDays(6);
        
        // Get all active cafe employees
        List<EmployeeBasicDetail> cafeEmployees = getActiveCafeEmployees();
        
        for (EmployeeBasicDetail employee : cafeEmployees) {
            for (LocalDate date = weekStart; !date.isAfter(weekEnd); date = date.plusDays(1)) {
                createShiftInstanceForEmployee(employee, date, "SYSTEM");
            }
        }
        
        log.info("Completed weekly shift schedule creation for {} employees", cafeEmployees.size());
    }
    
    /**
     * Manually recreate shift instances for specific date range and employees
     */
    @Override
    @Transactional
    public ShiftInstanceRecreationResponseDTO recreateShiftInstances(ShiftInstanceRecreationRequestDTO request) {
        String operationId = UUID.randomUUID().toString();
        log.info("Starting shift instance recreation. Operation ID: {}, Request: {}", operationId, request);
        
        LocalDateTime startTime = LocalDateTime.now();
        List<String> errors = new ArrayList<>();
        int totalInstancesCreated = 0;
        int totalInstancesDeleted = 0;
        int totalInstancesSkipped = 0;
        
        try {
            // Validate request
            validateRecreationRequest(request);
            
            // Get employees to process
            List<EmployeeBasicDetail> employeesToProcess = getEmployeesToProcess(request.getEmpIds());
            
            // Delete existing instances if force recreate is enabled
            if (Boolean.TRUE.equals(request.getForceRecreate())) {
                totalInstancesDeleted = deleteExistingInstances(employeesToProcess, request.getStartDate(), request.getEndDate());
                log.info("Deleted {} existing instances for force recreate", totalInstancesDeleted);
            }
            
            // Create new instances
            for (EmployeeBasicDetail employee : employeesToProcess) {
                for (LocalDate date = request.getStartDate(); !date.isAfter(request.getEndDate()); date = date.plusDays(1)) {
                    try {
                        boolean created = createShiftInstanceForEmployee(employee, date, request.getCreatedBy());
                        if (created) {
                            totalInstancesCreated++;
                        } else {
                            totalInstancesSkipped++;
                        }
                    } catch (Exception e) {
                        String error = String.format("Error creating instance for employee %d on date %s: %s", 
                                                   employee.getId(), date, e.getMessage());
                        errors.add(error);
                        log.error(error, e);
                    }
                }
            }
            
            // Determine status
            String status = determineOperationStatus(errors, totalInstancesCreated);
            String message = String.format("Recreation completed. Created: %d, Deleted: %d, Skipped: %d, Errors: %d", 
                                         totalInstancesCreated, totalInstancesDeleted, totalInstancesSkipped, errors.size());
            
            log.info("Shift instance recreation completed. Operation ID: {}, Duration: {} ms", 
                    operationId, Duration.between(startTime, LocalDateTime.now()).toMillis());
            
            return ShiftInstanceRecreationResponseDTO.builder()
                .operationId(operationId)
                .processedAt(LocalDateTime.now())
                .totalEmployees(employeesToProcess.size())
                .totalInstancesCreated(totalInstancesCreated)
                .totalInstancesDeleted(totalInstancesDeleted)
                .totalInstancesSkipped(totalInstancesSkipped)
                .errors(errors)
                .status(status)
                .message(message)
                .build();
                
        } catch (Exception e) {
            log.error("Error during shift instance recreation. Operation ID: {}", operationId, e);
            errors.add("Critical error: " + e.getMessage());
            
            return ShiftInstanceRecreationResponseDTO.builder()
                .operationId(operationId)
                .processedAt(LocalDateTime.now())
                .totalEmployees(0)
                .totalInstancesCreated(0)
                .totalInstancesDeleted(0)
                .totalInstancesSkipped(0)
                .errors(errors)
                .status("FAILED")
                .message("Recreation failed: " + e.getMessage())
                .build();
        }
    }
    
    /**
     * Get shift instances for an employee within a date range
     */
    @Override
    public EmployeeShiftInstancesRangeResponseDTO getShiftInstancesForEmployee(Integer empId, LocalDate startDate, LocalDate endDate) {
        log.debug("Getting shift instances for employee {} from {} to {}", empId, startDate, endDate);
        
        List<EmployeeShiftInstances> instances = employeeShiftInstancesRepository
            .findByEmpIdAndBusinessDateBetweenAndInstanceStatus(empId, startDate, endDate, RosteringConstants.ACTIVE);
        
        List<EmployeeShiftInstancesDTO> shiftInstances = instances.stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());
        
        return EmployeeShiftInstancesRangeResponseDTO.builder()
            .empId(empId)
            .startDate(startDate)
            .endDate(endDate)
            .shiftInstances(shiftInstances)
            .totalInstances(shiftInstances.size())
            .build();
    }
    
    /**
     * Get shift instances for multiple employees within a date range
     */
    @Override
    public EmployeeShiftInstancesMultiEmployeeResponseDTO getShiftInstancesForEmployees(List<Integer> empIds, LocalDate startDate, LocalDate endDate) {
        log.debug("Getting shift instances for {} employees from {} to {}", empIds.size(), startDate, endDate);
        
        List<EmployeeShiftInstances> instances = employeeShiftInstancesRepository
            .findByEmpIdsAndDateRange(empIds, startDate, endDate);
        
        List<EmployeeShiftInstancesDTO> shiftInstances = instances.stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());
        
        return EmployeeShiftInstancesMultiEmployeeResponseDTO.builder()
            .empIds(empIds)
            .startDate(startDate)
            .endDate(endDate)
            .shiftInstances(shiftInstances)
            .totalInstances(shiftInstances.size())
            .totalEmployees(empIds.size())
            .build();
    }
    
    /**
     * Get shift instances for a specific date
     */
    @Override
    public EmployeeShiftInstancesDateResponseDTO getShiftInstancesForDate(LocalDate businessDate) {
        log.debug("Getting shift instances for date {}", businessDate);
        
        List<EmployeeShiftInstances> instances = employeeShiftInstancesRepository
            .findByBusinessDate(businessDate);
        
        List<EmployeeShiftInstancesDTO> shiftInstances = instances.stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());
        
        return EmployeeShiftInstancesDateResponseDTO.builder()
            .businessDate(businessDate)
            .shiftInstances(shiftInstances)
            .totalInstances(shiftInstances.size())
            .build();
    }
    
    @Override
    public EmployeeShiftInstancesWeekResponseDTO getCurrentWeekShiftInstances(Integer empId) {
        log.info("Getting current week shift instances for employee: {}", empId);
        
        LocalDate weekStart = LocalDate.now().with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        LocalDate weekEnd = weekStart.plusDays(6);
        
        if (empId != null) {
            EmployeeShiftInstancesRangeResponseDTO rangeResponse = getShiftInstancesForEmployee(empId, weekStart, weekEnd);
            return EmployeeShiftInstancesWeekResponseDTO.builder()
                .empId(empId)
                .weekType("CURRENT_WEEK")
                .weekStart(weekStart)
                .weekEnd(weekEnd)
                .shiftInstances(rangeResponse.getShiftInstances())
                .totalInstances(rangeResponse.getTotalInstances())
                .build();
        } else {
            // Get all current week instances
            List<EmployeeShiftInstances> instances = employeeShiftInstancesRepository
                .findByBusinessDateBetweenAndInstanceStatus(weekStart, weekEnd , RosteringConstants.ACTIVE);
            List<EmployeeShiftInstancesDTO> shiftInstances = instances.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
            
            return EmployeeShiftInstancesWeekResponseDTO.builder()
                .empId(null)
                .weekType("CURRENT_WEEK")
                .weekStart(weekStart)
                .weekEnd(weekEnd)
                .shiftInstances(shiftInstances)
                .totalInstances(shiftInstances.size())
                .build();
        }
    }
    
    @Override
    public EmployeeShiftInstancesWeekResponseDTO getNextWeekShiftInstances(Integer empId) {
        log.info("Getting next week shift instances for employee: {}", empId);
        
        LocalDate nextWeekStart = LocalDate.now().with(TemporalAdjusters.next(DayOfWeek.MONDAY));
        LocalDate nextWeekEnd = nextWeekStart.plusDays(6);
        
        if (empId != null) {
            EmployeeShiftInstancesRangeResponseDTO rangeResponse = getShiftInstancesForEmployee(empId, nextWeekStart, nextWeekEnd);
            return EmployeeShiftInstancesWeekResponseDTO.builder()
                .empId(empId)
                .weekType("NEXT_WEEK")
                .weekStart(nextWeekStart)
                .weekEnd(nextWeekEnd)
                .shiftInstances(rangeResponse.getShiftInstances())
                .totalInstances(rangeResponse.getTotalInstances())
                .build();
        } else {
            // Get all next week instances
            List<EmployeeShiftInstances> instances = employeeShiftInstancesRepository
                .findByBusinessDateBetweenAndInstanceStatus(nextWeekStart, nextWeekEnd , RosteringConstants.ACTIVE);
            List<EmployeeShiftInstancesDTO> shiftInstances = instances.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
            
            return EmployeeShiftInstancesWeekResponseDTO.builder()
                .empId(null)
                .weekType("NEXT_WEEK")
                .weekStart(nextWeekStart)
                .weekEnd(nextWeekEnd)
                .shiftInstances(shiftInstances)
                .totalInstances(shiftInstances.size())
                .build();
        }
    }
    
    @Override
    public EmployeeShiftInstancesMonthResponseDTO getEmployeeShiftInstancesByMonth(Integer empId, int year, int month) {
        log.info("Getting shift instances for employee {} for month {}/{}", empId, month, year);
        
        // Calculate start and end dates for the month
        LocalDate monthStart = LocalDate.of(year, month, 1);
        LocalDate monthEnd = monthStart.withDayOfMonth(monthStart.lengthOfMonth());
        
        // Get shift instances for the employee
        List<EmployeeShiftInstances> instances = employeeShiftInstancesRepository
            .findByEmpIdAndBusinessDateBetweenAndInstanceStatus(empId, monthStart, monthEnd, RosteringConstants.ACTIVE);
        
        // Convert to detailed DTOs
        List<EmployeeShiftInstanceDetailDTO> shiftInstances = instances.stream()
            .map(this::convertToDetailDTO)
            .collect(Collectors.toList());
        
        // Build response wrapper
        return EmployeeShiftInstancesMonthResponseDTO.builder()
            .empId(empId)
            .year(year)
            .month(month)
            .shiftInstances(shiftInstances)
            .totalInstances(shiftInstances.size())
            .build();
    }
    
    @Override
    public EmployeeShiftInstancesMonthAggregatedResponseDTO getEmployeeShiftInstancesAggregatedByMonth(Integer empId, int year, int month) {
        log.info("Getting aggregated shift instances for employee {} for month {}/{}", empId, month, year);
        
        // Calculate start and end dates for the month
        LocalDate monthStart = LocalDate.of(year, month, 1);
        LocalDate monthEnd = monthStart.withDayOfMonth(monthStart.lengthOfMonth());
        
        // If current month, limit to current date
        LocalDate currentDate = LocalDate.now();
        if (monthStart.getYear() == currentDate.getYear() && monthStart.getMonth() == currentDate.getMonth()) {
            monthEnd = currentDate;
            log.info("Current month detected, limiting end date to: {}", monthEnd);
        }
        
        // Get shift instances for the employee
        List<EmployeeShiftInstances> instances = employeeShiftInstancesRepository
            .findByEmpIdAndBusinessDateBetweenAndInstanceStatus(empId, monthStart, monthEnd, RosteringConstants.ACTIVE);
        
        // Calculate aggregated statistics
        BigDecimal totalIdealWorkHours = BigDecimal.ZERO;
        BigDecimal totalActualWorkHours = BigDecimal.ZERO;
        int totalOnTimeDays = 0;
        int totalCompletedDays = 0;
        int totalAbsentDays = 0;
        int totalPartialDays = 0;
        
        for (EmployeeShiftInstances instance : instances) {
            // Add ideal hours
            if (instance.getIdealHours() != null) {
                totalIdealWorkHours = totalIdealWorkHours.add(instance.getIdealHours());
            }
            
            // Calculate actual work hours (capped at ideal hours if exceeded)
            if (instance.getActualHours() != null && instance.getIdealHours() != null) {
                BigDecimal actualHours = instance.getActualHours();
                BigDecimal idealHours = instance.getIdealHours();
                
                // Cap actual hours at ideal hours if exceeded
                if (actualHours.compareTo(idealHours) > 0) {
                    actualHours = idealHours;
                }
                totalActualWorkHours = totalActualWorkHours.add(actualHours);
            }
            
            // Count attendance status and on-time performance
            if (instance.getStatus() != null) {
                switch (instance.getStatus()) {
                    case PRESENT:
                        totalCompletedDays++;
                        // Check if on time (check-in time is before or equal to expected start time)
                        if (instance.getActualStartTime() != null && instance.getExpectedStartTime() != null) {
                            if (!instance.getActualStartTime().isAfter(instance.getExpectedStartTime())) {
                                totalOnTimeDays++;
                            }
                        }
                        break;
                    case ABSENT:
                        totalAbsentDays++;
                        // For ABSENT instances: if they had a scheduled shift, they were NOT on time
                        // (This is already handled - totalOnTimeDays remains unchanged)
                        break;
                    case LESS_WORKING_HOURS:
                        totalPartialDays++;
                        // Check if they were on time for partial attendance
                        if (instance.getActualStartTime() != null && instance.getExpectedStartTime() != null) {
                            if (!instance.getActualStartTime().isAfter(instance.getExpectedStartTime())) {
                                totalOnTimeDays++;
                            }
                        }
                        break;
                    default:
                        // Other statuses are not counted
                        break;
                }
            }
        }
        
        int totalDays = instances.size();
        
        // Calculate percentages
        // On-time percentage: Percentage of instances where employee was on time (regardless of attendance status)
        BigDecimal onTimePercentage = totalDays > 0 ? 
            BigDecimal.valueOf(totalOnTimeDays).multiply(BigDecimal.valueOf(100)).divide(BigDecimal.valueOf(totalDays), 2, BigDecimal.ROUND_HALF_UP) : 
            BigDecimal.ZERO;
        
        // Attendance percentage: Percentage of instances where employee was present
        BigDecimal attendancePercentage = totalDays > 0 ? 
            BigDecimal.valueOf(totalCompletedDays).multiply(BigDecimal.valueOf(100)).divide(BigDecimal.valueOf(totalDays), 2, BigDecimal.ROUND_HALF_UP) : 
            BigDecimal.ZERO;
        
        return EmployeeShiftInstancesMonthAggregatedResponseDTO.builder()
            .empId(empId)
            .year(year)
            .month(month)
            .totalIdealWorkHours(totalIdealWorkHours)
            .totalActualWorkHours(totalActualWorkHours)
            .totalOnTimeDays(totalOnTimeDays)
            .totalDays(totalDays)
            .totalCompletedDays(totalCompletedDays)
            .totalAbsentDays(totalAbsentDays)
            .totalPartialDays(totalPartialDays)
            .onTimePercentage(onTimePercentage)
            .attendancePercentage(attendancePercentage)
            .build();
    }
    
    /**
     * Create shift instance for a specific employee and date
     */
    private boolean createShiftInstanceForEmployee(EmployeeBasicDetail employee, LocalDate businessDate, String createdBy) {
        // Check if instance already exists
        if (employeeShiftInstancesRepository.existsByEmpIdAndBusinessDateAndInstanceStatus(employee.getId(), businessDate , RosteringConstants.ACTIVE)) {
            log.debug("Shift instance already exists for employee {} on date {}", employee.getId(), businessDate);
            return false;
        }
        
        // Get shift mapping for this employee and date
        Shift shift = getShiftForEmployee(employee.getId(), businessDate);
        
        // Calculate expected times and hours using ShiftHelper for universal shift handling
        LocalDateTime expectedStartTime;
        LocalDateTime expectedEndTime;
        
        // Check if this is a universal shift and use appropriate calculation
        if (shiftHelper.isUniversalShift(shift)) {
            expectedStartTime = shiftHelper.calculateExpectedStartTimeForUniversalShift(employee.getId(), businessDate);
            expectedEndTime = shiftHelper.calculateExpectedEndTimeForUniversalShift(employee.getId(), businessDate);
        } else {
            expectedStartTime = shiftHelper.calculateExpectedStartTime(shift, businessDate);
            expectedEndTime = shiftHelper.calculateExpectedEndTime(shift, businessDate);
        }
        
        BigDecimal idealHours = shiftHelper.calculateIdealHours(expectedStartTime, expectedEndTime);
        
        // Create shift instance
        EmployeeShiftInstances instance = EmployeeShiftInstances.builder()
            .empId(employee.getId())
            .businessDate(businessDate)
            .shiftId(shift.getShiftId())
            .unitId(unitResolutionService.getUnitIdForEmployee(employee.getId(), businessDate))
            .expectedStartTime(expectedStartTime)
            .expectedEndTime(expectedEndTime)
            .idealHours(idealHours)
            .status(AttendanceStatus.ABSENT)
            .createdBy(createdBy)
                .instanceStatus("ACTIVE")
            .build();
        
        employeeShiftInstancesRepository.save(instance);
        log.debug("Created shift instance for employee {} on date {} with shift {}", 
                 employee.getId(), businessDate, shift.getShiftName());
        return true;
    }
    
    /**
     * Get shift for employee on specific date (with override priority)
     */
    private Shift getShiftForEmployee(Integer empId, LocalDate businessDate) {
        // First check for temporary override
        EmpShiftOverride override = getActiveOverrideForDate(empId, businessDate);
        
        if (override != null) {
            Shift overrideShift = shiftRepository.findById(override.getShiftId())
                .orElseThrow(() -> new RuntimeException("Override shift not found"));
            log.debug("Using override shift {} for employee {} on date {}", 
                     overrideShift.getShiftName(), empId, businessDate);
            return overrideShift;
        }
        
        // Check regular shift mapping using Reladomo
        EmpShiftMapping regularMapping = getEmpShiftMappingForDate(empId, businessDate);
        
        if (regularMapping != null) {
            Shift regularShift = shiftRepository.findById(regularMapping.getShiftId())
                .orElseThrow(() -> new RuntimeException("Regular shift not found"));
            log.debug("Using regular shift {} for employee {} on date {}", 
                     regularShift.getShiftName(), empId, businessDate);
            return regularShift;
        }
        
        // Use default universal shift
        Shift defaultShift = shiftHelper.getDefaultUniversalShift();
        log.debug("Using default shift {} for employee {} on date {}", 
                 defaultShift.getShiftName(), empId, businessDate);
        return defaultShift;
    }
    
    /**
     * Get active override for specific date
     */
    private EmpShiftOverride getActiveOverrideForDate(Integer empId, LocalDate businessDate) {
        return empShiftOverrideRepository.findByEmpIdAndStartDateLessThanEqualAndEndDateGreaterThanEqualAndStatus(
            empId, businessDate, businessDate, "ACTIVE");
    }
    
    /**
     * Get employee shift mapping for specific date using Reladomo
     */
    private EmpShiftMapping getEmpShiftMappingForDate(Integer empId, LocalDate businessDate) {
        try {
            // Convert LocalDate to Timestamp for Reladomo
            Timestamp businessDateTimestamp = Timestamp.valueOf(businessDate.atStartOfDay());
            Timestamp currentTime = Timestamp.valueOf(LocalDateTime.now());
            
            // Use Reladomo to find active shift mappings for the employee at the business date
            EmpShiftMappingList mappings = EmpShiftMappingFinder.findMany(
                EmpShiftMappingFinder.empId().eq(empId)
                .and(EmpShiftMappingFinder.status().eq("ACTIVE"))
                .and(EmpShiftMappingFinder.businessDateFrom().lessThanEquals(businessDateTimestamp))
                .and(EmpShiftMappingFinder.businessDateTo().greaterThan(businessDateTimestamp))
                .and(EmpShiftMappingFinder.processingDateFrom().lessThanEquals(currentTime))
                .and(EmpShiftMappingFinder.processingDateTo().greaterThan(currentTime))
            );
            
            if (mappings != null && !mappings.isEmpty()) {
                EmpShiftMapping mapping = mappings.get(0);
                log.debug("Found Reladomo shift mapping for employee {} on date {}: shiftId={}", 
                         empId, businessDate, mapping.getShiftId());
                return mapping;
            }
            
            log.debug("No active Reladomo shift mapping found for employee {} on date {}", empId, businessDate);
            return null;
            
        } catch (Exception e) {
            log.error("Error getting Reladomo shift mapping for employee {} on date {}", empId, businessDate, e);
            return null;
        }
    }
    

    

    
    /**
     * Get active cafe employees
     */
    private List<EmployeeBasicDetail> getActiveCafeEmployees() {
        return userCacheService.getAllUserCache().values().stream()
            .filter(emp -> com.stpl.tech.util.AppConstants.ACTIVE.equals(emp.getStatus().name()))
            .filter(emp -> "CAFE".equalsIgnoreCase(emp.getDepartmentName()))
            .collect(Collectors.toList());
    }
    
    /**
     * Validate recreation request
     */
    private void validateRecreationRequest(ShiftInstanceRecreationRequestDTO request) {
        if (request.getStartDate() == null || request.getEndDate() == null) {
            throw new IllegalArgumentException("Start date and end date are required");
        }
        
        if (request.getStartDate().isAfter(request.getEndDate())) {
            throw new IllegalArgumentException("Start date cannot be after end date");
        }
        
        if (request.getCreatedBy() == null || request.getCreatedBy().trim().isEmpty()) {
            throw new IllegalArgumentException("Created by is required");
        }
    }
    
    /**
     * Get employees to process based on request
     */
    private List<EmployeeBasicDetail> getEmployeesToProcess(List<Integer> empIds) {
        if (empIds != null && !empIds.isEmpty()) {
            // Process specific employees
            return empIds.stream()
                .map(id -> userCacheService.getUserById(id))
                .filter(emp -> emp != null && com.stpl.tech.util.AppConstants.ACTIVE.equals(emp.getStatus().name()))
                .collect(Collectors.toList());
        } else {
            // Process all cafe employees
            return getActiveCafeEmployees();
        }
    }
    
    /**
     * Delete existing instances for force recreate
     */
    private int deleteExistingInstances(List<EmployeeBasicDetail> employees, LocalDate startDate, LocalDate endDate) {
        List<Integer> empIds = employees.stream()
            .map(EmployeeBasicDetail::getId)
            .collect(Collectors.toList());
        
        List<EmployeeShiftInstances> existingInstances = employeeShiftInstancesRepository
            .findByEmpIdsAndDateRange(empIds, startDate, endDate);
        
        employeeShiftInstancesRepository.deleteAll(existingInstances);
        return existingInstances.size();
    }
    
    /**
     * Determine operation status based on results
     */
    private String determineOperationStatus(List<String> errors, int totalCreated) {
        if (errors.isEmpty()) {
            return "SUCCESS";
        } else if (totalCreated > 0) {
            return "PARTIAL_SUCCESS";
        } else {
            return "FAILED";
        }
    }
    
    /**
     * Convert entity to DTO
     */
    private EmployeeShiftInstancesDTO convertToDTO(EmployeeShiftInstances instance) {
        EmployeeBasicDetail employee = userCacheService.getUserById(instance.getEmpId());
        Shift shift = shiftRepository.findById(instance.getShiftId()).orElse(null);
        
        return EmployeeShiftInstancesDTO.builder()
            .id(instance.getId())
            .empId(instance.getEmpId())
            .empName(employee != null ? employee.getName() : null)
            .empCode(employee != null ? employee.getEmployeeCode() : null)
            .businessDate(instance.getBusinessDate())
            .shiftId(instance.getShiftId())
            .shiftName(shift != null ? shift.getShiftName() : null)
            .unitId(instance.getUnitId())
            .unitName(getUnitName(instance.getUnitId()))
            .expectedStartTime(instance.getExpectedStartTime())
            .expectedEndTime(instance.getExpectedEndTime())
            .idealHours(instance.getIdealHours())
            .actualStartTime(instance.getActualStartTime())
            .actualEndTime(instance.getActualEndTime())
            .actualHours(instance.getActualHours())
            .status(instance.getStatus())
            .type(instance.getType())
            .createdBy(instance.getCreatedBy())
            .createdAt(instance.getCreatedAt())
            .updatedBy(instance.getUpdatedBy())
            .updatedAt(instance.getUpdatedAt())
            .build();
    }
    
    /**
     * Convert entity to detailed DTO with shift data and attendance information
     */
    private EmployeeShiftInstanceDetailDTO convertToDetailDTO(EmployeeShiftInstances instance) {
        Shift shift = shiftRepository.findById(instance.getShiftId()).orElse(null);
        
        // Build shift data
        EmployeeShiftInstanceDetailDTO.ShiftData shiftData = EmployeeShiftInstanceDetailDTO.ShiftData.builder()
            .shiftId(instance.getShiftId())
            .shiftName(shift != null ? shift.getShiftName() : null)
            .shiftStartTime(instance.getExpectedStartTime())
            .shiftEndTime(instance.getExpectedEndTime())
            .build();
        
        // Determine status based on schedule status and attendance
        String status = determineAttendanceStatus(instance);
        
        return EmployeeShiftInstanceDetailDTO.builder()
            .businessDate(instance.getBusinessDate())
            .shiftData(shiftData)
            .checkInTime(instance.getActualStartTime())
            .checkOutTime(instance.getActualEndTime())
            .totalWorkHours(instance.getActualHours())
            .status(status)
            .build();
    }
    
    /**
     * Determine attendance status based on shift instance data
     */
    private String determineAttendanceStatus(EmployeeShiftInstances instance) {
        // Check if this is a WEEK_OFF type first
        if (instance.getType() != null && AppConstants.WEEK_OFF.equalsIgnoreCase(instance.getType())) {
            return AppConstants.WEEK_OFF;
        }
        
        // For regular shifts, determine status based on attendance data
        if (instance.getActualStartTime() != null && instance.getActualEndTime() != null) {
            return instance.getStatus().name();
        } else if (instance.getActualStartTime() != null) {
            return "CHECKED_IN";
        } else if (instance.getBusinessDate().isBefore(LocalDate.now())) {
            return "ABSENT";
        } else {
            return "SCHEDULED";
        }
    }



    /**
     * Get unit name by unit ID
     * @param unitId Unit ID
     * @return Unit name or null if not found
     */
    private String getUnitName(Integer unitId) {
        if (unitId == null) {
            return null;
        }
        try {
            UnitBasicDetail unit = unitCacheService.getUnitBasicDetail(unitId);
            return unit != null ? unit.getName() : null;
        } catch (Exception e) {
            log.warn("Error getting unit name for unitId: {}", unitId, e);
            return null;
        }
    }
} 