package com.stpl.tech.attendance.approval.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApprovalRequest {
    private String requestId;
    private String entityType;
    private String entityId;
    private String requesterId;
    private String currentStep;
    private String status;
    private List<ApprovalStep> steps;
    private String comments;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ApprovalStep {
        private Integer stepOrder;
        private String approverRole;
        private String approverId;
        private String comments;
    }
} 