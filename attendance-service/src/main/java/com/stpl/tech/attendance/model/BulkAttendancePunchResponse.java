package com.stpl.tech.attendance.model;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class BulkAttendancePunchResponse {
    private boolean success;
    private String message;
    private LocalDateTime processedAt;
    private int totalRequests;
    private int successfulRequests;
    private int failedRequests;
    private List<AttendancePunchResponse> results;
    private String bulkOperationId; // For tracking the bulk operation
} 