package com.stpl.tech.attendance.service;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

@Service
public class MetricsService {
    
    private final MeterRegistry meterRegistry;
    
    // Attendance metrics
    private final Counter checkInCounter;
    private final Counter checkOutCounter;
    private final Timer checkInTimer;
    private final Timer checkOutTimer;
    private final Counter checkInSuccessCounter;
    private final Counter checkInFailureCounter;
    private final Counter checkOutSuccessCounter;
    private final Counter checkOutFailureCounter;
    
    // Approval metrics
    private final Counter approvalRequestCounter;
    private final Counter approvalApprovedCounter;
    private final Counter approvalRejectedCounter;
    private final Timer approvalProcessTimer;
    private final Counter bulkApprovalCounter;
    private final Counter approvalSuccessCounter;
    private final Counter approvalFailureCounter;
    
    // Biometric metrics
    private final Counter biometricRegistrationCounter;
    private final Counter biometricDeregistrationCounter;
    private final Timer biometricRegistrationTimer;
    private final Counter biometricStatusCheckCounter;
    private final Counter biometricVerificationCounter;
    private final Timer biometricVerificationTimer;
    private final Timer biometricStatusCheckTimer;
    private final Counter biometricSuccessCounter;
    private final Counter biometricFailureCounter;
    
    // Metadata metrics
    private final Counter metadataRequestCounter;
    private final Counter biometricMetadataRequestCounter;
    private final Counter devicePairingCounter;
    private final Counter deviceValidationCounter;
    private final Timer metadataOperationTimer;
    private final Timer deviceOperationTimer;
    private final Counter metadataSuccessCounter;
    private final Counter metadataFailureCounter;
    private final Counter deviceSuccessCounter;
    private final Counter deviceFailureCounter;

    // Notification metrics
    private final Counter notificationRequestCounter;
    private final Counter bulkMarkReadCounter;
    private final Timer notificationOperationTimer;
    private final Counter notificationSuccessCounter;
    private final Counter notificationFailureCounter;

    // Error metrics
    private final Counter attendanceErrorCounter;
    private final Counter validationErrorCounter;
    private final Counter systemErrorCounter;

    public MetricsService(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        
        // Initialize attendance metrics
        this.checkInCounter = Counter.builder("attendance.checkin.total")
                .description("Total number of check-ins")
                .register(meterRegistry);
                
        this.checkOutCounter = Counter.builder("attendance.checkout.total")
                .description("Total number of check-outs")
                .register(meterRegistry);
                
        this.checkInTimer = Timer.builder("attendance.checkin.duration")
                .description("Time taken for check-in operations")
                .register(meterRegistry);
                
        this.checkOutTimer = Timer.builder("attendance.checkout.duration")
                .description("Time taken for check-out operations")
                .register(meterRegistry);

        this.checkInSuccessCounter = Counter.builder("attendance.checkin.success.total")
                .description("Total number of successful check-ins")
                .register(meterRegistry);

        this.checkInFailureCounter = Counter.builder("attendance.checkin.failure.total")
                .description("Total number of failed check-ins")
                .register(meterRegistry);

        this.checkOutSuccessCounter = Counter.builder("attendance.checkout.success.total")
                .description("Total number of successful check-outs")
                .register(meterRegistry);

        this.checkOutFailureCounter = Counter.builder("attendance.checkout.failure.total")
                .description("Total number of failed check-outs")
                .register(meterRegistry);
                
        // Initialize approval metrics
        this.approvalRequestCounter = Counter.builder("attendance.approval.requests.total")
                .description("Total number of approval requests")
                .register(meterRegistry);
                
        this.approvalApprovedCounter = Counter.builder("attendance.approval.approved.total")
                .description("Total number of approved requests")
                .register(meterRegistry);
                
        this.approvalRejectedCounter = Counter.builder("attendance.approval.rejected.total")
                .description("Total number of rejected requests")
                .register(meterRegistry);
                
        this.approvalProcessTimer = Timer.builder("attendance.approval.process.duration")
                .description("Time taken for approval processing")
                .register(meterRegistry);
                
        this.bulkApprovalCounter = Counter.builder("attendance.approval.bulk.total")
                .description("Total number of bulk approval operations")
                .register(meterRegistry);

        this.approvalSuccessCounter = Counter.builder("attendance.approval.success.total")
                .description("Total number of successful approval operations")
                .register(meterRegistry);

        this.approvalFailureCounter = Counter.builder("attendance.approval.failure.total")
                .description("Total number of failed approval operations")
                .register(meterRegistry);
                
        // Initialize biometric metrics
        this.biometricRegistrationCounter = Counter.builder("attendance.biometric.registration.total")
                .description("Total number of biometric registrations")
                .register(meterRegistry);
                
        this.biometricDeregistrationCounter = Counter.builder("attendance.biometric.deregistration.total")
                .description("Total number of biometric deregistrations")
                .register(meterRegistry);
                
        this.biometricRegistrationTimer = Timer.builder("attendance.biometric.registration.duration")
                .description("Time taken for biometric registration")
                .register(meterRegistry);
                
        this.biometricStatusCheckCounter = Counter.builder("attendance.biometric.status.check.total")
                .description("Total number of biometric status checks")
                .register(meterRegistry);

        this.biometricVerificationCounter = Counter.builder("attendance.biometric.verification.total")
                .description("Total number of biometric verifications")
                .register(meterRegistry);

        this.biometricVerificationTimer = Timer.builder("attendance.biometric.verification.duration")
                .description("Time taken for biometric verification")
                .register(meterRegistry);

        this.biometricStatusCheckTimer = Timer.builder("attendance.biometric.status.check.duration")
                .description("Time taken for biometric status checks")
                .register(meterRegistry);

        this.biometricSuccessCounter = Counter.builder("attendance.biometric.success.total")
                .description("Total number of successful biometric operations")
                .register(meterRegistry);

        this.biometricFailureCounter = Counter.builder("attendance.biometric.failure.total")
                .description("Total number of failed biometric operations")
                .register(meterRegistry);
                
        // Initialize metadata metrics
        this.metadataRequestCounter = Counter.builder("attendance.metadata.requests.total")
                .description("Total number of metadata requests")
                .register(meterRegistry);
                
        this.biometricMetadataRequestCounter = Counter.builder("attendance.metadata.biometric.requests.total")
                .description("Total number of biometric metadata requests")
                .register(meterRegistry);
                
        this.devicePairingCounter = Counter.builder("attendance.device.pairing.total")
                .description("Total number of device pairings")
                .register(meterRegistry);
                
        this.deviceValidationCounter = Counter.builder("attendance.device.validation.total")
                .description("Total number of device validations")
                .register(meterRegistry);

        this.metadataOperationTimer = Timer.builder("attendance.metadata.operation.duration")
                .description("Time taken for metadata operations")
                .register(meterRegistry);

        this.deviceOperationTimer = Timer.builder("attendance.device.operation.duration")
                .description("Time taken for device operations")
                .register(meterRegistry);

        this.metadataSuccessCounter = Counter.builder("attendance.metadata.success.total")
                .description("Total number of successful metadata operations")
                .register(meterRegistry);

        this.metadataFailureCounter = Counter.builder("attendance.metadata.failure.total")
                .description("Total number of failed metadata operations")
                .register(meterRegistry);

        this.deviceSuccessCounter = Counter.builder("attendance.device.success.total")
                .description("Total number of successful device operations")
                .register(meterRegistry);

        this.deviceFailureCounter = Counter.builder("attendance.device.failure.total")
                .description("Total number of failed device operations")
                .register(meterRegistry);

        // Initialize notification metrics
        this.notificationRequestCounter = Counter.builder("attendance.notification.requests.total")
                .description("Total number of notification requests")
                .register(meterRegistry);
                
        this.bulkMarkReadCounter = Counter.builder("attendance.notification.bulk.mark.read.total")
                .description("Total number of bulk mark as read operations")
                .register(meterRegistry);

        this.notificationOperationTimer = Timer.builder("attendance.notification.operation.duration")
                .description("Time taken for notification operations")
                .register(meterRegistry);

        this.notificationSuccessCounter = Counter.builder("attendance.notification.success.total")
                .description("Total number of successful notification operations")
                .register(meterRegistry);

        this.notificationFailureCounter = Counter.builder("attendance.notification.failure.total")
                .description("Total number of failed notification operations")
                .register(meterRegistry);

        // Initialize error metrics
        this.attendanceErrorCounter = Counter.builder("attendance.errors.total")
                .description("Total number of attendance service errors")
                .register(meterRegistry);

        this.validationErrorCounter = Counter.builder("attendance.errors.validation.total")
                .description("Total number of validation errors")
                .register(meterRegistry);

        this.systemErrorCounter = Counter.builder("attendance.errors.system.total")
                .description("Total number of system errors")
                .register(meterRegistry);
    }

    // Attendance metrics methods
    public void incrementCheckIn() {
        checkInCounter.increment();
    }

    public void incrementCheckOut() {
        checkOutCounter.increment();
    }

    public void incrementCheckInSuccess() {
        checkInSuccessCounter.increment();
    }

    public void incrementCheckInFailure() {
        checkInFailureCounter.increment();
    }

    public void incrementCheckOutSuccess() {
        checkOutSuccessCounter.increment();
    }

    public void incrementCheckOutFailure() {
        checkOutFailureCounter.increment();
    }

    public Timer.Sample startCheckInTimer() {
        return Timer.start(meterRegistry);
    }

    public Timer.Sample startCheckOutTimer() {
        return Timer.start(meterRegistry);
    }

    public void recordCheckInDuration(Timer.Sample sample) {
        sample.stop(checkInTimer);
    }

    public void recordCheckOutDuration(Timer.Sample sample) {
        sample.stop(checkOutTimer);
    }

    // Approval metrics methods
    public void incrementApprovalRequest() {
        approvalRequestCounter.increment();
    }

    public void incrementApprovalApproved() {
        approvalApprovedCounter.increment();
    }

    public void incrementApprovalRejected() {
        approvalRejectedCounter.increment();
    }

    public void incrementBulkApproval() {
        bulkApprovalCounter.increment();
    }

    public void incrementApprovalSuccess() {
        approvalSuccessCounter.increment();
    }

    public void incrementApprovalFailure() {
        approvalFailureCounter.increment();
    }

    public Timer.Sample startApprovalProcessTimer() {
        return Timer.start(meterRegistry);
    }

    public void recordApprovalProcessDuration(Timer.Sample sample) {
        sample.stop(approvalProcessTimer);
    }

    // Biometric metrics methods
    public void incrementBiometricRegistration() {
        biometricRegistrationCounter.increment();
    }

    public void incrementBiometricDeregistration() {
        biometricDeregistrationCounter.increment();
    }

    public void incrementBiometricStatusCheck() {
        biometricStatusCheckCounter.increment();
    }

    public void incrementBiometricVerification() {
        biometricVerificationCounter.increment();
    }

    public void incrementBiometricSuccess() {
        biometricSuccessCounter.increment();
    }

    public void incrementBiometricFailure() {
        biometricFailureCounter.increment();
    }

    public Timer.Sample startBiometricRegistrationTimer() {
        return Timer.start(meterRegistry);
    }

    public Timer.Sample startBiometricVerificationTimer() {
        return Timer.start(meterRegistry);
    }

    public Timer.Sample startBiometricStatusCheckTimer() {
        return Timer.start(meterRegistry);
    }

    public void recordBiometricRegistrationDuration(Timer.Sample sample) {
        sample.stop(biometricRegistrationTimer);
    }

    public void recordBiometricVerificationDuration(Timer.Sample sample) {
        sample.stop(biometricVerificationTimer);
    }

    public void recordBiometricStatusCheckDuration(Timer.Sample sample) {
        sample.stop(biometricStatusCheckTimer);
    }

    // Metadata metrics methods
    public void incrementMetadataRequest() {
        metadataRequestCounter.increment();
    }

    public void incrementBiometricMetadataRequest() {
        biometricMetadataRequestCounter.increment();
    }

    public void incrementDevicePairing() {
        devicePairingCounter.increment();
    }

    public void incrementDeviceValidation() {
        deviceValidationCounter.increment();
    }

    public void incrementMetadataSuccess() {
        metadataSuccessCounter.increment();
    }

    public void incrementMetadataFailure() {
        metadataFailureCounter.increment();
    }

    public void incrementDeviceSuccess() {
        deviceSuccessCounter.increment();
    }

    public void incrementDeviceFailure() {
        deviceFailureCounter.increment();
    }

    public Timer.Sample startMetadataOperationTimer() {
        return Timer.start(meterRegistry);
    }

    public Timer.Sample startDeviceOperationTimer() {
        return Timer.start(meterRegistry);
    }

    public void recordMetadataOperationDuration(Timer.Sample sample) {
        sample.stop(metadataOperationTimer);
    }

    public void recordDeviceOperationDuration(Timer.Sample sample) {
        sample.stop(deviceOperationTimer);
    }

    // Notification metrics methods
    public void incrementNotificationRequest() {
        notificationRequestCounter.increment();
    }

    public void incrementBulkMarkRead() {
        bulkMarkReadCounter.increment();
    }

    public void incrementNotificationSuccess() {
        notificationSuccessCounter.increment();
    }

    public void incrementNotificationFailure() {
        notificationFailureCounter.increment();
    }

    public Timer.Sample startNotificationOperationTimer() {
        return Timer.start(meterRegistry);
    }

    public void recordNotificationOperationDuration(Timer.Sample sample) {
        sample.stop(notificationOperationTimer);
    }

    // Error metrics methods
    public void incrementError() {
        attendanceErrorCounter.increment();
    }

    public void incrementValidationError() {
        validationErrorCounter.increment();
    }

    public void incrementSystemError() {
        systemErrorCounter.increment();
    }

    public Timer getCheckInTimer() {
        return checkInTimer;
    }
} 