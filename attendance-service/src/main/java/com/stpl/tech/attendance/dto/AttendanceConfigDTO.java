package com.stpl.tech.attendance.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class AttendanceConfigDTO {
    // Registration liveliness properties
    private boolean attendanceRegistrationIsLivelinessCheck;
    private Integer attendanceRegistrationStartAngle;
    private Integer attendanceRegistrationEndAngle;
    private Integer attendanceRegistrationAngleChangeInterval;
    private Integer attendanceRegistrationAngleChangeThreshold;
    private List<String> attendanceRegistrationLivelinessChallenges;

    // Registration multi-frame properties
    private boolean attendanceRegistrationIsMultiFrameMode;
    private Integer attendanceRegistrationFrameTimeLimit;
    private List<String> attendanceRegistrationFramesList;
    private Double attendanceRegistrationGoodLightThreshold;
    private Double attendanceRegistrationBlurThreshold;
    private Double attendanceRegistrationFaceSizeThreshold;
    private Double attendanceRegistrationDeltaThreshold;
    private Double attendanceRegistrationMaxDeltaThreshold;

    // Punch-in liveliness properties
    private boolean attendancePunchinIsLivelinessCheck;
    private Integer attendancePunchinStartAngle;
    private Integer attendancePunchinEndAngle;
    private Integer attendancePunchinAngleChangeInterval;
    private Integer attendancePunchinAngleChangeThreshold;
    private List<String> attendancePunchinLivelinessChallenges;

    // Punch-in multi-frame properties
    private boolean attendancePunchinIsMultiFrameMode;
    private Integer attendancePunchinFrameTimeLimit;
    private List<String> attendancePunchinFramesList;
    private Double attendancePunchinGoodLightThreshold;
    private Double attendancePunchinBlurThreshold;
    private Double attendancePunchinFaceSizeThreshold;
    private Double attendancePunchinDeltaThreshold;
    private Double attendancePunchinMaxDeltaThreshold;

    // Admin contact properties
    private String attendanceAdminDefaultContact;
    private String attendanceAdminDefaultEmail;

    // Registration threshold properties
    private Double attendanceRegistrationBrightnessThreshold;
    private Double attendanceRegistrationContrastThreshold;
    private Double attendanceRegistrationRotationThreshold;

    // Punch-in threshold properties
    private Double attendancePunchinBrightnessThreshold;
    private Double attendancePunchinContrastThreshold;
    private Double attendancePunchinRotationThreshold;
} 