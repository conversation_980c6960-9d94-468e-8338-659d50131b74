package com.stpl.tech.attendance.dto.RosteringDto;

import com.stpl.tech.attendance.dto.EmployeeMetadataDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShiftEmployeesResponseDTO {
    private List<ShiftDTO> shifts;
    private GenericFilterMetadataDTO filterMetadata;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ShiftDTO {
        private Integer shiftId;
        private String shiftName;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
//        private List<EmployeeDTO> employees;
        private List<EmployeeMetadataDTO> employeeMetadata;
    }
//
//    @Data
//    @Builder
//    @NoArgsConstructor
//    @AllArgsConstructor
//    public static class EmployeeDTO {
//        private Integer employeeId;
//        private String name;
//    }
}
