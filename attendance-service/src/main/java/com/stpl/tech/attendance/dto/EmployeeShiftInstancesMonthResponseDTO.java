package com.stpl.tech.attendance.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmployeeShiftInstancesMonthResponseDTO {
    private Integer empId;
    private int year;
    private int month;
    private List<EmployeeShiftInstanceDetailDTO> shiftInstances;
    private int totalInstances;
} 