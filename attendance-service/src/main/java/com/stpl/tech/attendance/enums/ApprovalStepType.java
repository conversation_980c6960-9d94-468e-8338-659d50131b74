package com.stpl.tech.attendance.enums;

public enum ApprovalStepType {
    SEQUENTIAL,     // Step must be completed before next step starts
    PARALLEL,       // Multiple approvers can approve in parallel
    ANY_ONE,        // Only one approval is required from multiple approvers
    ALL,            // All approvers must approve
    MINIMUM         // Minimum number of approvals required from multiple approvers
} 