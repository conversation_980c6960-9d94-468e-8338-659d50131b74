package com.stpl.tech.attendance.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApplyRegularisationRequest {
    
    /**
     * Date for the regularisation
     */
    private LocalDateTime date;

    private Integer empId;
    
    /**
     * Check-in time for the day
     */
    private LocalDateTime checkIn;
    
    /**
     * Check-out time for the day
     */
    private LocalDateTime checkOut;
    
    /**
     * Reason for regularisation
     */
    private String reason;
    
    /**
     * Additional comments
     */
    private String comments;

    private Boolean overrideRequest;
} 