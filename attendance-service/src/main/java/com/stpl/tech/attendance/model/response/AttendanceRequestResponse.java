package com.stpl.tech.attendance.model.response;

import com.stpl.tech.attendance.dto.EmployeeInfoDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AttendanceRequestResponse {
    private Long requestId;
    private EmployeeInfoDTO employeeInfo;
    private EmployeeInfoDTO createdBy;
    private EmployeeInfoDTO updatedBy;
    private String requestType; // This will be the type from EMP_ATTENDANCE_REQUEST table
    private String status;
    private String reason;
    private LocalDate startDate; // Extracted from startTime (check-in)
    private LocalDate endDate;   // Extracted from endTime (check-out)
    private LocalDateTime requestDate;
    private LocalDateTime lastUpdatedDate;
    private String comments;
    private String registrationImageUrl;
} 