package com.stpl.tech.attendance.notification.dto;

import com.stpl.tech.attendance.notification.entity.Notification.NotificationType;
import com.stpl.tech.attendance.notification.entity.Notification.Priority;
import com.stpl.tech.attendance.notification.entity.NotificationRecipient.DeliveryStatus;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationResponse {
    private String notificationId;
    private NotificationType type;
    private String title;
    private String message;
    private Priority priority;
    private Map<String, Object> metadata;
    private DeliveryStatus status;
    private LocalDateTime createdDate;
    private LocalDateTime readDate;
    private String requesterId;
    private String requesterName;
    private String requesterDesignation;
    private String requesterImageUrl;
    private String requesterEmployeeCode;
} 