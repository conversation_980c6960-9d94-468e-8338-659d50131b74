package com.stpl.tech.attendance.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stpl.tech.attendance.approval.entity.ApprovalRequest;
import com.stpl.tech.attendance.approval.entity.SubApprovalStep;
import com.stpl.tech.attendance.approval.service.ApprovalEngineService;
import com.stpl.tech.attendance.approval.repository.ApprovalStepRepository;
import com.stpl.tech.attendance.approval.repository.SubApprovalStepRepository;
import com.stpl.tech.attendance.cache.service.UserCacheService;
import com.stpl.tech.attendance.constants.AppConstants;
import com.stpl.tech.attendance.context.JwtContext;
import com.stpl.tech.attendance.dto.ApplyLeaveRequest;
import com.stpl.tech.attendance.dto.ApplyLeaveResponse;
import com.stpl.tech.attendance.dto.ApplyOdWfhRequest;
import com.stpl.tech.attendance.dto.ApplyOdWfhResponse;
import com.stpl.tech.attendance.dto.ApplyRegularisationRequest;
import com.stpl.tech.attendance.dto.ApplyRegularisationResponse;
import com.stpl.tech.attendance.dto.UploadLeaveDocumentRequest;
import com.stpl.tech.attendance.dto.UploadLeaveDocumentResponse;
import com.stpl.tech.attendance.dto.ApplyWeekOffRequest;
import com.stpl.tech.attendance.dto.ApplyWeekOffResponse;

import com.stpl.tech.attendance.entity.DailyAttendanceSummary;
import com.stpl.tech.attendance.entity.EmpAttendanceReserveData;
import com.stpl.tech.attendance.entity.EmployeeAttendanceRequest;
import com.stpl.tech.attendance.entity.EmployeeAttendanceRequestDetail;
import com.stpl.tech.attendance.entity.EmpAttendanceBalanceData;
import com.stpl.tech.attendance.entity.AttendanceStatus;
import com.stpl.tech.attendance.entity.AttendanceType;
import com.stpl.tech.attendance.enums.ApprovalStatus;
import com.stpl.tech.attendance.enums.ApprovalType;
import com.stpl.tech.attendance.enums.ApprovalStepStatus;
import com.stpl.tech.attendance.exception.BusinessException;
import com.stpl.tech.attendance.repository.DailyAttendanceSummaryRepository;
import com.stpl.tech.attendance.repository.EmployeeAttendanceRequestRepository;
import com.stpl.tech.attendance.repository.EmpAttendanceReserveDataRepository;
import com.stpl.tech.attendance.repository.EmpAttendanceBalanceDataRepository;
import com.stpl.tech.attendance.repository.EmpLeaveDataLogsRepository;
import com.stpl.tech.attendance.service.AttendanceMetadataService;
import com.stpl.tech.attendance.service.EmployeeAttendanceRequestDetailService;
import com.stpl.tech.attendance.service.EmployeeAttendanceService;
import com.stpl.tech.attendance.service.EmployeeShiftInstanceRecreationService;
import com.stpl.tech.attendance.service.LeaveBalanceService;
import com.stpl.tech.attendance.service.S3Service;
import com.stpl.tech.attendance.util.DateTimeUtil;
import com.stpl.tech.attendance.approval.repository.ApprovalRequestRepository;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.orm.ObjectOptimisticLockingFailureException;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;

import com.stpl.tech.attendance.dto.CancelRequestRequest;
import com.stpl.tech.attendance.dto.CancelRequestResponse;
import com.stpl.tech.attendance.service.DailyAttendanceSummaryLogsService;
import com.stpl.tech.attendance.entity.DailyAttendanceSummaryLogs;
import com.stpl.tech.attendance.validation.LeaveRequestValidator;
import com.stpl.tech.attendance.validation.OdWfhRequestValidator;
import com.stpl.tech.attendance.validation.RegularisationRequestValidator;
import com.stpl.tech.attendance.validation.WeekOffRequestValidator;

@Slf4j
@Service
@RequiredArgsConstructor
public class EmployeeAttendanceServiceImpl implements EmployeeAttendanceService {
    
    private final EmployeeAttendanceRequestRepository employeeAttendanceRequestRepository;
    private final EmployeeAttendanceRequestDetailService employeeAttendanceRequestDetailService;
    private final UserCacheService userCacheService;
    private final EmployeeSearchService employeeSearchService;
    private final ApprovalEngineService approvalEngineService;
    private final ObjectMapper objectMapper;
    private final S3Service s3Service;
    private final DailyAttendanceSummaryRepository summaryRepository;
    private final EmployeeAttendanceApprovalServiceImpl employeeAttendanceApprovalService;
    private final LeaveBalanceService leaveBalanceService;
    private final ApprovalStepRepository approvalStepRepository;
    private final SubApprovalStepRepository subApprovalStepRepository;
    private final ApprovalRequestRepository approvalRequestRepository;
    private final DailyAttendanceSummaryLogsService dailyAttendanceSummaryLogsService;
    private final EmpAttendanceReserveDataRepository empAttendanceReserveDataRepository;
    private final EmpAttendanceBalanceDataRepository empAttendanceBalanceDataRepository;
    private final EmpLeaveDataLogsRepository empLeaveDataLogsRepository;
    private final AttendanceRequestUtil attendanceRequestUtil;
    private final EmployeeShiftInstanceRecreationService employeeShiftInstanceRecreationService;
    private final AttendanceMetadataService attendanceMetadataService;
    private final AsyncOperationService asyncOperationService;
    private final LeaveRequestValidator leaveRequestValidator;
    private final OdWfhRequestValidator odWfhRequestValidator;
    private final RegularisationRequestValidator regularisationRequestValidator;
    private final WeekOffRequestValidator weekOffRequestValidator;
    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
    public ApplyLeaveResponse applyLeave(ApplyLeaveRequest request) {
        log.info("Applying leave for employee with {} dates", request.getLeaveDates().size());
        
        // Get employee ID from JWT context
        Integer empId = JwtContext.getInstance().getUserId();
        if (empId == null) {
            throw new BusinessException("Employee ID not found in authentication context");
        }
        // Validate request
        try {
            // Check if there are any valid dates after weekend validation
            if (request.getLeaveDates() == null || request.getLeaveDates().isEmpty()) {
                throw new BusinessException("No valid leave dates remaining after validation");
            }
            
            Map<String, Object> processResult = processLeaveDates(request, empId);
            
            @SuppressWarnings("unchecked")
            List<ApplyLeaveResponse.CreatedEntry> createdEntries = (List<ApplyLeaveResponse.CreatedEntry>) processResult.get("createdEntries");
            @SuppressWarnings("unchecked")
            Map<LocalDateTime, String> filteredLeaveDatesMap = (Map<LocalDateTime, String>) processResult.get("filteredLeaveDatesMap");
            
            if (createdEntries.isEmpty()) {
                throw new BusinessException("No leave records were created. Some dates may already have pending requests or be invalid.");
            }
            
            // Insert leave detail data for tracking reserved counts
            BigDecimal totalDays = calculateTotalLeaveDays(filteredLeaveDatesMap, empId, request.getLeaveType());
            insertLeaveDetailData(empId, request.getLeaveType(), totalDays);
            
            return createSuccessResponse(createdEntries,totalDays);
                    
        } catch (BusinessException e) {
            // Re-throw BusinessException as-is
            throw e;
        } catch (Exception e) {
            log.error("Error applying leave for employee: {}", empId, e);
            throw new BusinessException("Failed to apply leave");
        }
    }

    private Map<String, Object> processLeaveDates(ApplyLeaveRequest request, Integer empId) {
        List<ApplyLeaveResponse.CreatedEntry> createdEntries = new ArrayList<>();
        String userId = empId.toString();
        LocalDateTime currentTime = LocalDateTime.now();
        
        // Get all dates from the map to determine start_date and end_date for the single entry
        List<LocalDateTime> allDates = new ArrayList<>(request.getLeaveDates().keySet());
        allDates.sort(LocalDateTime::compareTo); // Sort dates in ascending order
        
        // Filter out weekend and holiday dates
        List<LocalDateTime> filteredDates = attendanceRequestUtil.filterLeaveDatesByWeekOffAndHolidays(allDates, empId);
        
        if (filteredDates.isEmpty()) {
            throw new BusinessException("Leave can't be applied as all selected dates fall on weekends/holidays");
        }
        
        // Create a filtered request with only valid dates for validation
        ApplyLeaveRequest filteredRequest = createFilteredLeaveRequest(request, filteredDates);
        
        // Validate the filtered request (not the original)
        leaveRequestValidator.validate(filteredRequest);
        
        // Create filtered leave dates map for leave balance calculation and detail records
        Map<LocalDateTime, String> filteredLeaveDatesMap = new HashMap<>();
        for (LocalDateTime filteredDate : filteredDates) {
            // Get the request type from original request for this date
            String requestType = request.getLeaveDates().get(filteredDate);
            if (requestType != null) {
                filteredLeaveDatesMap.put(filteredDate, requestType);
            }
        }
        
        LocalDateTime startDate = filteredDates.get(0); // First date
        LocalDateTime endDate = filteredDates.get(filteredDates.size() - 1); // Last date
        
        // For leave applications, set endTime to end of day (23:59:59) to represent full day leave
        assert endDate != null;
        endDate = endDate.toLocalDate().atTime(23, 59, 59);
        
        // Create single attendance record with start_time and end_time
        Long entryId = createLeaveAttendanceRecord(empId, startDate, endDate, request.getLeaveType(), 
                request.getComments(), userId, currentTime, filteredLeaveDatesMap, request.getDocuments());
        if (entryId == null) {
            log.error("Failed to create leave attendance record");
            throw new BusinessException("Failed to create leave attendance record");
        }
        
        // Get the created attendance record for approval request
        EmployeeAttendanceRequest attendanceRecord = employeeAttendanceRequestRepository.findById(entryId)
                .orElseThrow(() -> new BusinessException("Attendance record not found after creation"));
        
        // Process leave application asynchronously (approval request creation, etc.)
        asyncOperationService.postLeaveProcessing(request, attendanceRecord, empId,JwtContext.getInstance().getUnitId());
        
        // Add to created entries for response
        createdEntries.add(ApplyLeaveResponse.CreatedEntry.builder()
                .date(startDate)
                .entryId(entryId)
                .comment(request.getComments())
                .build());
        
        // Return both created entries and filtered dates map
        Map<String, Object> result = new HashMap<>();
        result.put("createdEntries", createdEntries);
        result.put("filteredLeaveDatesMap", filteredLeaveDatesMap);
        
        return result;
    }

    /**
     * Create a new leave attendance record with start_time and end_time
     * Creates a single entry in EMP_ATTENDANCE_REQUEST table
     * @param startDate Start date (first date from the map)
     * @param endDate End date (last date from the map)
     * @param leaveType Leave type
     * @param comments Comments
     * @param userId User ID
     * @param currentTime Current timestamp
     * @param leaveDates Map of leave dates and types
     * @return Created record ID or null if creation failed
     */
    @Transactional(rollbackFor = Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
    private Long createLeaveAttendanceRecord(Integer empId, LocalDateTime startDate, LocalDateTime endDate, 
                                           String leaveType, String comments, String userId, 
                                           LocalDateTime currentTime, Map<LocalDateTime, String> leaveDates,String documents) {
        try {
            // Create one database entry with start_time and end_time
            EmployeeAttendanceRequest attendance = EmployeeAttendanceRequest.builder()
                    .empId(empId)
                    .date(startDate)  // Use startDate as the main date
                    .type(leaveType)
                    .status(AppConstants.STATUS_PENDING)
                    .reason("Leave application")
                    .comments(comments)
                    .startTime(startDate)
                    .endTime(endDate)
                    .createdBy(userId)
                    .creationTime(currentTime)
                    .document(documents)
                    .build();
            
            EmployeeAttendanceRequest savedAttendance = employeeAttendanceRequestRepository.save(attendance);
            log.info("Successfully created leave attendance record with ID: {} for date range: {} to {}", 
                    savedAttendance.getId(), startDate, endDate);
            
            // Create detail records for each date with specific type and request type
            employeeAttendanceRequestDetailService.createLeaveDetailRecords(savedAttendance.getId(), leaveDates, leaveType, userId);
            asyncOperationService.logAttendanceRequestCreationAsync(
                    savedAttendance.getId(), empId, leaveType, JwtContext.getInstance().getUserId().toString());
            return savedAttendance.getId();
            
        } catch (Exception e) {
            log.error("Failed to create leave attendance record for employee {} from {} to {}", empId, startDate, endDate, e);
            return null;
        }
    }
    

    
    /**
     * Create a filtered leave request with only valid dates for validation
     * @param originalRequest The original request
     * @param filteredDates List of valid dates after weekend/holiday filtering
     * @return Filtered request with only valid dates
     */
    private ApplyLeaveRequest createFilteredLeaveRequest(ApplyLeaveRequest originalRequest, List<LocalDateTime> filteredDates) {
        Map<LocalDateTime, String> filteredLeaveDates = new HashMap<>();
        
        // Only include dates that are in the filtered list
        for (LocalDateTime date : filteredDates) {
            String requestType = originalRequest.getLeaveDates().get(date);
            if (requestType != null) {
                filteredLeaveDates.put(date, requestType);
            }
        }
        
        return ApplyLeaveRequest.builder()
                .leaveType(originalRequest.getLeaveType())
                .leaveDates(filteredLeaveDates)
                .comments(originalRequest.getComments())
                .documents(originalRequest.getDocuments())
                .build();
    }
    
    /**
     * Create success response for leave application
     * @param createdEntries List of created entries with date and ID
     * @return ApplyLeaveResponse with success details
     */
    private ApplyLeaveResponse createSuccessResponse(List<ApplyLeaveResponse.CreatedEntry> createdEntries, BigDecimal totalDays) {
        return ApplyLeaveResponse.builder()
                .success(true)
                .message("Successfully applied leave for " + totalDays + " date(s)")
                .createdEntries(createdEntries)
                .build();
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
    public ApplyOdWfhResponse applyOdWfh(ApplyOdWfhRequest request) {
        log.info("Applying OD/WFH for employee with {} entries", request.getOdWfhEntries().size());
        // Validate request
        Integer empId = Objects.requireNonNullElse(request.getEmpId(), JwtContext.getInstance().getUserId());
        odWfhRequestValidator.validate(request);
        try {
            List<ApplyOdWfhResponse.CreatedEntry> createdEntries = processOdWfhEntries(request, empId);
            
            if (createdEntries.isEmpty()) {
                throw new BusinessException("No OD/WFH records were created. Some dates may already have pending requests or be invalid.");
            }
            
            return createOdWfhSuccessResponse(createdEntries);
                    
        } catch (BusinessException e) {
            // Re-throw BusinessException as-is
            throw e;
        } catch (Exception e) {
            log.error("Error applying OD/WFH for employee: {}", request.getEmpId(), e);
            throw new BusinessException("Failed to apply OD/WFH");
        }
    }
    /**
     * Process all OD/WFH entries and create attendance records
     * Each date gets its own database entry in EMP_ATTENDANCE_REQUEST table
     * Each date now gets its own approval request for independent approval workflow
     * @param request The OD/WFH request
     * @param empId Employee ID
     * @return List of created entries with date and ID
     */
    @Transactional(rollbackFor = Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
    private List<ApplyOdWfhResponse.CreatedEntry> processOdWfhEntries(ApplyOdWfhRequest request, Integer empId) {
        List<ApplyOdWfhResponse.CreatedEntry> createdEntries = new ArrayList<>();
        String userId = empId.toString();
        LocalDateTime currentTime = LocalDateTime.now();
        
        // Get the type from request (OD or WFH)
        String type = request.getType().toUpperCase();
        
        List<EmployeeAttendanceRequest> attendanceRecords = new ArrayList<>();
        
        // Process each entry and create individual attendance records
        for (ApplyOdWfhRequest.OdWfhEntry entry : request.getOdWfhEntries()) {
            LocalDateTime date = entry.getDate();
            LocalDateTime checkIn = entry.getCheckIn();
            LocalDateTime checkOut = entry.getCheckOut();
            String comments = entry.getComments();

            // Create attendance record with the specific type (OD or WFH)
        Long entryId = createOdWfhAttendanceRecord(empId, date, checkIn, checkOut, comments, userId, currentTime, request.getType(),request.getOverrideRequest());
        if (entryId != null) {
            createdEntries.add(ApplyOdWfhResponse.CreatedEntry.builder()
                    .date(date)
                    .entryId(entryId)
                    .comments(comments)
                    .build());

            // Get the created attendance record for approval request
            EmployeeAttendanceRequest attendanceRecord = employeeAttendanceRequestRepository.findById(entryId)
                    .orElseThrow(() -> new BusinessException("Attendance record not found after creation"));
            attendanceRecords.add(attendanceRecord);

            log.info("Created {} attendance record - ID: {}, Employee: {}, Date: {}, Comments: {}",
                    type, entryId, empId, date, comments);
            }
            Long summaryId = createDailySummary(request.getEmpId(), date.toLocalDate(), null,null, request.getType(),AttendanceStatus.PRESENT.name());
            asyncOperationService.logWeekOffDailySummaryChange(summaryId,JwtContext.getInstance().getUserId().toString());
            employeeShiftInstanceRecreationService.recreateShiftInstancesFromEffectiveDate(request.getEmpId(), date.toLocalDate(), JwtContext.getInstance().getUserId().toString());

        }
        // Process OD/WFH application asynchronously (approval request creation, etc.)
        if (!attendanceRecords.isEmpty()&& Boolean.FALSE.equals(request.getOverrideRequest())) {
            asyncOperationService.postOdWfhProcessing(request, attendanceRecords, Long.valueOf(JwtContext.getInstance().getUnitId()));
        }
        return createdEntries;
    }
    /**
     * Create a new OD/WFH attendance record
     * Each date gets its own entry in the EMP_ATTENDANCE_REQUEST table
     * @param empId Employee ID
     * @param date Date
     * @param checkIn Check-in time
     * @param checkOut Check-out time
     * @param comments Comments
     * @param userId User ID
     * @param currentTime Current timestamp
     * @param type Type (OD or WFH)
     * @return Created record ID or null if creation failed
     */
    @Transactional(rollbackFor = Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
    private Long createOdWfhAttendanceRecord(Integer empId, LocalDateTime date, LocalDateTime checkIn, 
                                                   LocalDateTime checkOut, String comments, String userId, LocalDateTime currentTime, String type,Boolean odBypassRequest) {
        try {
            // Create one database entry per date as per EMP_ATTENDANCE_REQUEST table structure
            String status = odBypassRequest ? AppConstants.STATUS_APPROVED : AppConstants.STATUS_PENDING;
            EmployeeAttendanceRequest attendance = EmployeeAttendanceRequest.builder()
                    .empId(empId)
                    .date(date)  // Use LocalDateTime directly
                    .type(type)
                    .status(status)
                    .reason(type + " application")
                    .comments(comments)
                    .startTime(checkIn)
                    .endTime(checkOut)
                    .createdBy(userId)
                    .creationTime(currentTime)
                    .build();
            
            EmployeeAttendanceRequest savedAttendance = employeeAttendanceRequestRepository.save(attendance);
            log.info("Successfully created {} attendance record with ID: {} for date: {}", 
                    type, savedAttendance.getId(), date);

            // NEW: Log the creation asynchronously
            String toStatus = odBypassRequest ? AppConstants.STATUS_APPROVED : AppConstants.STATUS_PENDING;
            asyncOperationService.logAttendanceRequestAsync( savedAttendance.getId(), empId, AppConstants.STATUS_INITIATED,toStatus, type, userId, AppConstants.STATUS_CREATED );

            return savedAttendance.getId();
            
        } catch (Exception e) {
            log.error("Failed to create {} attendance record for employee {} on date {}", type, empId, date, e);
            return null;
        }
    }
    
    /**
     * Create success response for OD/WFH application
     * @param createdEntries List of created entries with date and ID
     * @return ApplyOdWfhResponse with success details
     */
    private ApplyOdWfhResponse createOdWfhSuccessResponse(List<ApplyOdWfhResponse.CreatedEntry> createdEntries) {
        return ApplyOdWfhResponse.builder()
                .success(true)
                .message("Successfully applied OD/WFH for " + createdEntries.size() + " date(s)")
                .createdEntries(createdEntries)
                .build();
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
    public ApplyRegularisationResponse applyRegularisation(ApplyRegularisationRequest request) {
        log.info("Applying regularisation for employee");
        
        Integer empId = request.getEmpId();
        if (empId == null) {
            throw new BusinessException("Employee ID not found in authentication context");
        }
        EmployeeBasicDetail employee = userCacheService.getUserById(empId);

        // Validate request
        regularisationRequestValidator.validate(request);
        
        try {
            ApplyRegularisationResponse.CreatedEntry createdEntry = processRegularisation(request, empId,employee.getLocCode());
            
            if (createdEntry == null) {
                throw new BusinessException("Regularisation record was not created. The date may already have a pending request or be invalid.");
            }
            
            return createRegularisationSuccessResponse(createdEntry);
                    
        } catch (BusinessException e) {
            // Re-throw BusinessException as-is
            throw e;
        } catch (Exception e) {
            log.error("Error applying regularisation for employee: {}", empId, e);
            throw new BusinessException("Failed to apply regularisation: " + e.getMessage());
        }
    }

    private ApplyRegularisationResponse.CreatedEntry processRegularisation(ApplyRegularisationRequest request, Integer empId,Integer unitId) {
        LocalDateTime date = request.getDate();
        LocalDateTime checkIn = request.getCheckIn();
        LocalDateTime checkOut = request.getCheckOut();
        
        if (!isDuplicateRegularisationRecord(empId, date)) {
            Long entryId = createRegularisationAttendanceRecord(empId, date, checkIn, checkOut, 
                    request.getReason(), request.getComments(),request.getOverrideRequest());
            if (entryId != null) {
                // Get the created attendance record for approval request
                EmployeeAttendanceRequest attendanceRecord = employeeAttendanceRequestRepository.findById(entryId)
                        .orElseThrow(() -> new BusinessException("Attendance record not found after creation"));
                Long summaryId = createDailySummary(request.getEmpId(), date.toLocalDate(),checkIn,checkOut,
                        AttendanceType.REGULARISATION.name(),AttendanceStatus.PRESENT.name());
                asyncOperationService.logWeekOffDailySummaryChange(summaryId,JwtContext.getInstance().getUserId().toString());
                employeeShiftInstanceRecreationService.recreateShiftInstancesFromEffectiveDate(request.getEmpId(), date.toLocalDate(), JwtContext.getInstance().getUserId().toString());
                // Process regularisation application asynchronously (approval request creation, etc.)
                if (attendanceRecord != null && Boolean.FALSE.equals(request.getOverrideRequest())) {
                    asyncOperationService.postRegularisation(request, attendanceRecord, empId,unitId);
                }
                ApplyRegularisationResponse.CreatedEntry createdEntry = ApplyRegularisationResponse.CreatedEntry.builder()
                        .date(checkIn)
                        .entryId(entryId)
                        .build();
                log.info("Created regularisation attendance record - ID: {}, Employee: {}, Date: {}", 
                        entryId, empId, checkIn);
                return createdEntry;
            }
        }
        
        return null;
    }
    
    /**
     * Check if a record already exists for the same employee and date for regularisation
     * @param empId Employee ID
     * @param date Date
     * @return true if duplicate exists, false otherwise
     */
    private boolean isDuplicateRegularisationRecord(Integer empId, LocalDateTime date) {
        // Check for PENDING status only - REJECTED requests should allow new applications
        boolean exists = employeeAttendanceRequestRepository.existsByEmpIdAndDateAndTypeAndStatus(
                empId, date, AppConstants.REGULARISATION, AppConstants.STATUS_PENDING);
        
        if (exists) {
            log.warn("Regularisation attendance record already exists for employee {} on date {}", 
                    empId, date);
        }
        
        return exists;
    }
    
    /**
     * Create a new regularisation attendance record
     * @param empId Employee ID
     * @param date Date
     * @param checkIn Check-in time
     * @param checkOut Check-out time
     * @param reason Reason for regularisation
     * @param comments Comments
     * @param overrideRequest Whether to bypass approval workflow
     * @return Created record ID or null if creation failed
     */
    @Transactional(rollbackFor = Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
    private Long createRegularisationAttendanceRecord(Integer empId, LocalDateTime date, LocalDateTime checkIn, 
                                                           LocalDateTime checkOut, String reason, String comments, Boolean overrideRequest) {
        try {
            String userId = empId.toString();
            LocalDateTime currentTime = LocalDateTime.now();
            
            // Create one database entry as per EMP_ATTENDANCE_REQUEST table structure
            String status = overrideRequest ? AppConstants.STATUS_APPROVED : AppConstants.STATUS_PENDING;
            EmployeeAttendanceRequest attendance = EmployeeAttendanceRequest.builder()
                    .empId(empId)
                    .date(checkIn)  // Use LocalDateTime directly
                    .type(AppConstants.REGULARISATION)
                    .status(status)
                    .reason(reason)
                    .comments(comments)
                    .startTime(checkIn)
                    .endTime(checkOut)
                    .createdBy(userId)
                    .creationTime(currentTime)
                    .build();
            
            EmployeeAttendanceRequest savedAttendance = employeeAttendanceRequestRepository.save(attendance);
            log.info("Successfully created regularisation attendance record with ID: {} for date: {}", 
                    savedAttendance.getId(), date);
            
            // NEW: Log the creation asynchronously
            String toStatus = overrideRequest ? AppConstants.STATUS_APPROVED : AppConstants.STATUS_PENDING;
            asyncOperationService.logAttendanceRequestAsync(savedAttendance.getId(), empId, AppConstants.STATUS_INITIATED, toStatus, attendance.getType(), attendance.getCreatedBy(), AppConstants.STATUS_CREATED);
            
            return savedAttendance.getId();
            
        } catch (Exception e) {
            log.error("Failed to create regularisation attendance record for employee {} on date {}", empId, date, e);
            return null;
        }
    }
    
    /**
     * Create success response for regularisation application
     * @param createdEntry Created entry with date and ID
     * @return ApplyRegularisationResponse with success details
     */
    private ApplyRegularisationResponse createRegularisationSuccessResponse(ApplyRegularisationResponse.CreatedEntry createdEntry) {
        return ApplyRegularisationResponse.builder()
                .success(true)
                .message("Successfully applied regularisation for date: " + createdEntry.getDate().toLocalDate())
                .createdEntry(createdEntry)
                .build();
    }

    /**
     * Creates and starts the approval workflow for any type of application
     * Unified function to replace createLeaveApprovalRequest and createRegularisationApprovalRequest
     */

    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
    public void handleApprovalResponse(ApprovalRequest approvalRequest, ApprovalStatus status, String attendanceType) {
        try {
            Long referenceId = approvalRequest.getReferenceId();
            
            // Get the attendance record directly from EMP_ATTENDANCE_REQUEST
            EmployeeAttendanceRequest attendanceRecord = employeeAttendanceRequestRepository.findById(referenceId)
                    .orElseThrow(() -> new BusinessException("Attendance record not found with ID: " + referenceId));
            Map<String, Object> metadataMap = objectMapper.readValue(approvalRequest.getMetadata(), Map.class);
            String isCancellationRequest = (String) metadataMap.get("isCancellationRequest");
            // Handle cancellation approval specifically
            if (isCancellationRequest != null && isCancellationRequest.equalsIgnoreCase(Boolean.TRUE.toString())) {
                handleCancellationApprovalResponse(approvalRequest, attendanceRecord, status);
                return;
            }
            
            // Update the attendance record status for non-cancellation requests
            String fromStatus = attendanceRecord.getStatus();
            attendanceRecord.setStatus(String.valueOf(status));
            attendanceRecord.setUpdatedBy(JwtContext.getInstance().getUserId().toString());
            attendanceRecord.setUpdationTime(LocalDateTime.now());
            employeeAttendanceRequestRepository.save(attendanceRecord);

            // NEW: Log the status change asynchronously
            asyncOperationService.logAttendanceRequestStatusChangeAsync(
                attendanceRecord.getId(),
                attendanceRecord.getEmpId(),
                fromStatus,
                String.valueOf(status),
                attendanceRecord.getType(),
                approvalRequest.getUpdatedBy() != null ? approvalRequest.getUpdatedBy() : "SYSTEM"
            );
            
            log.info("Successfully updated {} approval status for attendance record ID: {}", attendanceType, referenceId);
            
            // Handle DailyAttendanceSummary records based on approval status
            if (status == ApprovalStatus.APPROVED) {
                // Update or create DailyAttendanceSummary records for approved requests
                updateDailyAttendanceSummaries(attendanceRecord, attendanceType, referenceId);
                
                // Deduct leave balance and reserved count for approved leave requests
                if (isLeaveType(attendanceType)) {
                    deductLeaveBalanceAndReservedCount(attendanceRecord.getEmpId(), attendanceType, List.of(attendanceRecord), true);
                }
                employeeShiftInstanceRecreationService.recreateShiftInstancesFromEffectiveDate(attendanceRecord.getEmpId(),attendanceRecord.getStartTime().toLocalDate(),JwtContext.getInstance().getUserId().toString());
            } else if (status == ApprovalStatus.REJECTED) {
                // Clean up DailyAttendanceSummary records for rejected requests
                cleanupDailyAttendanceSummaries(attendanceRecord);
                
                // Deduct reserved count for rejected leave requests (because they are no longer pending)
                if (isLeaveType(attendanceType)) {
                    deductLeaveBalanceAndReservedCount(attendanceRecord.getEmpId(), attendanceType, List.of(attendanceRecord), false);
                }
            }
            
        } catch (BusinessException e) {
            // Re-throw BusinessException as-is
            throw e;
        } catch (Exception e) {
            log.error("Error handling {} approval response for approval request ID: {}", attendanceType, approvalRequest.getId(), e);
            throw new BusinessException("Failed to handle " + attendanceType.toLowerCase() + " approval response: " + e.getMessage());
        }
    }
    
    /**
     * Check if the attendance type is a leave type that requires balance deduction
     * @param attendanceType The attendance type to check
     * @return true if it's a leave type, false otherwise
     */
    private boolean isLeaveType(String attendanceType) {
        if (attendanceType == null) {
            return false;
        }
        
        String upperType = attendanceType.toUpperCase();
        boolean isLeaveType = "LEAVE".equals(upperType) || "COMP_OFF".equals(upperType) || "LWP".equals(upperType);
        
        log.info("Checking if attendanceType '{}' is a leave type: {}", attendanceType, isLeaveType);
        return isLeaveType;
    }
    
    /**
     * Unified method to handle both leave balance and reserved count deduction
     * @param empId Employee ID
     * @param leaveType Type of leave (LEAVE, COMP_OFF, LWP)
     * @param attendanceRecords List of attendance records
     * @param isApproved true if approved (deduct from both balance and reserved), false if rejected (deduct only from reserved)
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    private void deductLeaveBalanceAndReservedCount(Integer empId, String leaveType, List<EmployeeAttendanceRequest> attendanceRecords, boolean isApproved) {
        try {
            String action = isApproved ? "approved" : "rejected";
            log.info("Starting {} leave request processing - Employee: {}, Type: {}, Records: {}, Action: {}", 
                    action, empId, leaveType, attendanceRecords.size(), isApproved ? "APPROVED" : "REJECTED");
            
            BigDecimal totalDaysToDeduct = BigDecimal.ZERO;
            String actualLeaveType = leaveType; // Default to passed leaveType
            
            for (EmployeeAttendanceRequest record : attendanceRecords) {
                log.info("Processing {} attendance record ID: {} for employee: {}", action, record.getId(), empId);
                
                // Get the approval request to access metadata with leave dates
                ApprovalRequest approvalRequest = getApprovalRequestByReferenceId(record.getId());
                
                if (approvalRequest != null && approvalRequest.getMetadata() != null) {
                    log.info("Found approval request ID: {} with metadata for {} attendance record ID: {}", 
                            approvalRequest.getId(), action, record.getId());
                    
                    // Get the actual leaveType from metadata (this is more accurate)
                    String metadataLeaveType = getLeaveTypeFromMetadata(approvalRequest.getMetadata());
                    if (metadataLeaveType != null && !metadataLeaveType.trim().isEmpty()) {
                        actualLeaveType = metadataLeaveType;
                        log.info("Using leaveType from metadata: {} instead of passed leaveType: {}", actualLeaveType, leaveType);
                    }
                    
                    // Calculate deduction based on metadata from approval request
                    BigDecimal daysToDeduct = calculateDeductionFromMetadata(approvalRequest.getMetadata());
                    totalDaysToDeduct = totalDaysToDeduct.add(daysToDeduct);
                    log.info("Calculated deduction from metadata: {} days for {} attendance record ID: {} (running total: {})", 
                            daysToDeduct, action, record.getId(), totalDaysToDeduct);
                }
            }
            
            log.info("Final total deduction calculation for {} request: {} days for employee {} leave type {}", 
                    action, totalDaysToDeduct, empId, actualLeaveType);
            
            if (totalDaysToDeduct.compareTo(BigDecimal.ZERO) > 0) {
                if (isApproved) {
                    // For approved requests: deduct from both leave balance and reserved count
                    log.info("Processing approved leave request - deducting {} days from leave balance and reserved count for employee {} leave type {}", 
                            totalDaysToDeduct, empId, actualLeaveType);
                    
                    // First deduct from leave balance
                    deductLeaveBalance(empId, actualLeaveType, totalDaysToDeduct);
                    
                    // Note: deductLeaveBalance already calls deductReservedCount, so we don't need to call it again
                    log.info("Successfully processed approved leave request - employee {} leave type {}: {} days deducted from both balance and reserved count", 
                            empId, actualLeaveType, totalDaysToDeduct);
                } else {
                    // For rejected requests: deduct only from reserved count
                    log.info("Processing rejected leave request - deducting {} days from reserved count for employee {} leave type {}", 
                            totalDaysToDeduct, empId, actualLeaveType);
                    
                    deductReservedCount(empId, actualLeaveType, totalDaysToDeduct);
                    
                    log.info("Successfully processed rejected leave request - employee {} leave type {}: {} days deducted from reserved count", 
                            empId, actualLeaveType, totalDaysToDeduct);
                }
            } else {
                log.warn("No days to process for {} leave request - employee {} leave type {}", action, empId, actualLeaveType);
            }
        } catch (BusinessException e) {
            // Re-throw BusinessException as-is
            String actionType = isApproved ? "approved" : "rejected";
            log.error("Business exception during {} leave request processing - employee {}: {}", actionType, empId, e.getMessage());
            throw e;
        } catch (Exception e) {
            String actionType = isApproved ? "approved" : "rejected";
            log.error("Error processing {} leave request - employee {}: {}", actionType, empId, e.getMessage(), e);
            // Don't throw exception to avoid rolling back the main operation
            // The processing can be done later through a separate process
        }
    }

    /**
     * Deduct leave balance for the approved leave request
     * @param empId Employee ID
     * @param leaveType Type of leave (LEAVE, COMP_OFF, LWP)
     * @param daysApproved Number of days approved
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    private void deductLeaveBalance(Integer empId, String leaveType, BigDecimal daysApproved) {
        try {
            log.info("Starting leave balance deduction for employee {}: {} days from {} balance", empId, daysApproved, leaveType);
            
            // Get current leave balance (now returns BigDecimal)
            BigDecimal currentBalance = leaveBalanceService.getAvailableLeaveCount(empId, leaveType);
            
            if (currentBalance == null) {
                log.warn("No leave balance record found for employee {} with leaveType {}. Cannot deduct balance.", empId, leaveType);
                return;
            }
            
            log.info("Current {} balance for employee {}: {} days", leaveType, empId, currentBalance);
            
            // Validate if sufficient balance exists before deduction
            if (currentBalance.compareTo(daysApproved) < 0) {
                String errorMessage = String.format("Insufficient %s balance for employee. Required: %.1f days, Available: %.1f days",
                        leaveType, daysApproved, currentBalance);
                log.error(errorMessage);
                throw new BusinessException(errorMessage);
            }
            
            // Calculate new balance
            BigDecimal newBalance = currentBalance.subtract(daysApproved);
            
            // Update leave balance (using BigDecimal for the update method)
            BigDecimal newBalanceBigDecimal = newBalance.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : newBalance; // Ensure non-negative
            leaveBalanceService.updateLeaveBalance(empId, leaveType, newBalanceBigDecimal);
            
            // Deduct reserved count from EMP_ATTENDANCE_RESERVE_DATA table
            deductReservedCount(empId, leaveType, daysApproved);
            
            log.info("Successfully deducted {} days from {} balance for employee {}. Old balance: {}, New balance: {}", 
                    daysApproved, leaveType, empId, currentBalance, newBalanceBigDecimal);
                    
        } catch (ObjectOptimisticLockingFailureException e) {
            // Handle optimistic locking failure (concurrent modification)
            String errorMessage = String.format("Concurrent modification detected for %s balance of employee %d. " +
                    "Another request has modified the leave balance. Please retry the approval.", leaveType, empId);
            log.error("Optimistic locking failure during leave balance deduction for employee {} with leaveType {}: {}", 
                    empId, leaveType, e.getMessage());
            throw new BusinessException(errorMessage);
        } catch (BusinessException e) {
            // Re-throw BusinessException as-is
            log.error("Business exception during leave balance deduction for employee {} with leaveType {}: {}", 
                    empId, leaveType, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Error deducting leave balance for employee {} with leaveType {}: {}", empId, leaveType, e.getMessage(), e);
            // Don't throw exception to avoid rolling back the approval
            // The balance can be updated later through a separate process
        }
    }

    /**
     * Updates or creates DailyAttendanceSummary records based on the approval status.
     * This method is only called for APPROVED requests.
     * @param attendanceRecord The attendance record from EMP_ATTENDANCE_REQUEST.
     * @param attendanceType The type of attendance (LEAVE, OD, WFH, REGULARISATION).
     * @param referenceId The ID of the attendance record (referenceId).
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    private void updateDailyAttendanceSummaries(EmployeeAttendanceRequest attendanceRecord, String attendanceType, Long referenceId) {
        try {
            // Get the approval request to access metadata
            ApprovalRequest approvalRequest = getApprovalRequestByReferenceId(referenceId);
            
            if (approvalRequest != null && approvalRequest.getMetadata() != null) {
                // Parse metadata to get approval type and data
                Map<String, Object> metadataMap = objectMapper.readValue(approvalRequest.getMetadata(), Map.class);
                
                // Handle different approval types
                if (approvalRequest.getRequestType() == ApprovalType.EMPLOYEE_LEAVE) {
                    // Handle leave approval with leaveDates
                    handleLeaveApproval(attendanceRecord, attendanceType, referenceId, metadataMap);
                } else if (approvalRequest.getRequestType() == ApprovalType.EMPLOYEE_OD ||
                           approvalRequest.getRequestType() == ApprovalType.EMPLOYEE_WFH ||
                           approvalRequest.getRequestType() == ApprovalType.EMPLOYEE_REGULARISATION) {
                    // Handle OD/WFH/Regularisation approval with fromDate and toDate
                    handleOdWfhRegularisationApproval(attendanceRecord, attendanceType, referenceId, metadataMap, approvalRequest.getRequestType());
                }
//                else if (approvalRequest.getRequestType() == ApprovalType.EMPLOYEE_CANCELLATION_APPROVAL) {
//                    // Handle cancellation approval by restoring DailyAttendanceSummary from logs
//                    handleCancellationApproval(attendanceRecord, referenceId, metadataMap);
//                }
            }
            
        } catch (Exception e) {
            log.error("Error updating DailyAttendanceSummary records for employee {}: {}", attendanceRecord.getEmpId(), e.getMessage(), e);
        }
    }
    
    /**
     * Handle leave approval with leaveDates metadata
     */
    private void handleLeaveApproval(EmployeeAttendanceRequest attendanceRecord, String attendanceType, Long referenceId, Map<String, Object> metadataMap) {
        try {
            String leaveDatesJson = (String) metadataMap.get("leaveDates");
            
            if (leaveDatesJson != null && !leaveDatesJson.trim().isEmpty()) {
                // Parse the leaveDates JSON to get all dates
                Map<String, String> leaveDatesMap = objectMapper.readValue(leaveDatesJson, Map.class);
                
                log.info("Creating DailyAttendanceSummary records for {} leave dates from metadata for employee {}", 
                        leaveDatesMap.size(), attendanceRecord.getEmpId());
                
                // Create/update summary records for each date
                for (Map.Entry<String, String> entry : leaveDatesMap.entrySet()) {
                    String dateStr = entry.getKey();
                    String requestType = entry.getValue();
                    
                    try {
                        // Parse the date string to LocalDate
                        LocalDateTime dateTime = LocalDateTime.parse(dateStr);
                        LocalDate attendanceDate = dateTime.toLocalDate();
                        
                        // Check if summary record exists for this date
                        DailyAttendanceSummary existingSummary = summaryRepository.findByEmployeeIdAndAttendanceDate(
                            attendanceRecord.getEmpId(), attendanceDate).orElse(null);

                        // Map attendance type string to AttendanceType enum
                        AttendanceType summaryType = mapAttendanceType(attendanceType);
                        
                        if (existingSummary != null) {
                            // Update existing record
                            existingSummary.setType(summaryType);
                            existingSummary.setReferenceId(referenceId.intValue());
                            summaryRepository.save(existingSummary);
                            
                            // Log the current state after update
                            dailyAttendanceSummaryLogsService.updateOrCreateLog(
                                existingSummary,
                                DailyAttendanceSummaryLogs.ActionStatus.CREATED,
                                    String.valueOf(existingSummary.getType()),
                                attendanceRecord.getCreatedBy(),"ABSENT"
                            );
                            
                            log.info("Updated existing DailyAttendanceSummary for employee {} on date {} with type {} and requestType {}", 
                                attendanceRecord.getEmpId(), attendanceDate, attendanceType, requestType);
                        } else {
                            // Create new record
                            DailyAttendanceSummary newSummary = new DailyAttendanceSummary();
                            newSummary.setEmployeeId(attendanceRecord.getEmpId());
                            newSummary.setAttendanceDate(attendanceDate);
                            newSummary.setType(summaryType);
                            newSummary.setReferenceId(referenceId.intValue());
                            newSummary.setStatus(AttendanceStatus.ABSENT); // Use SPECIAL_CASE for approved requests
                            newSummary.setTotalPunches(0); // Default punches
                            DailyAttendanceSummary savedSummary = summaryRepository.save(newSummary);
                            
                            // Log the creation action - this is a first-time creation
                            dailyAttendanceSummaryLogsService.updateOrCreateLog(
                                    savedSummary,
                                    DailyAttendanceSummaryLogs.ActionStatus.CREATED,
                                    String.valueOf(savedSummary.getType()),
                                    attendanceRecord.getCreatedBy(),"ABSENT"
                            );
                            
                            log.info("Created new DailyAttendanceSummary for employee {} on date {} with type {} and requestType {}", 
                                attendanceRecord.getEmpId(), attendanceDate, attendanceType, requestType);
                        }
                    } catch (Exception e) {
                        log.error("Error processing date {} for DailyAttendanceSummary: {}", dateStr, e.getMessage(), e);
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error processing leave approval metadata for employee {}: {}", attendanceRecord.getEmpId(), e.getMessage(), e);
        }
    }
    
    /**
     * Handle OD/WFH/Regularisation approval with fromDate and toDate metadata
     */
    private void handleOdWfhRegularisationApproval(EmployeeAttendanceRequest attendanceRecord, String attendanceType, Long referenceId, 
                                                   Map<String, Object> metadataMap, ApprovalType requestType) {
        try {
            // Extract fromDate and toDate from metadata
            String fromDateStr = (String) metadataMap.get("fromDate");
            String toDateStr = (String) metadataMap.get("toDate");
            
            if (fromDateStr == null || toDateStr == null) {
                log.error("Missing fromDate or toDate in metadata for {} approval. Available keys: {}", 
                        requestType, metadataMap.keySet());
                log.error("Cannot proceed without date information for {} approval", requestType);
//                createSingleDateSummary(attendanceRecord, attendanceType, referenceId);
                return;
            }
            
            log.info("Processing {} approval metadata - fromDate: {}, toDate: {}", requestType, fromDateStr, toDateStr);
            
            // Parse dates and times
            LocalDateTime fromDateTime = parseDateTime(fromDateStr);
            LocalDateTime toDateTime = parseDateTime(toDateStr);

            log.info("Parsed times - fromDateTime: {}, toDateTime: {}", fromDateTime, toDateTime);
            
            LocalDate attendanceDate = fromDateTime.toLocalDate();
            
            // Calculate working hours and determine attendance status
            AttendanceStatus attendanceStatus = calculateAttendanceStatus(fromDateTime, toDateTime);
            
            log.info("Calculated attendance status: {} for working hours from {} to {}", 
                    attendanceStatus, fromDateTime, toDateTime);
            
            // Check if summary record exists for this date
            DailyAttendanceSummary existingSummary = summaryRepository.findByEmployeeIdAndAttendanceDate(
                attendanceRecord.getEmpId(), attendanceDate).orElse(null);

            // Map attendance type string to AttendanceType enum
            AttendanceType summaryType = mapAttendanceType(attendanceType);
            
            if (existingSummary != null) {
                
                // Update existing record with times and calculated status
                existingSummary.setType(summaryType);
                existingSummary.setReferenceId(referenceId.intValue());
                existingSummary.setFirstCheckIn(fromDateTime);
                existingSummary.setLastCheckOut(toDateTime);
                existingSummary.setStatus(attendanceStatus);
                
                summaryRepository.save(existingSummary);
                
                // Log the current state after update
                dailyAttendanceSummaryLogsService.updateOrCreateLog(
                    existingSummary,
                    DailyAttendanceSummaryLogs.ActionStatus.CREATED,
                    attendanceType,
                    attendanceRecord.getCreatedBy(),"PRESENT"
                );
                
                log.info("Updated existing DailyAttendanceSummary for employee {} on date {} with type {}, status {}, and times: {} to {}", 
                    attendanceRecord.getEmpId(), attendanceDate, attendanceType, attendanceStatus, fromDateTime, toDateTime);
            } else {
                // Create new record with times and calculated status
                DailyAttendanceSummary newSummary = new DailyAttendanceSummary();
                newSummary.setEmployeeId(attendanceRecord.getEmpId());
                newSummary.setAttendanceDate(attendanceDate);
                newSummary.setType(summaryType);
                newSummary.setReferenceId(referenceId.intValue());
                newSummary.setFirstCheckIn(fromDateTime);
                newSummary.setLastCheckOut(toDateTime);
                newSummary.setStatus(attendanceStatus);
                newSummary.setTotalPunches(0);
                DailyAttendanceSummary savedSummary = summaryRepository.save(newSummary);
                
                // Log the creation action
                dailyAttendanceSummaryLogsService.updateOrCreateLog(
                    savedSummary, 
                    DailyAttendanceSummaryLogs.ActionStatus.CREATED,
                    attendanceType,
                    attendanceRecord.getCreatedBy(),"PRESENT"
                );
                
                log.info("Created new DailyAttendanceSummary for employee {} on date {} with type {}, status {}, and times: {} to {}", 
                    attendanceRecord.getEmpId(), attendanceDate, attendanceType, attendanceStatus, fromDateTime, toDateTime);
            }
            
        } catch (Exception e) {
            log.error("Error processing {} approval metadata for employee {}: {}", 
                    requestType, attendanceRecord.getEmpId(), e.getMessage(), e);
        }
    }

    /**
     * Handle cancellation approval response from the approval workflow
     * This method is called when EMPLOYEE_CANCELLATION_APPROVAL is approved/rejected
     * @param approvalRequest The approval request containing metadata
     * @param attendanceRecord The attendance record to be cancelled
     * @param status The approval status (APPROVED/REJECTED)
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    private void handleCancellationApprovalResponse(ApprovalRequest approvalRequest, EmployeeAttendanceRequest attendanceRecord, ApprovalStatus status) {
        try {
            log.info("Processing cancellation approval response for employee {} with status: {}",
                    attendanceRecord.getEmpId(), status);

            if (status == ApprovalStatus.APPROVED) {
                // Update attendance record status to CANCELLED
                String fromStatus = attendanceRecord.getStatus();
                attendanceRecord.setStatus(AppConstants.STATUS_CANCELLED);
                attendanceRecord.setUpdatedBy(JwtContext.getInstance().getUserId().toString());
                attendanceRecord.setUpdationTime(LocalDateTime.now());
                employeeAttendanceRequestRepository.save(attendanceRecord);

                // NEW: Log the cancellation asynchronously
                asyncOperationService.logAttendanceRequestCancellationAsync(
                    attendanceRecord.getId(),
                    attendanceRecord.getEmpId(),
                    fromStatus,
                    attendanceRecord.getType(),
                    JwtContext.getInstance().getUserId().toString()
                );

                // Update approval steps status to CANCELLED
                updateApprovalStepsStatus(approvalRequest.getId());

                // Get the original attendance record that was being cancelled
                // This is stored in the metadata of the cancellation approval request
                if (approvalRequest.getMetadata() != null && !approvalRequest.getMetadata().trim().isEmpty()) {
                    Map<String, Object> metadataMap = objectMapper.readValue(approvalRequest.getMetadata(), Map.class);
                    String leaveDayCountStr = (String) metadataMap.get("leaveDayCount");

                    // Parse leaveDayCount from String to BigDecimal for comparison
                    BigDecimal leaveDayCount = BigDecimal.ZERO;
                    if (leaveDayCountStr != null && !leaveDayCountStr.trim().isEmpty()) {
                        try {
                            leaveDayCount = new BigDecimal(leaveDayCountStr);
                        } catch (NumberFormatException e) {
                            log.warn("Invalid leaveDayCount format: {}", leaveDayCountStr);
                        }
                    }

                    // Handle leave balance restoration for leave types
                    if (leaveDayCount.compareTo(BigDecimal.ZERO) > 0) {
                        restoreLeaveBalance(approvalRequest, attendanceRecord);
                    }

                    // Restore DailyAttendanceSummary from logs
                    restoreDailyAttendanceSummaryFromLogs(approvalRequest, attendanceRecord);
                }

                log.info("Successfully processed cancellation approval for employee {} - request cancelled",
                        attendanceRecord.getEmpId());

            } else if (status == ApprovalStatus.REJECTED) {
                // If cancellation is rejected, keep the original request as is
                // No changes needed to the attendance record
                log.info("Cancellation request rejected for employee {} - original request remains unchanged",
                        attendanceRecord.getEmpId());
            }

        } catch (Exception e) {
            log.error("Error processing cancellation approval response for employee {}: {}",
                    attendanceRecord.getEmpId(), e.getMessage(), e);
            throw new BusinessException("Failed to process cancellation approval response: " + e.getMessage());
        }
    }

    /**
     * Parse date-time string with flexible format handling
     * @param dateTimeStr Date-time string to parse
     * @return LocalDateTime object
     */
    private LocalDateTime parseDateTime(String dateTimeStr) {
        try {
            // Try ISO format first (e.g., "2025-08-14T10:05")
            if (dateTimeStr.contains("T")) {
                return LocalDateTime.parse(dateTimeStr);
            }
            
            // Try date-only format and set default time
            if (dateTimeStr.length() == 10) {
                LocalDate date = LocalDate.parse(dateTimeStr);
                return date.atTime(0, 0); // Set to 00:00
            }
            
            // Try other common formats
            if (dateTimeStr.contains(" ")) {
                return LocalDateTime.parse(dateTimeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }
            
            // Default fallback
            LocalDate date = LocalDate.parse(dateTimeStr);
            return date.atTime(0, 0);
            
        } catch (Exception e) {
            log.error("Failed to parse date-time string: {}", dateTimeStr, e);
            throw new RuntimeException("Invalid date-time format: " + dateTimeStr, e);
        }
    }
    
    /**
     * Calculate attendance status based on working hours
     * @param fromDateTime Start time
     * @param toDateTime End time
     * @return AttendanceStatus based on working hours:
     *         - LESS_WORKING_HOURS: if < 4 hours
     *         - HALF_DAY: if < 8 hours
     *         - PRESENT: if >= 8 hours
     */
    private AttendanceStatus calculateAttendanceStatus(LocalDateTime fromDateTime, LocalDateTime toDateTime) {
        try {
            // Calculate duration between fromDateTime and toDateTime
            java.time.Duration duration = java.time.Duration.between(fromDateTime, toDateTime);
            
            // Convert to hours (including fractional hours)
            double workingHours = duration.toMinutes() / 60.0;
            
            log.info("Calculated working hours: {} hours ({} minutes) from {} to {}", 
                    workingHours, duration.toMinutes(), fromDateTime, toDateTime);
            
            // Determine status based on working hours
            if (workingHours < 4.0) {
                log.info("Working hours < 4: Setting status to LESS_WORKING_HOURS");
                return AttendanceStatus.LESS_WORKING_HOURS;
            } else if (workingHours < 8.0) {
                log.info("Working hours < 8: Setting status to HALF_DAY");
                return AttendanceStatus.HALF_DAY;
            } else {
                log.info("Working hours >= 8: Setting status to PRESENT");
                return AttendanceStatus.PRESENT;
            }
            
        } catch (Exception e) {
            log.error("Error calculating working hours from {} to {}: {}", fromDateTime, toDateTime, e.getMessage(), e);
            // Default to PRESENT in case of error
            return AttendanceStatus.PRESENT;
        }
    }

    /**
     * Cleans up DailyAttendanceSummary records for rejected requests.
     * @param attendanceRecord The attendance record from EMP_ATTENDANCE_REQUEST.
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    private void cleanupDailyAttendanceSummaries(EmployeeAttendanceRequest attendanceRecord) {
        LocalDate attendanceDate = attendanceRecord.getDate().toLocalDate();
        DailyAttendanceSummary existingSummary = summaryRepository.findByEmployeeIdAndAttendanceDate(
            attendanceRecord.getEmpId(), attendanceDate).orElse(null);

//        if (existingSummary != null) {
//            summaryRepository.delete(existingSummary);
//            log.info("Deleted DailyAttendanceSummary for employee {} on date {} for rejected request",
//                attendanceRecord.getEmpId(), attendanceDate);
//        }
        // Only log if there's an existing summary to log
        if (existingSummary != null) {
            // Log the current state for cancellation
            dailyAttendanceSummaryLogsService.updateOrCreateLog(
                existingSummary,
                DailyAttendanceSummaryLogs.ActionStatus.CANCELLED,
                    String.valueOf(existingSummary.getType()),
                attendanceRecord.getCreatedBy(),
                "ABSENT"
            );
            log.info("Logged cancellation for DailyAttendanceSummary for employee {} on date {} for rejected request",
                attendanceRecord.getEmpId(), attendanceDate);
        } else {
            log.info("No existing DailyAttendanceSummary found for employee {} on date {} for rejected request - skipping logging",
                attendanceRecord.getEmpId(), attendanceDate);
        }
    }

    /**
     * Get approval request by reference ID (attendance record ID)
     * @param referenceId The reference ID to search for
     * @return ApprovalRequest or null if not found
     */
    private ApprovalRequest getApprovalRequestByReferenceId(Long referenceId) {
        try {
            // Search for approval request by reference ID for any approval type
            // This will handle LEAVE, OD, WFH, and REGULARISATION approvals
            List<ApprovalRequest> approvalRequests = approvalRequestRepository.findByReferenceId(referenceId);
            
            if (approvalRequests == null || approvalRequests.isEmpty()) {
                log.warn("No approval request found for referenceId: {}", referenceId);
                return null;
            }
            
            // If multiple found, take the first one (should be only one per referenceId)
            ApprovalRequest approvalRequest = approvalRequests.get(0);
            log.debug("Found approval request ID: {} with type {} for referenceId: {}", 
                    approvalRequest.getId(), approvalRequest.getRequestType(), referenceId);
            return approvalRequest;
            
        } catch (Exception e) {
            log.error("Error fetching approval request for referenceId: {}", referenceId, e);
            return null;
        }
    }

    
    /**
     * Get leave type from approval request metadata
     * @param metadata The metadata string from approval request
     * @return Leave type string or null if not found
     */
    private String getLeaveTypeFromMetadata(String metadata) {
        try {
            if (metadata == null || metadata.trim().isEmpty()) {
                log.warn("Metadata is null or empty, cannot extract leaveType");
                return null;
            }
            
            // Parse the metadata JSON
            Map<String, Object> metadataMap = objectMapper.readValue(metadata, Map.class);
            String leaveType = (String) metadataMap.get("leaveType");
            
            if (leaveType != null && !leaveType.trim().isEmpty()) {
                log.info("Found leaveType in metadata: {}", leaveType);
                return leaveType;
            } else {
                log.warn("leaveType not found in metadata or is empty");
                return null;
            }
            
        } catch (Exception e) {
            log.error("Error extracting leaveType from metadata: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * Calculate leave deduction from approval request metadata
     * @param metadata The metadata string from approval request
     * @return Total days to deduct
     */
    private BigDecimal calculateDeductionFromMetadata(String metadata) {
        try {
            if (metadata == null || metadata.trim().isEmpty()) {
                log.warn("Metadata is null or empty, cannot calculate deduction");
                return BigDecimal.ZERO;
            }
            
            log.info("Processing metadata for deduction calculation: {}", metadata);
            
            // Parse the metadata JSON
            Map<String, Object> metadataMap = objectMapper.readValue(metadata, Map.class);
            String leaveDatesJson = (String) metadataMap.get("leaveDates");
            
            if (leaveDatesJson == null || leaveDatesJson.trim().isEmpty()) {
                log.warn("leaveDates not found in metadata, cannot calculate deduction");
                return BigDecimal.ZERO;
            }
            
            log.info("Found leaveDates JSON: {}", leaveDatesJson);
            
            // Parse the leaveDates JSON to get Map<String, String> (dates as strings)
            Map<String, String> leaveDatesMap = objectMapper.readValue(leaveDatesJson, Map.class);
            
            log.info("Parsed leaveDates map with {} entries: {}", leaveDatesMap.size(), leaveDatesMap);
            
            BigDecimal totalDeduction = BigDecimal.ZERO;
            
            // Calculate deduction based on request types
            for (Map.Entry<String, String> entry : leaveDatesMap.entrySet()) {
                String dateStr = entry.getKey();
                String requestType = entry.getValue();
                
                log.info("Processing date: {} with requestType: {}", dateStr, requestType);
                
                if (requestType != null) {
                    BigDecimal deductionForThisDate = switch (requestType.toUpperCase()) {
                        case "FULL_DAY", "FULL DAY" -> BigDecimal.ONE;
                        case "FIRST_HALF", "FIRST HALF" -> new BigDecimal("0.5");
                        case "SECOND_HALF", "SECOND HALF" -> new BigDecimal("0.5");
                        default -> {
                            log.warn("Date {}: Unknown requestType '{}' in metadata. Defaulting to full day.", dateStr, requestType);
                            yield BigDecimal.ONE;
                        }
                    };

                    totalDeduction = totalDeduction.add(deductionForThisDate);
                    log.info("Date {}: Added {} day, running total: {} days", dateStr, deductionForThisDate, totalDeduction);
                    
                } else {
                    log.warn("Date {}: requestType is null in metadata. Defaulting to full day.", dateStr);
                    totalDeduction = totalDeduction.add(BigDecimal.ONE);
                    log.info("Date {}: Added 1.0 day (null requestType), running total: {} days", dateStr, totalDeduction);
                }
            }
            
            log.info("Final deduction calculation: {} days from {} leave entries", totalDeduction, leaveDatesMap.size());
            return totalDeduction;
            
        } catch (Exception e) {
            log.error("Error calculating deduction from metadata: {}", e.getMessage(), e);
            return BigDecimal.ZERO;
        }
    }

    @Override
    public UploadLeaveDocumentResponse uploadLeaveDocument(UploadLeaveDocumentRequest request) {

        Integer empId = JwtContext.getInstance().getUserId();
        if (empId == null) {
            throw new BusinessException("Employee ID not found in JWT context");
        }

        if (request.getFile() == null || request.getFile().isEmpty()) {
            throw new BusinessException("File is required");
        }

        try {
            MultipartFile file = request.getFile();
            validateDocumentType(file.getContentType());

            String documentKey = generateDocumentKey(empId, file.getOriginalFilename());

            // Upload directly to S3 without Base64 conversion
            String uploadedKey = s3Service.uploadLeaveDocument(
                file,
                "master-service",
                documentKey
            );

            String documentUrl = s3Service.getLeaveDocumentCloudfrontUrl(uploadedKey);

            return UploadLeaveDocumentResponse.builder()
                    .documentUrl(documentUrl)
                    .fileName(file.getOriginalFilename())
                    .contentType(file.getContentType())
                    .message("Document uploaded successfully")
                    .success(true)
                    .build();

        } catch (BusinessException e) {
            log.error("Business error during document upload: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Error uploading leave document: {}", e.getMessage());
            throw new BusinessException("Failed to upload document: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
    public CancelRequestResponse cancelRequest(CancelRequestRequest request) {
        log.info("Cancelling attendance request with ID: {}", request.getRequestId());
        
        try {
            // 1. Find the attendance record directly by requestId (new flow)
            EmployeeAttendanceRequest attendanceRecord = employeeAttendanceRequestRepository
                    .findById(request.getRequestId())
                    .orElseThrow(() -> new BusinessException("Attendance record not found with ID: " + request.getRequestId()));

            if(attendanceRecord.getType().equalsIgnoreCase(AppConstants.WEEK_OFF)){
                throw new BusinessException("Week off cannot be cancelled");
            }

            log.info("Found attendance record with status: {} for request ID: {}",
                    attendanceRecord.getStatus(), request.getRequestId());
            
            // 2. Validate payroll processing dates before status checks
            leaveRequestValidator.validatePayrollProcessingDates(Collections.singletonList(attendanceRecord.getDate()));

            // 3. Check if request is already cancelled
            if (AppConstants.STATUS_CANCELLED.equals(attendanceRecord.getStatus())) {
                throw new BusinessException("Request is already cancelled");
            }
            
            // 4. Process cancellation based on current status
            switch (attendanceRecord.getStatus()) {
                case AppConstants.STATUS_PENDING -> {
                    log.info("Processing direct cancellation for PENDING request ID: {}", request.getRequestId());
                    return handlePendingCancellation(attendanceRecord);
                }
                case AppConstants.STATUS_APPROVED -> {
                    log.info("Processing approval-based cancellation for APPROVED request ID: {}", request.getRequestId());
                    return handleApprovedCancellation(attendanceRecord);
                }
                default -> throw new BusinessException("Cannot cancel request in status: " + attendanceRecord.getStatus());
            }
                    
        } catch (BusinessException e) {
            // Re-throw BusinessException as-is
            throw e;
        } catch (Exception e) {
            log.error("Error cancelling request ID: {}", request.getRequestId(), e);
            throw new BusinessException("Failed to cancel request: " + e.getMessage());
        }
    }
    /**
     * Restore DailyAttendanceSummary from logs after cancellation
     * @param approvalRequest The approval request containing metadata
     * @param attendanceRecord The attendance record
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    private void restoreDailyAttendanceSummaryFromLogs(ApprovalRequest approvalRequest, EmployeeAttendanceRequest attendanceRecord) {
        try {
            String leaveDatesJson = null;
            Map<String, Object> cancellationMetadataMap = objectMapper.readValue(approvalRequest.getMetadata(), Map.class);
            String isCancellationRequest = (String) cancellationMetadataMap.get("isCancellationRequest");
            // Check if this is a cancellation approval request
            if (isCancellationRequest != null && isCancellationRequest.equalsIgnoreCase(Boolean.TRUE.toString())) {
                // For cancellation requests, get leaveDates from the original approval request
                String attendanceRecordId = null;
                if (approvalRequest.getMetadata() != null) {
                    attendanceRecordId = (String) cancellationMetadataMap.get("attendanceRecordId");
                }

                if (attendanceRecordId != null) {
                    // Find the original approval request by referenceId
                    List<ApprovalRequest> originalRequests = approvalRequestRepository.findByReferenceId(Long.valueOf(attendanceRecordId));
                    if (originalRequests != null && !originalRequests.isEmpty()) {
                        ApprovalRequest originalRequest = originalRequests.get(0);
                        if (originalRequest.getMetadata() != null) {
                            @SuppressWarnings("unchecked")
                            Map<String, Object> originalMetadataMap = objectMapper.readValue(originalRequest.getMetadata(), Map.class);
                            leaveDatesJson = (String) originalMetadataMap.get("leaveDates");
                        }
                    }
                }
            } else {
                // For regular approval requests, get leaveDates from current metadata
                if (approvalRequest.getMetadata() != null && !approvalRequest.getMetadata().trim().isEmpty()) {
                    Map<String, Object> metadataMap = objectMapper.readValue(approvalRequest.getMetadata(), Map.class);
                    leaveDatesJson = (String) metadataMap.get("leaveDates");
                }
            }

            if (leaveDatesJson != null && !leaveDatesJson.trim().isEmpty()) {
                // Parse the leaveDates JSON to get all dates
                Map<String, String> leaveDatesMap = objectMapper.readValue(leaveDatesJson, Map.class);

                log.info("Restoring DailyAttendanceSummary from logs for {} dates after cancellation for employee {}",
                        leaveDatesMap.size(), attendanceRecord.getEmpId());

                // Restore summary records for each date
                for (Map.Entry<String, String> entry : leaveDatesMap.entrySet()) {
                    String dateStr = entry.getKey();

                    try {
                        // Parse the date string to LocalDate
                        LocalDateTime dateTime = LocalDateTime.parse(dateStr);
                        LocalDate attendanceDate = dateTime.toLocalDate();

                        // Find and restore summary record for this date
                        DailyAttendanceSummary existingSummary = summaryRepository.findByEmployeeIdAndAttendanceDate(
                            attendanceRecord.getEmpId(), attendanceDate).orElse(null);

                        if (existingSummary != null) {
                            DailyAttendanceSummary restoredSummary = dailyAttendanceSummaryLogsService.restoreFromLogs(
                                existingSummary.getId(),
                                JwtContext.getInstance().getUserId().toString()
                            );

                            if (restoredSummary != null) {
                                log.info("Successfully restored DailyAttendanceSummary from logs for employee {} on date {}",
                                    attendanceRecord.getEmpId(), attendanceDate);
                            } else {
                                log.warn("Failed to restore DailyAttendanceSummary from logs for employee {} on date {}",
                                    attendanceRecord.getEmpId(), attendanceDate);
                            }
                        }
                    } catch (Exception e) {
                        log.error("Error processing date {} for DailyAttendanceSummary restoration: {}", dateStr, e.getMessage(), e);
                    }
                }
            } else {
                log.warn("No leaveDates found in metadata for employee {}", attendanceRecord.getEmpId());
            }
            
        } catch (Exception e) {
            log.error("Error restoring DailyAttendanceSummary from logs for employee {}: {}", attendanceRecord.getEmpId(), e.getMessage(), e);
        }
    }
    
    /**
     * Restore leave balance for cancelled leave requests
     * @param approvalRequest The approval request containing metadata
     * @param attendanceRecord The attendance record
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    private void restoreLeaveBalance(ApprovalRequest approvalRequest, EmployeeAttendanceRequest attendanceRecord) {
        try {
            if (approvalRequest.getMetadata() == null || approvalRequest.getMetadata().trim().isEmpty()) {
                log.warn("No metadata found in approval request for leave balance restoration");
                return;
            }
            
            // Parse metadata to get leave information
            Map<String, Object> metadataMap = objectMapper.readValue(approvalRequest.getMetadata(), Map.class);
            String isCancellationRequest = (String) metadataMap.get("isCancellationRequest");
            // For cancellation approval requests, the metadata structure is different
            String leaveType;
            String leaveDayCountStr;

            if (isCancellationRequest.trim().equalsIgnoreCase(Boolean.TRUE.toString())) {
                // For cancellation approval requests, use requestType as leaveType
                leaveType = (String) metadataMap.get("leaveType");
                leaveDayCountStr = (String) metadataMap.get("leaveDayCount");
            } else {
                // For regular approval requests, use the original structure
                leaveType = (String) metadataMap.get("leaveType");
                leaveDayCountStr = (String) metadataMap.get("leaveDayCount");
            }
            
            if (leaveType == null || leaveDayCountStr == null) {
                log.warn("Missing leaveType/requestType or leaveDayCount in metadata for leave balance restoration. Available keys: {}", metadataMap.keySet());
                return;
            }
            
            BigDecimal leaveDayCount = new BigDecimal(leaveDayCountStr);
            Integer empId = attendanceRecord.getEmpId();
            
            log.info("Restoring leave balance for employee {}: {} days to {} balance", 
                    empId, leaveDayCount, leaveType);
            
            // Get current balance
            BigDecimal currentBalance = leaveBalanceService.getAvailableLeaveCount(empId, leaveType);
            if (currentBalance == null) {
                currentBalance = BigDecimal.ZERO;
            }
            
            // Calculate new balance
            BigDecimal newBalance = currentBalance.add(leaveDayCount);
            
            // Update leave balance
            leaveBalanceService.updateLeaveBalance(empId, leaveType, newBalance);
            log.info("Successfully restored leave balance for employee {}: {} balance updated from {} to {} days", 
                    empId, leaveType, currentBalance, newBalance);
                    
        } catch (Exception e) {
            log.error("Error restoring leave balance for attendance record ID: {}", attendanceRecord.getId(), e);
            // Don't throw exception to avoid rolling back the cancellation
            // The balance can be updated later through a separate process
        }
    }
    /**
     * Update approval steps status to CANCELLED
     *
     * @param requestId The approval request ID
     */
    private void updateApprovalStepsStatus(Long requestId) {
        try {
            // Fetch all steps for the request
            List<com.stpl.tech.attendance.approval.entity.ApprovalStep> steps = approvalStepRepository.findByApprovalRequestId(requestId);

            if (steps == null || steps.isEmpty()) {
                log.warn("No approval steps found for request ID: {} to cancel", requestId);
                return;
            }

            for (com.stpl.tech.attendance.approval.entity.ApprovalStep step : steps) {
                // Update sub-steps first
                List<SubApprovalStep> subSteps = subApprovalStepRepository.findByApprovalStepId(step.getId());
                if (subSteps != null && !subSteps.isEmpty()) {
                    for (com.stpl.tech.attendance.approval.entity.SubApprovalStep subStep : subSteps) {
                        if (subStep.getStatus() != ApprovalStepStatus.APPROVED && subStep.getStatus() != ApprovalStepStatus.REJECTED) {
                            subStep.setStatus(ApprovalStepStatus.CANCELLED);
                            subStep.setUpdatedDate(DateTimeUtil.now());
                            subStep.setUpdatedBy(JwtContext.getInstance().getUserId() != null ? JwtContext.getInstance().getUserId().toString() : "SYSTEM");
                            subApprovalStepRepository.save(subStep);
                        }
                    }
                }

                // Update main step
                if (step.getStatus() != ApprovalStepStatus.APPROVED && step.getStatus() != ApprovalStepStatus.REJECTED) {
                    step.setStatus(ApprovalStepStatus.CANCELLED);
                    step.setUpdatedDate(DateTimeUtil.now());
                    step.setUpdatedBy(JwtContext.getInstance().getUserId() != null ? JwtContext.getInstance().getUserId().toString() : "SYSTEM");
                    approvalStepRepository.save(step);
                }
            }

            log.info("Updated approval steps and sub-steps to CANCELLED for request ID: {}", requestId);
        } catch (Exception e) {
            log.error("Error updating approval steps status for request ID: {}", requestId, e);
            // Don't throw exception to avoid rolling back the cancellation
        }
    }

    /**
     * Validate the document type
     * @param contentType The content type to validate
     * @throws BusinessException if content type is not supported
     */
    private void validateDocumentType(String contentType) {
        if (contentType == null || contentType.trim().isEmpty()) {
            throw new BusinessException("Content type is required");
        }
        
        // Supported content types
        String[] supportedTypes = {
            "image/jpeg", "image/jpg", "image/png", "application/pdf"
        };
        
        boolean isSupported = false;
        for (String supportedType : supportedTypes) {
            if (contentType.equalsIgnoreCase(supportedType)) {
                isSupported = true;
                break;
            }
        }
        
        if (!isSupported) {
            throw new BusinessException("Unsupported content type: " + contentType + 
                    ". Supported types are: JPEG, JPG, PNG, PDF");
        }
    }
    
    /**
     * Generate unique document key
     *
     * @param employeeId The employee ID
     * @param fileName   The original file name
     * @return Unique document key
     */
    private String generateDocumentKey(Integer employeeId, String fileName) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String fileExtension = getFileExtension(fileName);
        String documentTypePath = "";
        
        return String.format("%s/%d/%s_%s%s", 
                documentTypePath, employeeId, timestamp, 
                sanitizeFileName(fileName), fileExtension);
    }
    
    /**
     * Get file extension from file name
     * @param fileName The file name
     * @return File extension with dot
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || !fileName.contains(".")) {
            return "";
        }
        return fileName.substring(fileName.lastIndexOf("."));
    }
    
    /**
     * Sanitize file name for S3 key
     * @param fileName The original file name
     * @return Sanitized file name
     */
    private String sanitizeFileName(String fileName) {
        if (fileName == null) {
            return "document";
        }
        // Remove extension and replace special characters
        String nameWithoutExtension = fileName.contains(".") ? 
                fileName.substring(0, fileName.lastIndexOf(".")) : fileName;
        return nameWithoutExtension.replaceAll("[^a-zA-Z0-9_-]", "_");
    }

    /**
     * Maps string attendance type to AttendanceType enum.
     * This is necessary because the attendanceType string from the approval workflow
     * might not exactly match the enum value, but they are semantically similar.
     * For example, "OD" might map to AttendanceType.OD, "WFH" to AttendanceType.WFH, etc.
     * @param attendanceType The string representation of the attendance type.
     * @return The corresponding AttendanceType enum value.
     */
    private AttendanceType mapAttendanceType(String attendanceType) {
        if (attendanceType == null) {
            return AttendanceType.NORMAL; // Default to NORMAL
        }
        return switch (attendanceType.toUpperCase()) {
            case "OD" -> AttendanceType.OD;
            case "WFH" -> AttendanceType.WFH;
            case "LEAVE" -> AttendanceType.LEAVE;
            case "REGULARISATION" -> AttendanceType.REGULARISATION;
            default -> AttendanceType.NORMAL; // Fallback to NORMAL for unknown types
        };
    }

    /**
     * Deduct reserved count from EMP_ATTENDANCE_RESERVE_DATA table
     * This function is called when leave is approved or rejected to reduce the reserved count
     * @param empId Employee ID
     * @param leaveType Type of leave (LEAVE, COMP_OFF, LWP)
     * @param daysCount Number of days to deduct from reserved count
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    private void deductReservedCount(Integer empId, String leaveType, BigDecimal daysCount) {
        try {
            log.info("Deducting reserved count for employee {}: {} days of type {}", empId, daysCount, leaveType);
            
            // Validate leave type
            if (leaveType == null || leaveType.trim().isEmpty()) {
                log.warn("Leave type is null or empty for employee {}, skipping reserved count deduction", empId);
                return;
            }
            
            String upperLeaveType = leaveType.toUpperCase();
            if (!"LEAVE".equals(upperLeaveType) && !"COMP_OFF".equals(upperLeaveType) && !"LWP".equals(upperLeaveType)) {
                log.warn("Invalid leave type '{}' for employee {}, skipping reserved count deduction", leaveType, empId);
                return;
            }
            
                    // Get the employee's leave data record to get EMP_ATTENDANCE_BALANCE_DATA_ID
            EmpAttendanceBalanceData existingLeaveData = empAttendanceBalanceDataRepository.findByEmpId(empId).orElse(null);
            if (existingLeaveData == null) {
            log.warn("No EMP_ATTENDANCE_BALANCE_DATA record found for employee {}, skipping reserved count deduction", empId);
                return;
            }
            Long empAttendanceBalanceDataId = existingLeaveData.getId();
            
            // Find the detail record for this employee and leave type
            EmpAttendanceReserveData existingDetail = empAttendanceReserveDataRepository
                    .findByEmpAttendanceBalanceIdAndType(empAttendanceBalanceDataId, upperLeaveType)
                    .orElse(null);
            
            if (existingDetail != null) {
                // Update existing record - deduct the days from reserved count
                BigDecimal currentReservedCount = existingDetail.getReservedCount() != null ? existingDetail.getReservedCount() : BigDecimal.ZERO;
                BigDecimal newReservedCount = currentReservedCount.subtract(daysCount).compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : currentReservedCount.subtract(daysCount); // Ensure non-negative
                
                existingDetail.setReservedCount(newReservedCount);
                existingDetail.setUpdatedBy(empId.toString());
                empAttendanceReserveDataRepository.save(existingDetail);
                
                log.info("Deducted reserved count for employee: {} type {} - reserved count: {} -> {}",
                        empId, upperLeaveType, currentReservedCount, newReservedCount);
            } else {
                log.warn("No EMP_ATTENDANCE_RESERVE_DATA record found for employee {} with type {}, cannot deduct reserved count", 
                        empId, upperLeaveType);
            }
            
        } catch (Exception e) {
            log.error("Error deducting reserved count for employee {} with leave type {}: {}", 
                    empId, leaveType, e.getMessage(), e);
            // Don't throw exception to avoid rolling back the main operation
            // The reserved count can be updated later through a separate process
        }
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    private void insertLeaveDetailData(Integer empId, String leaveType, BigDecimal daysCount) {
        try {
            log.info("Inserting leave detail data for employee {}: {} days of type {}", empId, daysCount, leaveType);
            
            // Validate leave type
            if (leaveType == null || leaveType.trim().isEmpty()) {
                log.warn("Leave type is null or empty for employee {}, skipping leave detail data insertion", empId);
                return;
            }
            
            String upperLeaveType = leaveType.toUpperCase();
            if (!"LEAVE".equals(upperLeaveType) && !"COMP_OFF".equals(upperLeaveType) && !"LWP".equals(upperLeaveType)) {
                log.warn("Invalid leave type '{}' for employee {}, skipping leave detail data insertion", leaveType, empId);
                return;
            }
            
            // Get the employee's leave data record to get EMP_ATTENDANCE_BALANCE_DATA_ID
            EmpAttendanceBalanceData existingLeaveData = empAttendanceBalanceDataRepository.findByEmpId(empId).orElse(null);
            assert existingLeaveData != null;
            Long empAttendanceBalanceDataId = existingLeaveData.getId();
            
            // Check if detail record already exists for this employee and leave type
            EmpAttendanceReserveData existingDetail = empAttendanceReserveDataRepository
                    .findByEmpAttendanceBalanceIdAndType(empAttendanceBalanceDataId, upperLeaveType)
                    .orElse(null);
            
            if (existingDetail != null) {
                // Update existing record - add the new days to reserved count
                BigDecimal newReservedCount = existingDetail.getReservedCount().add(daysCount);
                existingDetail.setReservedCount(newReservedCount);
                existingDetail.setUpdatedBy(empId.toString());
                empAttendanceReserveDataRepository.save(existingDetail);
                
                log.info("Updated existing leave detail data for employee: {} type {} - reserved count: {} -> {}",
                        empId, upperLeaveType, existingDetail.getReservedCount().subtract(daysCount), newReservedCount);
            } else {
                // Create new record
                EmpAttendanceReserveData newDetail = com.stpl.tech.attendance.entity.EmpAttendanceReserveData.builder()
                        .empAttendanceBalanceId(empAttendanceBalanceDataId)
                        .type(upperLeaveType)
                        .reservedCount(daysCount)
                        .updatedBy(empId.toString())
                        .build();
                
                empAttendanceReserveDataRepository.save(newDetail);
                
                log.info("Created new leave detail data for employee: {} type {} with reserved count: {}",
                        empId, upperLeaveType, daysCount);
            }
            
        } catch (Exception e) {
            log.error("Error inserting leave detail data for employee {} with leave type {}: {}", 
                    empId, leaveType, e.getMessage(), e);
            // Don't throw exception to avoid rolling back the leave application
            // The detail data can be inserted later through a separate process
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
    public ApplyWeekOffResponse applyWeekOff(ApplyWeekOffRequest request) {
        log.info("Applying week off for employee with date: {} and forUpcomingWeeks: {}", 
                request.getDate(), request.isForUpcomingWeeks());
        Integer empId = request.getEmpId();
        try {
            // Validate request
            weekOffRequestValidator.validate(request);

            // Process week off application
            ApplyWeekOffResponse.CreatedEntry createdEntry = processWeekOffApplication(request);
            if (createdEntry == null) {
                throw new BusinessException("Failed to create week off record");
            }
            Long summaryId = createDailySummary(request.getEmpId(), request.getDate().toLocalDate(),null,null,AttendanceType.WEEK_OFF.name(),AttendanceStatus.ABSENT.name());
            asyncOperationService.postWeekOffApplicationAsync(request,createdEntry.getEntryId(),JwtContext.getInstance().getUserId(),summaryId);
            return createWeekOffSuccessResponse(createdEntry);
                    
        } catch (BusinessException e) {
            // Re-throw BusinessException as-is
            throw e;
        } catch (Exception e) {
            log.error("Error applying week off for employee: {}", empId, e);
            throw new BusinessException("Failed to apply week off");
        }
    }
    public Long createDailySummary(Integer empId, LocalDate date,LocalDateTime checkIn, LocalDateTime checkOut, String type, String status) {
        DailyAttendanceSummary summary = summaryRepository.findByEmployeeIdAndAttendanceDate(
                empId, date).orElse(null);
        if(Objects.isNull(summary)){
            summary = new DailyAttendanceSummary();
            summary.setEmployeeId(empId);
            summary.setAttendanceDate(date);
            summary.setStatus(AttendanceStatus.valueOf(status));
            summary.setReferenceId(null); // No approval flow, so reference ID is null
            summary.setTotalPunches(0);
        }
        if(checkIn!=null){
            summary.setFirstCheckIn(checkIn);
        }
        if(checkOut!=null){
            summary.setLastCheckOut(checkOut);
        }

        summary.setType(AttendanceType.valueOf(type));

        DailyAttendanceSummary savedSummary = summaryRepository.save(summary);

        // LOG THE CREATION
        try {
            dailyAttendanceSummaryLogsService.updateOrCreateLog(
                    savedSummary,
                    DailyAttendanceSummaryLogs.ActionStatus.CREATED,
                    type,
                    JwtContext.getInstance().getUserId().toString(),status
            );
            log.debug("Logged week off summary creation for employee: {} on date: {}", empId, date);
        } catch (Exception e) {
            log.error("Failed to log week off summary creation for employee: {} on date: {}", empId, date, e);
            // Don't throw exception to avoid rolling back the main transaction
        }

        log.info("Created week off daily summary with ID: {} for employee {} on date {}",
                savedSummary.getId(), empId, date);

        return savedSummary.getId();
    }

    @Transactional(rollbackFor = Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
    private ApplyWeekOffResponse.CreatedEntry processWeekOffApplication(ApplyWeekOffRequest request) {
        LocalDateTime currentTime = DateTimeUtil.now();
        LocalDate weekOffDate = request.getDate().toLocalDate();
        String weekOffDay = weekOffDate.getDayOfWeek().name();
        // Create entry in EMP_ATTENDANCE_REQUEST table
        Long attendanceRequestId = createWeekOffAttendanceRequest(request, request.getEmpId(), currentTime, request.getEmpId().toString(), weekOffDay);
        Integer empId = JwtContext.getInstance().getUserId();
        return ApplyWeekOffResponse.CreatedEntry.builder()
                .date(request.getDate())
                .entryId(attendanceRequestId)
                .weekOffDay(weekOffDay)
                .isTemporary(!request.isForUpcomingWeeks())
                .build();
    }

    /**
     * Create week off entry in EMP_ATTENDANCE_REQUEST table
     */
    private Long createWeekOffAttendanceRequest(ApplyWeekOffRequest request, Integer empId,
                                              LocalDateTime currentTime, String userId, String weekOffDay) {
        LocalDateTime startTime, endTime;

        if (Boolean.FALSE.equals(request.isForUpcomingWeeks())) {
            // Temporary: start and end time same date (00:00:00 to 23:59:59)
            startTime = request.getDate().toLocalDate().atStartOfDay();
            endTime = request.getDate().toLocalDate().atTime(23, 59, 59);
        } else {
            // Permanent: start time is the date, end time is infinity
            startTime = request.getDate().toLocalDate().atStartOfDay();
            endTime = LocalDateTime.of(9999, 12, 31, 23, 59, 59); // Infinity equivalent
        }

        EmployeeAttendanceRequest attendanceRequest = EmployeeAttendanceRequest.builder()
                .empId(empId)
                .date(request.getDate())
                .type(AttendanceType.WEEK_OFF.name())
                .status(AppConstants.STATUS_APPROVED)
                .reason("Week off application")
                .comments(weekOffDay) // Store day in caps (THURSDAY)
                .startTime(startTime)
                .endTime(endTime)
                .createdBy(userId)
                .creationTime(currentTime)
                .build();

        EmployeeAttendanceRequest savedRequest = employeeAttendanceRequestRepository.save(attendanceRequest);
        log.info("Created week off attendance request with ID: {} for employee {} on date {}",
                savedRequest.getId(), empId, request.getDate());

        // NEW: Log the creation asynchronously
        asyncOperationService.logAttendanceRequestCreationAsync(savedRequest.getId(), empId, AttendanceType.WEEK_OFF.name(), userId);

        return savedRequest.getId();
    }

    /**
     * Update week off day in EMP_ATTENDANCE_BALANCE_DATA table
     */

    
    /**
     * Create success response for week off application
     * @param createdEntry Created entry with details
     * @return ApplyWeekOffResponse with success details
     */
    private ApplyWeekOffResponse createWeekOffSuccessResponse(ApplyWeekOffResponse.CreatedEntry createdEntry) {
        return ApplyWeekOffResponse.builder()
                .success(true)
                .message("Successfully applied week off for " + createdEntry.getWeekOffDay())
                .createdEntry(createdEntry)
                .build();
    }

    public ApprovalRequest createApprovalRequest(Object request, EmployeeAttendanceRequest attendanceRecord, ApprovalType approvalType,Integer empId) {
        // Get employee details to find reporting manager
        if (empId == null) {
            throw new BusinessException("Employee ID not found in authentication context");
        }

        EmployeeBasicDetail employee = userCacheService.getUserById(empId);

        if (employee == null) {
            throw new BusinessException("Employee not found with ID: " + empId);
        }

        // Get reporting manager as approver
        List<String> approvers = new ArrayList<>();
        if (employee.getReportingManagerId() != null) {
            approvers.add(employee.getReportingManagerId().toString());
        }

        if (approvers.isEmpty()) {
            throw new BusinessException("No reporting manager found for employee: " + empId);
        }

        // Create approval request
        return buildApprovalRequest(request, attendanceRecord, approvers, approvalType);
    }

    /**
     * Builds the approval request object for any type of application
     * Unified function to replace buildLeaveApprovalRequest and buildRegularisationApprovalRequest
     */
    private ApprovalRequest buildApprovalRequest(
            Object request,
            EmployeeAttendanceRequest attendanceRecord,
            List<String> approvers,
            ApprovalType approvalType
    ) {
        try {
            Map<String, String> metadata = buildMetadata(request, attendanceRecord, approvers, approvalType);
            LocalDateTime now = DateTimeUtil.now();

            // Get unit ID with null check
            Integer unitId = JwtContext.getInstance().getUnitId();
            if (unitId == null) {
                throw new BusinessException("Unit ID not found in authentication context");
            }

            // Get user ID with null check
            Integer userId = JwtContext.getInstance().getUserId();
            if (userId == null) {
                throw new BusinessException("User ID not found in authentication context");
            }

            return ApprovalRequest.builder()
                    .requestType(approvalType)
                    .requesterId(Long.valueOf(userId))
                    .status(ApprovalStatus.PENDING)
                    .currentStep(1)
                    .totalSteps(1)
                    .requestDate(now)
                    .createdDate(now)
                    .createdBy(userId.toString())
                    .referenceId(attendanceRecord.getId())
                    .unitId(Long.valueOf(unitId))
                    .metadata(objectMapper.writeValueAsString(metadata))
                    .build();
        } catch (JsonProcessingException e) {
            throw new BusinessException("Failed to create approval request metadata: " + e.getMessage());
        }
    }

    /**
     * Helper class to build metadata for different types of approval requests
     */
    private Map<String, String> buildMetadata(Object request, EmployeeAttendanceRequest attendanceRecord,
                                              List<String> approvers, ApprovalType approvalType) {
        Map<String, String> metadata = new HashMap<>();

        try {
            switch (approvalType) {
                case EMPLOYEE_LEAVE:
                    buildLeaveMetadata((ApplyLeaveRequest) request, attendanceRecord, metadata);
                    break;
                case EMPLOYEE_REGULARISATION:
                    buildRegularisationMetadata((ApplyRegularisationRequest) request, attendanceRecord, approvers, metadata);
                    break;
                default:
                    log.warn("Unknown approval type: {}. Using default metadata.", approvalType);
                    buildDefaultMetadata(attendanceRecord, metadata);
            }
        } catch (ClassCastException e) {
            log.error("Invalid request type for approval type: {}. Error: {}", approvalType, e.getMessage());
            throw new BusinessException("Invalid request type for approval type: " + approvalType);
        }

        return metadata;
    }

    /**
     * Build metadata specifically for leave applications
     */
    private void buildLeaveMetadata(ApplyLeaveRequest request, EmployeeAttendanceRequest attendanceRecord,
                                    Map<String, String> metadata) {
        try {
            // Fetch detail records to get date-wise leave information
            List<EmployeeAttendanceRequestDetail> detailRecords =
                    employeeAttendanceRequestDetailService.getDetailsByAttendanceRequestId(attendanceRecord.getId());

            // Create Map<LocalDateTime, String> for date-wise leave types
            Map<LocalDateTime, String> leaveDatesMap = new HashMap<>();
            for (EmployeeAttendanceRequestDetail detail : detailRecords) {
                leaveDatesMap.put(detail.getDate(), detail.getRequestType());
            }

            // Sort the dates to ensure they appear in chronological order in metadata
            // Create a sorted map using TreeMap to maintain date order
            Map<LocalDateTime, String> sortedLeaveDatesMap = new TreeMap<>(leaveDatesMap);

            // Convert the sorted Map to JSON string for metadata
            String leaveDatesJson = objectMapper.writeValueAsString(sortedLeaveDatesMap);

            // Calculate total leave days from the request
            BigDecimal totalLeaveDays = calculateTotalLeaveDays(leaveDatesMap, attendanceRecord.getEmpId(), request.getLeaveType());

            metadata.put("leaveDates", leaveDatesJson);
            metadata.put("leaveType", request.getLeaveType());
            metadata.put("leaveDayCount", String.valueOf(totalLeaveDays));
            metadata.put("reason", attendanceRecord.getReason() != null ? attendanceRecord.getReason() : "Leave application");
            metadata.put("documents", request.getDocuments() != null ? request.getDocuments() : "");
            metadata.put("comments", attendanceRecord.getComments() != null ? attendanceRecord.getComments() : "");

        } catch (Exception e) {
            log.error("Error building leave metadata: {}", e.getMessage(), e);
            throw new BusinessException("Failed to build leave metadata: " + e.getMessage());
        }
    }

    /**
     * Build metadata specifically for regularisation applications
     */
    private void buildRegularisationMetadata(ApplyRegularisationRequest request, EmployeeAttendanceRequest attendanceRecord,
                                             List<String> approvers, Map<String, String> metadata) {
        metadata.put("fromDate", request.getCheckIn().toString());
        metadata.put("toDate", request.getCheckOut().toString());
        metadata.put("reason", attendanceRecord.getReason() != null ? attendanceRecord.getReason() : "Regularisation application");
        metadata.put("documents", "");
        metadata.put("comments", request.getComments() != null ? request.getComments() : "");
        metadata.put("approvers", String.join(",", approvers));
    }

    /**
     * Build default metadata for unknown approval types
     */
    private void buildDefaultMetadata(EmployeeAttendanceRequest attendanceRecord, Map<String, String> metadata) {
        metadata.put("reason", attendanceRecord.getReason() != null ? attendanceRecord.getReason() : "Application");
        metadata.put("comments", attendanceRecord.getComments() != null ? attendanceRecord.getComments() : "");
        metadata.put("documents", "");
    }

    public BigDecimal calculateTotalLeaveDays(Map<LocalDateTime, String> leaveDatesMap, Integer empId, String leaveType) {
        try {
            if (leaveDatesMap == null || leaveDatesMap.isEmpty()) {
                log.warn("Leave dates map is null or empty, returning 0 days");
                return BigDecimal.ZERO;
            }

            BigDecimal totalDays = BigDecimal.ZERO;

            for (Map.Entry<LocalDateTime, String> entry : leaveDatesMap.entrySet()) {
                LocalDateTime date = entry.getKey();
                String requestType = entry.getValue();

                log.debug("Processing date: {} with requestType: {}", date, requestType);

                if (requestType != null) {
                    switch (requestType.toUpperCase()) {
                        case "FIRST_HALF":
                        case "FIRST HALF", "SECOND_HALF", "SECOND HALF":
                            totalDays = totalDays.add(new BigDecimal("0.5"));
                            break;
                        default:
                            totalDays = totalDays.add(BigDecimal.ONE);
                            break;
                    }
                } else {
                    log.warn("Date {}: requestType is null. Defaulting to full day.", date);
                    totalDays = totalDays.add(BigDecimal.ONE);
                }
            }

            log.info("Calculated total leave days: {} days from {} leave entries", totalDays, leaveDatesMap.size());
            return totalDays;

        } catch (Exception e) {
            log.error("Error calculating total leave days: {}", e.getMessage(), e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * Handle cancellation for PENDING status requests
     * Direct cancellation without approval workflow
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    private CancelRequestResponse handlePendingCancellation(EmployeeAttendanceRequest attendanceRecord) {
        try {
            String previousStatus = attendanceRecord.getStatus();

            // 1. Update EMP_ATTENDANCE_REQUEST status to CANCELLED
            attendanceRecord.setStatus(AppConstants.STATUS_CANCELLED);
            attendanceRecord.setUpdatedBy(JwtContext.getInstance().getUserId().toString());
            attendanceRecord.setUpdationTime(LocalDateTime.now());
            employeeAttendanceRequestRepository.save(attendanceRecord);

            // 2. Find and update APPROVAL_REQUEST (except for WEEK_OFF and OD)
            ApprovalRequest approvalRequest = getApprovalRequestByReferenceId(attendanceRecord.getId());
            if (approvalRequest != null) {
                approvalRequest.setStatus(ApprovalStatus.CANCELLED);
                approvalRequest.setUpdatedDate(LocalDateTime.now());
                approvalRequest.setUpdatedBy(JwtContext.getInstance().getUserId().toString());
                approvalRequestRepository.save(approvalRequest);
                // 3. Update APPROVAL_STEP and SUB_APPROVAL_STEP
                updateApprovalStepsStatus(approvalRequest.getId());
            }

            // 4. Handle reserve count deduction for LEAVE types
            if (isLeaveType(attendanceRecord.getType())) {
                handleReserveCountDeduction(attendanceRecord);
            }

            // 5. Async logging
            asyncOperationService.logAttendanceRequestCancellationAsync(
                attendanceRecord.getId(),
                attendanceRecord.getEmpId(),
                previousStatus,
                attendanceRecord.getType(),
                JwtContext.getInstance().getUserId().toString()
            );

            log.info("Successfully cancelled PENDING request ID: {} from status: {} to CANCELLED",
                    attendanceRecord.getId(), previousStatus);

            return CancelRequestResponse.builder()
                    .message("Request cancelled successfully")
                    .requestId(attendanceRecord.getId())
                    .previousStatus(previousStatus)
                    .newStatus(AppConstants.STATUS_CANCELLED)
                    .build();

        } catch (Exception e) {
            log.error("Error cancelling PENDING request ID: {}", attendanceRecord.getId(), e);
            throw new RuntimeException("Failed to cancel PENDING request: " + e.getMessage(), e);
        }
    }

    /**
     * Handle cancellation for APPROVED status requests
     * Creates approval workflow for cancellation
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    private CancelRequestResponse handleApprovedCancellation(EmployeeAttendanceRequest attendanceRecord) {
        try {
            String previousStatus = attendanceRecord.getStatus();

            // 1. Create approval request for cancellation
            ApprovalRequest cancellationApprovalRequest = employeeAttendanceApprovalService
                    .createCancellationApprovalRequest(attendanceRecord.getId(), attendanceRecord);

            // 2. Save the cancellation approval request
            approvalEngineService.createRequest(cancellationApprovalRequest);

            log.info("Created cancellation approval request with ID: {} for original request: {}",
                    cancellationApprovalRequest.getId(), attendanceRecord.getId());

            return CancelRequestResponse.builder()
                    .message("Cancellation request submitted for approval")
                    .requestId(attendanceRecord.getId())
                    .previousStatus(previousStatus)
                    .newStatus(AppConstants.STATUS_PENDING)
                    .cancellationApprovalId(cancellationApprovalRequest.getId())
                    .build();
        } catch (BusinessException e) {
            // re-throw BusinessException as-is
            throw e;
        } catch (Exception e) {
            log.error("Error creating cancellation approval for APPROVED request ID: {}", attendanceRecord.getId(), e);
           throw new RuntimeException("Failed to create cancellation approval: " + e.getMessage(), e);
        }
    }

    /**
     * Handle reserve count deduction for leave types
     */
    private void handleReserveCountDeduction(EmployeeAttendanceRequest attendanceRecord) {
        try {
            // Find the approval request to get metadata
            ApprovalRequest approvalRequest = getApprovalRequestByReferenceId(attendanceRecord.getId());
            if (approvalRequest != null && approvalRequest.getMetadata() != null) {
                Map<String, Object> metadataMap = objectMapper.readValue(approvalRequest.getMetadata(), Map.class);
                String leaveType = (String) metadataMap.get("leaveType");
                String leaveDayCountStr = (String) metadataMap.get("leaveDayCount");

                if (leaveType != null && leaveDayCountStr != null) {
                    BigDecimal leaveDayCount = new BigDecimal(leaveDayCountStr);
                    Integer empId = attendanceRecord.getEmpId();
                    deductReservedCount(empId, leaveType, leaveDayCount);
                    log.info("Deducted {} days from reserve count for employee {} leave type {}",
                            leaveDayCount, empId, leaveType);
                }
            }
        } catch (Exception e) {
            log.error("Error deducting reserve count for request ID: {}", attendanceRecord.getId(), e);
            // Don't throw exception to avoid rolling back the cancellation
        }
    }

    /**
     * Update approval steps status to CANCELLED
     */
//    private void updateApprovalStepsStatus(Long requestId) {
//        try {
//            // Fetch all steps for the request
//            List<com.stpl.tech.attendance.approval.entity.ApprovalStep> steps = approvalStepRepository.findByApprovalRequestId(requestId);
//
//            if (steps == null || steps.isEmpty()) {
//                log.warn("No approval steps found for request ID: {} to cancel", requestId);
//                return;
//            }
//
//            for (com.stpl.tech.attendance.approval.entity.ApprovalStep step : steps) {
//                // Update sub-steps first
//                List<SubApprovalStep> subSteps = subApprovalStepRepository.findByApprovalStepId(step.getId());
//                if (subSteps != null && !subSteps.isEmpty()) {
//                    for (com.stpl.tech.attendance.approval.entity.SubApprovalStep subStep : subSteps) {
//                        if (subStep.getStatus() != ApprovalStepStatus.APPROVED && subStep.getStatus() != ApprovalStepStatus.REJECTED) {
//                            subStep.setStatus(ApprovalStepStatus.CANCELLED);
//                            subStep.setUpdatedDate(DateTimeUtil.now());
//                            subStep.setUpdatedBy(JwtContext.getInstance().getUserId() != null ? JwtContext.getInstance().getUserId().toString() : "SYSTEM");
//                            subApprovalStepRepository.save(subStep);
//                        }
//                    }
//                }
//
//                // Update main step
//                if (step.getStatus() != ApprovalStepStatus.APPROVED && step.getStatus() != ApprovalStepStatus.REJECTED) {
//                    step.setStatus(ApprovalStepStatus.CANCELLED);
//                    step.setUpdatedDate(DateTimeUtil.now());
//                    step.setUpdatedBy(JwtContext.getInstance().getUserId() != null ? JwtContext.getInstance().getUserId().toString() : "SYSTEM");
//                    approvalStepRepository.save(step);
//                }
//            }
//
//            log.info("Updated approval steps and sub-steps to CANCELLED for request ID: {}", requestId);
//        } catch (Exception e) {
//            log.error("Error updating approval steps status for request ID: {}", requestId, e);
//            // Don't throw exception to avoid rolling back the cancellation
//        }
//    }
}