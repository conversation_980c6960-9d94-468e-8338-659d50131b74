package com.stpl.tech.attendance.context;

import lombok.Getter;
import lombok.Setter;

/**
 * Thread-local context holder for JWT-related information
 */
public  class JwtContext {
    private static final ThreadLocal<JwtContext> context = new ThreadLocal<>();

    @Getter
    @Setter
    private Integer userId;

    @Getter
    @Setter
    private Integer unitId;

    @Getter
    @Setter
    private Integer terminalId;

    @Getter
    @Setter
    private String deviceId;

    @Getter
    @Setter
    private String latLong;

    private JwtContext() {}

    public static JwtContext getInstance() {
        JwtContext instance = context.get();
        if (instance == null) {
            instance = new JwtContext();
            context.set(instance);
        }
        return instance;
    }

    public static void clear() {
        context.remove();
    }

    public static void setContext(Integer userId, Integer unitId , Integer terminalId) {
        JwtContext instance = getInstance();
        instance.setUserId(userId);
        instance.setUnitId(unitId);
        instance.setTerminalId(terminalId);
    }

    public static void setDeviceContext(String deviceId, String latLong) {
        JwtContext instance = getInstance();
        instance.setDeviceId(deviceId);
        instance.setLatLong(latLong);
    }

}