package com.stpl.tech.attendance.notification.repository;

import com.stpl.tech.attendance.notification.entity.Notification;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface NotificationRepository extends JpaRepository<Notification, String> {
    
    @Query("SELECT n FROM Notification n WHERE n.type = :type")
    Page<Notification> findByType(@Param("type") Notification.NotificationType type, Pageable pageable);
    
    @Query("SELECT n FROM Notification n WHERE n.priority = :priority")
    Page<Notification> findByPriority(@Param("priority") Notification.Priority priority, Pageable pageable);
} 