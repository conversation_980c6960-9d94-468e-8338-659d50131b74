package com.stpl.tech.attendance.repository;

import com.stpl.tech.attendance.entity.EmployeeShiftUnitAttendance;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface EmployeeShiftUnitAttendanceRepository extends JpaRepository<EmployeeShiftUnitAttendance, Long> {
    
    /**
     * Find unit attendance by shift instance ID and actual unit ID
     */
    Optional<EmployeeShiftUnitAttendance> findByShiftInstanceIdAndActualUnitId(Long shiftInstanceId, Integer actualUnitId);

    /**
     * Find all unit attendance records by shift instance ID
     */
    List<EmployeeShiftUnitAttendance> findByShiftInstanceId(Long shiftInstanceId);

    /**
     * Find unit attendance records by multiple actual unit IDs and business date
     */
    @Query("SELECT esua FROM EmployeeShiftUnitAttendance esua " +
           "JOIN EmployeeShiftInstances esi ON esua.shiftInstanceId = esi.id " +
           "WHERE esua.actualUnitId IN :actualUnitIds AND esi.businessDate = :businessDate AND esi.instanceStatus = 'ACTIVE' ")
    List<EmployeeShiftUnitAttendance> findByActualUnitIdsAndBusinessDate(
        @Param("actualUnitIds") List<Integer> actualUnitIds, 
        @Param("businessDate") LocalDate businessDate);

    @Query("SELECT esua FROM EmployeeShiftUnitAttendance esua " +
            "JOIN EmployeeShiftInstances esi ON esua.shiftInstanceId = esi.id " +
            "WHERE esua.actualUnitId IN :actualUnitIds " +
            "AND esi.businessDate = :businessDate " +
            "AND esi.instanceStatus = 'ACTIVE' " +
            "AND :currentTime >= esi.actualStartTime " +
            "AND (:currentTime <= esi.actualEndTime OR esi.actualEndTime IS NULL)")
    List<EmployeeShiftUnitAttendance> findByActualUnitIdsAndBusinessDateAndTimeRange(
            @Param("actualUnitIds") List<Integer> actualUnitIds,
            @Param("businessDate") LocalDate businessDate,@Param("currentTime") LocalDateTime currentTime);
    
    /**
     * Find unit attendance records by multiple actual unit IDs, business date, and time range
     */
    @Query("SELECT esua FROM EmployeeShiftUnitAttendance esua " +
           "JOIN EmployeeShiftInstances esi ON esua.shiftInstanceId = esi.id " +
           "WHERE esua.actualUnitId IN :actualUnitIds " +
           "AND esi.businessDate = :businessDate AND esi.instanceStatus = 'ACTIVE' " +
           "AND esua.actualStartTime IS NOT NULL " +
           "AND (esua.actualStartTime <= :endTime AND (esua.actualEndTime IS NULL OR esua.actualEndTime >= :startTime))")
    List<EmployeeShiftUnitAttendance> findByActualUnitIdsAndBusinessDateAndTimeRange(
        @Param("actualUnitIds") List<Integer> actualUnitIds, 
        @Param("businessDate") LocalDate businessDate,
        @Param("startTime") LocalDateTime startTime,
        @Param("endTime") LocalDateTime endTime);



} 