package com.stpl.tech.attendance.enums;

import lombok.Getter;

/**
 * Enum representing different types of attendance attributes
 * that can be configured at department level
 */
@Getter
public enum AttendanceAttributeType {
    
    /**
     * Boolean attributes for leave permissions
     */
    IS_LEAVE_ALLOWED(AttributeDataType.BOOLEAN),
    IS_OD_ALLOWED(AttributeDataType.BOOLEAN),
    IS_WFH_ALLOWED(AttributeDataType.BOOLEAN),
    IS_REGULARISATION_ALLOWED(AttributeDataType.BOOLEAN),
    FIXED_WEEK_OFF(AttributeDataType.BOOLEAN),
    
    /**
     * Leave credit cycle configuration
     */
    LEAVE_CREDIT_CYCLE(AttributeDataType.CYCLIC),
    
    /**
     * Leave count configuration
     */
    TOTAL_NUMBER_OF_LEAVES(AttributeDataType.INTEGER),
    
    /**
     * Payroll processing day configuration
     */
    PAYROLL_PROCESSING_START_DAY(AttributeDataType.INTEGER),
    PAYROLL_PROCESSING_END_DAY(AttributeDataType.INTEGER),
    
    /**
     * Holiday and weekend configuration
     */
    IS_HOLIDAY_ALLOWED(AttributeDataType.BOOLEAN),
    WEEKEND_DAYS(AttributeDataType.STRING);

    /**
     * -- GETTER --
     *  Get the data type for this attribute
     *
     */
    private final AttributeDataType dataType;
    
    /**
     * Constructor with data type information
     * @param dataType the expected data type for this attribute
     */
    AttendanceAttributeType(AttributeDataType dataType) {
        this.dataType = dataType;
    }

    /**
     * Check if the attribute type is a boolean type
     * @return true if the attribute expects boolean values
     */
    public boolean isBooleanType() {
        return this.dataType == AttributeDataType.BOOLEAN;
    }
    
    /**
     * Check if the attribute type is a leave credit cycle type
     * @return true if the attribute expects cycle values (YEARLY, QUARTERLY, HALF_YEAR)
     */
    public boolean isCycleType() {
        return this.dataType == AttributeDataType.CYCLIC;
    }
    
    /**
     * Check if the attribute type is a numeric type
     * @return true if the attribute expects numeric values
     */
    public boolean isNumericType() {
        return this.dataType == AttributeDataType.INTEGER;
    }
    
    /**
     * Check if the attribute type is a string type
     * @return true if the attribute expects string values
     */
    public boolean isStringType() {
        return this.dataType == AttributeDataType.STRING;
    }
    
    /**
     * Check if the attribute type is a weekend days type
     * @return true if the attribute expects weekend days values
     */
    public boolean isWeekendDaysType() {
        return this == WEEKEND_DAYS;
    }
    
    /**
     * Enum representing the data types that attendance attributes can have
     */
    @Getter
    public enum AttributeDataType {
        BOOLEAN("Boolean values (true/false)"),
        INTEGER("Integer values"),
        STRING("String values"),
        CYCLIC("Cyclic values (YEARLY, QUARTERLY, HALF_YEAR)");

        /**
         * -- GETTER --
         *  Get the description of this data type
         *
         */
        private final String description;
        
        AttributeDataType(String description) {
            this.description = description;
        }

    }
}


