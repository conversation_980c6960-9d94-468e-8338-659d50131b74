package com.stpl.tech.attendance.service;

import com.stpl.tech.attendance.dto.UnitAttendanceAnalyticsDTO;

import java.time.LocalDate;

public interface UnitAttendanceAnalyticsCacheService {
    
    /**
     * Get unit analytics from cache or calculate if not cached
     */
    UnitAttendanceAnalyticsDTO getUnitAnalytics(Integer unitId, LocalDate businessDate);
    
    /**
     * Update unit analytics cache with new employee attendance data
     * This method should be called asynchronously when new attendance is punched
     */
    void updateUnitAnalyticsCacheAsync(Integer unitId, LocalDate businessDate, Integer employeeId);
    
    /**
     * Clear all unit analytics cache (called by cron job)
     */
    void clearUnitAttendanceAnalyticsCache();

} 