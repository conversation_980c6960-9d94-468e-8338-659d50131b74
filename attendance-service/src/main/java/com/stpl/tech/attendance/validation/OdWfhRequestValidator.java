package com.stpl.tech.attendance.validation;

import com.stpl.tech.attendance.cache.service.UserCacheService;
import com.stpl.tech.attendance.dto.ApplyOdWfhRequest;
import com.stpl.tech.attendance.exception.BusinessException;
import com.stpl.tech.attendance.repository.EmployeeAttendanceRequestRepository;
import com.stpl.tech.attendance.service.AttendanceMetadataService;
import com.stpl.tech.attendance.service.impl.AttendanceRequestUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Validator for OD/WFH request validation.
 * Handles all OD/WFH-specific validation logic including date overlaps,
 * time overlaps, and employee eligibility validations.
 */
@Slf4j
@Component
public class OdWfhRequestValidator extends AttendanceRequestValidator<ApplyOdWfhRequest> {


    protected OdWfhRequestValidator(AttendanceRequestUtil attendanceRequestUtil, UserCacheService userCacheService, AttendanceMetadataService attendanceMetadataService, EmployeeAttendanceRequestRepository employeeAttendanceRequestRepository) {
        super(attendanceRequestUtil, userCacheService, attendanceMetadataService, employeeAttendanceRequestRepository);
    }

    @Override
    public void validate(ApplyOdWfhRequest request) {
        log.info("Validating OD/WFH request for employee");
        
        // Get employee ID from JWT context for validation
        Integer empId = validateEmployeeContext();
        
        // Validate basic request structure
        validateBasicRequest(request);
        
        // NEW: Validate payroll processing dates for all OD/WFH dates
        List<LocalDateTime> allOdWfhDates = request.getOdWfhEntries().stream()
            .map(ApplyOdWfhRequest.OdWfhEntry::getDate)
            .collect(Collectors.toList());
        validatePayrollProcessingDates(allOdWfhDates);
        
        // Validate employee eligibility for the specific type (OD or WFH)
        validateFromMetadata(empId, request.getType());
        
        // Validate each OD/WFH entry
        for (ApplyOdWfhRequest.OdWfhEntry entry : request.getOdWfhEntries()) {
            validateOdWfhEntry(entry, empId);
        }
        // validate overlap
        validateNoDateRangeOverlaps(empId, allOdWfhDates);
        
        log.info("OD/WFH request validation completed successfully for employee: {}", empId);
    }
    
    /**
     * Validates the basic structure of the OD/WFH request
     * 
     * @param request The OD/WFH request to validate
     * @throws BusinessException if basic validation fails
     */
    private void validateBasicRequest(ApplyOdWfhRequest request) {
        if (request.getOdWfhEntries() == null || request.getOdWfhEntries().isEmpty()) {
            log.error("No OD/WFH entries provided in request");
            throw new BusinessException("At least one OD/WFH entry is required");
        }
        
        if (request.getType() == null || request.getType().trim().isEmpty()) {
            log.error("Type is required for OD/WFH request");
            throw new BusinessException("Type is required for OD/WFH request");
        }
        
        // Validate that type is either OD or WFH
        if (!"OD".equalsIgnoreCase(request.getType()) && !"WFH".equalsIgnoreCase(request.getType())) {
            log.error("Invalid type for OD/WFH request: {}", request.getType());
            throw new BusinessException("Type must be either 'OD' or 'WFH'");
        }
    }
    
    /**
     * Validates an individual OD/WFH entry
     * 
     * @param entry The OD/WFH entry to validate
     * @param empId Employee ID
     * @throws BusinessException if validation fails
     */
    private void validateOdWfhEntry(ApplyOdWfhRequest.OdWfhEntry entry, Integer empId) {
        if (entry.getDate() == null) {
            log.error("No date provided in OD/WFH entry");
            throw new BusinessException("Date is required for OD/WFH entry");
        }
        
        // Check if any dates already have existing entries of any type
        List<LocalDateTime> datesToCheck = Collections.singletonList(entry.getDate());
        validateNoDateRangeOverlaps(empId, datesToCheck);
        
        // For OD/WFH requests, also check if any existing requests overlap with the date
        validateLeaveDateRangeOverlaps(empId, datesToCheck);
        
        // Validate time overlaps if check-in and check-out times are provided
        if (entry.getCheckIn() != null && entry.getCheckOut() != null) {
            if (entry.getCheckOut().isBefore(entry.getCheckIn())) {
                log.error("Check-out time cannot be before check-in time");
                throw new BusinessException("Check-out time cannot be before check-in time");
            }
            
            validateNoTimeOverlaps(empId, entry.getDate(), entry.getCheckIn(), entry.getCheckOut());
        }
    }
}
