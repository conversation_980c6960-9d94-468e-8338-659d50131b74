package com.stpl.tech.attendance.event;

import com.stpl.tech.attendance.cache.TransferCacheService;
import com.stpl.tech.attendance.model.TransferRequest;
import com.stpl.tech.attendance.notification.dto.NotificationRequest;
import com.stpl.tech.attendance.notification.entity.Notification.NotificationType;
import com.stpl.tech.attendance.notification.service.NotificationService;
import com.stpl.tech.attendance.service.TransferStatisticsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
@RequiredArgsConstructor
public class TransferEventListener {
    private final NotificationService notificationService;
    private final TransferCacheService transferCacheService;
   // private final TransferStatisticsService statisticsService;

    @Async
    @EventListener
    public void handleTransferEvent(TransferEvent event) {
        TransferRequest request = event.getTransferRequest();
        log.info("Processing transfer event: {} for request: {}", event.getEventType(), request.getTransferRequestId());

        // Invalidate transfer status cache for the employee
        transferCacheService.invalidateTransferStatus(request.getEmpId());

        switch (event.getEventType()) {
            case CREATED -> handleTransferCreated(request);
            case APPROVED -> handleTransferApproved(request);
            case REJECTED -> handleTransferRejected(request);
            case CANCELLED -> handleTransferCancelled(request);
        }
    }

    private void handleTransferCreated(TransferRequest request) {
        // Notify managers about new transfer request
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("transferId", request.getTransferRequestId());
        metadata.put("empId", request.getEmpId());
        metadata.put("transferType", request.getTransferType());

        /*notificationService.sendWorkflowNotification(
                String.valueOf(request.getTransferRequestId()),
            NotificationType.TRANSFER_REQUEST,
            "New Transfer Request",
            String.format("Transfer request has been created for employee %s", request.getEmpId()),
            Arrays.asList(request.getSourceManagerId(), request.getDestinationManagerId()),
            metadata
        );*/
    }

    private void handleTransferApproved(TransferRequest request) {
        // Notify employee about approved transfer
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("transferId", request.getTransferRequestId());
        metadata.put("transferType", request.getTransferType());

        notificationService.sendWorkflowNotification(
                String.valueOf(request.getTransferRequestId()),
            NotificationType.TRANSFER_APPROVED,
            "Transfer Request Approved",
            "Your transfer request has been approved",
            Arrays.asList(request.getEmpId()),
            metadata
        );

        // Update statistics
       // statisticsService.updateApprovedTransfer(request);
    }

    private void handleTransferRejected(TransferRequest request) {
        // Notify employee about rejected transfer
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("transferId", request.getTransferRequestId());
        metadata.put("transferType", request.getTransferType());

        notificationService.sendWorkflowNotification(
                String.valueOf(request.getTransferRequestId()),
            NotificationType.TRANSFER_REJECTED,
            "Transfer Request Rejected",
            "Your transfer request has been rejected",
            Arrays.asList(request.getEmpId()),
            metadata
        );

        // Update statistics
        //statisticsService.updateRejectedTransfer(request);
    }

    private void handleTransferCancelled(TransferRequest request) {
        // Notify all stakeholders about cancelled transfer
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("transferId", request.getTransferRequestId());
        metadata.put("empId", request.getEmpId());
        metadata.put("transferType", request.getTransferType());

        /*notificationService.sendWorkflowNotification(
                String.valueOf(request.getTransferRequestId()),
            NotificationType.TRANSFER_CANCELLED,
            "Transfer Request Cancelled",
            "Transfer request has been cancelled",
            Arrays.asList(
                request.getEmpId(),
                request.getSourceManagerId(),
                request.getDestinationManagerId()
            ),
            metadata
        );*/

        // Update statistics
       // statisticsService.updateCancelledTransfer(request);
    }
} 