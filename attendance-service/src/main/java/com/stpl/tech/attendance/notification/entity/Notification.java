package com.stpl.tech.attendance.notification.entity;

import com.vladmihalcea.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.hibernate.annotations.Type;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "NOTIFICATION")
public class Notification {
    
    @Id
    @Column(name = "NOTIFICATION_ID", length = 36)
    private String notificationId;
    
    @Column(name = "TYPE", length = 50, nullable = false)
    @Enumerated(EnumType.STRING)
    private NotificationType type;
    
    @Column(name = "TITLE", length = 100, nullable = false)
    private String title;
    
    @Column(name = "MESSAGE", nullable = false)
    private String message;
    
    @Column(name = "PRIORITY", length = 20)
    @Enumerated(EnumType.STRING)
    private Priority priority;
    
    @Column(name = "METADATA", columnDefinition = "json")
    @Type(JsonType.class)
    private Map<String, Object> metadata;
    
    @Column(name = "CREATED_DATE", nullable = false)
    private LocalDateTime createdDate;
    
    @Column(name = "CREATED_BY", length = 36, nullable = true)
    private String createdBy;
    
    @Column(name = "UPDATED_DATE", nullable = false)
    private LocalDateTime updatedDate;
    
    @Column(name = "UPDATED_BY", length = 36, nullable = true)
    private String updatedBy;
    
    public enum NotificationType {
        APPROVAL_REQUEST("KETTLE_OPS"),
        APPROVAL_STATUS("KETTLE_OPS"),
        APPROVAL_COMPLETED("KETTLE_OPS"),
        APPROVAL_REJECTED("KETTLE_OPS"),
        TRANSFER_APPROVED("KETTLE_OPS"),
        TRANSFER_REJECTED("KETTLE_OPS"),
        ROSTERING_TEMP_SHIFT_ASSIGNED("ROSTERING"),
        ROSTERING_SHIFT_ASSIGNED("ROSTERING");
        private final String type;

        NotificationType(String type) {
            this.type = type;
        }
        public static List<NotificationType> getNotificationTypes(String type) {
            return Arrays.stream(NotificationType.values())
                    .filter(t -> t.type.equals(type))
                    .collect(Collectors.toList());
        }
    }
    
    public enum Priority {
        LOW, MEDIUM, HIGH, URGENT
    }
} 