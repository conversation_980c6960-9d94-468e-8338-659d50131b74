package com.stpl.tech.attendance.service;

import com.stpl.tech.attendance.entity.EmpAttendanceBalanceData;
import com.stpl.tech.attendance.exception.BusinessException;

import java.math.BigDecimal;

/**
 * Service for managing employee leave balance operations
 */
public interface LeaveBalanceService {

    /**
     * Get leave balance data for an employee
     * @param empId Employee ID
     * @return Leave balance data or null if not found
     */
    EmpAttendanceBalanceData getAttendanceBalanceBalance(Integer empId);

    /**
     * Validate if employee has sufficient leave balance for the requested leave type
     * @param empId Employee ID
     * @param leaveType Type of leave (LEAVE, COMP_OFF, LWP)
     * @param requestedCount Number of days requested
     * @throws BusinessException if insufficient balance
     */
    void validateLeaveBalance(Integer empId, String leaveType, BigDecimal requestedCount);

    /**
     * Get available leave count for a specific leave type
     * @param empId Employee ID
     * @param leaveType Type of leave (LEAVE, COMP_OFF, LWP)
     * @return Available count for the leave type
     */
    BigDecimal getAvailableLeaveCount(Integer empId, String leaveType);

    /**
     * Create or update leave balance data for an employee
     *
     * @param empId     Employee ID
     * @param leaveType Type of leave to update
     * @param count     New count value
     */
    void updateLeaveBalance(Integer empId, String leaveType, BigDecimal count);

} 