package com.stpl.tech.attendance.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.util.List;

@Configuration
public class RedisSearchConfig {
    
    @Value("${spring.redis.host:localhost}")
    private String redisHost;
    
    @Value("${spring.redis.port:6379}")
    private int redisPort;
    
    @Bean
    public DefaultRedisScript<List> searchScript() {
        DefaultRedisScript<List> script = new DefaultRedisScript<>();
        script.setScriptText(
            "local pattern = ARGV[1] " +
            "local keys = redis.call('KEYS', pattern) " +
            "local results = {} " +
            "for i, key in ipairs(keys) do " +
            "    local value = redis.call('GET', key) " +
            "    if value then " +
            "        table.insert(results, value) " +
            "    end " +
            "end " +
            "return results"
        );
        script.setResultType(List.class);
        return script;
    }
    
    @Bean
    public RedisTemplate<String, Object> searchRedisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);
        template.setKeySerializer(new StringRedisSerializer());
        template.setValueSerializer(new StringRedisSerializer());
        template.setHashKeySerializer(new StringRedisSerializer());
        template.setHashValueSerializer(new StringRedisSerializer());
        template.afterPropertiesSet();
        return template;
    }
} 