package com.stpl.tech.attendance.cache.service;

import com.stpl.tech.attendance.dto.HolidayResponse;
import com.stpl.tech.attendance.entity.EmpHoliday;
import com.stpl.tech.attendance.repository.EmpHolidayRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Cache service for holiday management operations
 * Provides lazy loading and caching for frequently accessed holiday data
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class HolidayCacheService {
    
    private final EmpHolidayRepository holidayRepository;
    
    private static final String CACHE_NAME = "holidays";
    
    /**
     * Get cached holidays for a specific financial year
     * @param financialYear Financial year (e.g., "2024-25")
     * @return Cached holidays or null if not in cache
     */
    //@Cacheable(value = CACHE_NAME, key = "'financial_year_' + #financialYear")
    public List<HolidayResponse> getCachedHolidaysByFinancialYear(String financialYear) {
        log.debug("Cache miss for financial year holidays: {}", financialYear);
        return null; // This will trigger the actual data fetch
    }
    
    /**
     * Cache holidays for a specific financial year
     * @param financialYear Financial year
     * @param holidays List of holidays to cache
     * @return Cached holidays
     */
    //@CachePut(value = CACHE_NAME, key = "'financial_year_' + #financialYear")
    public List<HolidayResponse> cacheHolidaysByFinancialYear(String financialYear, List<HolidayResponse> holidays) {
        log.debug("Caching {} holidays for financial year: {}", holidays.size(), financialYear);
        return holidays;
    }
    
    /**
     * Get cached holidays for a specific date range
     * @param startDate Start date
     * @param endDate End date
     * @return Cached holidays or null if not in cache
     */
    //@Cacheable(value = CACHE_NAME, key = "'date_range_' + #startDate + '_' + #endDate")
    public List<HolidayResponse> getCachedHolidaysByDateRange(LocalDate startDate, LocalDate endDate) {
        log.debug("Cache miss for date range holidays: {} to {}", startDate, endDate);
        return null;
    }
    
    /**
     * Cache holidays for a specific date range
     * @param startDate Start date
     * @param endDate End date
     * @param holidays List of holidays to cache
     * @return Cached holidays
     */
   // @CachePut(value = CACHE_NAME, key = "'date_range_' + #startDate + '_' + #endDate")
    public List<HolidayResponse> cacheHolidaysByDateRange(LocalDate startDate, LocalDate endDate, List<HolidayResponse> holidays) {
        log.debug("Caching {} holidays for date range: {} to {}", holidays.size(), startDate, endDate);
        return holidays;
    }
    
    /**
     * Get cached holiday for a specific date
     * @param date Date to check
     * @return Cached holiday or null if not in cache
     */
   // @Cacheable(value = CACHE_NAME, key = "'date_' + #date")
    public HolidayResponse getCachedHolidayByDate(LocalDate date) {
        log.debug("Cache miss for holiday date: {}", date);
        return null;
    }
    
    /**
     * Cache holiday for a specific date
     * @param date Date
     * @param holiday Holiday to cache
     * @return Cached holiday
     */
    //@CachePut(value = CACHE_NAME, key = "'date_' + #date")
    public HolidayResponse cacheHolidayByDate(LocalDate date, HolidayResponse holiday) {
        log.debug("Caching holiday for date: {} - {}", date, holiday != null ? holiday.getHolidayName() : "null");
        return holiday;
    }
    
    /**
     * Get cached holidays by name pattern
     * @param namePattern Holiday name pattern
     * @return Cached holidays or null if not in cache
     */
    //@Cacheable(value = CACHE_NAME, key = "'search_' + #namePattern")
    public List<HolidayResponse> getCachedHolidaysByNamePattern(String namePattern) {
        log.debug("Cache miss for holiday name pattern: {}", namePattern);
        return null;
    }
    
    /**
     * Cache holidays by name pattern
     * @param namePattern Holiday name pattern
     * @param holidays List of holidays to cache
     * @return Cached holidays
     */
    @CachePut(value = CACHE_NAME, key = "'search_' + #namePattern")
    public List<HolidayResponse> cacheHolidaysByNamePattern(String namePattern, List<HolidayResponse> holidays) {
        log.debug("Caching {} holidays for name pattern: {}", holidays.size(), namePattern);
        return holidays;
    }
    
    /**
     * Get cached financial years
     * @return Cached financial years or null if not in cache
     */
    //@Cacheable(value = CACHE_NAME, key = "'financial_years'")
    public List<String> getCachedFinancialYears() {
        log.debug("Cache miss for financial years");
        return null;
    }
    
    /**
     * Cache financial years
     * @param financialYears List of financial years to cache
     * @return Cached financial years
     */
    @CachePut(value = CACHE_NAME, key = "'financial_years'")
    public List<String> cacheFinancialYears(List<String> financialYears) {
        log.debug("Caching {} financial years", financialYears.size());
        return financialYears;
    }
    
    /**
     * Get cached holiday existence for a specific date
     * @param date Date to check
     * @return Cached boolean or null if not in cache
     */
    //@Cacheable(value = CACHE_NAME, key = "'exists_' + #date")
    public Boolean getCachedHolidayExists(LocalDate date) {
        log.debug("Cache miss for holiday existence: {}", date);
        return null;
    }
    
    /**
     * Cache holiday existence for a specific date
     * @param date Date
     * @param exists Whether holiday exists
     * @return Cached boolean
     */
    @CachePut(value = CACHE_NAME, key = "'exists_' + #date")
    public Boolean cacheHolidayExists(LocalDate date, Boolean exists) {
        log.debug("Caching holiday existence for date: {} - {}", date, exists);
        return exists;
    }
    
    /**
     * Evict cache for a specific financial year
     * @param financialYear Financial year
     */
    @CacheEvict(value = CACHE_NAME, key = "'financial_year_' + #financialYear")
    public void evictFinancialYearCache(String financialYear) {
        log.debug("Evicting cache for financial year: {}", financialYear);
    }
    
    /**
     * Evict cache for a specific date
     * @param date Date
     */
    @CacheEvict(value = CACHE_NAME, key = "'date_' + #date")
    public void evictDateCache(LocalDate date) {
        log.debug("Evicting cache for date: {}", date);
    }
    
    /**
     * Evict cache for a specific date range
     * @param startDate Start date
     * @param endDate End date
     */
    @CacheEvict(value = CACHE_NAME, key = "'date_range_' + #startDate + '_' + #endDate")
    public void evictDateRangeCache(LocalDate startDate, LocalDate endDate) {
        log.debug("Evicting cache for date range: {} to {}", startDate, endDate);
    }
    
    /**
     * Evict all holiday cache entries
     */
    @CacheEvict(value = CACHE_NAME, allEntries = true)
    public void evictAllCache() {
        log.info("Evicting all holiday cache entries");
    }
    
    /**
     * Evict cache for a specific holiday (affects multiple cache keys)
     * @param holiday Holiday entity
     */
    public void evictHolidayCache(EmpHoliday holiday) {
        log.debug("Evicting cache for holiday: {} on date: {}", holiday.getHolidayName(), holiday.getHolidayDate());
        
        // Evict date-specific cache
        evictDateCache(holiday.getHolidayDate());
        
        // Evict financial year cache
        evictFinancialYearCache(holiday.getFinancialYear());
        
        // Evict date range cache that might include this date
        // Note: This is a simplified approach. In a more sophisticated system,
        // you might want to track which date ranges are cached and evict them specifically
        
        // Evict search cache (since name might have changed)
        // This is a broad eviction, but necessary for data consistency
        evictAllCache();
    }
    
    /**
     * Refresh cache for a specific financial year
     * @param financialYear Financial year
     */
    public void refreshFinancialYearCache(String financialYear) {
        log.info("Refreshing cache for financial year: {}", financialYear);
        
        // Evict existing cache
        evictFinancialYearCache(financialYear);
        
        // Fetch fresh data and cache it
        List<EmpHoliday> holidays = holidayRepository.findByFinancialYearOrderByHolidayDateAsc(financialYear);
        List<HolidayResponse> holidayResponses = holidays.stream()
                .map(this::convertToHolidayResponse)
                .collect(Collectors.toList());
        
        cacheHolidaysByFinancialYear(financialYear, holidayResponses);
        
        log.info("Cache refreshed for financial year: {} with {} holidays", financialYear, holidayResponses.size());
    }
    
    /**
     * Convert EmpHoliday entity to HolidayResponse DTO
     * @param holiday Holiday entity
     * @return HolidayResponse DTO
     */
    private HolidayResponse convertToHolidayResponse(EmpHoliday holiday) {
        return HolidayResponse.builder()
                .holidayId(holiday.getId())
                .financialYear(holiday.getFinancialYear())
                .holidayName(holiday.getHolidayName())
                .holidayDate(holiday.getHolidayDate())
                .isFullDay(holiday.getIsFullDay())
                .holidayTypeDisplay(holiday.getHolidayTypeDisplay())
                .build();
    }
}
