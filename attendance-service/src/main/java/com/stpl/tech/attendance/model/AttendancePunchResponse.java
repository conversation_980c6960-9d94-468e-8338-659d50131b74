package com.stpl.tech.attendance.model;

import com.stpl.tech.attendance.enums.PunchType;
import lombok.Data;
import java.time.LocalDateTime;

@Data
public class AttendancePunchResponse {
    private boolean success;
    private String message;
    private String attendanceRequestId;
    private PunchType punchType;
    private LocalDateTime punchTime;
    private String employeeCode;
    private String employeeName;
    private String createdBy;
    private String systemGeneratedEntries; // Details of any system-generated entries
    private String nextAction; // SUGGESTED_MIDDAY, CHECKOUT, etc.
    private String imageUrl;
    private String registeredImageUrl;
} 