package com.stpl.tech.attendance.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShiftInstanceRecreationRequestDTO {
    private List<Integer> empIds;  // Optional: specific employee IDs, if null then all cafe employees
    private LocalDate startDate;   // Required: start date for recreation
    private LocalDate endDate;     // Required: end date for recreation
    private Boolean forceRecreate; // Optional: if true, delete existing instances before creating new ones
    private String createdBy;      // Required: who is creating these instances
} 