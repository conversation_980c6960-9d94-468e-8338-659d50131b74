package com.stpl.tech.attendance.repository;

import com.stpl.tech.attendance.entity.EmpHoliday;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * Repository interface for EmpHoliday entity
 */
@Repository
public interface EmpHolidayRepository extends JpaRepository<EmpHoliday, Long> {
    
    /**
     * Find all holidays for a specific financial year
     * @param financialYear Financial year (e.g., "2024-25")
     * @return List of holidays for the financial year
     */
    List<EmpHoliday> findByFinancialYearOrderByHolidayDateAsc(String financialYear);
    
    /**
     * Find holidays for a specific financial year and date range
     * @param financialYear Financial year
     * @param startDate Start date of range
     * @param endDate End date of range
     * @return List of holidays in the date range
     */
    @Query("SELECT h FROM EmpHoliday h WHERE h.financialYear = :financialYear " +
           "AND h.holidayDate BETWEEN :startDate AND :endDate " +
           "ORDER BY h.holidayDate ASC")
    List<EmpHoliday> findByFinancialYearAndDateRange(
        @Param("financialYear") String financialYear,
        @Param("startDate") LocalDate startDate,
        @Param("endDate") LocalDate endDate
    );
    
    /**
     * Find holidays for a specific date
     * @param holidayDate Date to search for
     * @return List of holidays on the specified date
     */
    List<EmpHoliday> findByHolidayDate(LocalDate holidayDate);
    
    /**
     * Find holidays for a specific date range
     * @param startDate Start date
     * @param endDate End date
     * @return List of holidays in the date range
     */
    @Query("SELECT h FROM EmpHoliday h WHERE h.holidayDate BETWEEN :startDate AND :endDate " +
           "ORDER BY h.holidayDate ASC")
    List<EmpHoliday> findByDateRange(
        @Param("startDate") LocalDate startDate,
        @Param("endDate") LocalDate endDate
    );
    
    /**
     * Check if a specific date is a holiday
     * @param holidayDate Date to check
     * @return true if the date is a holiday
     */
    boolean existsByHolidayDate(LocalDate holidayDate);
    
    /**
     * Get all available financial years
     * @return List of distinct financial years
     */
    @Query("SELECT DISTINCT h.financialYear FROM EmpHoliday h ORDER BY h.financialYear DESC")
    List<String> findAllFinancialYears();
    
    /**
     * Find holidays by name pattern
     * @param holidayNamePattern Holiday name pattern (can use % for wildcard)
     * @return List of holidays matching the pattern
     */
    @Query("SELECT h FROM EmpHoliday h WHERE h.holidayName LIKE %:holidayNamePattern% " +
           "ORDER BY h.holidayDate DESC")
    List<EmpHoliday> findByHolidayNamePattern(@Param("holidayNamePattern") String holidayNamePattern);
}
