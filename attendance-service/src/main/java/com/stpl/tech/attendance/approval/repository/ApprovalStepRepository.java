package com.stpl.tech.attendance.approval.repository;

import com.stpl.tech.attendance.approval.entity.ApprovalStep;
import com.stpl.tech.attendance.enums.ApprovalStepStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ApprovalStepRepository extends JpaRepository<ApprovalStep, Long> {
    
    @Query("SELECT as FROM ApprovalStep as WHERE as.approvalRequest.id = :requestId ORDER BY as.stepNumber")
    List<ApprovalStep> findByRequestIdOrderByStepNumber(@Param("requestId") Long requestId);
    
//    @Query("SELECT as FROM ApprovalStep as WHERE as.approverId = :approverId AND as.status = :status")
//    List<ApprovalStep> findByApproverIdAndStatus(@Param("approverId") Long approverId, @Param("status") ApprovalStepStatus status);
//
    @Query("SELECT as FROM ApprovalStep as WHERE as.approvalRequest.id = :requestId AND as.stepNumber = :stepNumber")
    Optional<ApprovalStep> findByRequestIdAndStepNumber(@Param("requestId") Long requestId, @Param("stepNumber") Integer stepNumber);

//    @Query("SELECT as FROM ApprovalStep as WHERE as.approvalRequest.id = :requestId AND as.stepNumber = :stepNumber AND as.approverId = :approverId")
//    Optional<ApprovalStep> findByRequestIdAndStepNumberAndApproverId(@Param("requestId") Long requestId, @Param("stepNumber") Integer stepNumber,
//                                                                     @Param("approverId") Long approverId);

    List<ApprovalStep> findByApprovalRequestId(Long requestId);
} 