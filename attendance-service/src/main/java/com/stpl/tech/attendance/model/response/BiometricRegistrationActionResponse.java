package com.stpl.tech.attendance.model.response;

import lombok.Builder;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@Builder
public class BiometricRegistrationActionResponse {
    private String empId;
    private String empCode;
    private String empName;
    private String action; // "REGISTER" or "DEREGISTER"
    private String status;
    private String message;
    private LocalDateTime actionTime;
    private String actionBy;
    private String requestId; // For tracking the registration/deregistration request
    private String imageUrl;
    private String registrationImageUrl;
    
    /**
     * ID of the biometric registration record in the database
     * Used for linking additional images and other related data
     */
    private Long biometricRegistrationId;
} 