package com.stpl.tech.attendance.model.response;

import com.stpl.tech.attendance.approval.entity.ApprovalRequest;
import lombok.Builder;
import lombok.Data;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Set;

@Data
@Builder
public class ApprovalListResponse {
    private List<ApprovalRequestResponse> approvals;
    private Set<String> uniqueStatuses;
    private Set<String> uniqueRequestTypes;
    private Set<String> uniqueDesignations;
    private Page<?> page;
} 