package com.stpl.tech.attendance.approval.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ApprovalStep {
    private Long id;
    private Long requestId;
    private Integer stepNumber;
    private String stepName;
    private String approverId;
    private String status;
    private String comments;
    private LocalDateTime processedDate;
    private String processedBy;
    private LocalDateTime createdDate;
    private LocalDateTime updatedDate;
    private String createdBy;
    private String updatedBy;
} 