package com.stpl.tech.attendance.controller;

import com.stpl.tech.attendance.cache.service.EmployeeShiftDataCacheService;
import com.stpl.tech.attendance.cache.service.UnitCacheService;
import com.stpl.tech.attendance.constants.ApiConstants;
import com.stpl.tech.attendance.context.JwtContext;
import com.stpl.tech.attendance.dto.RosteringDto.RosterLiveDashboardResponseDTO;
import com.stpl.tech.attendance.dto.RosteringDto.CafeShiftDataDTO;
import com.stpl.tech.attendance.dto.RosteringDto.EmpShiftUpdateRequestDTO;
import com.stpl.tech.attendance.dto.RosteringDto.EmpShiftUpdateResponseDTO;
import com.stpl.tech.attendance.dto.RosteringDto.EmployeeShiftDataResponseDTO;
import com.stpl.tech.attendance.dto.RosteringDto.GenericFilterRequestDTO;
import com.stpl.tech.attendance.dto.RosteringDto.HierarchyEmployeesResponseDTO;
import com.stpl.tech.attendance.dto.RosteringDto.RosteringMetadataResponseDTO;
import com.stpl.tech.attendance.dto.RosteringDto.ShiftCafeMappingRequestDTO;
import com.stpl.tech.attendance.dto.RosteringDto.ShiftCafeMappingResponseDTO;
import com.stpl.tech.attendance.dto.RosteringDto.ShiftEmployeesResponseDTO;
import com.stpl.tech.attendance.dto.RosteringDto.ShiftEmployeesWithFiltersRequestDTO;
import com.stpl.tech.attendance.dto.RosteringDto.ShiftRequestDTO;
import com.stpl.tech.attendance.dto.RosteringDto.ShiftResponseDTO;
import com.stpl.tech.attendance.model.response.ApiResponse;
import com.stpl.tech.attendance.service.MetadataService;
import com.stpl.tech.attendance.service.RosteringService.ReladomoService;
import com.stpl.tech.attendance.service.RosteringService.RosteringService;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;

// Reladomo classes are now available and can be used in services

@RestController
@RequestMapping(ApiConstants.Paths.ROSTER)
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Rostering", description = "Rostering management APIs")
public class RosteringController extends BaseController{

    private final RosteringService rosteringService;
    private final ReladomoService reladomoEmpShiftService;
    private final MetadataService metadataService;
    private final UnitCacheService unitCacheService;
    private final EmployeeShiftDataCacheService employeeShiftDataCacheService;

    @GetMapping("/get-rostering-metadata")
    @Operation(summary = "Get rostering metadata", description = "Get metadata flags for rostering features")
    public ResponseEntity<ApiResponse<RosteringMetadataResponseDTO>> getRosteringMetadata() {
        Integer employeeId = JwtContext.getInstance().getUserId();
        Integer unitId = JwtContext.getInstance().getUnitId();
        log.info("Getting rostering metadata for employeeId: {}, unitId: {}", employeeId, unitId);
        RosteringMetadataResponseDTO metadata = rosteringService.getRosteringMetadata(employeeId, unitId);
        return success(metadata);
    }


    @PostMapping("/dashboard/roster-live-dashboard")
    @Operation(summary = "Get cafe live dashboard with filters", description = "Get live dashboard data with generic filters")
    public ResponseEntity<ApiResponse<RosterLiveDashboardResponseDTO>> getCafeLiveDashboardWithFilters(
            @Parameter(description = "Generic filter request", required = false)
            @RequestBody(required = false) GenericFilterRequestDTO filterRequest) {
        Integer employeeId = JwtContext.getInstance().getUserId();
        log.info("Getting cafe live dashboard with filters for Employee : {}, filters: {}", employeeId, filterRequest);
        RosterLiveDashboardResponseDTO dashboard = rosteringService.getRosterLiveDashboardWithFilters(employeeId, filterRequest);
        return success(dashboard);
    }

    @PostMapping("/shifts/shift-employees")
    @Operation(summary = "Get shift employees with filters", description = "Get employees assigned to shifts with generic filters")
    public ResponseEntity<ApiResponse<ShiftEmployeesResponseDTO>> getShiftEmployeesWithFilters(
            @Valid @RequestBody ShiftEmployeesWithFiltersRequestDTO request) {
        log.info("Getting shift employees with filters for date: {}, filters: {}"
                , request.getDate(), request.getFilters());
        ShiftEmployeesResponseDTO response = rosteringService.getShiftEmployeesForUser(request.getDate(),
                JwtContext.getInstance().getUserId(), request.getFilters());
        return success(response);
    }

    @GetMapping("/shifts/emp-shift-data/{empId}")
    @Operation(summary = "Get employee shift data", description = "Get shift assignments for a specific employee")
    public ResponseEntity<ApiResponse<EmployeeShiftDataResponseDTO>> getEmpUpcomingShiftData(
            @Parameter(description = "Employee ID", required = true)
            @PathVariable Integer empId,
            @Parameter(description = "Start time filter (optional)")
            @RequestParam(required = false)
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "End time filter (optional)")
            @RequestParam(required = false)
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        log.info("Getting employee shift data for empId: {}, startDate: {}, endDate: {}", empId, startDate, endDate);
        EmployeeShiftDataResponseDTO response = rosteringService.getEmpUpcomingShiftData(empId,  startDate,  endDate);
        return success(response);
    }

    @PostMapping("/shifts/emp-shift-update")
    @Operation(summary = "Update employee shifts", description = "Update employee shift assignments")
    public ResponseEntity<ApiResponse<EmpShiftUpdateResponseDTO>> updateEmpShift(
            @Parameter(description = "Employee shift update request", required = true)
            @Valid @RequestBody EmpShiftUpdateRequestDTO request) {
        log.info("Updating employee shift mapping with Reladomo: empId={}, shiftId={}, unitId={}",
                request.getEmpId(), request.getShiftId(), request.getUnitId());

        request.setUpdatedBy(String.valueOf(JwtContext.getInstance().getUserId()));
        EmpShiftUpdateResponseDTO result = new EmpShiftUpdateResponseDTO();
        if((Boolean.FALSE.equals(request.getUpdateUpcomingShifts()))){
            rosteringService.createEmpShiftMappingOverriding(request);
        }else{
            result = reladomoEmpShiftService.updateEmpShifts(request);
        }
        return success(result);
    }

    @GetMapping("/shifts/cafe-shift-data/{unitId}")
    @Operation(summary = "Get cafe shift data", description = "Get shift data for cafes/units")
    public ResponseEntity<ApiResponse<CafeShiftDataDTO>> getCafeShiftData(
            @Parameter(description = "Unit ID", required = true)
            @PathVariable Integer unitId ) {
        Integer employeeId = JwtContext.getInstance().getUserId();
        log.info("Getting cafe shift data for employeeId: {} and unitId: {} ", employeeId, unitId);
        CafeShiftDataDTO shiftData = rosteringService.getCafeShiftData(employeeId, unitId);
        return success(shiftData);
    }

    @PostMapping("/employees/get-hierarchy-employees")
    @Operation(summary = "Get hierarchy employees with filters", description = "Get employee hierarchy data with generic filters")
    public ResponseEntity<ApiResponse<HierarchyEmployeesResponseDTO>> getHierarchyEmployeesWithFilters(
            @Parameter(description = "Generic filter request", required = false)
            @RequestBody(required = false) GenericFilterRequestDTO filterRequest,
            @Parameter(description = "Search term (optional)")
            @RequestParam(required = false) String searchTerm,
            @Parameter(description = "Page number (default: 0)")
            @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size (default: 10)")
            @RequestParam(defaultValue = "10") int size) {
        Integer employeeId = JwtContext.getInstance().getUserId();
        log.info("Getting hierarchy employees with filters for employeeId: {}, searchTerm: {}, page: {}, size: {}, filters: {}", 
                employeeId, searchTerm, page, size, filterRequest);

        HierarchyEmployeesResponseDTO response = rosteringService.getHierarchyEmployeesWithFilters(employeeId, filterRequest, searchTerm, page, size);
        return success(response);
    }


    @PostMapping("/shifts/create")
    @Operation(summary = "Create shift", description = "Create a new shift")
    public ResponseEntity<ApiResponse<ShiftResponseDTO>> createShift(
            @Parameter(description = "Shift data", required = true)
            @Valid @RequestBody ShiftRequestDTO shiftRequestDTO) {
        Integer employeeId = JwtContext.getInstance().getUserId();
        log.info("Creating new shift: {}", shiftRequestDTO.getShiftName());
        ShiftResponseDTO created = rosteringService.createShift(shiftRequestDTO, String.valueOf(employeeId));
        return success(created);
    }

    @PostMapping("/shifts/update")
    @Operation(summary = "Update shift", description = "Update an existing shift")
    public ResponseEntity<ApiResponse<ShiftResponseDTO>> updateShift(
            @Parameter(description = "Updated shift data", required = true)
            @Valid @RequestBody ShiftRequestDTO shiftRequestDTO) {
        Integer employeeId = JwtContext.getInstance().getUserId();
        log.info("Updating shift with ID: {}", shiftRequestDTO.getShiftId());
        ShiftResponseDTO updated = rosteringService.updateShift(shiftRequestDTO, String.valueOf(employeeId));
        return success(updated);
    }

    @GetMapping("/shifts")
    @Operation(summary = "Get all shifts")
    public ResponseEntity<ApiResponse<List<ShiftResponseDTO>>> getAllShifts() {
        log.info("Getting all shifts which are active ");
        List<ShiftResponseDTO> shifts = rosteringService.getAllShifts();
        shifts.sort(Comparator.comparing(ShiftResponseDTO::getCreationTime).reversed());
        return success(shifts);
    }

    // Shift-Cafe mapping endpoints

    @PostMapping("/shifts/cafe-shift-mapping/create")
    @Operation(summary = "Create shift-cafe mapping", description = "Map a shift to a cafe/unit")
    public ResponseEntity<ApiResponse<ShiftCafeMappingResponseDTO>> createShiftCafeMapping(
            @Parameter(description = "Shift cafe mapping request", required = true)
            @Valid @RequestBody ShiftCafeMappingRequestDTO request) {
        log.info("Creating shift cafe mapping for shiftId: {}, unitId: {}", request.getShiftId(), request.getUnitId());
        ShiftCafeMappingResponseDTO mapping = rosteringService.createShiftCafeMapping(request);
        return success(mapping);
    }

    @PostMapping("/shifts/cafe-shift-mapping/update")
    @Operation(summary = "Update shift-cafe mapping", description = "Update an existing shift-cafe mapping")
    public ResponseEntity<ApiResponse<ShiftCafeMappingResponseDTO>> updateShiftCafeMapping(
            @Parameter(description = "Shift cafe mapping request", required = true)
            @Valid @RequestBody ShiftCafeMappingRequestDTO request) {
        Integer employeeId = JwtContext.getInstance().getUserId();
        log.info("Updating shift cafe mapping for shiftId: {}, unitId: {}", request.getShiftId(), request.getUnitId());
        ShiftCafeMappingResponseDTO mapping = rosteringService.updateShiftCafeMapping(request);
        return success(mapping);
    }

    @GetMapping("/all-units")
    @Operation(summary = "Get all units", description = "Get all units managed by the current user")
    public ResponseEntity<ApiResponse<List<UnitBasicDetail>>> getAllUnits() {
        Integer userId = JwtContext.getInstance().getUserId();
        log.info("Getting managed units for userId: {}", userId);
        
        List<UnitBasicDetail> units = rosteringService.getManagedUnits(userId);
        return success(units);
    }

    // Cache management APIs

    @PostMapping("/cache/refresh/employee/{empId}")
    @Operation(summary = "Refresh cache for specific employee", description = "Refresh employee shift data cache for a specific employee")
    public ResponseEntity<ApiResponse<String>> refreshEmployeeCache(
            @Parameter(description = "Employee ID", required = true)
            @PathVariable Integer empId) {
        log.info("Refreshing cache for employee: {}", empId);
        
        try {
            employeeShiftDataCacheService.evictAllEmployeeShiftData(empId);
            return success("Cache refreshed successfully for employee " + empId);
        } catch (Exception e) {
            log.error("Error refreshing cache for employee: {}", empId, e);
            return ResponseEntity.badRequest().build();
        }
    }

    @PostMapping("/cache/refresh/all")
    @Operation(summary = "Refresh all cache", description = "Refresh all employee shift data cache")
    public ResponseEntity<ApiResponse<String>> refreshAllCache() {
        log.info("Refreshing all employee shift data cache");
        
        try {
            employeeShiftDataCacheService.evictAllCache();
            return success("All cache refreshed successfully");
        } catch (Exception e) {
            log.error("Error refreshing all cache", e);
            return ResponseEntity.badRequest().build();
        }
    }

}
