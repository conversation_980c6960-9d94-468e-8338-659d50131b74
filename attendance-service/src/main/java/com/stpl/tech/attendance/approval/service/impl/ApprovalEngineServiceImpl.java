package com.stpl.tech.attendance.approval.service.impl;

import com.stpl.tech.attendance.approval.dto.ApprovalDecision;
import com.stpl.tech.attendance.approval.entity.ApprovalRequest;
import com.stpl.tech.attendance.approval.entity.ApprovalStep;
import com.stpl.tech.attendance.approval.entity.SubApprovalStep;
import com.stpl.tech.attendance.approval.repository.ApprovalRequestRepository;
import com.stpl.tech.attendance.approval.repository.ApprovalStepRepository;
import com.stpl.tech.attendance.approval.repository.SubApprovalStepRepository;
import com.stpl.tech.attendance.approval.service.ApprovalEngineService;
import com.stpl.tech.attendance.cache.service.UserCacheService;
import com.stpl.tech.attendance.context.JwtContext;
import com.stpl.tech.attendance.enums.ApprovalErrorCode;
import com.stpl.tech.attendance.enums.ApprovalStatus;
import com.stpl.tech.attendance.enums.ApprovalStepStatus;
import com.stpl.tech.attendance.enums.ApprovalStepType;
import com.stpl.tech.attendance.exception.ApprovalException;
import com.stpl.tech.attendance.model.request.ApprovalRequestFilter;
import com.stpl.tech.attendance.notification.service.NotificationService;
import com.stpl.tech.attendance.util.DateTimeUtil;
import com.stpl.tech.master.domain.model.Designation;
import com.stpl.tech.util.AppConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.UserTask;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ApprovalEngineServiceImpl implements ApprovalEngineService {

    private final ApprovalRequestRepository approvalRequestRepository;
    private final ApprovalStepRepository approvalStepRepository;
    private final SubApprovalStepRepository subApprovalStepRepository;
    private final NotificationService notificationService;
    private final RuntimeService runtimeService;
    private final TaskService taskService;;
    private final RepositoryService repositoryService;
    private final UserCacheService userCacheService;

    public int getUserTaskCount(String processInstanceId) {
        String processDefinitionId = getProcessDefinitionId(processInstanceId);
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionId);
        int userTaskCount = 0;

        for (Process process : bpmnModel.getProcesses()) {
            for (FlowElement flowElement : process.getFlowElements()) {
                if (flowElement instanceof UserTask) {
                    userTaskCount++;
                }
            }
        }

        return userTaskCount;
    }

    public String getProcessDefinitionId(String processInstanceId) {
        ProcessInstance processInstance = runtimeService
                .createProcessInstanceQuery()
                .processInstanceId(processInstanceId)
                .singleResult();

        return processInstance.getProcessDefinitionId();
    }

    @Override
    @Transactional
    public ApprovalRequest createRequest(ApprovalRequest request) {
        log.info("Creating new approval request: {}", request);
        
        // Set initial values
        request.setStatus(ApprovalStatus.PENDING);
        request.setRequestDate(DateTimeUtil.now());
        request.setCreatedDate(DateTimeUtil.now());
        request.setUpdatedDate(DateTimeUtil.now());
        if(request.getCreatedBy()!=null){
            request.setCreatedBy(request.getCreatedBy());
        }else{
            request.setCreatedBy(JwtContext.getInstance().getUserId().toString());
        }
        request.setUpdatedBy(request.getRequesterId().toString());
        
        // Save the request first to get the ID
        ApprovalRequest savedRequest = approvalRequestRepository.save(request);
        
        // Start the workflow process
        Map<String, Object> variables = new HashMap<>();
        variables.put("requestId", savedRequest.getId().toString());
        variables.put("requesterId", savedRequest.getRequesterId().toString());
        variables.put("requestType", savedRequest.getRequestType().toString());
        variables.put("unitId", savedRequest.getUnitId().toString());
        variables.put("approvalRequestId", savedRequest.getId().toString());
        variables.put("createdBy", savedRequest.getCreatedBy().toString());
        variables.put("currentStep", "1");

        String processInstanceId = runtimeService.startProcessInstanceByKey(
            "attendance-approval",
            savedRequest.getId().toString(),
            variables
        ).getId();

        Integer totalStepsCount = getUserTaskCount(processInstanceId);
        savedRequest.setTotalSteps(totalStepsCount);
        savedRequest.setProcessInstanceId(processInstanceId);
        savedRequest = approvalRequestRepository.save(savedRequest);


        
        return savedRequest;
    }

    @Override
    @Transactional(rollbackFor = Exception.class , readOnly = false)
    public ApprovalRequest processStep(Long requestId, Long stepId, ApprovalDecision decision) {
        log.info("Processing approval step: {} for request: {} with decision: {}", stepId, requestId, decision);
        decision.setApproverId(JwtContext.getInstance().getUserId().toString());
        
        ApprovalRequest request = approvalRequestRepository.findById(requestId)
                .orElseThrow(() -> new RuntimeException("Approval request not found"));
        
        ApprovalStep step = approvalStepRepository.findById(stepId)
                .orElseThrow(() -> new RuntimeException("Approval step not found"));
        
        if (!step.getApprovalRequest().getId().equals(requestId)) {
            throw new RuntimeException("Step does not belong to the specified request");
        }

        // Find the sub-step for this approver
        SubApprovalStep subStep = subApprovalStepRepository.findByApprovalStepIdAndStatus(stepId, ApprovalStepStatus.PENDING)
                .stream()
                .filter(s -> Objects.equals(s.getApproverId(), Long.valueOf(decision.getApproverId())))
                .findFirst()
                .orElseThrow(() -> new ApprovalException(ApprovalErrorCode.INVALID_APPROVER,"No pending sub-step found for this approver"));

        // Complete the sub-task
        Task subTask = taskService.createTaskQuery()
                .taskId(subStep.getSubTaskId())
                .singleResult();

        if (subTask == null) {
            throw new RuntimeException("No active sub-task found for this step");
        }

        // Update sub-step status
        subStep.setStatus(ApprovalStepStatus.valueOf(decision.getDecision().name()));
        subStep.setActionDate(DateTimeUtil.now());
        subStep.setRemarks(decision.getComments());
        subStep.setUpdatedDate(DateTimeUtil.now());
        subStep.setUpdatedBy(decision.getApproverId());
        subApprovalStepRepository.save(subStep);

        // Complete the sub-task
        Map<String, Object> subTaskVariables = new HashMap<>();
        subTaskVariables.put("decision", decision.getDecision().toString());
        subTaskVariables.put("remarks", decision.getComments());
        subTaskVariables.put("approverId", decision.getApproverId());
        taskService.complete(subTask.getId(), subTaskVariables);

        // Update main step status
        step.setCurrentApprovals(step.getCurrentApprovals() + 1);
        step.setLastApproverId(Long.valueOf(decision.getApproverId()));
        step.setLastApprovalDate(DateTimeUtil.now());
        step.setUpdatedDate(DateTimeUtil.now());
        step.setUpdatedBy(decision.getApproverId().toString());

        // Check if main step should be completed
        boolean shouldCompleteMainStep = false;
        if (step.getStepType() == ApprovalStepType.PARALLEL) {
            shouldCompleteMainStep = step.getCurrentApprovals() >= step.getMinimumApprovals();
        } else if (step.getStepType() == ApprovalStepType.SEQUENTIAL) {
            shouldCompleteMainStep = step.getCurrentApprovals() >= step.getRequiredApprovals();
        }

        if (shouldCompleteMainStep) {
            // Complete main task
            Task mainTask = taskService.createTaskQuery()
                    .taskId(step.getTaskId())
                    .processInstanceId(request.getProcessInstanceId())
                    .singleResult();

            if (mainTask != null) {
                Map<String, Object> mainTaskVariables = new HashMap<>();
                mainTaskVariables.put("action", decision.getDecision().toString());
                mainTaskVariables.put("currentStep", step.getStepNumber());
                mainTaskVariables.put("totalSteps", request.getTotalSteps());
                mainTaskVariables.put("approverId", decision.getApproverId().toString());
                taskService.complete(mainTask.getId(), mainTaskVariables);
            }

            // Update status of remaining sub-steps
            List<SubApprovalStep> remainingSubSteps = subApprovalStepRepository.findByApprovalStepIdAndStatus(stepId, ApprovalStepStatus.PENDING);
            for (SubApprovalStep remainingSubStep : remainingSubSteps) {
                remainingSubStep.setStatus(ApprovalStepStatus.SKIPPED);
                remainingSubStep.setUpdatedDate(DateTimeUtil.now());
                remainingSubStep.setUpdatedBy(String.valueOf(AppConstants.SYSTEM_EMPLOYEE_ID));
                subApprovalStepRepository.save(remainingSubStep);
            }

            step.setStatus(ApprovalDecision.Decision.APPROVED.equals(decision.getDecision()) ? ApprovalStepStatus.APPROVED : ApprovalStepStatus.REJECTED);
        }

        approvalStepRepository.save(step);
        return getRequest(requestId);
    }

    @Override
    public ApprovalRequest getRequest(Long requestId) {
        return approvalRequestRepository.findById(requestId)
                .orElseThrow(() -> new RuntimeException("Approval request not found"));
    }

    @Override
    public List<ApprovalRequest> getPendingApprovals(String approverId, List<Long> requesterIds, ApprovalRequestFilter approvalRequestFilter) {
        Boolean isRequesterIdsNull = Objects.isNull(requesterIds);
        Boolean isTypesEmpty = Objects.isNull(approvalRequestFilter.getTypes()) || approvalRequestFilter.getTypes().isEmpty();
        Boolean isDesignationsEmpty = Objects.isNull(approvalRequestFilter.getDesignations()) || approvalRequestFilter.getDesignations().isEmpty();
        Boolean isStatusesEmpty = Objects.isNull(approvalRequestFilter.getStatuses()) || approvalRequestFilter.getStatuses().isEmpty();
        return subApprovalStepRepository.findByApproverIdAndStatusAndRequesterIds(
                Long.valueOf(approverId),
                ApprovalStepStatus.PENDING.name(),
                requesterIds,
                isRequesterIdsNull,
                approvalRequestFilter.getTypes(),
                isTypesEmpty,
                approvalRequestFilter.getDesignations(),
                isDesignationsEmpty,
                approvalRequestFilter.getStatuses(),
                isStatusesEmpty,
                approvalRequestFilter.getFromDate(),
                approvalRequestFilter.getToDate()
            ).stream()
            .map(subStep -> subStep.getApprovalStep().getApprovalRequest())
            .distinct()
            .toList();
    }

    @Override
    public Page<ApprovalRequest> getCompletedApprovals(String requesterId, Pageable pageable) {
        return approvalRequestRepository.findCompletedRequestsByRequester(requesterId, pageable);
    }

    @Override
    public Page<ApprovalRequest> getCompletedApprovalsByApprover(Long approverId, Pageable pageable, List<Long> requesterIds,
                                                                 ApprovalRequestFilter approvalRequestFilter) {
        Boolean requesterIdsIsNull = Objects.isNull(requesterIds);
        Boolean typesIsNull = Objects.isNull(approvalRequestFilter.getTypes()) || approvalRequestFilter.getTypes().isEmpty();
        Boolean designationsIsNull = Objects.isNull(approvalRequestFilter.getDesignations()) || approvalRequestFilter.getDesignations().isEmpty();
        Boolean statusesIsNull = Objects.isNull(approvalRequestFilter.getStatuses()) || approvalRequestFilter.getStatuses().isEmpty();
        return approvalRequestRepository.findByApproverIdAndStatusNotWithStepsAndRequesterIds(
            approverId,
            ApprovalStatus.PENDING.name(),
            requesterIds,
            requesterIdsIsNull,
            approvalRequestFilter.getTypes(),
            typesIsNull,
            approvalRequestFilter.getDesignations(),
            designationsIsNull,
            approvalRequestFilter.getStatuses(),
            statusesIsNull,
            approvalRequestFilter.getFromDate(),
            approvalRequestFilter.getToDate(),
            pageable
        );
    }

    @Override
    public ApprovalStep getCurrentStep(Long requestId) {
        return approvalStepRepository.findByRequestIdAndStepNumber(requestId, 1)
                .orElseThrow(() -> new RuntimeException("No approval steps found for request: " + requestId));
    }

    @Override
    public List<ApprovalStep> getSteps(Long requestId) {
        return approvalStepRepository.findByRequestIdOrderByStepNumber(requestId);
    }

    @Override
    @Transactional
    public ApprovalRequest cancelRequest(Long requestId, String reason) {
        log.info("Cancelling approval request: {} with reason: {}", requestId, reason);
        
        ApprovalRequest request = getRequest(requestId);
        request.setStatus(ApprovalStatus.CANCELLED);
        request.setUpdatedDate(DateTimeUtil.now());
        request.setUpdatedBy("SYSTEM");
        
        // Cancel all steps and sub-steps for this request
        try {
            cancelAllStepsForRequest(requestId);
        } catch (Exception e) {
            log.error("Failed to set steps to CANCELLED for request {}", requestId, e);
        }

        // Delete the process instance
        runtimeService.deleteProcessInstance(request.getProcessInstanceId(), reason);
        
        // Send notification
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("requestId", request.getId());
        metadata.put("requestType", request.getRequestType());
        metadata.put("reason", reason);
        
        /*notificationService.sendWorkflowNotification(
            request.getRequesterId().toString(),
            "Request Cancelled",
            "Your request has been cancelled: " + reason,
            request.getId().toString(),
            metadata
        );*/
        
        return approvalRequestRepository.save(request);
    }

    private void cancelAllStepsForRequest(Long requestId) {
        List<ApprovalStep> steps = approvalStepRepository.findByApprovalRequestId(requestId);
        if (steps == null || steps.isEmpty()) {
            log.warn("No approval steps found to cancel for request {}", requestId);
            return;
        }

        for (ApprovalStep step : steps) {
            // Cancel pending sub-steps
            List<SubApprovalStep> subSteps = subApprovalStepRepository.findByApprovalStepId(step.getId());
            if (subSteps != null && !subSteps.isEmpty()) {
                for (SubApprovalStep subStep : subSteps) {
                    if (subStep.getStatus() != ApprovalStepStatus.APPROVED && subStep.getStatus() != ApprovalStepStatus.REJECTED) {
                        subStep.setStatus(ApprovalStepStatus.CANCELLED);
                        subStep.setUpdatedDate(DateTimeUtil.now());
                        subStep.setUpdatedBy(String.valueOf(AppConstants.SYSTEM_EMPLOYEE_ID));
                        subApprovalStepRepository.save(subStep);
                    }
                }
            }

            // Cancel the main step if not terminal
            if (step.getStatus() != ApprovalStepStatus.APPROVED && step.getStatus() != ApprovalStepStatus.REJECTED) {
                step.setStatus(ApprovalStepStatus.CANCELLED);
                step.setUpdatedDate(DateTimeUtil.now());
                step.setUpdatedBy(String.valueOf(AppConstants.SYSTEM_EMPLOYEE_ID));
                approvalStepRepository.save(step);
            }
        }
        log.info("Set all steps and sub-steps to CANCELLED for request {}", requestId);
    }

    @Override
    public ApprovalStep getStep(Long stepId) {
        return approvalStepRepository.findById(stepId)
                .orElseThrow(() -> new RuntimeException("Approval step not found"));
    }

    @Override
    public List<ApprovalStep> getRequestSteps(Long requestId) {
        return approvalStepRepository.findByRequestIdOrderByStepNumber(requestId);
    }

    @Override
    public Set<String> getUniqueDesignations() {
        return userCacheService.getAllDesignations().values().stream()
                .map(Designation::getName).sorted()
                .collect(Collectors.toCollection(LinkedHashSet::new));
    }

}
