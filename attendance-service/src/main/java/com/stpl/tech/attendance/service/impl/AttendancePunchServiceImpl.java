package com.stpl.tech.attendance.service.impl;

import com.google.gson.Gson;
import com.stpl.tech.attendance.cache.service.UnitCacheService;
import com.stpl.tech.attendance.cache.service.UserCacheService;
import com.stpl.tech.attendance.config.AttendanceConfig;
import com.stpl.tech.attendance.config.EnvironmentProperties;
import com.stpl.tech.attendance.constants.AppConstants;
import com.stpl.tech.attendance.context.BiometricContext;
import com.stpl.tech.attendance.context.JwtContext;
import com.stpl.tech.attendance.dto.BiometricRegistrationDTO;
import com.stpl.tech.attendance.dto.BiometricUpdateImagePathRequestDTO;
import com.stpl.tech.attendance.dto.BiometricMultiImageIdentificationRequestDTO;
import com.stpl.tech.attendance.dto.BiometricMultiImageIdentificationResponseDTO;
import com.stpl.tech.attendance.entity.AttendanceRecord;
import com.stpl.tech.attendance.entity.AttendanceImage;
import com.stpl.tech.attendance.entity.AttendanceType;
import com.stpl.tech.attendance.entity.DailyAttendanceSummary;
import com.stpl.tech.attendance.entity.EmployeeShiftInstances;
import com.stpl.tech.attendance.entity.EmployeeAttendanceRequest;
import com.stpl.tech.attendance.entity.RosteringEntity.RosteringConstants;
import com.stpl.tech.attendance.enums.AttendanceErrorCode;
import com.stpl.tech.attendance.enums.AttendanceStatus;
import com.stpl.tech.attendance.enums.BiometricErrorCode;
import com.stpl.tech.attendance.enums.BiometricStatus;
import com.stpl.tech.attendance.enums.PunchType;
import com.stpl.tech.attendance.enums.SpecialCaseType;
import com.stpl.tech.attendance.exception.AttendanceException;
import com.stpl.tech.attendance.exception.AttendanceValidationException;
import com.stpl.tech.attendance.exception.BiometricRegistrationException;
import com.stpl.tech.attendance.interceptor.RequestIdInterceptor;
import com.stpl.tech.attendance.model.AttendancePunchRequest;
import com.stpl.tech.attendance.model.AttendancePunchResponse;
import com.stpl.tech.attendance.model.AttendanceResponse;
import com.stpl.tech.attendance.model.BulkAttendancePunchRequest;
import com.stpl.tech.attendance.model.BulkAttendancePunchResponse;
import com.stpl.tech.attendance.model.AttendanceImageRequest;
import com.stpl.tech.attendance.repository.AttendanceRecordRepository;
import com.stpl.tech.attendance.repository.AttendanceImageRepository;
import com.stpl.tech.attendance.repository.DailyAttendanceSummaryRepository;
import com.stpl.tech.attendance.repository.EmpAttendanceBalanceDataRepository;
import com.stpl.tech.attendance.repository.EmployeeShiftInstancesRepository;
import com.stpl.tech.attendance.repository.EmployeeAttendanceRequestRepository;
import com.stpl.tech.attendance.repository.EmpHolidayRepository;
import com.stpl.tech.attendance.service.AttendancePunchService;
import com.stpl.tech.attendance.service.BiometricRegistrationService;
import com.stpl.tech.attendance.service.BiometricService;
import com.stpl.tech.attendance.service.EmpEligibilityService;
import com.stpl.tech.attendance.service.EmpUnitShiftInstanceAnalyticsService;
import com.stpl.tech.attendance.service.ExternalAttendanceSyncService;
import com.stpl.tech.attendance.service.S3Service;
import com.stpl.tech.attendance.service.AttendanceMetadataService;
import com.stpl.tech.attendance.util.BiometricDeviceIdUtil;
import com.stpl.tech.attendance.util.DateTimeUtil;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.DayOfWeek;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.HashMap;
import java.util.Comparator;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class AttendancePunchServiceImpl implements AttendancePunchService {

    private final UserCacheService userCacheService;
    private final UnitCacheService unitCacheService;
    private final AttendanceRecordRepository attendanceRecordRepository;
    private final AttendanceImageRepository attendanceImageRepository;
    private final DailyAttendanceSummaryRepository summaryRepository;
    private final EmployeeShiftInstancesRepository employeeShiftInstancesRepository;
    private final EmpEligibilityService empEligibilityService;
    private final S3Service s3Service;
    private final BiometricRegistrationService biometricRegistrationService;
    private final BiometricService biometricService;
    private final EnvironmentProperties environmentProperties;
    private final AttendanceConfig attendanceConfig;
    private final ThreadPoolTaskExecutor asyncTaskExecutor;
    private final AsyncOperationService asyncOperationService;
    private final ExternalAttendanceSyncService externalAttendanceSyncService;
    private final BiometricDeviceIdUtil biometricDeviceIdUtil;
    private final EmpUnitShiftInstanceAnalyticsService empUnitShiftInstanceAnalyticsService;
    private final EmployeeAttendanceRequestRepository employeeAttendanceRequestRepository;
    private final AttendanceRequestUtil attendanceRequestUtil;


    @Value("${s3.bucket.name:product.image.dev}")
    private String bucketName;

    @Value("${aws.cloudfront.domain:d1nqp92n3q8zl7.cloudfront.net}")
    private String cloudfrontDomain;


    @Override
    @Transactional(rollbackFor = Exception.class , readOnly = false)
    public AttendancePunchResponse processAttendancePunch(AttendancePunchRequest request) {
        return processAttendancePunch(request, false);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = false)
    public AttendancePunchResponse processAttendancePunch(AttendancePunchRequest request, boolean useMultiImage) {
        long startTime = System.currentTimeMillis();
        log.info("Starting attendance punch processing for employee: {} (multiImage: {})", request.getEmployeeId(), useMultiImage);

        // Validate and prepare request
        long validationStart = System.currentTimeMillis();
        validateAndPrepareRequest(request,useMultiImage);
        log.info("Request validation completed in {} ms for employee: {}",
                System.currentTimeMillis() - validationStart, request.getEmployeeId());

        // Get and validate employee (branch for multi-image)
        long employeeStart = System.currentTimeMillis();
        EmployeeBasicDetail employee;
        employee = getAndValidateEmployee(request);

        log.info("Employee validation completed in {} ms for employee: {}",
                System.currentTimeMillis() - employeeStart, request.getEmployeeId());

        // Validate punch eligibility
        long eligibilityStart = System.currentTimeMillis();
        validatePunchEligibility(request, employee);
        log.info("Punch eligibility validation completed in {} ms for employee: {}",
                System.currentTimeMillis() - eligibilityStart, request.getEmployeeId());

        // Process the punch and get record
        long punchStart = System.currentTimeMillis();
        AttendanceRecord punchRecord = processPunchRecord(request);
        log.info("Punch record processing completed in {} ms for employee: {}",
                System.currentTimeMillis() - punchStart, request.getEmployeeId());

        // Upload processed image
        long imageStart = System.currentTimeMillis();
        String processedImageUrl = uploadProcessedImage(request, employee);
        punchRecord.setImageUrl(processedImageUrl);
        attendanceRecordRepository.save(punchRecord);
        log.info("Processed image upload completed in {} ms for employee: {}",
                System.currentTimeMillis() - imageStart, request.getEmployeeId());


        // Start async image processing (if needed)
        String  refId = BiometricContext.getInstance().getRequestId();
        CompletableFuture.runAsync(() -> {
            long asyncStart = System.currentTimeMillis();
            try {
                // Save additional images if present
                if (request.getAdditionalImages() != null && !request.getAdditionalImages().isEmpty()) {
                    long additionalImagesStart = System.currentTimeMillis();
                    processAndSaveImages(request, punchRecord,refId,employee);
                    log.info("Additional images processing completed in {} ms for employee: {}",
                            System.currentTimeMillis() - additionalImagesStart, request.getEmployeeId());
                }else{
                    log.info("Starting async image processing for employee: {}", request.getEmployeeId());
                    processImagesAsync(request, employee, punchRecord, refId
                    );
                }
                log.info("Async image processing completed in {} ms for employee: {}",
                        System.currentTimeMillis() - asyncStart, request.getEmployeeId());
            } catch (Exception e) {
                log.error("Error in async image processing for employee: {} after {} ms",
                        request.getEmployeeId(), System.currentTimeMillis() - asyncStart, e);
            }
        });

        log.info("Total attendance punch processing completed in {} ms for employee: {}",
                System.currentTimeMillis() - startTime, request.getEmployeeId());



        // Return response with punch details
        return createResponse(true,
                punchRecord.getPunchType() + " recorded successfully",
                request, employee, processedImageUrl,punchRecord.getId());
    }

    private void validateAndPrepareRequest(AttendancePunchRequest request ,  boolean useMultiImage) {
        try {
            Integer employeeId;
            if(request.getEmployeeId() != null){
                employeeId = request.getEmployeeId();
            }else if (useMultiImage || (request.getAdditionalImages() != null && !request.getAdditionalImages().isEmpty())) {
                employeeId = getAndValidateEmployeeWithMultiImages(request);
            }else{
                employeeId = getEmployeeId(request);
            }
            request.setEmployeeId(employeeId);
            
            // Set default values if not provided
            if (request.getPunchTime() == null) {
                request.setPunchTime(DateTimeUtil.now());
            }

        } catch (Exception e) {
            log.error("Error validating attendance punch request: {}", e.getMessage());
            // Save failed image to S3 if saveFailedImage flag is true
            if (request.getSaveFailedImage() && request.getBase64Image() != null) {
                String refId = BiometricContext.getInstance().getRequestId();
                CompletableFuture.runAsync(() -> {
                    handleBiometricFailureWithImages(request, e, refId);
                }, asyncTaskExecutor);
            }
            throw e;
        }
    }

    /**
     * Auto-detect and set offline mode based on various conditions
     * @param request The attendance punch request
     */
    private void detectAndSetOfflineMode(AttendancePunchRequest request) {
        // If special case type is already set, don't override
        if (request.getSpecialCaseType() != null) {
            return;
        }

        LocalDateTime currentTime = DateTimeUtil.now();
        LocalDateTime punchTime = request.getPunchTime();
        
        // Check if punch time is significantly delayed (more than 5 minutes)
        long timeDifferenceMinutes = Duration.between(punchTime, currentTime).toMinutes();
        
        if (timeDifferenceMinutes > 5) {
            // This appears to be an offline punch that was delayed
            request.setIsSpecialCase(true);
            request.setSpecialCaseType(SpecialCaseType.OFFLINE_MODE);
            
            // Update remarks to indicate it was detected as offline
            String existingRemarks = request.getRemarks() != null ? request.getRemarks() : "";
            request.setRemarks(existingRemarks + " (Auto-detected offline mode - " + timeDifferenceMinutes + " min delay)");
            
            log.info("Auto-detected offline mode for employee: {} with {} minutes delay", 
                    request.getEmployeeId(), timeDifferenceMinutes);
        }
        
        // Additional offline detection logic can be added here:
        // - Check for specific device IDs that are known to work offline
        // - Check for network connectivity indicators
        // - Check for bulk upload patterns
        // - Check for specific time windows when offline mode is common
    }

    private EmployeeBasicDetail getAndValidateEmployee(AttendancePunchRequest request) {
        EmployeeBasicDetail employee = userCacheService.getUserById(request.getEmployeeId());
        if (employee == null) {
            throw new AttendanceException(AttendanceErrorCode.EMPLOYEE_NOT_FOUND);
        }
        if (!employee.getStatus().name().equals(com.stpl.tech.util.AppConstants.ACTIVE)) {
            throw new AttendanceException(AttendanceErrorCode.INACTIVE_EMPLOYEE);
        }
        return employee;
    }

    private void validatePunchEligibility(AttendancePunchRequest request, EmployeeBasicDetail employee) {
        // Check employee eligibility
        if(!empEligibilityService.isEligibleForAttendance(
            request.getEmployeeId().toString(),
            request.getUnitId().toString()
        )) {
            throw new AttendanceException(AttendanceErrorCode.NOT_ELIGIBLE_FOR_ATTENDANCE);
        }

        // Validate biometric registration exists and is approved
        BiometricRegistrationDTO registration = biometricService.getBiometricRegistration(
            request.getEmployeeId().toString()
        );
        if (registration == null || registration.getStatus() != BiometricStatus.APPROVED) {
            throw new AttendanceException(AttendanceErrorCode.BIOMETRIC_NOT_REGISTERED);
        }

        // Get attendance day boundaries
        LocalDateTime dayStart = getAttendanceDayStart(request.getPunchTime());
        LocalDateTime dayEnd = getAttendanceDayEnd(request.getPunchTime());
        
        // Get last record within attendance day
        AttendanceRecord lastRecord = attendanceRecordRepository
            .findLastAttendanceByEmployeeIdAndTimeRange(
                request.getEmployeeId(), 
                dayStart, 
                dayEnd
            );
            
        // Check for duplicate punch
        if (isDuplicatePunch(lastRecord, request.getPunchTime())) {
            throw new AttendanceException(AttendanceErrorCode.DUPLICATE_ATTENDANCE,"Punch ignored - within duplicate threshold");
        }
        
        // Check max punches per day
        int punchesToday = attendanceRecordRepository
            .countPunchesByEmployeeIdAndDate(
                request.getEmployeeId(), 
                request.getPunchTime().toLocalDate()
            );
            
        if (punchesToday >= attendanceConfig.getMaxPunchesAllowedPerDay()) {
            throw new AttendanceException(AttendanceErrorCode.ATTENDANCE_UPDATE_ERROR,"Maximum punches allowed per day exceeded");
        }
    }

    private AttendanceRecord processPunchRecord(AttendancePunchRequest request) {
        // Get attendance day boundaries
        LocalDateTime dayStart = getAttendanceDayStart(request.getPunchTime());
        LocalDateTime dayEnd = getAttendanceDayEnd(request.getPunchTime());
        
        // Get last record within attendance day
        AttendanceRecord lastRecord = attendanceRecordRepository
            .findLastAttendanceByEmployeeIdAndTimeRange(
                request.getEmployeeId(), 
                dayStart, 
                dayEnd
            );

        // Check if this is a checkout for previous day
        if (lastRecord == null) {
            LocalDateTime previousDayStart = request.getPunchTime().minusDays(1)
                .withHour(attendanceConfig.getDayStartHour())
                .withMinute(0)
                .withSecond(0);
            LocalDateTime previousDayEnd = request.getPunchTime()
                .withHour(attendanceConfig.getDayEndHour())
                .withMinute(59)
                .withSecond(59);

            AttendanceRecord previousDayRecord = attendanceRecordRepository
                .findLastAttendanceByEmployeeIdAndTimeRange(
                    request.getEmployeeId(),
                    previousDayStart,
                    previousDayEnd
                );

            if (previousDayRecord != null && 
                previousDayRecord.getPunchType() == PunchType.CHECK_IN) {
                // Check if within max shift hours
                Duration timeSinceLastPunch = Duration.between(
                    previousDayRecord.getPunchTime(), 
                    request.getPunchTime()
                );
                
                if (timeSinceLastPunch.toHours() < attendanceConfig.getMaxShiftHours()) {
                    // Create checkout record for previous day
                    AttendanceRecord checkoutRecord = createAttendanceRecord(request);
                    checkoutRecord.setPunchType(PunchType.CHECK_OUT);
                    
                    // Save the record
                    checkoutRecord = attendanceRecordRepository.save(checkoutRecord);
                    
                    // Queue for external sync
                    queueForExternalSync(checkoutRecord);
                    
                    // Update previous day's summary asynchronously
                    LocalDate previousDay = getAttendanceDate(previousDayRecord.getPunchTime());
                    updateSummaryAsync(
                        request.getEmployeeId(),
                        previousDay,
                        previousDayRecord.getPunchTime(),
                        request.getPunchTime()
                    );
                    
                    return checkoutRecord;
                } else {
                    // Create system checkout for previous day at the same time as check-in
                    AttendanceRecord systemCheckout = createAttendanceRecord(request);
                    systemCheckout.setPunchTime(previousDayRecord.getPunchTime());
                    systemCheckout.setCreatedBy(String.valueOf(com.stpl.tech.util.AppConstants.SYSTEM_EMPLOYEE_ID));
                    systemCheckout.setRemarks("System generated checkout for missed punch");
                    systemCheckout.setIsSpecialCase(true);
                    systemCheckout.setSpecialCaseType(SpecialCaseType.SYSTEM_GENERATED);
                    systemCheckout.setPunchType(PunchType.CHECK_OUT);
                    
                    // Save the record
                    systemCheckout = attendanceRecordRepository.save(systemCheckout);
                    
                    // Queue for external sync
                    queueForExternalSync(systemCheckout);
                    
                    // Update previous day's summary asynchronously
                    LocalDate previousDay = getAttendanceDate(previousDayRecord.getPunchTime());
                    updateSummaryAsync(
                        request.getEmployeeId(),
                        previousDay,
                        previousDayRecord.getPunchTime(),
                        previousDayRecord.getPunchTime() // Same time as check-in
                    );
                }
            }
        }

        // Normal record creation
        AttendanceRecord newRecord = createAttendanceRecord(request);
        newRecord.setPunchType(determinePunchType(request.getEmployeeId(), lastRecord, request.getPunchTime()));
        request.setPunchType(newRecord.getPunchType());

        // Get biometric registration ID
        BiometricRegistrationDTO registration = biometricService.getBiometricRegistration(
            request.getEmployeeId().toString()
        );
        if (registration != null) {
            newRecord.setBiometricId(registration.getBiometricId());
        }

        // Save the record
        newRecord = attendanceRecordRepository.save(newRecord);
        
        // Queue for external sync
        queueForExternalSync(newRecord);

        // Update unit attendance tracking
        updateUnitAttendanceTracking(newRecord);

        // Update summary asynchronously
        if (newRecord != null) {
            LocalDate currentDay = getAttendanceDate(newRecord.getPunchTime());
            updateSummaryAsync(
                newRecord.getEmployeeId(),
                    currentDay,
                PunchType.CHECK_IN.equals(request.getPunchType()) ? newRecord.getPunchTime() : null,
                PunchType.CHECK_OUT.equals(request.getPunchType()) ? newRecord.getPunchTime() : null
            );
        }
        
        return newRecord;
    }

    private void processImagesAsync(AttendancePunchRequest request, EmployeeBasicDetail employee,
                                    AttendanceRecord punchRecord , String refId) {
        CompletableFuture.runAsync(() -> {
            try {
                // Upload processed image
               // String processedImageUrl = uploadProcessedImage(request, employee);
                
                // Upload original image
                String originalImageUrl = uploadOriginalImage(request, employee);
                
                // Update record with image URLs
                //punchRecord.setImageUrl(processedImageUrl);
                punchRecord.setOriginalImageUrl(originalImageUrl);
                attendanceRecordRepository.save(punchRecord);
                
                // Update image path in biometric service
                BiometricUpdateImagePathRequestDTO updateRequest = BiometricUpdateImagePathRequestDTO.builder()
                    .refId(refId)
                    .imageUrl(punchRecord.getImageUrl())
                    .build();
                biometricService.updateImagePath(updateRequest);
                
            } catch (Exception e) {
                log.error("Error processing images for punch record {}: {}", 
                    punchRecord.getId(), e.getMessage());
            }
        }, asyncTaskExecutor);
    }

    private String uploadProcessedImage(AttendancePunchRequest request, EmployeeBasicDetail employee) {
        String genderPath = getGenderBasedPath(employee);
        String cloudfrontKey = s3Service.uploadBase64Image(
            request.getBase64Image(),
            "master-service",
            genderPath + employee.getEmployeeCode() + "/" + System.currentTimeMillis() + ".jpg"
        );
        return s3Service.getCloudfrontUrl(cloudfrontKey);
    }

    private String uploadOriginalImage(AttendancePunchRequest request, EmployeeBasicDetail employee) {
        String genderPath = getGenderBasedPath(employee);
        String cloudfrontKey = s3Service.uploadBase64Image(
            request.getOriginalImage(),
            "master-service",
            genderPath +"original/" + employee.getEmployeeCode() + "/original_" + System.currentTimeMillis() + ".jpg"
        );
        return s3Service.getCloudfrontUrl(cloudfrontKey);
    }

    private String getGenderBasedPath(EmployeeBasicDetail employee) {
        if (employee.getGender() == null) {
            return "attendance/DEFAULT/";
        }
        return "attendance/" + employee.getGender().toUpperCase() + "/";
    }

    private boolean isDuplicatePunch(AttendanceRecord lastRecord, LocalDateTime currentPunch) {
        if (lastRecord == null) return false;
        if(currentPunch.isBefore(lastRecord.getPunchTime())) return false;
        return Duration.between(lastRecord.getPunchTime(), currentPunch)
                      .toMinutes() < attendanceConfig.getDuplicatePunchThresholdMinutes();
    }

    private LocalDateTime getAttendanceDayStart(LocalDateTime punchTime) {
        LocalDateTime dayStart = punchTime.withHour(attendanceConfig.getDayStartHour())
                                        .withMinute(0)
                                        .withSecond(0);
        if (punchTime.getHour() < attendanceConfig.getDayStartHour()) {
            dayStart = dayStart.minusDays(1);
        }
        return dayStart;
    }

    private LocalDateTime getAttendanceDayEnd(LocalDateTime punchTime) {
        LocalDateTime dayEnd = punchTime.withHour(attendanceConfig.getDayEndHour())
                                      .withMinute(59)
                                      .withSecond(59);
        if (punchTime.getHour() >= attendanceConfig.getDayEndHour()) {
            dayEnd = dayEnd.plusDays(1);
        }
        return dayEnd;
    }

    private void updateSummaryAsync(Integer employeeId, LocalDate attendanceDate, LocalDateTime checkInTime, LocalDateTime checkOutTime) {
        asyncOperationService.updateSummaryAsync(employeeId, attendanceDate, checkInTime, checkOutTime);
    }

    /**
     * Update unit attendance tracking when processing attendance punch
     */
    private void updateUnitAttendanceTracking(AttendanceRecord punchRecord) {
        try {
            // Get the shift instance for this employee and business date
            LocalDate businessDate = getAttendanceDate(punchRecord.getPunchTime());
            Optional<EmployeeShiftInstances> shiftInstance = employeeShiftInstancesRepository
                    .findByEmpIdAndBusinessDateAndInstanceStatus(punchRecord.getEmployeeId(), businessDate
                    , RosteringConstants.ACTIVE);

                    if (shiftInstance.isPresent()) {
            // Update unit attendance tracking
            empUnitShiftInstanceAnalyticsService.updateUnitAttendance(
                shiftInstance.get().getId(),
                punchRecord.getUnitId(),
                punchRecord.getPunchTime()
            );
                log.debug("Updated unit attendance tracking for employee: {}, unit: {}, time: {}",
                    punchRecord.getEmployeeId(), punchRecord.getUnitId(), punchRecord.getPunchTime());
            } else {
                log.debug("No shift instance found for employee: {} on date: {}",
                    punchRecord.getEmployeeId(), businessDate);
            }
        } catch (Exception e) {
            log.error("Error updating unit attendance tracking for employee: {}, unit: {}",
                punchRecord.getEmployeeId(), punchRecord.getUnitId(), e);
        }
    }

    private AttendanceRecord createAttendanceRecord(AttendancePunchRequest request) {
        AttendanceRecord record = new AttendanceRecord();
        record.setEmployeeId(request.getEmployeeId());
        record.setPunchTime(request.getPunchTime());
        record.setUnitId(request.getUnitId());
        record.setMacAddress(StringUtils.isEmpty(request.getMacAddress()) ?JwtContext.getInstance().getDeviceId()
                : request.getMacAddress());
        record.setGeoLocation(StringUtils.isEmpty(request.getGeoLocation())?JwtContext.getInstance().getLatLong():
                request.getGeoLocation());
        record.setCreatedBy(request.getCreatedBy());
        record.setRemarks(request.getRemarks());
        record.setIsSpecialCase(request.getIsSpecialCase());
        record.setSpecialCaseType(request.getSpecialCaseType());

        // Set biometric device ID
        String biometricDeviceId = request.getBiometricDeviceId();
        if (biometricDeviceId == null || biometricDeviceId.isEmpty()) {
            // Generate biometric device ID if not provided
            biometricDeviceId = generateBiometricDeviceId(request);
        } else if (!biometricDeviceIdUtil.isValidBiometricDeviceId(biometricDeviceId)) {
            log.warn("Invalid biometric device ID format provided: {}. Generating new one.", biometricDeviceId);
            biometricDeviceId = generateBiometricDeviceId(request);
        }
        
        record.setBiometricDeviceId(biometricDeviceId);

        // Get biometric registration ID
        BiometricRegistrationDTO registration = biometricService.getBiometricRegistration(
            request.getEmployeeId().toString()
        );
        if (registration != null) {
            record.setBiometricId(registration.getBiometricId());
        }

        return record;
    }

    /**
     * Generate biometric device ID in format: terminalId_unitType_unitId
     * This method can be customized based on your specific requirements
     */
    private String generateBiometricDeviceId(AttendancePunchRequest request) {
        // Get terminal ID from JWT context or request
        String terminalId = String.valueOf(JwtContext.getInstance().getTerminalId());
        if (terminalId == null || terminalId.isEmpty()) {
            terminalId = "UNKNOWN";
        }
        
        // Get unit type - you might need to fetch this from a service
        String unitType = getUnitType(request.getUnitId());
        
        return biometricDeviceIdUtil.generateBiometricDeviceId(terminalId, unitType, request.getUnitId());
    }

    /**
     * Get unit type for a given unit ID
     * This method should be implemented based on your unit management system
     */
    private String getUnitType(Integer unitId) {
        return unitCacheService.getUnitById(unitId).getFamily().name();
    }

    @Override
    public AttendanceResponse getTodayAttendance(Integer employeeId) {
        LocalDate today = LocalDate.now();
        List<AttendanceRecord> records = attendanceRecordRepository
            .findByEmployeeIdAndDate(employeeId, today);

        return mapToAttendanceResponse(employeeId, records);
    }

    @Override
    public List<AttendanceResponse> getAttendanceByDate(Integer employeeId, LocalDate date, Integer month, Integer year) {
        List<AttendanceRecord> records;
        if (date != null) {
            records = attendanceRecordRepository.findByEmployeeIdAndDate(employeeId, date);
        } else if (month != null && year != null) {
            records = attendanceRecordRepository.findByEmployeeIdAndMonthYear(employeeId, month, year);
        } else if (year != null) {
            records = attendanceRecordRepository.findByEmployeeIdAndYear(employeeId, year);
        } else {
            throw new AttendanceValidationException("Either date or month/year or year must be provided");
        }

        return records.stream()
            .collect(Collectors.groupingBy(record -> record.getPunchTime().toLocalDate()))
            .entrySet().stream()
            .map(entry -> mapToAttendanceResponse(employeeId, entry.getValue()))
            .collect(Collectors.toList());
    }

    @Override
    public List<AttendanceResponse> getAttendanceHistory(Long employeeId, LocalDate date, Integer month, Integer year) {
        log.info("Getting attendance history for employee: {}, date: {}, month: {}, year: {}",
                employeeId, date, month, year);
        
        List<DailyAttendanceSummary> summaries;
        LocalDate startDate = null;
        LocalDate endDate = null;
        
        if (date != null) {
            startDate = date;
            endDate = date;
            Optional<DailyAttendanceSummary> summaryOpt = summaryRepository.findByEmployeeIdAndAttendanceDate(employeeId.intValue(), date);
            summaries = summaryOpt.map(List::of).orElse(List.of());
        } else if (month != null && year != null) {
            startDate = LocalDate.of(year, month, 1);
            endDate = startDate.withDayOfMonth(startDate.lengthOfMonth());
            summaries = summaryRepository.findByEmployeeIdAndMonthYear(employeeId.intValue(), month, year);
        } else if (year != null) {
            startDate = LocalDate.of(year, 1, 1);
            endDate = startDate.withDayOfYear(startDate.lengthOfYear());
            summaries = summaryRepository.findByEmployeeIdAndYear(employeeId.intValue(), year);
        } else {
            throw new IllegalArgumentException("Either date or month/year or year must be provided");
        }

        EmployeeBasicDetail employeeBasicDetail = userCacheService.getUserById(employeeId.intValue());
        if (employeeBasicDetail == null) {
            log.error("Employee not found for ID: {}", employeeId);
            throw new AttendanceException(AttendanceErrorCode.EMPLOYEE_NOT_FOUND);
        }

        // Create a map of existing summaries by date for quick lookup
        Map<LocalDate, DailyAttendanceSummary> summaryMap = summaries.stream()
                .collect(Collectors.toMap(DailyAttendanceSummary::getAttendanceDate, summary -> summary));

        List<AttendanceResponse> responses = new ArrayList<>();

        // Generate entries for all dates in the range
        for (LocalDate currentDate = startDate; !currentDate.isAfter(endDate); currentDate = currentDate.plusDays(1)) {
            List<EmployeeAttendanceRequest> pendingRequests = employeeAttendanceRequestRepository
                    .findByEmpIdAndStatusAndDateBetweenStartAndEnd(employeeId.intValue(), AppConstants.STATUS_PENDING,currentDate);

            // For multi-day requests, we need to find the request that covers the current date
            // Since the query already filters by date range, we can use the first matching request
            EmployeeAttendanceRequest pendingRequest = pendingRequests.isEmpty() ? null : pendingRequests.get(0);
            
            DailyAttendanceSummary summary = summaryMap.get(currentDate);
            
            if (summary != null) {
                // Existing attendance record
                AttendanceResponse response = createAttendanceResponseFromSummary(summary, employeeBasicDetail);
                response.setHexColor(getHexColorForAttendance(summary.getStatus(), summary.getType(), pendingRequest,currentDate));
                responses.add(response);
            } else {
                // Create dummy entry for missing date
                AttendanceResponse dummyResponse = createDummyAttendanceResponse(currentDate, employeeBasicDetail, pendingRequest);
                // Set hex color for dummy response based on type and pending requests
                AttendanceType attendanceType = dummyResponse.getType() != null ? 
                    AttendanceType.valueOf(dummyResponse.getType()) : AttendanceType.NORMAL;
                dummyResponse.setHexColor(getHexColorForAttendance(com.stpl.tech.attendance.entity.AttendanceStatus.valueOf(dummyResponse.getStatus().name())
                        , attendanceType, pendingRequest, currentDate));
                responses.add(dummyResponse);
            }
        }

        // Sort by date
        responses.sort(Comparator.comparing(AttendanceResponse::getAttendanceDate));

        log.info("Generated {} attendance responses for employee: {} from {} to {}", 
                responses.size(), employeeId, startDate, endDate);

        return responses;
    }

    /**
     * Create attendance response from existing summary
     */
    private AttendanceResponse createAttendanceResponseFromSummary(DailyAttendanceSummary summary, EmployeeBasicDetail employeeBasicDetail) {
        AttendanceResponse response = new AttendanceResponse();
        response.setEmployeeId(Long.valueOf(summary.getEmployeeId()));
        response.setEmployeeCode(employeeBasicDetail.getEmployeeCode());
        response.setDesignation(employeeBasicDetail.getDesignation());
        response.setEmployeeName(employeeBasicDetail.getName());
        response.setAttendanceTime(summary.getFirstCheckIn());
        response.setCheckInTime(summary.getFirstCheckIn());
        response.setCheckOutTime(summary.getLastCheckOut());
        response.setStatus(mapEntityStatusToEnumStatus(summary.getStatus()));
        response.setAttendanceType(summary.getLastCheckOut() != null ? PunchType.CHECK_OUT : PunchType.CHECK_IN);
        
        if (summary.getAttendanceDate() != null) {
            response.setAttendanceDate(summary.getAttendanceDate().atStartOfDay());
        }
        if (summary.getType() != null) {
            response.setType(summary.getType().name());
        }
        
        return response;
    }

    /**
     * Map entity AttendanceStatus to enum AttendanceStatus
     */
    private AttendanceStatus mapEntityStatusToEnumStatus(com.stpl.tech.attendance.entity.AttendanceStatus entityStatus) {
        if (entityStatus == null) {
            return AttendanceStatus.ABSENT;
        }
        
        return switch (entityStatus) {
            case PRESENT -> AttendanceStatus.PRESENT;
            case ABSENT -> AttendanceStatus.ABSENT;
            case HALF_DAY -> AttendanceStatus.HALF_DAY;
            case LATE -> AttendanceStatus.MIDDAY; // Map LATE to MIDDAY
            case SPECIAL_CASE -> AttendanceStatus.SPECIAL_CASE;
            case LESS_WORKING_HOURS -> AttendanceStatus.LESS_WORKING_HOURS;
            case NORMAL -> AttendanceStatus.PRESENT;
        };
    }

    /**
     * Create dummy attendance response for missing dates
     */
    private AttendanceResponse createDummyAttendanceResponse(LocalDate date, EmployeeBasicDetail employeeBasicDetail, 
                                                           EmployeeAttendanceRequest pendingRequest) {

        AttendanceResponse response = new AttendanceResponse();
        response.setEmployeeId(Long.valueOf(employeeBasicDetail.getId()));
        response.setEmployeeCode(employeeBasicDetail.getEmployeeCode());
        response.setDesignation(employeeBasicDetail.getDesignation());
        response.setEmployeeName(employeeBasicDetail.getName());
        response.setAttendanceDate(date.atStartOfDay());
        response.setAttendanceTime(null);
        response.setCheckInTime(null);
        response.setCheckOutTime(null);
        response.setAttendanceType(PunchType.CHECK_IN);
        
        // Determine status and type based on pending requests and date characteristics
        if (pendingRequest != null) {
            // Pending Regularisation/OD/WFH request
            response.setStatus(AttendanceStatus.ABSENT); // Will be overridden by hex color logic
            response.setType(pendingRequest.getType());
        } else if (attendanceRequestUtil.isWeekOff(date,employeeBasicDetail.getId(), employeeBasicDetail.getDepartmentId())) {
            // Week off (Saturday/Sunday)
            response.setStatus(AttendanceStatus.ABSENT); // Will be overridden by hex color logic
            response.setType(String.valueOf(AttendanceType.WEEK_OFF));
        } else if (attendanceRequestUtil.isHoliday(date, employeeBasicDetail.getDepartmentId())) {
            // Company holiday
            response.setStatus(AttendanceStatus.ABSENT); // Will be overridden by hex color logic
            response.setType(String.valueOf(AttendanceType.HOLIDAY));
        } else {
            // Regular absent day
            response.setStatus(AttendanceStatus.ABSENT);
            response.setType(String.valueOf(AttendanceType.NORMAL));
        }
        
        return response;
    }

    /**
     * Get hex color code based on attendance status, type and pending requests
     */
    private String getHexColorForAttendance(com.stpl.tech.attendance.entity.AttendanceStatus status,
                                            AttendanceType type,
                                            EmployeeAttendanceRequest pendingRequest,LocalDate currentDate) {
        
        // Check for pending Regularisation/OD/WFH requests first (Yellow)
        if (pendingRequest != null) {
            String requestType = pendingRequest.getType();
            if (AppConstants.REGULARISATION.equalsIgnoreCase(requestType) ||
                AppConstants.OD.equalsIgnoreCase(requestType) ||
                AppConstants.WFH.equalsIgnoreCase(requestType) ||
                AppConstants.LEAVE.equalsIgnoreCase(requestType) ||
                AppConstants.COMP_OFF.equalsIgnoreCase(requestType)) {
                return AppConstants.YELLOW; // Yellow for pending Regularisation/OD/WFH
            }
        }
        
        // Check for Leave/Holiday (Blue)
        if (type == AttendanceType.LEAVE || type == AttendanceType.OD || type == AttendanceType.WFH || type == AttendanceType.REGULARISATION ||
            type == AttendanceType.HOLIDAY ||
            AppConstants.HOLIDAY.equalsIgnoreCase(type.name())) {
            return AppConstants.BLUE; // Blue for Leave/Holiday
        }
        // if pendingrrequest is null and dates are in future, return transparent
        if(pendingRequest == null && currentDate.isAfter(LocalDate.now())){
            return AppConstants.TRANSPARENT;
        }
        // Check for Week Off (Grey)
        if (type == AttendanceType.WEEK_OFF ||
            "WEEKEND".equalsIgnoreCase(type.name())) {
            return AppConstants.GREY; // Grey for Week Off
        }
        
        // Check for Present status (Green)
        if (status == com.stpl.tech.attendance.entity.AttendanceStatus.PRESENT) {
            return AppConstants.GREEN; // Green for Present
        }
        
        // Check for Absent/Less Working Hours (Red)
        if (status == com.stpl.tech.attendance.entity.AttendanceStatus.ABSENT ||
            status == com.stpl.tech.attendance.entity.AttendanceStatus.LESS_WORKING_HOURS) {
            return AppConstants.RED; // Red for Absent/Less Working Hours
        }
        
        // Default to Red for any other cases
        return AppConstants.RED;
    }

    /**
     * Get hex color code for dummy attendance response
     */
    private String getHexColorForDummyAttendance(String type, EmployeeAttendanceRequest pendingRequest,LocalDate currentDate) {
        // Check for pending Regularisation/OD/WFH requests first (Yellow)
        if (pendingRequest != null) {
            String requestType = pendingRequest.getType();
            if (AppConstants.REGULARISATION.equalsIgnoreCase(requestType) ||
                AppConstants.OD.equalsIgnoreCase(requestType) ||
                AppConstants.WFH.equalsIgnoreCase(requestType) ||
                AppConstants.LEAVE.equalsIgnoreCase(requestType) ||
                AppConstants.COMP_OFF.equalsIgnoreCase(requestType)) {
                return AppConstants.YELLOW; // Yellow for pending Regularisation/OD/WFH
            }
        }
        
        // Check for Leave/Holiday (Blue)
        if (AppConstants.LEAVE.equalsIgnoreCase(type) || AppConstants.HOLIDAY.equalsIgnoreCase(type)) {
            return AppConstants.BLUE; // Blue for Leave/Holiday
        }
        
        if(pendingRequest == null && currentDate.isAfter(LocalDate.now())){
            return AppConstants.TRANSPARENT;
        }
        
        // Check for Week Off (Grey)
        if ("WEEKEND".equalsIgnoreCase(type) || AppConstants.WEEK_OFF.equalsIgnoreCase(type)) {
            return AppConstants.GREY; // Grey for Week Off
        }
        
        // Default to Red for any other cases (including NORMAL)
        return AppConstants.RED;
    }

    @Override
    public AttendanceResponse getCurrentDayAttendance(Long employeeId) {
        log.info("Getting current day attendance for employee: {}", employeeId);
        
        // Get current time and determine the attendance date
        LocalDateTime now = DateTimeUtil.now();
        LocalDate attendanceDate = getAttendanceDate(now);
        
        log.info("Current time: {}, Attendance date for query: {}", now, attendanceDate);
        
        Optional<DailyAttendanceSummary> summaryOpt = summaryRepository
            .findByEmployeeIdAndAttendanceDate(employeeId.intValue(), attendanceDate);
            
        EmployeeBasicDetail employeeBasicDetail = userCacheService.getUserById(employeeId.intValue());
        if (employeeBasicDetail == null) {
            log.error("Employee not found for ID: {}", employeeId);
            throw new AttendanceException(AttendanceErrorCode.EMPLOYEE_NOT_FOUND);
        }
        
        if (summaryOpt.isEmpty()) {
            // Create a response with basic employee info and ABSENT status
            AttendanceResponse response = new AttendanceResponse();
            response.setEmployeeId(employeeId);
            response.setEmployeeName(employeeBasicDetail.getName());
            response.setEmployeeCode(employeeBasicDetail.getEmployeeCode());
            response.setDesignation(employeeBasicDetail.getDesignation());
            response.setStatus(AttendanceStatus.ABSENT);
            response.setAttendanceTime(now);
            response.setRemarks("No attendance record found for current attendance day");
            
            // Check for pending requests to determine hex color
            List<EmployeeAttendanceRequest> pendingRequests = employeeAttendanceRequestRepository
                .findByEmpIdAndStatusAndDateBetweenStartAndEnd(employeeId.intValue(), AppConstants.STATUS_PENDING, attendanceDate);
            if (!pendingRequests.isEmpty()) {
                response.setType(pendingRequests.get(0).getType());
                response.setHexColor(getHexColorForDummyAttendance(pendingRequests.get(0).getType(), pendingRequests.get(0),attendanceDate));
            } else if (attendanceRequestUtil.isWeekOff(attendanceDate,employeeBasicDetail.getId(), employeeBasicDetail.getDepartmentId())) {
                //response.setType("WEEKEND");
                response.setHexColor(AppConstants.GREY); // Grey for Week Off
            } else if (attendanceRequestUtil.isHoliday(attendanceDate, employeeBasicDetail.getDepartmentId())) {
                response.setType("HOLIDAY");
                response.setHexColor(AppConstants.BLUE); // Blue for Holiday
            } else {
                response.setType("NORMAL");
                response.setHexColor(AppConstants.RED); // Red for Absent
            }
            
            return response;
        }
        
        DailyAttendanceSummary summary = summaryOpt.get();
        AttendanceResponse response = new AttendanceResponse();
        response.setEmployeeId(Long.valueOf(summary.getEmployeeId()));
        response.setEmployeeCode(employeeBasicDetail.getEmployeeCode());
        response.setDesignation(employeeBasicDetail.getDesignation());
        response.setEmployeeName(employeeBasicDetail.getName());
        response.setAttendanceTime(summary.getFirstCheckIn());
        response.setAttendanceType(summary.getLastCheckOut() != null
                ? PunchType.CHECK_OUT : PunchType.CHECK_IN);
        
        // Set check-in/check-out times
        response.setCheckInTime(summary.getFirstCheckIn());
        response.setCheckOutTime(summary.getLastCheckOut());
        
        // Set attendance status based on summary status
        response.setStatus(mapEntityStatusToEnumStatus(summary.getStatus()));
        
        // Set hex color based on attendance status and type
        List<EmployeeAttendanceRequest> pendingRequests = employeeAttendanceRequestRepository
            .findByEmpIdAndStatusAndDateBetweenStartAndEnd(employeeId.intValue(), AppConstants.STATUS_PENDING, attendanceDate);
        EmployeeAttendanceRequest pendingRequest = pendingRequests.isEmpty() ? null : pendingRequests.get(0);
        response.setHexColor(getHexColorForAttendance(summary.getStatus(), summary.getType(), pendingRequest, attendanceDate));
        
        return response;
    }


    private AttendanceResponse mapToAttendanceResponse(Integer employeeId, List<AttendanceRecord> records) {
        EmployeeBasicDetail employee = userCacheService.getUserById(employeeId);
        if (employee == null) {
            throw new AttendanceException(AttendanceErrorCode.EMPLOYEE_NOT_FOUND);
        }

        if (!employee.getStatus().name().equals(com.stpl.tech.util.AppConstants.ACTIVE)) {
            throw new AttendanceException(AttendanceErrorCode.INACTIVE_EMPLOYEE);
        }

        AttendanceResponse response = new AttendanceResponse();
        response.setEmployeeId(Long.valueOf(employeeId));
        response.setEmployeeName(employee.getName());
        response.setDesignation(employee.getDesignation());
        
        for (AttendanceRecord record : records) {
            if (PunchType.CHECK_IN.equals(record.getPunchType())) {
                response.setCheckInTime(record.getPunchTime());
                response.setCheckInImageUrl(record.getImageUrl() != null ? record.getImageUrl() : 
                        environmentProperties.getDefaultEmployeeImage());
            } else if (PunchType.CHECK_OUT.equals(record.getPunchType())) {
                response.setCheckOutTime(record.getPunchTime());
                response.setCheckOutImageUrl(record.getImageUrl() != null ? record.getImageUrl() : 
                        environmentProperties.getDefaultEmployeeImage());
            }
        }
        
        // Set attendance type and status based on records
        if (response.getCheckInTime() != null && response.getCheckOutTime() != null) {
            response.setStatus(AttendanceStatus.PRESENT);
        } else if (response.getCheckInTime() != null) {
            response.setStatus(AttendanceStatus.MIDDAY);
        } else {
            response.setStatus(AttendanceStatus.ABSENT);
        }
        
        return response;
    }

    private Integer getEmployeeId(AttendancePunchRequest request) {
        if (request.getEmployeeId() != null) {
            return request.getEmployeeId();
        }

        if (request.getUnitId() == null) {
            throw new AttendanceValidationException("Unit ID is required for biometric lookup");
        }

        return getEmployeeIdFromImage(request.getBase64Image(), JwtContext.getInstance().getUnitId());
    }

    private Integer getEmployeeIdFromImage(String base64image, Integer unitId) {
        return biometricRegistrationService.getEmployeeIdFromImage(base64image, unitId);
    }

    private AttendancePunchResponse handleFirstPunch(AttendancePunchRequest request,EmployeeBasicDetail employeeBasicDetail, String imageUrl) {
        if (!PunchType.CHECK_IN.equals(request.getPunchType())) {
            throw new AttendanceValidationException("First punch of the day must be CHECKIN");
        }

        AttendanceRecord record = createAttendanceRecord(request, imageUrl);
        attendanceRecordRepository.save(record);

        return createResponse(true, "Check-in recorded successfully", request,employeeBasicDetail,
                imageUrl,record.getId());
    }

    private AttendanceRecord createAttendanceRecord(AttendancePunchRequest request, String imageUrl) {
        AttendanceRecord record = new AttendanceRecord();
        record.setEmployeeId(request.getEmployeeId());
        record.setUnitId(request.getUnitId());
        record.setPunchTime(request.getPunchTime() != null ? request.getPunchTime() : DateTimeUtil.now());
        record.setPunchType(request.getPunchType());
        record.setMacAddress(JwtContext.getInstance().getDeviceId());
        record.setGeoLocation(request.getGeoLocation());
        record.setCreatedBy(request.getCreatedBy());
        record.setRemarks(request.getRemarks());
        record.setIsSpecialCase(request.getIsSpecialCase());
        record.setSpecialCaseType(request.getSpecialCaseType());
        record.setImageUrl(imageUrl);
        return record;
    }

    private AttendanceRecord createSystemCheckout(AttendanceRecord lastCheckin) {
        AttendanceRecord systemCheckout = new AttendanceRecord();
        systemCheckout.setEmployeeId(lastCheckin.getEmployeeId());
        systemCheckout.setUnitId(lastCheckin.getUnitId());
        systemCheckout.setPunchTime(lastCheckin.getPunchTime());
        systemCheckout.setPunchType(PunchType.CHECK_OUT);
        systemCheckout.setCreatedBy("SYSTEM");
        systemCheckout.setRemarks("System generated checkout for missed punch");
        return systemCheckout;
    }

    private AttendancePunchResponse createResponse(boolean success, String message,
            AttendancePunchRequest request, EmployeeBasicDetail employeeBasicDetail,
                                                   String imageUrl , Long attendanceRequestId) {
        AttendancePunchResponse response = new AttendancePunchResponse();
        response.setSuccess(success);
        response.setMessage(message);
        response.setPunchType(request.getPunchType());
        response.setPunchTime(request.getPunchTime());
        response.setCreatedBy(request.getCreatedBy());
        response.setImageUrl(imageUrl);
        response.setRegisteredImageUrl(employeeBasicDetail.getImagekey());
        response.setEmployeeCode(employeeBasicDetail.getEmployeeCode());
        response.setEmployeeName(employeeBasicDetail.getName());
        if(attendanceRequestId != null){
            response.setAttendanceRequestId(attendanceRequestId.toString());
        }
        return response;
    }

    private AttendancePunchResponse createResponse(boolean success, String message, 
            AttendancePunchRequest request,EmployeeBasicDetail employeeBasicDetail , String imageUrl,
                                                   String nextAction, Long attendanceRequestId) {
        AttendancePunchResponse response = createResponse(success, message, request,employeeBasicDetail,
                imageUrl,attendanceRequestId);
        response.setNextAction(nextAction);
        return response;
    }

    private PunchType determinePunchType(Integer employeeId,AttendanceRecord lastRecord, LocalDateTime currentPunchTime) {
        if (lastRecord == null) {
            // Check if there's a single punch from previous day
            LocalDateTime previousDayStart = currentPunchTime.minusDays(1)
                .withHour(attendanceConfig.getDayStartHour())
                .withMinute(0)
                .withSecond(0);
            LocalDateTime previousDayEnd = currentPunchTime.minusDays(1)
                .withHour(attendanceConfig.getDayEndHour())
                .withMinute(59)
                .withSecond(59);

            AttendanceRecord previousDayRecord = attendanceRecordRepository
                .findLastAttendanceByEmployeeIdAndTimeRange(
                    employeeId,
                    previousDayStart,
                    previousDayEnd
                );

            if (previousDayRecord != null && 
                previousDayRecord.getPunchType() == PunchType.CHECK_IN) {
                // Check if within max shift hours
                Duration timeSinceLastPunch = Duration.between(
                    previousDayRecord.getPunchTime(), 
                    currentPunchTime
                );
                
                if (timeSinceLastPunch.toHours() < attendanceConfig.getMaxShiftHours()) {
                    // Mark as previous day's checkout
                    return PunchType.CHECK_OUT;
                }
            }
            // First punch of the day is always CHECKIN
            return PunchType.CHECK_IN;
        } else {
            /*
             * Check If there is a summary record for punch date,
             * If so, the current punch is a checkout. Otherwise, it's a checkin.*/
            LocalDate currentDay = getAttendanceDate(currentPunchTime);
            Optional<DailyAttendanceSummary> currentDaySummary =  summaryRepository.
                    findByEmployeeIdAndAttendanceDate(employeeId,currentDay);
            if(currentDaySummary.isPresent()){
                return PunchType.CHECK_OUT;
            }
            return PunchType.CHECK_IN;
        }
    }

    private LocalDate getAttendanceDate(LocalDateTime punchTime) {
        if (punchTime.getHour() < attendanceConfig.getDayStartHour()) {
            return punchTime.toLocalDate().minusDays(1);
        }
        return punchTime.toLocalDate();
    }

    /**
     * Check if an attendance punch was made in offline mode
     * @param request The attendance punch request
     * @return true if the punch is in offline mode, false otherwise
     */
    private boolean isOfflineModePunch(AttendancePunchRequest request) {
        return request.getIsSpecialCase() != null && 
               request.getIsSpecialCase() && 
               SpecialCaseType.OFFLINE_MODE.equals(request.getSpecialCaseType());
    }

    /**
     * Check if an attendance record was made in offline mode
     * @param record The attendance record
     * @return true if the record is in offline mode, false otherwise
     */
    private boolean isOfflineModeRecord(AttendanceRecord record) {
        return record.getIsSpecialCase() != null && 
               record.getIsSpecialCase() && 
               SpecialCaseType.OFFLINE_MODE.equals(record.getSpecialCaseType());
    }

    @Override
    public BulkAttendancePunchResponse processBulkAttendancePunch(BulkAttendancePunchRequest request) {
        long startTime = System.currentTimeMillis();
        String bulkOperationId = UUID.randomUUID().toString();
        log.info("Starting bulk attendance punch processing. Operation ID: {}, Total requests: {}, Offline Mode: {}", 
                bulkOperationId, request.getAttendancePunchRequests().size(), request.getIsOfflineMode());

        // Set offline mode for all requests if bulk operation is marked as offline
        if (request.getIsOfflineMode() != null && request.getIsOfflineMode()) {
            setOfflineModeForBulkRequests(request.getAttendancePunchRequests());
            log.info("Set offline mode for all {} requests in bulk operation: {}", 
                    request.getAttendancePunchRequests().size(), bulkOperationId);
        }

        BulkAttendancePunchResponse response = new BulkAttendancePunchResponse();
        response.setBulkOperationId(bulkOperationId);
        response.setProcessedAt(LocalDateTime.now());
        response.setTotalRequests(request.getAttendancePunchRequests().size());
        response.setSuccessfulRequests(0);
        response.setFailedRequests(0);
        response.setResults(new ArrayList<>());

        // Process each attendance punch request asynchronously
        List<CompletableFuture<AttendancePunchResponse>> futures = request.getAttendancePunchRequests()
                .stream()
                .map(punchRequest -> CompletableFuture.supplyAsync(() -> {
                    try {
                        log.info("Processing attendance punch for employee: {} in bulk operation: {}", 
                                punchRequest.getEmployeeId(), bulkOperationId);
                        return processBulkAttendancePunchWithReordering(punchRequest);
                    } catch (Exception e) {
                        log.error("Error processing attendance punch for employee: {} in bulk operation: {}", 
                                punchRequest.getEmployeeId(), bulkOperationId, e);
                        
                        // Create error response
                        AttendancePunchResponse errorResponse = new AttendancePunchResponse();
                        errorResponse.setSuccess(false);
                        errorResponse.setMessage("Failed to process attendance punch: " + e.getMessage());
                        errorResponse.setPunchType(punchRequest.getPunchType());
                        errorResponse.setPunchTime(punchRequest.getPunchTime());
                        errorResponse.setCreatedBy(punchRequest.getCreatedBy());
                        
                        return errorResponse;
                    }
                }, asyncTaskExecutor))
                .collect(Collectors.toList());

        // Wait for all futures to complete and collect results
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        // Process results
        for (CompletableFuture<AttendancePunchResponse> future : futures) {
            try {
                AttendancePunchResponse punchResponse = future.get();
                response.getResults().add(punchResponse);
                
                if (punchResponse.isSuccess()) {
                    response.setSuccessfulRequests(response.getSuccessfulRequests() + 1);
                } else {
                    response.setFailedRequests(response.getFailedRequests() + 1);
                }
            } catch (Exception e) {
                log.error("Error getting result from future in bulk operation: {}", bulkOperationId, e);
                response.setFailedRequests(response.getFailedRequests() + 1);
                
                // Add error response
                AttendancePunchResponse errorResponse = new AttendancePunchResponse();
                errorResponse.setSuccess(false);
                errorResponse.setMessage("Failed to get result: " + e.getMessage());
                response.getResults().add(errorResponse);
            }
        }

        // Set overall success status
        response.setSuccess(response.getFailedRequests() == 0);
        response.setMessage(String.format("Bulk operation completed. Success: %d, Failed: %d", 
                response.getSuccessfulRequests(), response.getFailedRequests()));

        log.info("Bulk attendance punch processing completed. Operation ID: {}, Duration: {} ms, Success: {}, Failed: {}", 
                bulkOperationId, System.currentTimeMillis() - startTime, 
                response.getSuccessfulRequests(), response.getFailedRequests());

        return response;
    }

    /**
     * Set offline mode for all requests in a bulk operation
     * @param requests List of attendance punch requests
     */
    private void setOfflineModeForBulkRequests(List<AttendancePunchRequest> requests) {
        for (AttendancePunchRequest request : requests) {
            // Only set if not already set
            if (request.getSpecialCaseType() == null) {
                request.setIsSpecialCase(true);
                request.setMacAddress(JwtContext.getInstance().getDeviceId());
                request.setSpecialCaseType(SpecialCaseType.OFFLINE_MODE);
                
                // Add offline mode indicator to remarks
                String existingRemarks = request.getRemarks() != null ? request.getRemarks() : "";
                if (!existingRemarks.contains("Offline Mode")) {
                    request.setRemarks(existingRemarks + " (Bulk Offline Sync)");
                }
            }
        }
    }

    /**
     * Process bulk attendance punch with proper timestamp ordering and data consistency
     * This method handles cases where offline punches arrive with earlier timestamps
     * than existing punches for the same day
     */
    @Transactional(rollbackFor = Exception.class, readOnly = false)
    public AttendancePunchResponse processBulkAttendancePunchWithReordering(AttendancePunchRequest request) {
        long startTime = System.currentTimeMillis();
        log.info("Processing bulk attendance punch with reordering for employee: {} at time: {}", 
                request.getEmployeeId(), request.getPunchTime());

        // Validate and prepare request
        validateAndPrepareRequest(request,false);
        
        // Get and validate employee
        EmployeeBasicDetail employee = getAndValidateEmployee(request);
        
        // Validate punch eligibility (without duplicate check for bulk operations)
        validateBulkPunchEligibility(request, employee);

        // Get all existing records for the attendance day
        LocalDateTime dayStart = getAttendanceDayStart(request.getPunchTime());
        LocalDateTime dayEnd = getAttendanceDayEnd(request.getPunchTime());
        
        List<AttendanceRecord> existingRecords = attendanceRecordRepository
            .findByEmployeeIdAndTimeRange(request.getEmployeeId(), dayStart, dayEnd);
        
        // Sort existing records by punch time
        existingRecords.sort((a, b) -> a.getPunchTime().compareTo(b.getPunchTime()));
        
        // Check if this punch should be inserted as the first punch
        boolean shouldBeFirstPunch = existingRecords.isEmpty() || 
                                   request.getPunchTime().isBefore(existingRecords.get(0).getPunchTime());
        
        if (shouldBeFirstPunch) {
            log.info("Bulk punch for employee: {} at time: {} should be first punch of the day", 
                    request.getEmployeeId(), request.getPunchTime());
            
            // Create the new record as first punch
            AttendanceRecord newRecord =
                    createAttendanceRecord(request);
            newRecord.setPunchType(PunchType.CHECK_IN);
            request.setPunchType(PunchType.CHECK_IN);
            
            // Get biometric registration ID
            BiometricRegistrationDTO registration = biometricService.getBiometricRegistration(
                request.getEmployeeId().toString()
            );
            if (registration != null) {
                newRecord.setBiometricId(registration.getBiometricId());
            }
            
            // Save the new record
            newRecord = attendanceRecordRepository.save(newRecord);
            queueForExternalSync(newRecord);
            
            // Reorder existing records if needed
            if (!existingRecords.isEmpty()) {
                reorderExistingRecordsForBulkPunch(request.getEmployeeId(), existingRecords, newRecord);
            }
            
            // Update summary asynchronously
            LocalDate attendanceDate = getAttendanceDate(newRecord.getPunchTime());
            updateSummaryAsync(
                newRecord.getEmployeeId(),
                attendanceDate,
                newRecord.getPunchTime(),
                null
            );
            
            log.info("Bulk punch processing completed in {} ms for employee: {}",
                    System.currentTimeMillis() - startTime, request.getEmployeeId());
            
            return createResponse(true, "Check-in recorded successfully as first punch", 
                    request, employee, newRecord.getImageUrl(),newRecord.getId());
        } else {
            // This punch should be inserted in the middle or end of existing punches
            log.info("Bulk punch for employee: {} at time: {} should be inserted in sequence", 
                    request.getEmployeeId(), request.getPunchTime());
            
            // Find the correct position to insert this punch
            int insertIndex = findInsertPosition(existingRecords, request.getPunchTime());
            
            // Create the new record
            AttendanceRecord newRecord = createAttendanceRecord(request);
            newRecord.setPunchType(determinePunchTypeForBulkInsert(existingRecords, insertIndex, request.getPunchTime()));
            request.setPunchType(newRecord.getPunchType());
            
            // Get biometric registration ID
            BiometricRegistrationDTO registration = biometricService.getBiometricRegistration(
                request.getEmployeeId().toString()
            );
            if (registration != null) {
                newRecord.setBiometricId(registration.getBiometricId());
            }
            
            // Save the new record
            newRecord = attendanceRecordRepository.save(newRecord);
            queueForExternalSync(newRecord);
            
            // Reorder existing records if needed
            reorderExistingRecordsForBulkPunch(request.getEmployeeId(), existingRecords, newRecord);
            
            // Update summary asynchronously
            LocalDate attendanceDate = getAttendanceDate(newRecord.getPunchTime());
            updateSummaryAsync(
                newRecord.getEmployeeId(),
                attendanceDate,
                null,
                null
            );
            
            log.info("Bulk punch processing completed in {} ms for employee: {}", 
                    System.currentTimeMillis() - startTime, request.getEmployeeId());
            
            return createResponse(true, newRecord.getPunchType() + " recorded successfully", 
                    request, employee, newRecord.getImageUrl(),newRecord.getId());
        }
    }

    /**
     * Validate bulk punch eligibility without duplicate check
     */
    private void validateBulkPunchEligibility(AttendancePunchRequest request, EmployeeBasicDetail employee) {
        // Check employee eligibility
        if(!empEligibilityService.isEligibleForAttendance(
            request.getEmployeeId().toString(),
            request.getUnitId().toString()
        )) {
            throw new AttendanceException(AttendanceErrorCode.NOT_ELIGIBLE_FOR_ATTENDANCE);
        }

        // Validate biometric registration exists and is approved
        BiometricRegistrationDTO registration = biometricService.getBiometricRegistration(
            request.getEmployeeId().toString()
        );
        if (registration == null || registration.getStatus() != BiometricStatus.APPROVED) {
            throw new AttendanceException(AttendanceErrorCode.BIOMETRIC_NOT_REGISTERED);
        }
        
        // Check max punches per day (including the new punch)
        int punchesToday = attendanceRecordRepository
            .countPunchesByEmployeeIdAndDate(
                request.getEmployeeId(), 
                request.getPunchTime().toLocalDate()
            );
            
        if (punchesToday >= attendanceConfig.getMaxPunchesAllowedPerDay()) {
            throw new AttendanceException(AttendanceErrorCode.ATTENDANCE_UPDATE_ERROR,
                    "Maximum punches allowed per day exceeded");
        }
    }

    /**
     * Find the correct position to insert a new punch in the existing sequence
     */
    private int findInsertPosition(List<AttendanceRecord> existingRecords, LocalDateTime newPunchTime) {
        for (int i = 0; i < existingRecords.size(); i++) {
            if (newPunchTime.isBefore(existingRecords.get(i).getPunchTime())) {
                return i;
            }
        }
        return existingRecords.size(); // Insert at the end
    }

    /**
     * Determine punch type for bulk insert based on position in sequence
     */
    private PunchType determinePunchTypeForBulkInsert(List<AttendanceRecord> existingRecords, 
                                                 int insertIndex, LocalDateTime newPunchTime) {
        if (insertIndex == 0) {
            // First punch of the day
            return PunchType.CHECK_IN;
        } else if (insertIndex == existingRecords.size()) {
            // Last punch of the day
            return PunchType.CHECK_OUT;
        } else {
            // Middle punch - determine based on surrounding punches
            AttendanceRecord previousPunch = existingRecords.get(insertIndex - 1);
            AttendanceRecord nextPunch = existingRecords.get(insertIndex);
            
            if (previousPunch.getPunchType() == PunchType.CHECK_IN && 
                nextPunch.getPunchType() == PunchType.CHECK_OUT) {
                // This is a middle punch, could be either check-in or check-out
                // For now, we'll make it check-out to maintain sequence
                return PunchType.CHECK_OUT;
            } else if (previousPunch.getPunchType() == PunchType.CHECK_IN) {
                return PunchType.CHECK_OUT;
            } else {
                return PunchType.CHECK_IN;
            }
        }
    }

    /**
     * Reorder existing records when a new bulk punch is inserted
     * This method updates the punch types and summaries to maintain consistency
     */
    private void reorderExistingRecordsForBulkPunch(Integer employeeId, 
                                               List<AttendanceRecord> existingRecords, 
                                               AttendanceRecord newRecord) {
        log.info("Reordering existing records for employee: {} after inserting new punch at: {}", 
                employeeId, newRecord.getPunchTime());
        
        // Get all records for the day including the new one
        LocalDateTime dayStart = getAttendanceDayStart(newRecord.getPunchTime());
        LocalDateTime dayEnd = getAttendanceDayEnd(newRecord.getPunchTime());
        
        List<AttendanceRecord> allRecords = attendanceRecordRepository
            .findByEmployeeIdAndTimeRange(employeeId, dayStart, dayEnd);
        
        // Sort by punch time
        allRecords.sort((a, b) -> a.getPunchTime().compareTo(b.getPunchTime()));
        
        // Reassign punch types based on position
        for (int i = 0; i < allRecords.size(); i++) {
            AttendanceRecord record = allRecords.get(i);
            PunchType newPunchType;
            
            if (i == 0) {
                newPunchType = PunchType.CHECK_IN;
            } else if (i == allRecords.size() - 1) {
                newPunchType = PunchType.CHECK_OUT;
            } else {
                // Middle punches - alternate between check-in and check-out
                //newPunchType = (i % 2 == 0) ? PunchType.CHECK_IN : PunchType.CHECK_OUT;
                newPunchType = PunchType.CHECK_OUT;
            }
            
            if (!record.getPunchType().equals(newPunchType)) {
                log.info("Updating punch type for record {} from {} to {}", 
                        record.getId(), record.getPunchType(), newPunchType);
                record.setPunchType(newPunchType);
                record.setRemarks(record.getRemarks() + " (Updated during bulk sync)");
                attendanceRecordRepository.save(record);
            }
        }
        
        // Update the daily summary with the correct first check-in and last check-out
        LocalDate attendanceDate = getAttendanceDate(newRecord.getPunchTime());
        AttendanceRecord firstPunch = allRecords.get(0);
        AttendanceRecord lastPunch = allRecords.get(allRecords.size() - 1);
        
        updateSummaryAsync(
            employeeId,
            attendanceDate,
            firstPunch.getPunchTime(),
            lastPunch.getPunchType() == PunchType.CHECK_OUT ? lastPunch.getPunchTime() : null
        );
        
        log.info("Completed reordering for employee: {} with {} total records", 
                employeeId, allRecords.size());
    }

    /**
     * Queue attendance record for external sync
     */
    private void queueForExternalSync(AttendanceRecord record) {
        try {
            List<Integer> unitIds  = environmentProperties.lucidAttendanceSyncUnits();
            if(unitIds.contains(record.getUnitId())) {
                externalAttendanceSyncService.queueAttendanceForSync(record.getId());
                log.info("Queued attendance record ignored {} for external sync", record.getId());
            }
        } catch (Exception e) {
            log.error("Failed to queue attendance record {} for external sync", record.getId(), e);
            // Don't fail the punch operation if sync fails
        }
    }

    private Integer getAndValidateEmployeeWithMultiImages(AttendancePunchRequest request) {
        try {
            // Create multi-image identification request
            BiometricMultiImageIdentificationRequestDTO biometricRequest = createMultiImageIdentificationRequest(request);
            log.info("recognise v2 request : {} " , new Gson().toJson(biometricRequest));
            // Call biometric service
            BiometricMultiImageIdentificationResponseDTO response = biometricService.identifyFaceFromMultipleImages(biometricRequest);
            BiometricContext.setContext(response.getRequestId());
            log.info("recognise v2 response : {} " , new Gson().toJson(response));
            

            
            // Validate response
            if (response == null || !"success".equals(response.getStatus()) || response.getResult() == null) {
                throw new AttendanceValidationException("Failed to identify Employee");
            }

            if(!StringUtils.isEmpty(response.getResult().getStatus())
                    && com.stpl.tech.util.AppConstants.IN_PROGRESS.equalsIgnoreCase(response.getResult().getStatus())){
                throw new BiometricRegistrationException(BiometricErrorCode.BIOMETRIC_IN_PROGRESS);
            }

            // Get employee ID from response
            Integer employeeId = Integer.parseInt(response.getResult().getEmployeeId());
            request.setEmployeeId(employeeId);

            // Get and validate employee
            EmployeeBasicDetail employee = userCacheService.getUserById(employeeId);
            if (employee == null) {
                throw new AttendanceException(AttendanceErrorCode.EMPLOYEE_NOT_FOUND);
            }
            if (!employee.getStatus().name().equals(com.stpl.tech.util.AppConstants.ACTIVE)) {
                throw new AttendanceException(AttendanceErrorCode.INACTIVE_EMPLOYEE);
            }

            return employeeId;
        } catch (Exception e) {
            //handleBiometricFailureWithImages(request, e);
            throw e;
        }
    }

    private BiometricMultiImageIdentificationRequestDTO createMultiImageIdentificationRequest(AttendancePunchRequest request) {
        List<BiometricMultiImageIdentificationRequestDTO.InputData> imageDataList = new ArrayList<>();
        String requestId =  RequestIdInterceptor.getCurrentRequestId();

        // Add additional images
        if (request.getAdditionalImages() != null) {
            for (AttendanceImageRequest additionalImage : request.getAdditionalImages()) {
                if(StringUtils.isEmpty(additionalImage.getBase64Image())){
                    log.info("empty base64 image recieved for additional image : {}", additionalImage.getImageType());
                    continue;
                }
                BiometricMultiImageIdentificationRequestDTO.InputData biometricMultiImageIdentificationRequestDTO = BiometricMultiImageIdentificationRequestDTO.InputData.
                        builder()
                        .data(additionalImage.getBase64Image())
                        .id(additionalImage.getId() != null ? additionalImage.getId() : UUID.randomUUID().toString())
                        .imageType(additionalImage.getImageType().name())
                        .build();
                imageDataList.add(biometricMultiImageIdentificationRequestDTO);
                additionalImage.setId(biometricMultiImageIdentificationRequestDTO.getId());
            }
            BiometricMultiImageIdentificationRequestDTO.InputData biometricMultiImageIdentificationRequestDTO = BiometricMultiImageIdentificationRequestDTO.InputData.
                    builder()
                    .data(request.getOriginalImage())
                    .id(UUID.randomUUID().toString())
                    .imageType("ORIGINAL")
                    .build();
            imageDataList.add(biometricMultiImageIdentificationRequestDTO);
        }

        BiometricMultiImageIdentificationRequestDTO.InputDataRequest inputDataRequest = BiometricMultiImageIdentificationRequestDTO.InputDataRequest.builder()
                .data(imageDataList)
                .build();

        return BiometricMultiImageIdentificationRequestDTO.builder()
                .model_id("attendance")
                .parameters(BiometricMultiImageIdentificationRequestDTO.Parameters.builder()
                        .unitId(request.getUnitId())
                        .metadata(BiometricMultiImageIdentificationRequestDTO.Metadata.builder()
                                .build())
                        .requestId(requestId)
                        .build())
                .input_data(inputDataRequest)
                .processing_options(new HashMap<>())
                .build();
    }

    private void processAndSaveImages(AttendancePunchRequest request, AttendanceRecord punchRecord ,
                                      String refId , EmployeeBasicDetail employee) {
        List<AttendanceImage> attendanceImages = new ArrayList<>();
        List<BiometricUpdateImagePathRequestDTO.ImageData> imageDataList = new ArrayList<>();
        // Upload original image
        String originalImageUrl = uploadOriginalImage(request, employee);
        punchRecord.setOriginalImageUrl(originalImageUrl);
        attendanceRecordRepository.save(punchRecord);
        // Save additional images only (main image is already saved in the main flow)
        if (request.getAdditionalImages() != null) {
            for (AttendanceImageRequest additionalImage : request.getAdditionalImages()) {
                if(StringUtils.isEmpty(additionalImage.getBase64Image())){
                    log.info("skipping empty base64 image recieved for additional image : {}", additionalImage.getImageType());
                    continue;
                }
                String imageUrl = uploadImageToS3(additionalImage.getBase64Image(), additionalImage.getImageType().name().toLowerCase());
                AttendanceImage image = AttendanceImage.builder()
                        .attendanceId(punchRecord.getId())
                        .imageType(additionalImage.getImageType())
                        .imagePath(imageUrl)
                        .createdAt(LocalDateTime.now())
                        .imageId(additionalImage.getId() != null ? additionalImage.getId() : UUID.randomUUID().toString())
                        .build();
                attendanceImages.add(image);
                imageDataList.add(BiometricUpdateImagePathRequestDTO.ImageData.builder()
                        .id(additionalImage.getId())
                        .imageUrl(imageUrl)
                        .imageType(additionalImage.getImageType().name())
                        .build());
            }
        }
        // Save all additional images
        if (!attendanceImages.isEmpty()) {
            attendanceImageRepository.saveAll(attendanceImages);
        }
        BiometricUpdateImagePathRequestDTO updateRequest = BiometricUpdateImagePathRequestDTO.builder()
                .refId(refId)
                .images(imageDataList)
                .build();

        log.info("update image v2  request :: {}" , new Gson().toJson(updateRequest));
        biometricService.updateImagePathMultiple(updateRequest);

    }

    private void handleBiometricFailureWithImages(AttendancePunchRequest request, Exception e , String refId) {
        CompletableFuture.runAsync(() -> {
            try {
                // Create update image path request with all images
                List<BiometricUpdateImagePathRequestDTO.ImageData> imageDataList = new ArrayList<>();

                if (request.getBase64Image() != null && CollectionUtils.isEmpty(request.getAdditionalImages())) {
                    try {
                        String timestamp = String.valueOf(System.currentTimeMillis());
                        String key = "attendance/failed/" + timestamp + ".jpg";
                        String cloudfrontKey = s3Service.uploadBase64Image(request.getBase64Image(), "master-service", key);
                        String failedImageUrl = s3Service.getCloudfrontUrl(cloudfrontKey);
                        log.info("Saved failed attendance image to S3 with key: {}", key);
                        BiometricUpdateImagePathRequestDTO updateRequest = BiometricUpdateImagePathRequestDTO.builder()
                                .refId(refId)
                                .imageUrl(failedImageUrl)
                                .build();
                        biometricService.updateImagePath(updateRequest);
                    } catch (Exception ex) {
                        log.error("Failed to save attendance image to S3: {}", ex.getMessage());
                    }
                }else{
                    if (request.getAdditionalImages() != null) {
                        for (AttendanceImageRequest additionalImage : request.getAdditionalImages()) {
                            if(StringUtils.isEmpty(additionalImage.getBase64Image())){
                                log.info("empty base64 image recieved for additional image : {}", additionalImage.getImageType());
                                continue;
                            }
                            imageDataList.add(BiometricUpdateImagePathRequestDTO.ImageData.builder()
                                    .id(additionalImage.getId())
                                    .imageUrl(uploadImageToS3(additionalImage.getBase64Image(), additionalImage.getImageType().name().toLowerCase()))
                                    .imageType(additionalImage.getImageType().name())
                                    .build());

                        }
                    }
                    BiometricUpdateImagePathRequestDTO updateRequest = BiometricUpdateImagePathRequestDTO.builder()
                            .refId(refId)
                            .images(imageDataList)
                            .build();

                    log.info("update image v2  request :: {}" , new Gson().toJson(updateRequest));
                    biometricService.updateImagePathMultiple(updateRequest);
                }

            } catch (Exception ex) {
                log.error("Failed to update image paths for failed attendance: {}", ex.getMessage());
            }
        }, asyncTaskExecutor);
    }

    private String uploadImageToS3(String base64Image, String type) {
        try {
            String timestamp = String.valueOf(System.currentTimeMillis());
            String key = "attendance/" + type + "/" + timestamp + ".jpg";
            String cloudfrontKey = s3Service.uploadBase64Image(base64Image, "master-service", key);
            return s3Service.getCloudfrontUrl(cloudfrontKey);
        } catch (Exception e) {
            log.error("Failed to upload image to S3: {}", e.getMessage());
            return null;
        }
    }
} 