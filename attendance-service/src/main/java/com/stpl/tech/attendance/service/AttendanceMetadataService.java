package com.stpl.tech.attendance.service;

import com.stpl.tech.attendance.dto.AttendanceMetadataResponse;
import com.stpl.tech.attendance.entity.EmpAttendanceMetadata;
import com.stpl.tech.attendance.enums.AttendanceAttributeType;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * Service interface for attendance metadata operations
 */
public interface AttendanceMetadataService {
    
    /**
     * Get metadata value for a specific department and attribute
     * @param deptId Department ID
     * @param attributeCode Attribute code
     * @return Metadata response with resolved value
     */
    AttendanceMetadataResponse getMetadataValue(Integer deptId, AttendanceAttributeType attributeCode);
    
    /**
     * Get all metadata for a department with fallback to defaults
     * @param deptId Department ID
     * @return Map of attribute code to metadata response
     */
    Map<AttendanceAttributeType, AttendanceMetadataResponse> getAllMetadataForDept(Integer deptId);
    
    /**
     * Get boolean value for boolean type attributes
     * @param deptId Department ID
     * @param attributeCode Attribute code
     * @return Boolean value or null if not found
     */
    Boolean getBooleanValue(Integer deptId, AttendanceAttributeType attributeCode);
    
    /**
     * Get numeric value for numeric type attributes
     * @param deptId Department ID
     * @param attributeCode Attribute code
     * @return BigDecimal value or null if not found
     */
    BigDecimal getNumericValue(Integer deptId, AttendanceAttributeType attributeCode);
    
    /**
     * Check if leave is allowed for a department
     * @param deptId Department ID
     * @return true if leave is allowed, false otherwise
     */
    boolean isLeaveAllowed(Integer deptId);
    
    /**
     * Check if OD is allowed for a department
     * @param deptId Department ID
     * @return true if OD is allowed, false otherwise
     */
    boolean isOdAllowed(Integer deptId);
    
    /**
     * Check if WFH is allowed for a department
     * @param deptId Department ID
     * @return true if WFH is allowed, false otherwise
     */
    boolean isWfhAllowed(Integer deptId);
    
    /**
     * Check if regularisation is allowed for a department
     * @param deptId Department ID
     * @return true if regularisation is allowed, false otherwise
     */
    boolean isRegularisationAllowed(Integer deptId);
    
    /**
     * Get leave credit cycle for a department
     * @param deptId Department ID
     * @return Leave credit cycle value
     */
    String getLeaveCreditCycle(Integer deptId);
    
    /**
     * Get total number of leaves for a department
     * @param deptId Department ID
     * @return Total number of leaves
     */
    BigDecimal getTotalNumberOfLeaves(Integer deptId);
    
    /**
     * Get payroll processing start day for a department
     * @param deptId Department ID
     * @return Payroll processing start day
     */
    BigDecimal getPayrollProcessingStartDay(Integer deptId);
    
    /**
     * Get payroll processing end day for a department
     * @param deptId Department ID
     * @return Payroll processing end day
     */
    BigDecimal getPayrollProcessingEndDay(Integer deptId);
    
    /**
     * Create or update metadata for a department
     * @param metadata Metadata entity to save
     * @return Saved metadata entity
     */
    EmpAttendanceMetadata saveMetadata(EmpAttendanceMetadata metadata);
    
    /**
     * Get all metadata entities for a department
     * @param deptId Department ID
     * @return List of metadata entities
     */
    List<EmpAttendanceMetadata> getAllMetadataEntitiesForDept(Integer deptId);
    
    /**
     * Refresh cache for a specific department
     * @param deptId Department ID
     */
    void refreshCacheForDept(Integer deptId);
    
    /**
     * Refresh all cache
     */
    void refreshAllCache();

    AttendanceMetadataResponse getDefaultMetadata(AttendanceAttributeType attributeCode);

    /**
     * Check if holidays are allowed for a department
     * @param deptId Department ID
     * @return true if holidays are allowed, false otherwise
     */
    boolean isHolidayAllowed(Integer deptId);
    
    /**
     * Get weekend days for a department
     * @param deptId Department ID
     * @return Array of weekend days (e.g., ["SATURDAY", "SUNDAY"])
     */
    String[] getWeekendDays(Integer deptId);
    
    /**
     * Check if fixed weekend is allowed for a department
     * @param deptId Department ID
     * @return true if fixed weekend is allowed, false otherwise
     */
    boolean isFixedWeekendAllowed(Integer deptId);
}


