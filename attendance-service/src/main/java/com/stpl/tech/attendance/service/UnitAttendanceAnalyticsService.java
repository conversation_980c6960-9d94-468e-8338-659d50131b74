package com.stpl.tech.attendance.service;

import com.stpl.tech.attendance.dto.UnitAttendanceAnalyticsDTO;

import java.time.LocalDate;

public interface UnitAttendanceAnalyticsService {
    
    /**
     * Get attendance analytics for all units managed by a manager for a specific date
     */
    UnitAttendanceAnalyticsDTO.ManagerAnalyticsResponseDTO getManagerUnitAnalytics(Integer managerId, LocalDate businessDate);
    
    /**
     * Get attendance analytics for a specific unit and date (cached)
     */
    UnitAttendanceAnalyticsDTO getUnitAnalytics(Integer unitId, LocalDate businessDate);
    
    /**
     * Update unit analytics cache when new attendance is punched
     * This method should be called whenever a new attendance record is created
     */
    void updateUnitAnalyticsCache(Integer unitId, LocalDate businessDate);
    
    /**
     * Clear all unit analytics cache (called by cron job)
     */
    void clearUnitAnalyticsCache();
} 