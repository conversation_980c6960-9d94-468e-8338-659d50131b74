package com.stpl.tech.attendance.service.impl;

import com.stpl.tech.attendance.cache.service.UnitCacheService;
import com.stpl.tech.attendance.cache.service.UserCacheService;
import com.stpl.tech.attendance.config.EnvironmentProperties;
import com.stpl.tech.attendance.dto.BiometricRegistrationDTO;
import com.stpl.tech.attendance.dto.DevicePairingRequest;
import com.stpl.tech.attendance.dto.DevicePairingResponse;
import com.stpl.tech.attendance.dto.EmployeeAttendanceMetadataDTO;
import com.stpl.tech.attendance.dto.EmployeeDetailsDTO;
import com.stpl.tech.attendance.dto.EmployeeMetadataDTO;
import com.stpl.tech.attendance.dto.AttendanceConfigDTO;
import com.stpl.tech.attendance.dto.RosteringDto.GenericFilterMetadataDTO;
import com.stpl.tech.attendance.dto.RosteringDto.FilterValueOptionDTO;
import com.stpl.tech.attendance.dto.UnitEligibilityDTO;
import com.stpl.tech.attendance.entity.ApplicationInstallationData;
import com.stpl.tech.attendance.entity.BiometricRegistration;
import com.stpl.tech.attendance.entity.EmpEligibilityMapping;
import com.stpl.tech.attendance.entity.EmpAttendanceBalanceData;
import com.stpl.tech.attendance.entity.RosteringEntity.Shift;
import com.stpl.tech.attendance.enums.BiometricStatus;
import com.stpl.tech.attendance.enums.EligibilityType;
import com.stpl.tech.attendance.enums.MappingStatus;
import com.stpl.tech.attendance.enums.MappingType;
import com.stpl.tech.attendance.model.DeviceDetail;
import com.stpl.tech.attendance.model.DeviceDetailAudit;
import com.stpl.tech.attendance.model.TransferRequestStatus;
import com.stpl.tech.attendance.repository.ApplicationInstallationDataRepository;
import com.stpl.tech.attendance.repository.BiometricRegistrationRepository;
import com.stpl.tech.attendance.repository.DeviceDetailAuditRepository;
import com.stpl.tech.attendance.repository.DeviceDetailRepository;
import com.stpl.tech.attendance.repository.EmpEligibilityMappingRepository;
import com.stpl.tech.attendance.repository.EmpAttendanceReserveDataRepository;
import com.stpl.tech.attendance.service.AttendanceMetadataService;
import com.stpl.tech.attendance.service.BiometricService;
import com.stpl.tech.attendance.service.EmpEligibilityService;
import com.stpl.tech.attendance.service.HolidayService;
import com.stpl.tech.attendance.service.MetadataService;
import com.stpl.tech.attendance.service.TransferService;
import com.stpl.tech.attendance.service.LeaveBalanceService;
import com.stpl.tech.attendance.service.UnitResolutionService;
import com.stpl.tech.attendance.util.AttendanceDateUtil;
import com.stpl.tech.attendance.util.DateTimeUtil;
import com.stpl.tech.attendance.exception.CafeLiveDashboardException;
import com.stpl.tech.attendance.util.ShiftHelper;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.domain.model.UnitStatus;
import com.stpl.tech.util.AppConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.StopWatch;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import com.stpl.tech.master.domain.model.UnitBasicDetail;

import static com.stpl.tech.attendance.constants.ApiConstants.CAFE_MANAGER_DESIGNATION_ID;

@Slf4j
@Service
@RequiredArgsConstructor
public class MetadataServiceImpl implements MetadataService {

    private final UserCacheService userCache;
    private final DeviceDetailRepository deviceDetailRepository;
    private final DeviceDetailAuditRepository deviceDetailAuditRepository;
    private final ApplicationInstallationDataRepository applicationInstallationDataRepository;
    private final BiometricRegistrationRepository biometricRegistrationRepository;
    private final EnvironmentProperties environmentProperties;
    private final UnitCacheService unitCacheService;
    private final EmployeeSearchService employeeSearchService;
    private final BiometricService biometricService;
    private final EmpEligibilityMappingRepository empEligibilityMappingRepository;
    private final EmpEligibilityService empEligibilityService;
    private final TransferService transferService;
    private final LeaveBalanceService leaveBalanceService;
    private final EmpAttendanceReserveDataRepository empLeaveDetailDataRepository;
    private final ShiftHelper shiftHelper;
    private final HolidayService holidayService;
    private final AttendanceMetadataService attendanceMetadataService;
    private final UnitResolutionService unitResolutionService;

    @Override
    public Page<EmployeeMetadataDTO> getEmployeeMetadata(String searchTerm, int page, int size) {
        List<EmployeeBasicDetail> activeEmployees = employeeSearchService.searchEmployees(searchTerm);
        // Apply pagination
        int start = page * size;
        int end = Math.min(start + size, activeEmployees.size());

        List<EmployeeMetadataDTO> pagedResults = activeEmployees.subList(start, end).stream()
                .map(this::mapToEmployeeMetadataDTO)
                .collect(Collectors.toList());

        return new PageImpl<>(pagedResults, PageRequest.of(page, size), activeEmployees.size());
    }

    private EmployeeMetadataDTO mapToEmployeeMetadataDTO(EmployeeBasicDetail employee) {
        Optional<BiometricRegistrationDTO> biometricRegistration = Optional.empty();
        BiometricRegistrationDTO registration = biometricRegistration.orElse(null);

        // Get the latest transfer status for the employee
        TransferRequestStatus transferStatus = transferService.getLatestTransferStatus(String.valueOf(employee.getId()))
                .orElse(TransferRequestStatus.NOT_FOUND);

        return EmployeeMetadataDTO.builder()
                .empId(employee.getId())
                .empCode(employee.getEmployeeCode())
                .empName(employee.getName())
                .designation(employee.getDesignation())
                .companyId(employee.getCompanyId())
                .biometricRegistrationStatus(biometricRegistration.isPresent() ? biometricRegistration.get().getStatus() :
                        BiometricStatus.NOT_FOUND)
                .transferRequestStatus(transferStatus)
                .registrationDate(registration!=null ? registration.getUpdatedAt() : null)
                .registrationDeviceId(registration!=null ? registration.getDeviceId() : null)
                .status(employee.getStatus().name())
                .imageUrl(registration != null ? registration.getImageUrl() : null)
                .registrationImageUrl(employee.getImagekey())
                .build();
    }

    private Optional<BiometricRegistrationDTO> getBiometricRegistration(Integer employeeId) {
        /*return biometricRegistrationRepository.findByEmpIdAndStatusIn(employeeId.toString(),new ArrayList<>(Arrays.asList(BiometricStatus.APPROVED,
                BiometricStatus.PENDING)));*/
        return Optional.ofNullable(biometricService.getBiometricRegistration(String.valueOf(employeeId)));
    }

    @Override
    @Transactional
    public DevicePairingResponse pairDevice(DevicePairingRequest request) {
        // Check if device exists
        Optional<DeviceDetail> existingDevice = deviceDetailRepository.findByDeviceId(request.getDeviceId());
        
        if (existingDevice.isPresent()) {
            DeviceDetail deviceDetail = existingDevice.get();
            
            // If device is already paired with the same unit, return success
            if (deviceDetail.getUnitId().equalsIgnoreCase(request.getUnitId().toString())) {
                return DevicePairingResponse.builder()
                    .success(true)
                    .message("Device is already paired with this unit")
                    .existingPairing(false)
                        .existingUnitId(deviceDetail.getUnitId())
                        .existingUnitName(unitCacheService.getUnitBasicDetail(Integer.parseInt(deviceDetail.getUnitId())).getName())
                    .build();
            }
            
            // If device is paired with a different unit and override is not requested
            if (!request.isOverrideExisting()) {
                return DevicePairingResponse.builder()
                    .success(false)
                    .message("Device is already paired with another unit.")
                    .existingPairing(true)
                    .existingUnitId(deviceDetail.getUnitId())
                        .existingUnitName(unitCacheService.getUnitBasicDetail(Integer.parseInt(deviceDetail.getUnitId())).getName())
                    .build();
            }

            // Create audit log
            DeviceDetailAudit audit = DeviceDetailAudit.builder()
                    .deviceId(deviceDetail.getDeviceId())
                    .oldUnitId(deviceDetail.getUnitId())
                    .newUnitId(request.getUnitId().toString())
                    .action("OVERRIDE")
                    .timestamp(DateTimeUtil.now())
                    .build();


            // Handle override case
            deviceDetail.setLastUpdatedAt(DateTimeUtil.now());
            deviceDetail.setUnitId(request.getUnitId().toString());

            deviceDetailAuditRepository.save(audit);
            
            deviceDetailRepository.save(deviceDetail);
        } else {
            // Create new device
            DeviceDetail deviceDetail = DeviceDetail.builder()
                    .deviceId(request.getDeviceId())
                    .os(request.getOs())
                    .version(request.getVersion())
                    .unitId(request.getUnitId().toString())
                    .createdAt(DateTimeUtil.now())
                    .lastUpdatedAt(DateTimeUtil.now())
                    .build();
            deviceDetailRepository.save(deviceDetail);
        }

        // Handle application installation data
        Optional<ApplicationInstallationData> existingInstallation =
            applicationInstallationDataRepository.findByMachineIdAndStatus(
                request.getDeviceId(), 
                AppConstants.ACTIVE
            );

        if (existingInstallation.isPresent()) {
            ApplicationInstallationData installation = existingInstallation.get();
            if (!installation.getUnitId().equals(request.getUnitId().intValue())) {
                // Deactivate old installation
                installation.setStatus(AppConstants.IN_ACTIVE);
                applicationInstallationDataRepository.save(installation);

                // Create new installation
                createNewInstallation(request);
            }
        } else {
            createNewInstallation(request);
        }

        return DevicePairingResponse.builder()
            .success(true)
            .message("Device paired successfully")
            .existingPairing(false)
            .build();
    }

    private void createNewInstallation(DevicePairingRequest request) {
        Optional<ApplicationInstallationData> existingInstallation =
                applicationInstallationDataRepository.findByMachineId(
                        request.getDeviceId()
                );
        if(existingInstallation.isPresent()){
            existingInstallation.get().setUnitId(request.getUnitId().intValue());
            existingInstallation.get().setUpdatedDate(DateTimeUtil.now());
            existingInstallation.get().setUpdatedBy(request.getPairedBy().toString());
            existingInstallation.get().setStatus(AppConstants.ACTIVE);
            applicationInstallationDataRepository.save(existingInstallation.get());
        }else{
            ApplicationInstallationData newInstallation = ApplicationInstallationData.builder()
                    .machineId(request.getDeviceId())
                    .unitId(request.getUnitId().intValue())
                    .createdDate(DateTimeUtil.now())
                    .createdBy(request.getPairedBy().toString())
                    .osVersion(request.getOs())
                    .status(AppConstants.ACTIVE)
                    .build();
            applicationInstallationDataRepository.save(newInstallation);
        }
    }

    @Override
    public boolean isDevicePairedWithUnit(String deviceId, Long unitId) {
        // Check if device exists and is paired with the given unit
        Optional<DeviceDetail> deviceDetail = deviceDetailRepository.findByDeviceId(deviceId);
        if (deviceDetail.isEmpty()) {
            return false;
        }

        // Check if device is paired with the given unit
        return deviceDetail.get().getUnitId().equals(unitId.toString());
    }

    @Override
    @Transactional(readOnly = true)
    public Page<EmployeeMetadataDTO> getEmployeesWithBiometric(String searchTerm, int page, int size) {
        List<EmployeeBasicDetail> allEmployees = userCache.getAllUserCache().values()
                .stream()
                .toList();

        // Filter active employees with biometric registration
        List<EmployeeBasicDetail> activeEmployees = allEmployees.stream()
                .filter(emp -> AppConstants.ACTIVE.equals(emp.getStatus().name()))
                .collect(Collectors.toList());

        // Get all employee IDs
        List<Integer> employeeIds = activeEmployees.stream()
                .map(EmployeeBasicDetail::getId)
                .collect(Collectors.toList());

        // Batch fetch all biometric registrations
        List<BiometricRegistration> biometricRegistrations = biometricRegistrationRepository
                .findByEmpIdInAndStatus(employeeIds.stream().map(String::valueOf).collect(Collectors.toList()), BiometricStatus.APPROVED);

        // Create a map of employee ID to biometric registration
        Map<String, BiometricRegistration> registrationMap = biometricRegistrations.stream()
                .collect(Collectors.toMap(
                    BiometricRegistration::getEmpId,
                    registration -> registration
                ));

        // Filter employees with approved biometric registration
        List<EmployeeBasicDetail> employeesWithBiometric = activeEmployees.stream()
                .filter(emp -> registrationMap.containsKey(String.valueOf(emp.getId())))
                .collect(Collectors.toList());

        // Apply search filter if provided
        if (org.springframework.util.StringUtils.hasText(searchTerm)) {
            employeesWithBiometric = employeesWithBiometric.stream()
                    .filter(emp ->
                            (emp.getEmployeeCode() != null && emp.getEmployeeCode().equals(searchTerm)) ||
                            emp.getName().toLowerCase().startsWith(searchTerm.toLowerCase())
                            || String.valueOf(emp.getId()).equals(searchTerm)
                    )
                    .toList();
        }

        // Apply pagination
        int start = page * size;
        int end = Math.min(start + size, employeesWithBiometric.size());

        List<EmployeeMetadataDTO> pagedResults = employeesWithBiometric.subList(start, end).stream()
                .map(this::mapToEmployeeMetadataDTO)
                .collect(Collectors.toList());

        return new PageImpl<>(pagedResults, PageRequest.of(page, size), employeesWithBiometric.size());
    }

    @Override
    @Transactional(readOnly = true)
    public List<Integer> getManagedUnitIds(Integer empId) {
        log.info("Getting managed unit IDs for employee: {}", empId);
        EmployeeBasicDetail employee = userCache.getUserById(empId);
        Set<Integer> managedUnitIds = new HashSet<>();


        // Additional logic for cafe managers with locationCode
        if (employee != null && employee.getDesignationId().equals(CAFE_MANAGER_DESIGNATION_ID) &&
                employee.getLocCode() != null && employee.getLocCode() > 0) {
            Integer unitId = employee.getLocCode();
            // Verify this unit exists
            UnitBasicDetail locationUnit = unitCacheService.getUnitBasicDetail(unitId);
            if (locationUnit != null) {
                managedUnitIds.add(unitId);
                log.info("Added unit {} to managed units for cafe manager {} based on locationCode {}",
                        unitId, empId, employee.getLocCode());
            } else {
                log.warn("Unit with ID {} (from locationCode) not found for cafe manager {}",
                        unitId, empId);
            }

        }else{
            Map<Integer, com.stpl.tech.master.domain.model.Unit> unitMap = unitCacheService.getAllUnitCache();
            // Get units where employee is a manager (existing logic)
            unitMap.values().forEach(unit -> {
                if(!unit.getStatus().equals(UnitStatus.ACTIVE)){
                    return;
                }
             Integer unitManagerId = Objects.nonNull(unit.getUnitManager()) ? unit.getUnitManager().getId() : -1;
             Integer unitCafeManagerId = Objects.nonNull(unit.getUnitCafeManager()) ? unit.getUnitCafeManager() : -1;
             Integer cafeManagerId = Objects.nonNull(unit.getCafeManager()) ? unit.getCafeManager().getId() : -1;
                if (empId.equals(cafeManagerId) || empId.equals(unitManagerId) || empId.equals(unitCafeManagerId)) {
                    managedUnitIds.add(unit.getId());
                }
            });
            // Add units based on employee eligibility mappings (CITY and REGION)
            List<Integer> eligibilityBasedUnits = empEligibilityService.getUnitsFromEligibilityMappings(empId);
            managedUnitIds.addAll(eligibilityBasedUnits);
        }

        log.info("Found {} managed units for employee: {}", managedUnitIds.size(), empId);
        return new ArrayList<>(managedUnitIds);
    }

    @Override
    @Transactional(readOnly = true)
    //@Cacheable(value = "employeeHierarchy", key = "#empId")
    public List<EmployeeMetadataDTO> getEmployeeHierarchy(Integer empId) {
        log.info("Fetching team metadata for manager with empId: {}", empId);

        try {
            // 1. Get the manager's details
            EmployeeBasicDetail manager = userCache.getUserById(empId);
            if (manager == null) {
                log.warn("Manager not found with empId: {}", empId);
                return new ArrayList<>();
            }
            Set<Integer> employeeIds = new HashSet<>();
            LocalDate currentDate = LocalDate.now();

            // 2. Get all units where this employee is a manager
            List<Integer> managedUnitIds = getManagedUnitIds(empId);

            // Get employees who are actually assigned to shifts in managed units
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            // Get employees who are actually assigned to shifts in managed units
            employeeIds = unitResolutionService.getEmployeesForUnits(managedUnitIds, currentDate);
            stopWatch.stop();
            log.info("Time taken to fetch employees for units: {} ms", stopWatch.getTime());
            Map<Integer, EmployeeBasicDetail> employeeMap = userCache.getAllUserCache();
            // 5. Fetch employee details and create DTOs
            return employeeIds.stream()
                    .map(id -> {
                        EmployeeBasicDetail employee = employeeMap.get(id);
                        if (employee != null) {
                            return mapToEmployeeMetadataDTO(employee);
                        }
                        return null;
                    })
                    .filter(Objects::nonNull)
                    .toList();


        } catch (Exception e) {
            log.error("Error fetching employee hierarchy for empId: {}", empId, e);
            return new ArrayList<>();
        }
    }

    private List<EmployeeBasicDetail> getEmployeeHierarchyEmployees(Integer empId) {
        log.info("Fetching team metadata for manager with empId: {}", empId);

        try {
            // 1. Get the manager's details
            EmployeeBasicDetail manager = userCache.getUserById(empId);
            if (manager == null) {
                log.warn("Manager not found with empId: {}", empId);
                return new ArrayList<>();
            }
            Set<Integer> employeeIds = new HashSet<>();
            LocalDate currentDate = LocalDate.now();

            // 2. Get all units where this employee is a manager
            List<Integer> managedUnitIds = getManagedUnitIds(empId);

            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            // Get employees who are actually assigned to shifts in managed units
            employeeIds = unitResolutionService.getEmployeesForUnits(managedUnitIds, currentDate);
            stopWatch.stop();
            log.info("Time taken to fetch employees for units: {} ms", stopWatch.getTime());
            Map<Integer, EmployeeBasicDetail> employeeMap = userCache.getAllUserCache();
            // 5. Fetch employee details and create DTOs
            return employeeIds.stream()
                    .map(id -> {
                        EmployeeBasicDetail employee = employeeMap.get(id);
                        if (employee != null) {
                            return employee;
                        }
                        return null;
                    })
                    .filter(Objects::nonNull)
                    .toList();


        } catch (Exception e) {
            log.error("Error fetching employee hierarchy for empId: {}", empId, e);
            return new ArrayList<>();
        }
    }

    /**
     * Evict the employee hierarchy cache for a specific employee
     */
    @CacheEvict(value = "employeeHierarchy", key = "#empId")
    public void evictEmployeeHierarchyCache(Integer empId) {
        log.debug("Evicting employee hierarchy cache for empId: {}", empId);
    }

    /**
     * Evict the eligibility units cache for a specific employee
     */
    @CacheEvict(value = "eligibilityUnits", key = "#empId")
    public void evictEligibilityUnitsCache(Integer empId) {
        log.debug("Evicting eligibility units cache for empId: {}", empId);
    }


    /**
     * Apply search filter and pagination to a list of employees
     * @param employees List of employees to filter and paginate
     * @param searchTerm Search term to filter by (can be null)
     * @param page Page number (0-based)
     * @param size Page size
     * @return Page of filtered and paginated employees
     */
    @Override
    public Page<EmployeeMetadataDTO> applySearchAndPagination(List<EmployeeMetadataDTO> employees, String searchTerm, int page, int size) {
        List<EmployeeMetadataDTO> filteredEmployees = new ArrayList<>(employees);
        
        // Apply search filter if provided
        if (searchTerm != null && !searchTerm.trim().isEmpty()) {
            String searchTermLower = searchTerm.toLowerCase().trim();
            filteredEmployees = filteredEmployees.stream()
                .filter(emp ->
                    (emp.getEmpCode() != null && emp.getEmpCode().toLowerCase().equalsIgnoreCase(searchTermLower)) ||
                    (emp.getEmpName() != null && emp.getEmpName().toLowerCase().startsWith(searchTermLower)) ||
                    (emp.getEmpId() != null && emp.getEmpId().toString().equalsIgnoreCase(searchTermLower))
                )
                .collect(Collectors.toList());
            log.info("After search filter, found {} employees", filteredEmployees.size());
        }

        // Apply pagination
        int start = page * size;
        int end = Math.min(start + size, filteredEmployees.size());
        List<EmployeeMetadataDTO> pagedResults = start < filteredEmployees.size()
            ? filteredEmployees.subList(start, end)
            : new ArrayList<>();

        return new PageImpl<>(pagedResults, PageRequest.of(page, size), filteredEmployees.size());
    }


    private Page<EmployeeMetadataDTO> applySearchAndPaginationEmpDetail(List<EmployeeBasicDetail> employees,
                                                                       String searchTerm, int page, int size) {
        List<EmployeeBasicDetail> filteredEmployees = new ArrayList<>(employees);

        // Apply search filter if provided
        if (searchTerm != null && !searchTerm.trim().isEmpty()) {
            String searchTermLower = searchTerm.toLowerCase().trim();
            filteredEmployees = filteredEmployees.stream()
                    .filter(emp ->
                            (emp.getEmployeeCode() != null && emp.getEmployeeCode().toLowerCase().equalsIgnoreCase(searchTermLower)) ||
                                    (emp.getName() != null && emp.getName().toLowerCase().startsWith(searchTermLower)) ||
                                    (emp.getId() > 0 && String.valueOf(emp.getId()).equalsIgnoreCase(searchTermLower))
                    )
                    .collect(Collectors.toList());
            log.info("After search filter, found {} employees", filteredEmployees.size());
        }
        List<EmployeeMetadataDTO> employeeMetadataDTOs = filteredEmployees.stream()
                .map(this::mapToEmployeeMetadataDTO)
                .collect(Collectors.toList());
        log.info("Converted {} employee IDs to EmployeeMetadataDTO objects", employeeMetadataDTOs.size());
        // Apply pagination
        int start = page * size;
        int end = Math.min(start + size, employeeMetadataDTOs.size());
        List<EmployeeMetadataDTO> pagedResults = start < employeeMetadataDTOs.size()
                ? employeeMetadataDTOs.subList(start, end)
                : new ArrayList<>();

        return new PageImpl<>(pagedResults, PageRequest.of(page, size), employeeMetadataDTOs.size());
    }

    @Override
    @Transactional(readOnly = true)
    public Page<EmployeeMetadataDTO> getManagerTeamMetadata(Integer empId, String searchTerm, int page, int size) {
        log.info("Fetching team metadata for manager with empId: {}", empId);

        List<EmployeeMetadataDTO> employeeMetadata =  getEmployeeHierarchy(empId);
        Map<Integer, EmployeeBasicDetail> employeeMap = userCache.getAllUserCache();
        if (employeeMetadata.isEmpty()) {
            log.info("No units found where employee {} is a manager", empId);
            EmployeeBasicDetail employee = employeeMap.get(empId);
            if (employee != null) {
                employeeMetadata.add(mapToEmployeeMetadataDTO(employee));
            }
        }

        log.info("Found {} employees in manager's team", employeeMetadata.size());
        return applySearchAndPagination(employeeMetadata, searchTerm, page, size);
    }

    @Override
    @Transactional(readOnly = true)
    public List<UnitEligibilityDTO> getEmployeeAttendanceEligibleUnits(String empId) {
        log.info("Fetching attendance eligible units for employee: {}", empId);

        // Get all active attendance eligibility mappings for the employee
        List<EmpEligibilityMapping> eligibilityMappings = empEligibilityMappingRepository
            .findByEmpIdAndEligibilityTypeAndStatus(
                empId,
                EligibilityType.ATTENDANCE,
                MappingStatus.ACTIVE
            );

        // Filter mappings that are for units and are currently active (within date range)
        LocalDate currentDate = LocalDate.now();
        log.info("Current date: {}", currentDate);
        List<EmpEligibilityMapping> activeUnitMappings = eligibilityMappings.stream()
            .filter(mapping -> mapping.getMappingType() == MappingType.UNIT)
            .filter(mapping -> isMappingActive(mapping, currentDate))
            .toList();

        // Convert to DTOs with unit details
        List<UnitEligibilityDTO> unitEligibilities = activeUnitMappings.stream()
            .map(mapping -> {
                try {
                    Integer unitId = Integer.parseInt(mapping.getValue());
                    com.stpl.tech.master.domain.model.UnitBasicDetail unitDetail = unitCacheService.getUnitBasicDetail(unitId);

                    if (unitDetail != null) {
                        return UnitEligibilityDTO.builder()
                            .unitId(unitId)
                            .unitName(unitDetail.getName())
                            .unitCode(unitDetail.getReferenceName())
                            .startDate(mapping.getStartDate())
                            .endDate(mapping.getEndDate())
                            .status(mapping.getStatus().name())
                            .build();
                    } else {
                        log.warn("Unit details not found for unitId: {}", unitId);
                        return null;
                    }
                } catch (NumberFormatException e) {
                    log.error("Invalid unit ID format in mapping: {}", mapping.getValue());
                    return null;
                }
            })
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

        log.info("Found {} eligible units for employee: {}", unitEligibilities.size(), empId);
        return unitEligibilities;
    }

    @Override
    //@Cacheable(value = "eligibilityUnits", key = "#empId")
    @Transactional(readOnly = true)
    public Map<String, List<UnitEligibilityDTO>> getEmployeeAttendanceEligibleUnitsBatch(List<String> empIds) {
        log.info("Fetching attendance eligible units for {} employees in batch", empIds.size());

        if (empIds == null || empIds.isEmpty()) {
            return new HashMap<>();
        }

//        // Get all active attendance eligibility mappings for all employees in one query
//        List<EmpEligibilityMapping> allEligibilityMappings = empEligibilityMappingRepository
//            .findByEmpIdInAndMappingTypeAndEligibilityTypeAndStatus(
//                empIds,
//                MappingType.UNIT,
//                EligibilityType.ATTENDANCE,
//                MappingStatus.ACTIVE
//            );
        //List<Integer> unitIds = new ArrayList<>();
//        empIds.forEach(empId -> {
//            unitIds.add(unitResolutionService.getUnitIdForEmployee(Integer.parseInt(empId), LocalDate.now()));
//                });
//        log.info("Found {} unique unitIds for {} employees", unitIds.size(), empIds.size());

        // Filter mappings that are currently active (within date range)
        //LocalDate currentDate = LocalDate.now();
//        List<EmpEligibilityMapping> activeUnitMappings = allEligibilityMappings.stream()
//            .filter(mapping -> isMappingActive(mapping, currentDate))
//            .toList();

//        // Group mappings by employee ID
//        Map<String, List<EmpEligibilityMapping>> mappingsByEmployee = activeUnitMappings.stream()
//            .collect(Collectors.groupingBy(EmpEligibilityMapping::getEmpId));

        // Convert to DTOs with unit details for each employee
        Map<String, List<UnitEligibilityDTO>> result = new HashMap<>();
        Map<Integer , UnitBasicDetail> unitMap = unitCacheService.getAllUnitBasicDetailMap();
        for (String empId : empIds) {
            List<Integer> unitIds = new ArrayList<>();
            Integer unitId = unitResolutionService.getUnitIdForEmployee(Integer.parseInt(empId), LocalDate.now());
            unitIds.add(unitId);
            //List<EmpEligibilityMapping> employeeMappings = mappingsByEmployee.getOrDefault(empId, new ArrayList<>());

            List<UnitEligibilityDTO> unitEligibilities = unitIds.stream()
                .map(id -> {
                    try {
                        UnitBasicDetail unitDetail = unitMap.get(id);

                        if (unitDetail != null) {
                            return UnitEligibilityDTO.builder()
                                .unitId(unitId)
                                .unitName(unitDetail.getName())
                                .unitCode(unitDetail.getReferenceName())
                                .status(unitDetail.getStatus().name())
                                .build();
                        } else {
                            log.warn("Unit details not found for unitId: {}", unitId);
                            return null;
                        }
                    } catch (NumberFormatException e) {
                        log.error("Invalid unit ID format in mapping: {}", empId);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

            result.put(empId, unitEligibilities);
        }

        log.info("Found eligible units for {} employees in batch operation", result.size());
        return result;
    }

    @Override
    @Transactional(readOnly = true)
    public EmployeeDetailsDTO getEmployeeDetails(String empId) {
        log.info("Getting employee details for employee: {}", empId);

        // Get employee basic details from cache
        EmployeeBasicDetail employee = userCache.getUserById(Integer.parseInt(empId));
        if (employee == null) {
            log.warn("Employee not found with empId: {}", empId);
            throw new RuntimeException("Employee not found");
        }

        // Get biometric registration status
        BiometricStatus biometricStatus = BiometricStatus.NOT_FOUND;
        Optional<BiometricRegistrationDTO> biometricRegistration = getBiometricRegistration(employee.getId());
        if (biometricRegistration.isPresent()) {
            biometricStatus = biometricRegistration.get().getStatus();
        }

        // Check if employee has approval eligibility mapping
        //boolean hasApprovalEligibilityMapping = checkApprovalEligibilityMapping(empId);
        boolean hasApprovalEligibilityMapping = true;

        // Get real leave balance data from database
        EmployeeAttendanceMetadataDTO empAttendanceMetadata;
        try {
            Integer employeeId = Integer.parseInt(empId);
            EmpAttendanceBalanceData attendanceBalanceData = leaveBalanceService.getAttendanceBalanceBalance(employeeId);
            if(attendanceBalanceData == null){
                attendanceBalanceData = new EmpAttendanceBalanceData();
            }
            empAttendanceMetadata = EmployeeAttendanceMetadataDTO.createAttendanceMetadataWithDept(attendanceBalanceData,
                    empLeaveDetailDataRepository, employee.getDepartmentId(),
                            attendanceMetadataService, holidayService);
            if(empAttendanceMetadata == null){
                empAttendanceMetadata = new EmployeeAttendanceMetadataDTO();
            }
            log.info("Successfully retrieved real leave balance data for employee: {}", empId);
        } catch (Exception e) {
            log.warn("Failed to retrieve leave balance data for employee: {}, using default values. Error: {}", empId, e);
            throw new RuntimeException("Failed to retrieve attendance balance data");
        }
        Shift shift = shiftHelper.getShiftForEmployee(Integer.valueOf(empId),DateTimeUtil.now().toLocalDate());
        EmployeeDetailsDTO.EmployeeShift employeeShift = EmployeeDetailsDTO.EmployeeShift.builder()
                .shiftId(shift.getShiftId())
                .shiftName(shift.getShiftName())
                .shiftStartTime(shift.getStartTime().toLocalTime())
                .shiftEndTime(shift.getEndTime().toLocalTime())
                .build();




        return EmployeeDetailsDTO.builder()
                .empId(empId)
                .empName(employee.getName())
                .empCode(employee.getEmployeeCode())
                .empContact(employee.getContactNumber())
                .empEmail(employee.getEmailId())
                .biometricRegistrationStatus(biometricStatus)
                .hasApprovalEligibilityMapping(hasApprovalEligibilityMapping)
                .empAttendanceMetadata(empAttendanceMetadata)
                .employeeShift(employeeShift)
                .build();
    }

    /**
     * Check if employee has any approval eligibility mapping
     */
    private boolean checkApprovalEligibilityMapping(String empId) {
        List<EmpEligibilityMapping> approvalMappings = empEligibilityMappingRepository
            .findByEmpIdAndEligibilityTypeAndStatus(
                empId,
                EligibilityType.APPROVAL,
                MappingStatus.ACTIVE
            );

        // Check if any approval mappings exist and are currently active
        LocalDate currentDate = LocalDate.now();
        return approvalMappings.stream()
            .anyMatch(mapping -> isMappingActive(mapping, currentDate));
    }

    /**
     * Utility method to check if a mapping is currently active based on date range
     * Compares only the date part, ignoring any time components
     */
    private boolean isMappingActive(EmpEligibilityMapping mapping, LocalDate currentDate) {
        // Check start date: mapping is active if start date is null, before current date, or equal to current date
        boolean startDateValid = mapping.getStartDate() == null ||
                                mapping.getStartDate().isBefore(currentDate) ||
                                mapping.getStartDate().isEqual(currentDate);

        // Check end date: mapping is active if end date is null or after current date
        boolean endDateValid = mapping.getEndDate() == null ||
                              mapping.getEndDate().isAfter(currentDate);

        return startDateValid && endDateValid;
    }

    @Override
    public AttendanceConfigDTO getAttendanceConfig() {
        return AttendanceConfigDTO.builder()
                .attendanceRegistrationIsLivelinessCheck(environmentProperties.isLivelinessCheckEnabledForRegistration())
                .attendanceRegistrationStartAngle(environmentProperties.getStartAngleForAttendanceRegistration())
                .attendanceRegistrationEndAngle(environmentProperties.getEndAngleForAttendanceRegistration())
                .attendanceRegistrationAngleChangeInterval(environmentProperties.getAngleChangeIntervalForAttendanceRegistration())
                .attendanceRegistrationAngleChangeThreshold(environmentProperties.getAngleChangeThresholdForAttendanceRegistration())
                .attendanceRegistrationLivelinessChallenges(environmentProperties.getLivelinessChallengesForAttendanceRegistration())
                .attendanceRegistrationIsMultiFrameMode(environmentProperties.isMultiFrameModeForAttendanceRegistration())
                .attendanceRegistrationFrameTimeLimit(environmentProperties.getFrameTimeLimitForAttendanceRegistration())
                .attendanceRegistrationFramesList(environmentProperties.getFramesListForAttendanceRegistration())
                .attendanceRegistrationGoodLightThreshold(environmentProperties.getGoodLightThresholdForAttendanceRegistration())
                .attendanceRegistrationBlurThreshold(environmentProperties.getBlurThresholdForAttendanceRegistration())
                .attendanceRegistrationFaceSizeThreshold(environmentProperties.getFaceSizeThresholdForAttendanceRegistration())
                .attendanceRegistrationDeltaThreshold(environmentProperties.getDeltaThresholdForAttendanceRegistration())
                .attendanceRegistrationMaxDeltaThreshold(environmentProperties.getMaxDeltaThresholdForAttendanceRegistration())
                .attendancePunchinIsLivelinessCheck(environmentProperties.isLivelinessCheckEnabledForPunchIn())
                .attendancePunchinStartAngle(environmentProperties.getStartAngleForAttendancePunchIn())
                .attendancePunchinEndAngle(environmentProperties.getEndAngleForAttendancePunchIn())
                .attendancePunchinAngleChangeInterval(environmentProperties.getAngleChangeIntervalForAttendancePunchIn())
                .attendancePunchinAngleChangeThreshold(environmentProperties.getAngleChangeThresholdForAttendancePunchIn())
                .attendancePunchinLivelinessChallenges(environmentProperties.getLivelinessChallengesForAttendancePunchIn())
                .attendancePunchinIsMultiFrameMode(environmentProperties.isMultiFrameModeForAttendancePunchIn())
                .attendancePunchinFrameTimeLimit(environmentProperties.getFrameTimeLimitForAttendancePunchIn())
                .attendancePunchinFramesList(environmentProperties.getFramesListForAttendancePunchIn())
                .attendancePunchinGoodLightThreshold(environmentProperties.getGoodLightThresholdForAttendancePunchIn())
                .attendancePunchinBlurThreshold(environmentProperties.getBlurThresholdForAttendancePunchIn())
                .attendancePunchinFaceSizeThreshold(environmentProperties.getFaceSizeThresholdForAttendancePunchIn())
                .attendancePunchinDeltaThreshold(environmentProperties.getDeltaThresholdForAttendancePunchIn())
                .attendancePunchinMaxDeltaThreshold(environmentProperties.getMaxDeltaThresholdForAttendancePunchIn())
                .attendanceAdminDefaultContact(environmentProperties.getAttendanceAdminDefaultContact())
                .attendanceAdminDefaultEmail(environmentProperties.getAttendanceAdminDefaultEmail())
                .attendancePunchinRotationThreshold(environmentProperties.getAttendancePunchInRotationThreshold())
                .attendancePunchinBrightnessThreshold(environmentProperties.getAttendancePunchInBrightnessThreshold())
                .attendanceRegistrationBrightnessThreshold(environmentProperties.getAttendanceRegistrationBrightnessThreshold())
                .attendanceRegistrationRotationThreshold(environmentProperties.getAttendanceRegistrationRotationThreshold())
                .attendancePunchinContrastThreshold(environmentProperties.getAttendancePunchInContrastThreshold())
                .attendanceRegistrationContrastThreshold(environmentProperties.getAttendanceRegistrationContrastThreshold())
                .build();
    }

    @Override
    @Transactional(readOnly = true)
    public GenericFilterMetadataDTO getCafeLiveDashboardFilterMetadata(Integer userId) {
        log.info("Getting cafe live dashboard filter metadata for userId: {}", userId);

        try {
            // Get all units where this employee is a manager
            List<Integer> managedUnitIds = getManagedUnitIds(userId);
            
            if (managedUnitIds.isEmpty()) {
                log.info("No managed units found for userId: {}", userId);
                return GenericFilterMetadataDTO.builder()
                    .availableFilters(new ArrayList<>())
                    .appliedFilters(GenericFilterMetadataDTO.FilterAppliedFlags.builder()
                        .appliedFilters(new HashMap<>())
                        .anyFilterApplied(false)
                        .build())
                    .build();
            }

            // Get unit details for managed units (original unfiltered list)
            List<com.stpl.tech.master.domain.model.UnitBasicDetail> managedUnits = managedUnitIds.stream()
                .map(unitCacheService::getUnitBasicDetail)
                .filter(Objects::nonNull)
                .toList();

            // Build available filters
            List<GenericFilterMetadataDTO.FilterOption> availableFilters = new ArrayList<>();

            // Unit filter
            List<FilterValueOptionDTO> unitValueOptions = managedUnits.stream()
                .map(unit -> FilterValueOptionDTO.builder()
                    .value(String.valueOf(unit.getId()))
                    .displayName(unit.getName())
                    .build())
                .collect(Collectors.toList());

            availableFilters.add(GenericFilterMetadataDTO.FilterOption.builder()
                .filterKey("unitIds")
                .filterName("Units")
                .dataType("INTEGER")
                .valueOptions(unitValueOptions)
                .operator("IN")
                .build());

            // City filter
            List<String> cities = managedUnits.stream()
                .map(com.stpl.tech.master.domain.model.UnitBasicDetail::getCity)
                .filter(Objects::nonNull)
                .distinct()
                .sorted()
                .toList();

            List<FilterValueOptionDTO> cityValueOptions = cities.stream()
                .map(city -> FilterValueOptionDTO.builder()
                    .value(city)
                    .displayName(city)
                    .build())
                .collect(Collectors.toList());

            availableFilters.add(GenericFilterMetadataDTO.FilterOption.builder()
                .filterKey("cityNames")
                .filterName("Cities")
                .dataType("STRING")
                .valueOptions(cityValueOptions)
                .operator("IN")
                .build());

            log.info("Found {} units and {} cities for userId: {}", unitValueOptions.size(), cityValueOptions.size(), userId);
            
            return GenericFilterMetadataDTO.builder()
                .availableFilters(availableFilters)
                .appliedFilters(GenericFilterMetadataDTO.FilterAppliedFlags.builder()
                    .appliedFilters(new HashMap<>())
                    .anyFilterApplied(false)
                    .build())
                .build();

        } catch (CafeLiveDashboardException e) {
            log.error("Error getting cafe live dashboard filter metadata for userId: {}", userId, e);
            throw e;
        } catch (Exception e) {
            log.error("Error getting cafe live dashboard filter metadata for userId: {}", userId, e);
            throw new CafeLiveDashboardException("FILTER_METADATA_ERROR", "Failed to get cafe live dashboard filter metadata", e);
        }
    }

    @CacheEvict(value = "unitAttendanceAnalytics", key = "#unitId + '_' + #businessDate")
    public void evictAndRecacheUnitAnalytics(Integer unitId, LocalDate businessDate) {
        log.debug("Evicting and recaching unit analytics for unit: {} on date: {}", unitId, businessDate);
        // The cache will be automatically recached with the updated data
    }

}