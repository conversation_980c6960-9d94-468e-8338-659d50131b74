package com.stpl.tech.attendance.dto;

import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

@Data
public class DevicePairingRequest {
    @NotBlank(message = "Device ID is required")
    private String deviceId;
    
    @NotBlank(message = "OS is required")
    private String os;
    
    @NotBlank(message = "Version is required")
    private String version;
    
    @NotNull(message = "Unit ID is required")
    private Long unitId;
    
    @NotNull(message = "Paired by is required")
    private Long pairedBy;

    private String deviceModel;
    
    private boolean overrideExisting = false;
} 