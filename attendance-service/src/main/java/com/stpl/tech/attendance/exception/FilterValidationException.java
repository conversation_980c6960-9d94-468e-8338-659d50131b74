package com.stpl.tech.attendance.exception;

/**
 * Custom exception for filter validation errors
 */
public class FilterValidationException extends RuntimeException {
    
    private final String errorCode;
    
    public FilterValidationException(String message) {
        super(message);
        this.errorCode = "FILTER_VALIDATION_ERROR";
    }
    
    public FilterValidationException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = "FILTER_VALIDATION_ERROR";
    }
    
    public FilterValidationException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
    
    public FilterValidationException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
} 