package com.stpl.tech.attendance.model.request;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
public class BulkMarkReadRequest {
    @NotEmpty(message = "Notification IDs cannot be empty")
    private List<String> notificationIds;
    
    @NotNull(message = "User ID cannot be null")
    private String userId;
} 