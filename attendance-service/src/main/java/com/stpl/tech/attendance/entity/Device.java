package com.stpl.tech.attendance.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import java.time.LocalDateTime;

@Data
@Document(collection = "DEVICES")
public class Device {
    @Id
    private String id;
    
    private String macAddress;
    private String deviceName;
    private String deviceType;
    private String status;
    private LocalDateTime registeredAt;
    private LocalDateTime lastActiveAt;
    private String registeredBy;
    private String location;
    
    // Additional metadata
    private String osVersion;
    private String firmwareVersion;
    private Boolean isActive;
} 