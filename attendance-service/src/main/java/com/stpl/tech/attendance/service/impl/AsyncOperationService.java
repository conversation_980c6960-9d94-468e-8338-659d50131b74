package com.stpl.tech.attendance.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stpl.tech.attendance.approval.entity.ApprovalRequest;
import com.stpl.tech.attendance.approval.repository.ApprovalRequestRepository;
import com.stpl.tech.attendance.approval.service.ApprovalEngineService;
import com.stpl.tech.attendance.cache.service.UserCacheService;
import com.stpl.tech.attendance.config.AttendanceConfig;
import com.stpl.tech.attendance.constants.AppConstants;
import com.stpl.tech.attendance.dto.ApplyLeaveRequest;
import com.stpl.tech.attendance.dto.ApplyOdWfhRequest;
import com.stpl.tech.attendance.dto.ApplyRegularisationRequest;
import com.stpl.tech.attendance.dto.ApplyWeekOffRequest;
import com.stpl.tech.attendance.dto.BiometricAdditionalImageDTO;
import com.stpl.tech.attendance.entity.AttendanceType;
import com.stpl.tech.attendance.entity.BiometricAdditionalImage;
import com.stpl.tech.attendance.entity.DailyAttendanceSummary;
import com.stpl.tech.attendance.entity.DailyAttendanceSummaryLogs;
import com.stpl.tech.attendance.entity.EmpAttendanceBalanceData;
import com.stpl.tech.attendance.entity.EmpAttendanceBalanceDataLogs;
import com.stpl.tech.attendance.entity.EmployeeAttendanceRequest;
import com.stpl.tech.attendance.entity.EmployeeAttendanceRequestDetail;
import com.stpl.tech.attendance.entity.EmployeeAttendanceRequestLog;
import com.stpl.tech.attendance.enums.ApprovalStatus;
import com.stpl.tech.attendance.enums.ApprovalType;
import com.stpl.tech.attendance.enums.BiometricErrorCode;
import com.stpl.tech.attendance.exception.BiometricRegistrationException;
import com.stpl.tech.attendance.model.ApproverInfo;
import com.stpl.tech.attendance.notification.dto.NotificationRequest;
import com.stpl.tech.attendance.notification.service.NotificationService;
import com.stpl.tech.attendance.repository.BiometricAdditionalImageRepository;
import com.stpl.tech.attendance.repository.DailyAttendanceSummaryRepository;
import com.stpl.tech.attendance.repository.EmpAttendanceBalanceDataRepository;
import com.stpl.tech.attendance.repository.EmpLeaveDataLogsRepository;
import com.stpl.tech.attendance.repository.EmployeeAttendanceRequestLogRepository;
import com.stpl.tech.attendance.service.EmployeeAttendanceRequestDetailService;
import com.stpl.tech.attendance.service.EmployeeShiftInstanceRecreationService;
import com.stpl.tech.attendance.service.EmployeeShiftScheduleUpdateService;
import com.stpl.tech.attendance.service.S3Service;
import com.stpl.tech.attendance.service.UnitAttendanceAnalyticsCacheService;
import com.stpl.tech.attendance.service.UnitResolutionService;
import com.stpl.tech.attendance.util.DateTimeUtil;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import jakarta.transaction.Transactional;

import java.math.BigDecimal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.TreeMap;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class AsyncOperationService {

    private final S3Service s3Service;
    private final DailyAttendanceSummaryRepository summaryRepository;
    private final AttendanceConfig attendanceConfig;
    private final EmployeeShiftScheduleUpdateService employeeShiftScheduleUpdateService;
    private final UnitAttendanceAnalyticsCacheService unitAttendanceAnalyticsCacheService;
    private final UnitResolutionService unitResolutionService;
    private final BiometricAdditionalImageRepository biometricAdditionalImageRepository;
    private final ApprovalEngineService approvalEngineService;
    private final ApprovalRequestRepository approvalRequestRepository;
    private final ObjectMapper objectMapper;
    private final EmployeeShiftInstanceRecreationService employeeShiftInstanceRecreationService;
    private final EmpAttendanceBalanceDataRepository empAttendanceBalanceDataRepository;
    private final EmpLeaveDataLogsRepository empLeaveDataLogsRepository;
    private final DailyAttendanceSummaryLogsServiceImpl dailyAttendanceSummaryLogsService;
    private final EmployeeAttendanceRequestDetailService employeeAttendanceRequestDetailService;
    private final UserCacheService userCacheService;
    private final EmployeeAttendanceRequestLogRepository employeeAttendanceRequestLogRepository;
    private final EmployeeAttendanceApprovalServiceImpl employeeAttendanceApprovalService;
    private final NotificationService notificationService;
    @Async("biometricTaskExecutor")
    public CompletableFuture<String> uploadImageAsync(String base64Image, String path) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return s3Service.uploadBase64Image(base64Image, "master-service", path);
            } catch (Exception e) {
                log.error("Failed to upload image asynchronously: {}", e.getMessage());
                throw new BiometricRegistrationException(BiometricErrorCode.REGISTRATION_PROCESSING_ERROR, 
                    "Failed to upload image: " + e.getMessage());
            }
        });
    }

    @Async("asyncTaskExecutor")
    public CompletableFuture<String> uploadLeaveDocumentAsync(MultipartFile file, Integer employeeId, String documentType) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                validateFileSize(file);
                String documentKey = generateDocumentKey(employeeId, file.getOriginalFilename(), documentType);

                // Upload directly to S3 without Base64 conversion
                String uploadedKey = s3Service.uploadLeaveDocument(
                    file,
                    "leave-documents",
                    documentKey
                );

                log.info("Successfully uploaded leave document asynchronously for employee: {} with key: {}",
                        employeeId, uploadedKey);
                return uploadedKey;
            } catch (Exception e) {
                log.error("Failed to upload leave document asynchronously for employee: {}", employeeId, e);
                throw new RuntimeException("Failed to upload leave document: " + e.getMessage());
            }
        });
    }

    @Async("asyncTaskExecutor")
    public void updateSummaryAsync(Integer employeeId, LocalDate attendanceDate, LocalDateTime checkInTime,
                                   LocalDateTime checkOutTime) {
        try {
            // Check if summary already exists to determine if this is an update or creation
            Optional<DailyAttendanceSummary> existingSummaryOpt = summaryRepository
                .findByEmployeeIdAndAttendanceDate(employeeId, attendanceDate);

            DailyAttendanceSummary summary = existingSummaryOpt
                .orElseGet(() -> {
                    DailyAttendanceSummary newSummary = new DailyAttendanceSummary();
                    newSummary.setEmployeeId(employeeId);
                    newSummary.setAttendanceDate(attendanceDate);
                    newSummary.setType(AttendanceType.NORMAL);
                    newSummary.setStatus(com.stpl.tech.attendance.entity.AttendanceStatus.PRESENT);
                    return newSummary;
                });

            // Store old values for logging if this is an update
            LocalDateTime oldFirstCheckIn = summary.getFirstCheckIn();
            LocalDateTime oldLastCheckOut = summary.getLastCheckOut();
            Integer oldTotalPunches = summary.getTotalPunches();
            String oldStatus = summary.getStatus() != null ? summary.getStatus().name() : null;

            if (checkInTime != null) {
                summary.setFirstCheckIn(checkInTime);
            }
            if (checkOutTime != null) {
                summary.setLastCheckOut(checkOutTime);
            }
            
            if (summary.getFirstCheckIn() != null && summary.getLastCheckOut() != null) {
                Duration duration = Duration.between(summary.getFirstCheckIn(), summary.getLastCheckOut());
                summary.setTotalHours(BigDecimal.valueOf(duration.toMinutes() / 60.0));
            }
            
            summary.setTotalPunches(summary.getTotalPunches() + 1);
            
            // Update status based on working hours
            if (summary.getTotalHours().compareTo(attendanceConfig.getMinimumWorkHours()) < 0) {
                summary.setStatus(com.stpl.tech.attendance.entity.AttendanceStatus.LESS_WORKING_HOURS);
            } else {
                summary.setStatus(com.stpl.tech.attendance.entity.AttendanceStatus.PRESENT);
            }
            
            // Save the summary
            DailyAttendanceSummary savedSummary = summaryRepository.save(summary);

            // LOG THE CHANGE - Determine action type and status
            String actionType = determineActionType(checkInTime, checkOutTime, oldFirstCheckIn, oldLastCheckOut, oldTotalPunches, summary.getTotalPunches());
            DailyAttendanceSummaryLogs.ActionStatus actionStatus = DailyAttendanceSummaryLogs.ActionStatus.CREATED;

            // Log the current state after save
            try {
                dailyAttendanceSummaryLogsService.updateOrCreateLog(
                    savedSummary,
                    actionStatus,
                    actionType,
                    "SYSTEM" ,"PRESENT"
                );
                log.debug("Logged attendance summary change for employee: {} on date: {} with action: {}",
                    employeeId, attendanceDate, actionType);
            } catch (Exception e) {
                log.error("Failed to log attendance summary change for employee: {} on date: {}",
                    employeeId, attendanceDate, e);
                // Don't throw exception to avoid rolling back the main transaction
            }
            
            // NEW: Update shift schedule with attendance data
            employeeShiftScheduleUpdateService.updateShiftScheduleWithAttendance(
                employeeId, attendanceDate, checkInTime, checkOutTime , summary);
            
            // NEW: Update unit analytics cache asynchronously
            updateUnitAnalyticsCacheAsync(employeeId, attendanceDate);
            
            log.debug("Updated attendance summary, shift schedule, and unit analytics cache for employee: {} on date: {}", employeeId, attendanceDate);
        } catch (Exception e) {
            log.error("Failed to update attendance summary asynchronously: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Update unit analytics cache for the employee's unit asynchronously
     */
    @Async("asyncTaskExecutor")
    public void updateUnitAnalyticsCacheAsync(Integer employeeId, LocalDate attendanceDate) {
        try {
             Integer unitId = unitResolutionService.getUnitIdForEmployee(employeeId,attendanceDate);
             if (unitId != null) {
                 unitAttendanceAnalyticsCacheService.updateUnitAnalyticsCacheAsync(unitId, attendanceDate, employeeId);
             }
            
            log.debug("Unit analytics cache update placeholder for employee: {} on date: {}", employeeId, attendanceDate);
            
        } catch (Exception e) {
            log.error("Failed to update unit analytics cache for employee: {} on date: {}", employeeId, attendanceDate, e);
        }
    }

    /**
     * Generate unique document key for leave documents
     * @param employeeId The employee ID
     * @param fileName The original file name
     * @param documentType The document type
     * @return Unique document key
     */
    private String generateDocumentKey(Integer employeeId, String fileName, String documentType) {
        String timestamp = LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String fileExtension = getFileExtension(fileName);
        String documentTypePath = documentType != null ? documentType.toLowerCase() : "general";

        return String.format("%s/%d/%s_%s%s",
                documentTypePath, employeeId, timestamp,
                sanitizeFileName(fileName), fileExtension);
    }

    /**
     * Get file extension from file name
     * @param fileName The file name
     * @return File extension with dot
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || !fileName.contains(".")) {
            return "";
        }
        return fileName.substring(fileName.lastIndexOf("."));
    }

    /**
     * Sanitize file name for S3 key
     * @param fileName The original file name
     * @return Sanitized file name
     */
    private String sanitizeFileName(String fileName) {
        if (fileName == null) {
            return "document";
        }
        // Remove extension and replace special characters
        String nameWithoutExtension = fileName.contains(".") ?
                fileName.substring(0, fileName.lastIndexOf(".")) : fileName;
        return nameWithoutExtension.replaceAll("[^a-zA-Z0-9_-]", "_");
    }


    /**
     * Validate file size (maximum 2MB)
     * @param file The file to validate
     * @throws RuntimeException if file size exceeds limit
     */
    private void validateFileSize(MultipartFile file) {
        long maxSize = 2 * 1024 * 1024; // 2MB in bytes
        if (file.getSize() > maxSize) {
            throw new RuntimeException("File size exceeds maximum limit of 2MB. Current size: " +
                    String.format("%.2f", file.getSize() / (1024.0 * 1024.0)) + "MB");
        }
    }

    /**
     * Save additional images to S3 and update approval request metadata asynchronously
     * @param additionalImages List of additional images to save
     * @param biometricRegistrationId ID of the biometric registration
     */
    @Async("biometricTaskExecutor")
    @Transactional
    public void saveAdditionalImagesAsync(List<BiometricAdditionalImageDTO> additionalImages, Long biometricRegistrationId) {
        if (CollectionUtils.isEmpty(additionalImages) || biometricRegistrationId == null) {
            return;
        }

        try {
            log.info("Starting async processing of {} additional images for biometric registration ID: {}",
                    additionalImages.size(), biometricRegistrationId);

            // Find the approval request for this biometric registration
            ApprovalRequest approvalRequest = findApprovalRequestByBiometricRegistrationId(biometricRegistrationId);
            if (approvalRequest == null) {
                log.warn("No approval request found for biometric registration ID: {}", biometricRegistrationId);
                return;
            }

            // Upload images to S3 and collect URLs
            List<String> additionalImageUrls = new ArrayList<>();
            List<BiometricAdditionalImage> imagesToSave = new ArrayList<>();

            for (BiometricAdditionalImageDTO image : additionalImages) {
                try {
                    // Generate unique key for S3
                    String timestamp = String.valueOf(System.currentTimeMillis());
                    String imageKey = "biometric/additional/" + biometricRegistrationId + "/" + image.getType() + "_" + timestamp + ".jpg";

                    // Upload to S3
                    String uploadedKey = s3Service.uploadBase64Image(
                        image.getBase64Image(),
                        "master-service",
                        imageKey
                    );

                    // Get CloudFront URL
                    String imageUrl = s3Service.getCloudfrontUrl(uploadedKey);
                    additionalImageUrls.add(imageUrl);

                    // Prepare entity for database
                    BiometricAdditionalImage imageEntity = BiometricAdditionalImage.builder()
                            .biometricRegistrationId(biometricRegistrationId)
                            .imageType(image.getType())
                            .base64Image(image.getBase64Image())
                            .build();

                    imagesToSave.add(imageEntity);

                    log.info("Successfully uploaded additional image type: {} for biometric registration ID: {} to S3 key: {}",
                            image.getType(), biometricRegistrationId, uploadedKey);

                } catch (Exception e) {
                    log.error("Failed to upload additional image type: {} for biometric registration ID: {}",
                            image.getType(), biometricRegistrationId, e);
                    // Continue with other images even if one fails
                }
            }

            // Save images to database
            if (!imagesToSave.isEmpty()) {
                biometricAdditionalImageRepository.saveAll(imagesToSave);
                log.info("Saved {} additional images to database for biometric registration ID: {}",
                        imagesToSave.size(), biometricRegistrationId);
            }

            // Update approval request metadata with additional image URLs
            if (!additionalImageUrls.isEmpty()) {
                updateApprovalRequestMetadata(approvalRequest, additionalImageUrls);
                log.info("Updated approval request metadata with {} additional image URLs for biometric registration ID: {}",
                        additionalImageUrls.size(), biometricRegistrationId);
            }

            log.info("Completed async processing of additional images for biometric registration ID: {}", biometricRegistrationId);

        } catch (Exception e) {
            log.error("Error in async processing of additional images for biometric registration ID: {}",
                    biometricRegistrationId, e);
        }
    }

    /**
     * Find approval request by biometric registration ID
     * @param biometricRegistrationId The biometric registration ID
     * @return ApprovalRequest or null if not found
     */
    private ApprovalRequest findApprovalRequestByBiometricRegistrationId(Long biometricRegistrationId) {
        try {
            return approvalRequestRepository.findByReferenceIdAndRequestType(biometricRegistrationId,
                    ApprovalType.BIOMETRIC_REGISTRATION);
        } catch (Exception e) {
            log.error("Error finding approval request for biometric registration ID: {}", biometricRegistrationId, e);
        }
        return null;
    }

    /**
     * Update approval request metadata with additional image URLs
     * @param approvalRequest The approval request to update
     * @param additionalImageUrls List of additional image URLs
     */
    private void updateApprovalRequestMetadata(ApprovalRequest approvalRequest, List<String> additionalImageUrls) {
        try {
            // Parse existing metadata
            Map<String, Object> metadata = objectMapper.readValue(approvalRequest.getMetadata(), Map.class);

            // Add or update additionalImages field
            metadata.put("additionalImages", additionalImageUrls);

            // Update the approval request
            approvalRequest.setMetadata(objectMapper.writeValueAsString(metadata));
            approvalRequest.setUpdatedDate(DateTimeUtil.now());
            approvalRequest.setUpdatedBy(String.valueOf(com.stpl.tech.util.AppConstants.SYSTEM_EMPLOYEE_ID));

            // Save the updated approval request
            approvalRequestRepository.save(approvalRequest);

            log.info("Successfully updated approval request metadata with additional images for request ID: {}",
                    approvalRequest.getId());

        } catch (Exception e) {
            log.error("Failed to update approval request metadata for request ID: {}",
                    approvalRequest.getId(), e);
        }
    }

    @Async("asyncTaskExecutor")
    @Transactional
    public void postWeekOffApplicationAsync(ApplyWeekOffRequest request,Long attendanceRequestId,Integer createdBy,Long summaryId) {
        try {
            // Update EMP_ATTENDANCE_BALANCE_DATA table
            Long balanceDataId = null;
            if(Boolean.TRUE.equals(request.isForUpcomingWeeks())) {
                balanceDataId = updateWeekOffBalanceData(request.getEmpId(), request.getDate().getDayOfWeek().name(), LocalDateTime.now(), request.getEmpId().toString());
                logWeekOffBalanceChange(balanceDataId,  request.getDate().getDayOfWeek().name(), LocalDateTime.now(), createdBy.toString());
            }
            // Create entry in DAILY_ATTENDANCE_SUMMARY table
            // Log in DAILY_ATTENDANCE_SUMMARY_LOGS table
            logWeekOffDailySummaryChange(summaryId, createdBy.toString());
            String weekOffType = request.isForUpcomingWeeks() ? AppConstants.RECURRING : AppConstants.TEMPORARY;
            employeeAttendanceRequestDetailService.createLeaveDetailRecords( attendanceRequestId, Map.of(request.getDate(), weekOffType), AppConstants.WEEK_OFF, request.getEmpId().toString() );
            // Recreate shift instances if needed
            employeeShiftInstanceRecreationService.recreateShiftInstancesFromEffectiveDate(request.getEmpId(), request.getDate().toLocalDate(), createdBy.toString());
        } catch (Exception e) {
            log.error("Failed to post week off application asynchronously for employee: {}", request.getEmpId(), e);
            throw new RuntimeException("Failed to post week off application: " + e.getMessage());
        }
    }

    @Async("asyncTaskExecutor")
    @Transactional
    public void postRegularisation(ApplyRegularisationRequest request, EmployeeAttendanceRequest attendanceRecord, Integer empId,Integer unitId) {
        try {
            log.info("Starting async processing of regularisation application for employee: {} with attendance record ID: {}", 
                    empId, attendanceRecord.getId());
            
            // Create approval request for regularisation
            ApprovalRequest approvalRequest = createRegularisationApprovalRequest(request, attendanceRecord, empId,unitId);
            
            if (approvalRequest != null) {
                // Create the approval request using the approval engine
                approvalEngineService.createRequest(approvalRequest);
                log.info("Successfully created approval request for regularisation application - ID: {} for employee: {}", 
                        approvalRequest.getId(), empId);
            } else {
                log.warn("Failed to create approval request for regularisation application - employee: {}", empId);
            }
            
            log.info("Completed async processing of regularisation application for employee: {} with attendance record ID: {}", 
                    empId, attendanceRecord.getId());
            
        } catch (Exception e) {
            log.error("Failed to process regularisation application asynchronously for employee: {} with attendance record ID: {}", 
                    empId, attendanceRecord.getId(), e);
            // Don't throw exception to avoid affecting the main transaction
            // The approval can be created later through a separate process
        }
    }

    private ApprovalRequest createRegularisationApprovalRequest(ApplyRegularisationRequest request, 
                                                               EmployeeAttendanceRequest attendanceRecord, 
                                                               Integer empId,Integer unitId) {
        try {
            // Get employee details to find reporting manager
            EmployeeBasicDetail employee = userCacheService.getUserById(empId);
            
            if (employee == null) {
                log.error("Employee not found with ID: {}", empId);
                return null;
            }
            List<ApproverInfo> approvers = employeeAttendanceApprovalService.getApproversForAttendanceRequest(unitId.toString(), empId, "regularisation");
            if (approvers.isEmpty()) {
                log.error("No reporting manager found for employee: {}", empId);
                return null;
            }
            
            // Build metadata for regularisation
            Map<String, String> metadata = buildRegularisationMetadata(request, attendanceRecord, approvers);
            
            if (unitId == null || empId == null) {
                log.error("Unit ID or User ID not found in authentication context for employee: {}", empId);
                return null;
            }
            
            // Create approval request
            return ApprovalRequest.builder()
                    .requestType(ApprovalType.EMPLOYEE_REGULARISATION)
                    .requesterId(Long.valueOf(empId))
                    .status(ApprovalStatus.PENDING)
                    .currentStep(1)
                    .totalSteps(1)
                    .requestDate(DateTimeUtil.now())
                    .createdDate(DateTimeUtil.now())
                    .createdBy(empId.toString())
                    .referenceId(attendanceRecord.getId())
                    .unitId(Long.valueOf(unitId))
                    .metadata(objectMapper.writeValueAsString(metadata))
                    .build();
                    
        } catch (Exception e) {
            log.error("Error creating regularisation approval request for employee: {} with attendance record ID: {}", 
                    empId, attendanceRecord.getId(), e);
            return null;
        }
    }

    private Map<String, String> buildRegularisationMetadata(ApplyRegularisationRequest request, 
                                                           EmployeeAttendanceRequest attendanceRecord,
                                                           List<ApproverInfo> approvers) {
        Map<String, String> metadata = new HashMap<>();
        
        metadata.put("fromDate", request.getCheckIn().toString());
        metadata.put("toDate", request.getCheckOut().toString());
        metadata.put("reason", attendanceRecord.getReason() != null ? attendanceRecord.getReason() : "Regularisation application");
        metadata.put("documents", "");
        metadata.put("comments", request.getComments() != null ? request.getComments() : "");
        metadata.put("approvers", approvers.stream().map(approver -> approver.getId().toString()).collect(Collectors.joining(",")));
        
        return metadata;
    }

    /**
     * Process leave application asynchronously after attendance record creation
     * This method handles approval request creation and other post-processing tasks
     * @param request The leave request
     * @param attendanceRecord The created attendance record
     * @param empId The employee ID
     */
    @Async("asyncTaskExecutor")
    @Transactional
    public void postLeaveProcessing(ApplyLeaveRequest request, EmployeeAttendanceRequest attendanceRecord, Integer empId,Integer unitId) {
        try {
            log.info("Starting async processing of leave application for employee: {} with attendance record ID: {}",
                    empId, attendanceRecord.getId());
            
            // Create approval request for leave
            ApprovalRequest approvalRequest = createLeaveApprovalRequest(request, attendanceRecord, empId,unitId);
            
            if (approvalRequest != null) {
                // Create the approval request using the approval engine
                approvalEngineService.createRequest(approvalRequest);
                log.info("Successfully created approval request for leave application - ID: {} for employee: {}",
                        approvalRequest.getId(), empId);
            } else {
                log.warn("Failed to create approval request for leave application - employee: {}", empId);
            }
            
            log.info("Completed async processing of leave application for employee: {} with attendance record ID: {}",
                    empId, attendanceRecord.getId());
            
        } catch (Exception e) {
            log.error("Failed to process leave application asynchronously for employee: {} with attendance record ID: {}",
                    empId, attendanceRecord.getId(), e);
            // Don't throw exception to avoid affecting the main transaction
            // The approval can be created later through a separate process
        }
    }

    /**
     * Create approval request for leave application
     * This method replicates the logic from EmployeeAttendanceServiceImpl to avoid circular dependencies
     * @param request The leave request
     * @param attendanceRecord The attendance record
     * @param empId The employee ID
     * @return ApprovalRequest or null if creation fails
     */
    private ApprovalRequest createLeaveApprovalRequest(ApplyLeaveRequest request, 
                                                      EmployeeAttendanceRequest attendanceRecord, 
                                                      Integer empId,Integer unitId) {
        try {
            // Get employee details to find reporting manager
            EmployeeBasicDetail employee = userCacheService.getUserById(empId);
            
            if (employee == null) {
                log.error("Employee not found with ID: {}", empId);
                return null;
            }
            List<ApproverInfo> approvers = employeeAttendanceApprovalService.getApproversForAttendanceRequest(unitId.toString(), empId, "leave");
            if (approvers.isEmpty()) {
                log.error("No reporting manager found for employee: {}", empId);
                return null;
            }
            
            // Build metadata for leave
            Map<String, String> metadata = buildLeaveMetadata(request, attendanceRecord, approvers);
            if (unitId == null) {
                log.error("Unit ID not found in authentication context for employee: {}", empId);
                return null;
            }
            
            // Create approval request
            return ApprovalRequest.builder()
                    .requestType(ApprovalType.EMPLOYEE_LEAVE)
                    .requesterId(Long.valueOf(empId))
                    .status(ApprovalStatus.PENDING)
                    .currentStep(1)
                    .totalSteps(1)
                    .requestDate(DateTimeUtil.now())
                    .createdDate(DateTimeUtil.now())
                    .createdBy(empId.toString())
                    .referenceId(attendanceRecord.getId())
                    .unitId(Long.valueOf(unitId))
                    .metadata(objectMapper.writeValueAsString(metadata))
                    .build();
                    
        } catch (Exception e) {
            log.error("Error creating leave approval request for employee: {} with attendance record ID: {} ",
                    empId, attendanceRecord.getId(), e);
            return null;
        }
    }

    /**
     * Build metadata specifically for leave applications
     * This method replicates the logic from EmployeeAttendanceServiceImpl to avoid circular dependencies
     * @param request The leave request
     * @param attendanceRecord The attendance record
     * @param approvers List of approvers
     * @return Map containing metadata
     */
    private Map<String, String> buildLeaveMetadata(ApplyLeaveRequest request, 
                                                  EmployeeAttendanceRequest attendanceRecord,
                                                  List<ApproverInfo> approvers) {
        Map<String, String> metadata = new HashMap<>();
        
        try {
            // Fetch detail records to get date-wise leave information
            List<EmployeeAttendanceRequestDetail> detailRecords =
                    employeeAttendanceRequestDetailService.getDetailsByAttendanceRequestId(attendanceRecord.getId());

            // Create Map<LocalDateTime, String> for date-wise leave types
            Map<LocalDateTime, String> leaveDatesMap = new HashMap<>();
            for (EmployeeAttendanceRequestDetail detail : detailRecords) {
                leaveDatesMap.put(detail.getDate(), detail.getRequestType());
            }

            // Sort the dates to ensure they appear in chronological order in metadata
            // Create a sorted map using TreeMap to maintain date order
            Map<LocalDateTime, String> sortedLeaveDatesMap = new TreeMap<>(leaveDatesMap);

            // Convert the sorted Map to JSON string for metadata
            String leaveDatesJson = objectMapper.writeValueAsString(sortedLeaveDatesMap);

            // Calculate total leave days from the request
            BigDecimal totalLeaveDays = calculateTotalLeaveDays(leaveDatesMap, attendanceRecord.getEmpId(), request.getLeaveType());

            metadata.put("leaveDates", leaveDatesJson);
            metadata.put("leaveType", request.getLeaveType());
            metadata.put("leaveDayCount", String.valueOf(totalLeaveDays));
            metadata.put("reason", attendanceRecord.getReason() != null ? attendanceRecord.getReason() : "Leave application");
            metadata.put("documents", request.getDocuments() != null ? request.getDocuments() : "");
            metadata.put("comments", attendanceRecord.getComments() != null ? attendanceRecord.getComments() : "");
            metadata.put("approvers", approvers.stream().map(approver -> approver.getId().toString()).collect(Collectors.joining(",")));

        } catch (Exception e) {
            log.error("Error building leave metadata: {}", e.getMessage(), e);
            // Don't throw exception, return empty metadata instead
        }
        
        return metadata;
    }

    /**
     * Calculate total leave days from leave dates map
     * This method replicates the logic from EmployeeAttendanceServiceImpl to avoid circular dependencies
     * @param leaveDatesMap Map of leave dates and request types
     * @param empId Employee ID
     * @param leaveType Type of leave
     * @return Total days calculated
     */
    private BigDecimal calculateTotalLeaveDays(Map<LocalDateTime, String> leaveDatesMap, Integer empId, String leaveType) {
        try {
            if (leaveDatesMap == null || leaveDatesMap.isEmpty()) {
                log.warn("Leave dates map is null or empty, returning 0 days");
                return BigDecimal.ZERO;
            }

            BigDecimal totalDays = BigDecimal.ZERO;

            for (Map.Entry<LocalDateTime, String> entry : leaveDatesMap.entrySet()) {
                LocalDateTime date = entry.getKey();
                String requestType = entry.getValue();

                log.debug("Processing date: {} with requestType: {}", date, requestType);

                if (requestType != null) {
                    switch (requestType.toUpperCase()) {
                        case "FIRST_HALF":
                        case "FIRST HALF", "SECOND_HALF", "SECOND HALF":
                            totalDays = totalDays.add(new BigDecimal("0.5"));
                            break;
                        default:
                            totalDays = totalDays.add(BigDecimal.ONE);
                            break;
                    }
                } else {
                    log.warn("Date {}: requestType is null. Defaulting to full day.", date);
                    totalDays = totalDays.add(BigDecimal.ONE);
                }
            }

            log.info("Calculated total leave days: {} days from {} leave entries", totalDays, leaveDatesMap.size());
            return totalDays;

        } catch (Exception e) {
            log.error("Error calculating total leave days: {}", e.getMessage(), e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * Process OD/WFH application asynchronously after attendance record creation
     * This method handles approval request creation for each date
     * @param request The OD/WFH request
     * @param attendanceRecords List of attendance records (one per date)
     */
    @Async("asyncTaskExecutor")
    @Transactional
    public void postOdWfhProcessing(ApplyOdWfhRequest request, List<EmployeeAttendanceRequest> attendanceRecords,Long unitId) {
        try {
            log.info("Starting async processing of {} application for employee: {} with {} attendance records", 
                    request.getType(), request.getEmpId(), attendanceRecords.size());
            
            // Create separate approval request for each date
            for (EmployeeAttendanceRequest attendanceRecord : attendanceRecords) {
                // Create individual approval request for this date
                // Each date now gets its own approval request with its own attendance record reference
                ApprovalRequest approvalRequest = createOdWfhApprovalRequest(request, attendanceRecord,unitId);
                
                if (approvalRequest != null) {
                    approvalEngineService.createRequest(approvalRequest);
                    log.info("Successfully created approval request for {} application on date {} - ID: {} for employee: {}", 
                            request.getType(), attendanceRecord.getDate().toLocalDate(), approvalRequest.getId(), request.getEmpId());
                } else {
                    log.warn("Failed to create approval request for {} application on date {} for employee: {}", 
                            request.getType(), attendanceRecord.getDate().toLocalDate(), request.getEmpId());
                }
            }
            
            log.info("Completed async processing of {} application for employee: {} with {} attendance records", 
                    request.getType(), request.getEmpId(), attendanceRecords.size());
            
        } catch (Exception e) {
            log.error("Failed to process {} application asynchronously for employee: {} with {} attendance records", 
                    request.getType(), request.getEmpId(), attendanceRecords.size(), e);
            // Don't throw exception to avoid affecting the main transaction
            // The approval can be created later through a separate process
        }
    }

    /**
     * Create approval request for OD/WFH application
     * This method replicates the logic from EmployeeAttendanceApprovalServiceImpl to avoid circular dependencies
     * @param request The OD/WFH request
     * @param attendanceRecord The attendance record for a specific date
     * @return ApprovalRequest or null if creation fails
     */
    private ApprovalRequest createOdWfhApprovalRequest(ApplyOdWfhRequest request, EmployeeAttendanceRequest attendanceRecord,Long unitId) {
        try {
            // Get employee details to find reporting manager
            Integer empId = request.getEmpId();
            EmployeeBasicDetail employee = userCacheService.getUserById(empId);

            if (employee == null) {
                log.error("Employee not found with ID: {}", empId);
                return null;
            }

            // Get reporting manager as approver
            List<String> approvers = new ArrayList<>();
            if (employee.getReportingManagerId() != null) {
                approvers.add("140199");
                approvers.add(employee.getReportingManagerId().toString());
            }

            if (approvers.isEmpty()) {
                log.error("No reporting manager found for employee: {}", empId);
                return null;
            }

            // Create approval request - now handles individual attendance records
            return buildOdWfhApprovalRequest(request, attendanceRecord, approvers,unitId);
            
        } catch (Exception e) {
            log.error("Error creating OD/WFH approval request for employee: {} with attendance record ID: {}", 
                    request.getEmpId(), attendanceRecord.getId(), e);
            return null;
        }
    }

    /**
     * Builds the approval request object for OD/WFH application
     * Now handles individual attendance records for date-specific approval requests
     * This method replicates the logic from EmployeeAttendanceApprovalServiceImpl to avoid circular dependencies
     */
    private ApprovalRequest buildOdWfhApprovalRequest(
            ApplyOdWfhRequest request,
            EmployeeAttendanceRequest attendanceRecord,
            List<String> approvers,Long unitId
    ) {
        try {
            // Get the specific attendance record for this approval request
            Long empId = Long.valueOf(Objects.requireNonNullElse(request.getEmpId(), attendanceRecord.getEmpId()));

            // Create metadata map that can handle null values
            Map<String, String> metadata = new HashMap<>();
            metadata.put("fromDate", attendanceRecord.getStartTime() != null ? attendanceRecord.getStartTime().toString() : "");
            metadata.put("toDate", attendanceRecord.getEndTime() != null ? attendanceRecord.getEndTime().toString() : "");
            metadata.put("reason", attendanceRecord.getReason() != null ? attendanceRecord.getReason() : request.getType() + " application");
            metadata.put("documents", request.getDocuments() != null ? request.getDocuments() : "");
            metadata.put("comments", attendanceRecord.getComments() != null ? attendanceRecord.getComments() : "");
            metadata.put("approvers", String.join(",", approvers));

            LocalDateTime now = DateTimeUtil.now();

            // Determine the approval type based on the request type
            ApprovalType approvalType;
            if ("OD".equalsIgnoreCase(request.getType())) {
                approvalType = ApprovalType.EMPLOYEE_OD;
            } else if ("WFH".equalsIgnoreCase(request.getType())) {
                approvalType = ApprovalType.EMPLOYEE_WFH;
            } else {
                log.error("Invalid request type: {}", request.getType());
                return null;
            }

            return ApprovalRequest.builder()
                    .requestType(approvalType)
                    .requesterId(empId)
                    .status(ApprovalStatus.PENDING)
                    .currentStep(1)
                    .totalSteps(1)
                    .requestDate(now)
                    .createdDate(now)
                    .createdBy(empId.toString())
                    .referenceId(attendanceRecord.getId())
                    .unitId(unitId)
                    .metadata(objectMapper.writeValueAsString(metadata))
                    .build();
                    
        } catch (Exception e) {
            log.error("Error building OD/WFH approval request for employee: {} with attendance record ID: {}", 
                    request.getEmpId(), attendanceRecord.getId(), e);
            return null;
        }
    }

    public Long updateWeekOffBalanceData(Integer empId, String weekOffDay, LocalDateTime currentTime, String userId) {
        // Get or create balance data record
        EmpAttendanceBalanceData balanceData = empAttendanceBalanceDataRepository.findByEmpId(empId)
                .orElse(EmpAttendanceBalanceData.builder()
                        .empId(empId)
                        .weekOffDay(weekOffDay)
                        .leaveCount(BigDecimal.ZERO) // Initialize with default value
                        .compOffCount(BigDecimal.ZERO) // Initialize with default value
                        .lwpCount(BigDecimal.ZERO)
                        .createdBy(userId)
                        .updatedBy(userId)
                        .build());

        // Update week off day
        balanceData.setWeekOffDay(weekOffDay); // Set to the day name (e.g., THURSDAY)
        balanceData.setUpdatedBy(userId);
        balanceData.setUpdatedOn(currentTime);

        EmpAttendanceBalanceData savedBalanceData = empAttendanceBalanceDataRepository.save(balanceData);
        log.info("Updated week off balance data for employee {} with week off day: {}", empId, weekOffDay);

        return savedBalanceData.getId();
    }

    /**
     * Log week off balance change in EMP_ATTENDANCE_BALANCE_DATA_LOGS table
     */
    public void logWeekOffBalanceChange(Long balanceDataId, String weekOffDay, LocalDateTime currentTime, String createdBy) {
        try {
            // For the first time, previous value will be SUNDAY, new value will be applied day
            String previousValue = "SUNDAY"; // Default previous value

            EmpAttendanceBalanceDataLogs logEntry = EmpAttendanceBalanceDataLogs.builder()
                    .empAttendanceBalanceId(balanceDataId)
                    .action("CREATED")
                    .type(AttendanceType.WEEK_OFF.name())
                    .previousValue(previousValue)
                    .newValue(weekOffDay)
                    .updatedBy(createdBy)
                    .updatedOn(currentTime)
                    .build();

            // Save using the existing repository
            empLeaveDataLogsRepository.save(logEntry);
            log.info("Logged week off balance change for balance data ID: {} from {} to {}",
                    balanceDataId, previousValue, weekOffDay);
        } catch (Exception e) {
            log.error("Failed to log week off balance change for balance data ID: {}", balanceDataId, e);
            // Don't throw exception to avoid rolling back the main transaction
        }
    }

    /**
     * Log week off daily summary change in DAILY_ATTENDANCE_SUMMARY_LOGS table
     */
    public void logWeekOffDailySummaryChange(Long summaryId, String userId) {
        try {
            DailyAttendanceSummary tempSummary = new DailyAttendanceSummary();
            tempSummary.setId(summaryId);
            dailyAttendanceSummaryLogsService.updateOrCreateLog(
                    tempSummary,
                    DailyAttendanceSummaryLogs.ActionStatus.CREATED,
                    AttendanceType.WEEK_OFF.name(),
                    userId,"ABSENT"
            );

            log.info("Logged week off daily summary change for summary ID: {}", summaryId);
        } catch (Exception e) {
            log.error("Failed to log week off daily summary change for summary ID: {}", summaryId, e);
            // Don't throw exception to avoid rolling back the main transaction
        }
    }


    /**
     * Determine the action type based on what changed in the attendance summary
     */
    private String determineActionType(LocalDateTime newCheckIn, LocalDateTime newCheckOut,
                                      LocalDateTime oldCheckIn, LocalDateTime oldCheckOut,
                                      Integer oldTotalPunches, Integer newTotalPunches) {
        if (newCheckIn != null && oldCheckIn == null) {
            return "FIRST_CHECK_IN";
        } else if (newCheckOut != null && oldCheckOut == null) {
            return "FIRST_CHECK_OUT";
        } else if (newCheckIn != null && !newCheckIn.equals(oldCheckIn)) {
            return "CHECK_IN_UPDATE";
        } else if (newCheckOut != null && !newCheckOut.equals(oldCheckOut)) {
            return "CHECK_OUT_UPDATE";
        } else if (newTotalPunches > oldTotalPunches) {
            return "PUNCH_COUNT_UPDATE";
        } else {
            return "SUMMARY_UPDATE";
        }
    }

    @Async("asyncTaskExecutor")
    @Transactional
    public void logAttendanceRequestAsync(Long empAttendanceRequestId, Integer empId, String fromStatus, String toStatus,
                                         String type, String updatedBy, String logType) {
        try {
            log.info("Starting async logging of attendance request {} for request ID: {} employee: {} from {} to {} by user: {}",
                    logType, empAttendanceRequestId, empId, fromStatus, toStatus, updatedBy);

            // Create log entry
            EmployeeAttendanceRequestLog logEntry = EmployeeAttendanceRequestLog.builder()
                    .empAttendanceRequestId(empAttendanceRequestId)
                    .empId(empId)
                    .fromStatus(fromStatus)
                    .toStatus(toStatus)
                    .type(type)
                    .updatedBy(updatedBy)
                    .updatedOn(DateTimeUtil.now())
                    .build();

            // Save the log entry
            EmployeeAttendanceRequestLog savedLog = employeeAttendanceRequestLogRepository.save(logEntry);

            log.info("Successfully logged attendance request {} with log ID: {} for request ID: {} employee: {} from {} to {} by user: {}",
                    logType, savedLog.getEmpAttendanceRequestLogId(), empAttendanceRequestId, empId, fromStatus, toStatus, updatedBy);

        } catch (Exception e) {
            log.error("Failed to log attendance request {} for request ID: {} employee: {} from {} to {} by user: {}",
                    logType, empAttendanceRequestId, empId, fromStatus, toStatus, updatedBy, e);
            // Don't throw exception to avoid affecting the main transaction
        }
    }

    /**
     * Convenience method for logging attendance request creation
     */
    public void logAttendanceRequestCreationAsync(Long empAttendanceRequestId, Integer empId, String type, String createdBy) {
        logAttendanceRequestAsync(empAttendanceRequestId, empId, AppConstants.STATUS_INITIATED, AppConstants.STATUS_PENDING, type, createdBy, AppConstants.STATUS_CREATED);
    };


    /**
     * Convenience method for logging attendance request status changes
     */
    public void logAttendanceRequestStatusChangeAsync(Long empAttendanceRequestId, Integer empId, String fromStatus, String toStatus,
                                                     String type, String updatedBy) {
        logAttendanceRequestAsync(empAttendanceRequestId, empId, fromStatus, toStatus, type, updatedBy, AppConstants.STATUS_CHANGED);
    }

    /**
     * Convenience method for logging attendance request cancellations
     */
    public void logAttendanceRequestCancellationAsync(Long empAttendanceRequestId, Integer empId, String fromStatus, String type,
                                                     String cancelledBy) {
        logAttendanceRequestAsync(empAttendanceRequestId, empId, fromStatus, AppConstants.STATUS_CANCELLED, type, cancelledBy, "CANCELLATION");
    }


    /**
     * Send notification asynchronously
     */
    @Async("asyncTaskExecutor")
    public void sendNotificationAsync(NotificationRequest notificationRequest) {
        try {
            log.info("Starting async notification sending for type: {} to recipients: {}",
                    notificationRequest.getType(), notificationRequest.getRecipientIds());

            notificationService.sendNotification(notificationRequest);

            log.info("Successfully sent async notification for type: {} to recipients: {}",
                    notificationRequest.getType(), notificationRequest.getRecipientIds());
        } catch (Exception e) {
            log.error("Failed to send async notification for type: {} to recipients: {}",
                    notificationRequest.getType(), notificationRequest.getRecipientIds(), e);
            // Don't throw exception to avoid affecting the main transaction
        }
    }

}