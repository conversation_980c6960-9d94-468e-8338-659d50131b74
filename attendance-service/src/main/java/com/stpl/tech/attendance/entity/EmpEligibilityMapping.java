package com.stpl.tech.attendance.entity;

import com.stpl.tech.attendance.enums.EligibilityType;
import com.stpl.tech.attendance.enums.MappingStatus;
import com.stpl.tech.attendance.enums.MappingType;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Table(name = "EMP_ELIGIBILITY_MAPPING")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmpEligibilityMapping {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "EMP_ID", nullable = false)
    private String empId;

    @Column(name = "VALUE", nullable = false)
    private String value;

    @Enumerated(EnumType.STRING)
    @Column(name = "MAPPING_TYPE", nullable = false)
    private MappingType mappingType;

    @Enumerated(EnumType.STRING)
    @Column(name = "ELIGIBILITY_TYPE", nullable = false)
    private EligibilityType eligibilityType;

    @Enumerated(EnumType.STRING)
    @Column(name = "STATUS", nullable = false)
    private MappingStatus status;

    @Column(name = "START_DATE")
    private LocalDate startDate;

    @Column(name = "END_DATE")
    private LocalDate endDate;

    @Column(name = "CREATED_BY")
    private String createdBy;

    @Column(name = "UPDATED_BY")
    private String updatedBy;

    @Column(name = "CREATED_AT")
    private LocalDateTime createdAt;

    @Column(name = "UPDATED_AT")
    private LocalDateTime updatedAt;
} 