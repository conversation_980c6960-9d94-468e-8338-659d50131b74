package com.stpl.tech.attendance.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.time.LocalDateTime;

@Entity
@Table(name = "TRANSFER_STATISTICS")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransferStatistics implements Serializable {
    private static final long serialVersionUID = 1L;
    
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private String statisticsId;
    
    @Column(name = "UNIT_ID", nullable = false)
    private String unitId;
    
    @Column(name = "TOTAL_TRANSFERS", nullable = false)
    private Integer totalTransfers;
    
    @Column(name = "PENDING_TRANSFERS", nullable = false)
    private Integer pendingTransfers;
    
    @Column(name = "APPROVED_TRANSFERS", nullable = false)
    private Integer approvedTransfers;
    
    @Column(name = "REJECTED_TRANSFERS", nullable = false)
    private Integer rejectedTransfers;
    
    @Column(name = "PERMANENT_TRANSFERS", nullable = false)
    private Integer permanentTransfers;
    
    @Column(name = "TEMPORARY_TRANSFERS", nullable = false)
    private Integer temporaryTransfers;
    
    @Column(name = "TRANSFERS_BY_MONTH", columnDefinition = "jsonb")
    private String transfersByMonth;
    
    @Column(name = "TRANSFERS_BY_DEPARTMENT", columnDefinition = "jsonb")
    private String transfersByDepartment;
    
    @Column(name = "TRANSFER_STATISTICS_STATUS", nullable = false)
    private String transferStatisticsStatus;
    
    @Column(name = "CREATED_DATE", nullable = false)
    private LocalDateTime createdDate;
    
    @Column(name = "CREATED_BY", nullable = false)
    private String createdBy;
    
    @Column(name = "UPDATED_DATE", nullable = false)
    private LocalDateTime updatedDate;
    
    @Column(name = "UPDATED_BY", nullable = false)
    private String updatedBy;
    
    @Column(name = "DELETED_DATE")
    private LocalDateTime deletedDate;
    
    @Column(name = "DELETED_BY")
    private String deletedBy;
} 