package com.stpl.tech.attendance.repository;

import com.stpl.tech.attendance.entity.EmpAttendanceReserveData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * Repository for managing employee leave detail data
 */
@Repository
public interface EmpAttendanceReserveDataRepository extends JpaRepository<EmpAttendanceReserveData, Long> {

    /**
     * Find leave detail data by employee leave data ID and type
     * @param empAttendanceBalanceId The employee leave data ID
     * @param type The leave type (LEAVE, COMP_OFF, LWP)
     * @return Optional containing leave detail data if found
     */
    Optional<EmpAttendanceReserveData> findByEmpAttendanceBalanceIdAndType(Long empAttendanceBalanceId, String type);
    /**
     * Get reserved count for a specific employee and leave type
     * @param empAttendanceBalanceId The employee leave data ID
     * @param type The leave type (LEAVE, COMP_OFF, LWP)
     * @return Reserved count or 0.0 if not found
     */
    @Query("SELECT COALESCE(e.reservedCount, 0.0) FROM EmpAttendanceReserveData e WHERE e.empAttendanceBalanceId = :empAttendanceBalanceId AND e.type = :type")
    BigDecimal getReservedCountByEmpAttendanceBalanceIdAndType(@Param("empAttendanceBalanceId") Long empAttendanceBalanceId, @Param("type") String type);

    @Query("SELECT COALESCE(e.reservedCount, 0.0) FROM EmpAttendanceReserveData e WHERE e.empAttendanceBalanceId = :empAttendanceBalanceId ")
    BigDecimal getReservedCountByEmpLeaveDataId(@Param("empAttendanceBalanceId") Long empAttendanceBalanceId);

    @Query("SELECT COALESCE(SUM(e.reservedCount), 0.0) FROM EmpAttendanceReserveData e WHERE e.empAttendanceBalanceId = :empAttendanceBalanceId AND e.type IN :types")
    BigDecimal getReservedCountByEmpAttendanceBalanceIdAndTypes(@Param("empAttendanceBalanceId") Long empAttendanceBalanceId,
                                                           @Param("types") List<String> types);

}
