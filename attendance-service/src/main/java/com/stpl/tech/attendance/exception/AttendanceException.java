package com.stpl.tech.attendance.exception;

import com.stpl.tech.attendance.enums.AttendanceErrorCode;
import lombok.Getter;

@Getter
public class AttendanceException extends RuntimeException {
    private final AttendanceErrorCode errorCode;

    public AttendanceException(AttendanceErrorCode errorCode) {
        super(errorCode.getDefaultMessage());
        this.errorCode = errorCode;
    }

    public AttendanceException(AttendanceErrorCode errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public AttendanceException(AttendanceErrorCode errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }
} 