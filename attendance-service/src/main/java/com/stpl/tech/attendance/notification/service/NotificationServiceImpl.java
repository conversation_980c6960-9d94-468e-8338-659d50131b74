package com.stpl.tech.attendance.notification.service;

import com.google.firebase.messaging.FirebaseMessaging;
import com.google.firebase.messaging.Message;
import com.stpl.tech.attendance.approval.entity.ApprovalRequest;
import com.stpl.tech.attendance.approval.entity.ApprovalStep;
import com.stpl.tech.attendance.cache.service.UserCacheService;
import com.stpl.tech.attendance.config.EnvironmentProperties;
import com.stpl.tech.attendance.context.JwtContext;
import com.stpl.tech.attendance.notification.dto.NotificationRequest;
import com.stpl.tech.attendance.notification.dto.NotificationResponse;
import com.stpl.tech.attendance.notification.dto.RosteringNotificationRequest;
import com.stpl.tech.attendance.notification.entity.Notification;
import com.stpl.tech.attendance.notification.entity.NotificationRecipient;
import com.stpl.tech.attendance.notification.repository.NotificationRecipientRepository;
import com.stpl.tech.attendance.notification.repository.NotificationRepository;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.attendance.util.DateTimeUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class NotificationServiceImpl implements NotificationService {

    private final NotificationRepository notificationRepository;
    private final NotificationRecipientRepository recipientRepository;
    private final FirebaseMessaging firebaseMessaging;
    private final UserCacheService userCacheService;
    private final EnvironmentProperties environmentProperties;

    @Override
    @Transactional
    public void sendNotification(NotificationRequest request) {
        log.info("Creating notification: {}", request);
        Notification notification = Notification.builder()
                .notificationId(UUID.randomUUID().toString())
                .type(request.getType())
                .title(request.getTitle())
                .message(request.getMessage())
                .priority(request.getPriority())
                .metadata(request.getMetadata())
                .createdDate(DateTimeUtil.now())
                .createdBy(request.getRequesterId())
                .updatedDate(DateTimeUtil.now())
                .updatedBy(request.getRequesterId())
                .build();
        
        notification = notificationRepository.save(notification);
        
        // Create recipients
        Notification finalNotification = notification;
        List<NotificationRecipient> recipients = request.getRecipientIds().stream()
                .map(userId -> NotificationRecipient.builder()
                        .recipientId(UUID.randomUUID().toString())
                        .notificationId(finalNotification.getNotificationId())
                        .userId(userId)
                        .status(NotificationRecipient.DeliveryStatus.PENDING)
                        .createdDate(DateTimeUtil.now())
                        .createdBy("SYSTEM")
                        .updatedDate(DateTimeUtil.now())
                        .updatedBy("SYSTEM")
                        .build())
                .collect(Collectors.toList());
        
        recipientRepository.saveAll(recipients);
        
        // Send FCM notifications
        sendFcmNotifications(notification, request.getRecipientIds());
    }

    @Override
    @Transactional
    public void sendWorkflowNotification(
            String workflowId,
            Notification.NotificationType type,
            String title,
            String message,
            List<String> recipients,
            Map<String, Object> metadata) {
        
        NotificationRequest request = NotificationRequest.builder()
                .type(type)
                .title(title)
                .message(message)
                .priority(Notification.Priority.MEDIUM)
                .metadata(metadata)
                .recipientIds(recipients)
                .build();
        
        sendNotification(request);
    }

    @Override
    @Transactional(readOnly = true)
    public List<NotificationResponse> getUserNotifications(String userId, int page, int size) {
        Page<NotificationRecipient> notificationPage = recipientRepository.findByUserId(userId, PageRequest.of(page, size));
        List<NotificationRecipient> recipients = notificationPage.getContent();
        
        // Get all unique requester IDs
        Set<String> requesterIds = recipients.stream()
                .map(recipient -> recipient.getNotification().getCreatedBy())
                .collect(Collectors.toSet());
        
        // Batch fetch all requester details
        Map<String, EmployeeBasicDetail> requesterDetailsMap = new HashMap<>();
        try {
            requesterIds.forEach(requesterId -> {
                try {
                    EmployeeBasicDetail details = userCacheService.getUserById(Integer.parseInt(requesterId));
                    if (details != null) {
                        requesterDetailsMap.put(requesterId, details);
                    }
                } catch (Exception e) {
                    log.warn("Failed to fetch requester details for user ID: {}", requesterId, e);
                }
            });
        } catch (Exception e) {
            log.error("Failed to batch fetch requester details", e);
        }
        
        // Map notifications with requester details
        return recipients.stream()
                .map(recipient -> {
                    Notification notification = recipient.getNotification();
                    String requesterId = notification.getCreatedBy();
                    EmployeeBasicDetail requesterDetails = requesterDetailsMap.get(requesterId);
                    
                    return NotificationResponse.builder()
                            .notificationId(notification.getNotificationId())
                            .type(notification.getType())
                            .title(notification.getTitle())
                            .message(notification.getMessage())
                            .priority(notification.getPriority())
                            .metadata(notification.getMetadata())
                            .createdDate(notification.getCreatedDate())
                            .requesterId(requesterId)
                            .requesterName(requesterDetails != null ? requesterDetails.getName() : null)
                            .requesterDesignation(requesterDetails != null ? requesterDetails.getDesignation() : null)
                            .build();
                })
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void markAsRead(String notificationId, String userId) {
        NotificationRecipient recipient = recipientRepository.findByNotificationIdAndUserId(notificationId, userId);
        if (recipient != null) {
            recipient.setStatus(NotificationRecipient.DeliveryStatus.READ);
            recipient.setReadDate(DateTimeUtil.now());
            recipient.setUpdatedDate(DateTimeUtil.now());
            recipient.setUpdatedBy("SYSTEM");
            recipientRepository.save(recipient);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public NotificationRecipient.DeliveryStatus getDeliveryStatus(String notificationId, String userId) {
        NotificationRecipient recipient = recipientRepository.findByNotificationIdAndUserId(notificationId, userId);
        return recipient != null ? recipient.getStatus() : null;
    }

    @Override
    @Transactional
    public void deleteNotification(String notificationId, String userId) {
        notificationRepository.findById(notificationId)
                .ifPresent(notification -> {
                   /* notification.setDeletedDate(DateTimeUtil.now());
                    notification.setDeletedBy("SYSTEM");*/
                    notificationRepository.save(notification);
                });
    }

    @Override
    public void sendApprovalRequestNotification(ApprovalRequest request) {
        NotificationRequest notificationRequest = NotificationRequest.builder()
                .type(Notification.NotificationType.APPROVAL_REQUEST)
                .title("New Approval Request")
                .message(String.format("New %s approval request from %s", 
                    request.getRequestType(), request.getRequesterId()))
                .priority(Notification.Priority.HIGH)
                .metadata(Map.of(
                    "requestId", request.getId().toString(),
                    "currentStep", request.getCurrentStep()
                ))
                .recipientIds(getApproversForStep(request.getId().toString(), request.getCurrentStep()))
                .build();
        
        sendNotification(notificationRequest);
    }

    @Override
    public void sendApprovalStepNotification(ApprovalRequest request, ApprovalStep step) {
        NotificationRequest notificationRequest = NotificationRequest.builder()
                .type(Notification.NotificationType.APPROVAL_STATUS)
                .title("Approval Step Update")
                .message(String.format("Step %d of %d for %s approval request", 
                    step.getStepNumber(), request.getTotalSteps(), request.getRequestType()))
                .priority(Notification.Priority.HIGH)
                .metadata(Map.of(
                    "requestId", request.getId().toString(),
                    "stepNumber", step.getStepNumber()
                ))
                .recipientIds(getApproversForStep(request.getId().toString(), step.getStepNumber()))
                .build();
        
        sendNotification(notificationRequest);
    }

    @Override
    public void sendApprovalCompletionNotification(ApprovalRequest request) {
        NotificationRequest notificationRequest = NotificationRequest.builder()
                .type(Notification.NotificationType.APPROVAL_COMPLETED)
                .title("Approval Request Completed")
                .message(String.format("Your %s approval request has been approved", 
                    request.getRequestType().getDisplayName()))
                .priority(Notification.Priority.MEDIUM)
                .metadata(Map.of("requestId", request.getId().toString()))
                .recipientIds(List.of(String.valueOf(request.getRequesterId())))
                .build();
        
        sendNotification(notificationRequest);
    }

    @Override
    public void sendApprovalRejectionNotification(ApprovalRequest request) {
        NotificationRequest notificationRequest = NotificationRequest.builder()
                .type(Notification.NotificationType.APPROVAL_REJECTED)
                .title("Approval Request Rejected")
                .message(String.format("Your %s approval request has been rejected", 
                    request.getRequestType().getDisplayName()))
                .priority(Notification.Priority.HIGH)
                .metadata(Map.of("requestId", request.getId().toString()))
                .recipientIds(List.of(String.valueOf(request.getRequesterId())))
                .build();
        
        sendNotification(notificationRequest);
    }

    @Override
    public void sendApprovalCompletionNotification(String requestId, String requesterId, String requestType) {
        NotificationRequest notificationRequest = NotificationRequest.builder()
                .type(Notification.NotificationType.APPROVAL_COMPLETED)
                .title("Approval Request Completed")
                .message(String.format("Your %s approval request has been approved", requestType))
                .priority(Notification.Priority.MEDIUM)
                .metadata(Map.of("requestId", requestId))
                .recipientIds(List.of(requesterId))
                .build();
        
        sendNotification(notificationRequest);
    }

    @Override
    public void sendApprovalRejectionNotification(String requestId, String requesterId, String requestType, String comments) {
        NotificationRequest notificationRequest = NotificationRequest.builder()
                .type(Notification.NotificationType.APPROVAL_REJECTED)
                .title("Approval Request Rejected")
                .message(String.format("Your %s approval request has been rejected. Comments: %s", 
                    requestType, comments))
                .priority(Notification.Priority.HIGH)
                .metadata(Map.of(
                    "requestId", requestId,
                    "comments", comments
                ))
                .recipientIds(List.of(requesterId))
                .build();
        
        sendNotification(notificationRequest);
    }

    private void sendFcmNotifications(Notification notification, List<String> userIds) {
        // TODO: Get FCM tokens for users
        List<String> fcmTokens = getFcmTokensForUsers(userIds);
        userIds.forEach(userId -> {
            // Send FCM notification to each user
            try {
                Message message = Message.builder()
                        .setNotification(com.google.firebase.messaging.Notification.builder()
                                .setTitle(notification.getTitle())
                                .setBody(notification.getMessage())
                                .build())
                        .putData("notificationId", notification.getNotificationId())
                        .putData("type", notification.getType().name())
                        .putData("priority", notification.getPriority().name())
                        .setTopic(userId + "_notify")
                        .build();

                firebaseMessaging.send(message);
                log.info("FCM notification sent successfully to token: {}", "12345");
            } catch (Exception e) {
                log.error("Failed to send FCM notification to token: {}", "12345", e);
            }
        });

    }

    private List<String> getFcmTokensForUsers(List<String> userIds) {
        // TODO: Implement FCM token retrieval
        return List.of();
    }


    private List<String> getApproversForStep(String requestId, int stepNumber) {
        // TODO: Implement this method to fetch approvers for a specific step
        return List.of();
    }


    @Override
    public NotificationResponse mapToResponse(NotificationRecipient recipient) {
        Notification notification   =recipient.getNotification();

        String requesterId = notification.getCreatedBy();
        EmployeeBasicDetail employeeBasicDetail  =userCacheService.getUserById(Integer.parseInt(requesterId));
        EmployeeBasicDetail requesterDetails = null;
        try {
            requesterDetails = userCacheService.getUserById(Integer.parseInt(requesterId));
        } catch (Exception e) {
            log.warn("Failed to fetch requester details for user ID: {}", requesterId, e);
        }

        return NotificationResponse.builder()
                .notificationId(notification.getNotificationId())
                .type(notification.getType())
                .title(notification.getTitle())
                .message(notification.getMessage())
                .priority(notification.getPriority())
                .metadata(notification.getMetadata())
                .createdDate(notification.getCreatedDate())
                .requesterId(requesterId)
                .requesterName(requesterDetails != null ? requesterDetails.getName() : null)
                .requesterDesignation(requesterDetails != null ? requesterDetails.getDesignation() : null)
                .requesterImageUrl(employeeBasicDetail.getImagekey())
                .requesterEmployeeCode(employeeBasicDetail.getEmployeeCode())
                .status(recipient.getStatus())
                .build();
    }

    @Override
    @Transactional
    public void bulkMarkAsRead(List<String> notificationIds, String userId) {
        List<NotificationRecipient> recipients = recipientRepository.findByNotificationIdInAndUserId(notificationIds, userId);
        LocalDateTime now = DateTimeUtil.now();
        
        recipients.forEach(recipient -> {
            recipient.setStatus(NotificationRecipient.DeliveryStatus.READ);
            recipient.setReadDate(now);
            recipient.setUpdatedDate(now);
            recipient.setUpdatedBy("SYSTEM");
        });
        
        recipientRepository.saveAll(recipients);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<NotificationRecipient> getUserNotifications(String userId, Pageable pageable, String type) {
        // Get notification types based on the provided type string
        List<Notification.NotificationType> notificationTypes = Notification.NotificationType.getNotificationTypes(type);
        
        if (notificationTypes == null || notificationTypes.isEmpty()) {
            // If no types found, return empty page
            return Page.empty(pageable);
        }
        
        Page<NotificationRecipient> notificationPage = recipientRepository.findByUserIdAndTypeInOrderByCreatedDateDesc(
                userId, notificationTypes, pageable);
        List<NotificationRecipient> recipients = notificationPage.getContent();
        
        // Get all unique requester IDs
        Set<String> requesterIds = recipients.stream()
                .map(recipient -> recipient.getNotification().getCreatedBy())
                .collect(Collectors.toSet());
        
        // Batch fetch all requester details
        Map<String, EmployeeBasicDetail> requesterDetailsMap = new HashMap<>();
        try {
            requesterIds.forEach(requesterId -> {
                try {
                    EmployeeBasicDetail details = userCacheService.getUserById(Integer.parseInt(requesterId));
                    if (details != null) {
                        requesterDetailsMap.put(requesterId, details);
                    }
                } catch (Exception e) {
                    log.warn("Failed to fetch requester details for user ID: {}", requesterId, e);
                }
            });
        } catch (Exception e) {
            log.error("Failed to batch fetch requester details", e);
        }
        
        return notificationPage;
    }
    @Override
    public void sendRosteringNotification(RosteringNotificationRequest request, Notification.NotificationType notificationType,
                                          String title,String messagePrefix) {
        // Build message based on whether effectiveTo is null
        String message;
        if (request.getEffectiveTo() == null) {
            message = String.format("%s: %s shift at %s from %s", 
                messagePrefix, request.getShiftName(), request.getUnitName(), 
                formatDateTime(request.getEffectiveFrom()));
        } else {
            message = String.format("%s: %s shift at %s from %s to %s", 
                messagePrefix, request.getShiftName(), request.getUnitName(), 
                formatDateTime(request.getEffectiveFrom()), formatDateTime(request.getEffectiveTo()));
        }
        
        NotificationRequest notificationRequest = NotificationRequest.builder()
            .type(notificationType)
            .title(title)
                .requesterId(request.getUpdatedBy())
            .message(message)
            .priority(Notification.Priority.HIGH)
            .metadata(Map.of(
                "empId", request.getEmpId(),
                "shiftId", request.getShiftId(),
                "unitId", request.getUnitId(),
                "action", request.getAction(),
                "effectiveFrom", request.getEffectiveFrom() != null ? request.getEffectiveFrom().toString() : "",
                "effectiveTo", request.getEffectiveTo() != null ? request.getEffectiveTo().toString() : "",
                "updatedBy", request.getUpdatedBy()
            ))
            .recipientIds(List.of(String.valueOf(request.getEmpId())))
            .build();
        
        sendNotification(notificationRequest);
    }

    private String formatDateTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return " ";
        }
        return dateTime.format(java.time.format.DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm"));
    }
} 