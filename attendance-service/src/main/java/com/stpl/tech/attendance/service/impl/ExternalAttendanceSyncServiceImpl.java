package com.stpl.tech.attendance.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.stpl.tech.attendance.cache.service.UserCacheService;
import com.stpl.tech.attendance.config.ExternalSyncConfig;
import com.stpl.tech.attendance.dto.AttendanceSyncMessage;
import com.stpl.tech.attendance.dto.SyncStatistics;
import com.stpl.tech.attendance.entity.AttendanceRecord;
import com.stpl.tech.attendance.entity.AttendanceSyncRecord;
import com.stpl.tech.attendance.enums.AttendanceErrorCode;
import com.stpl.tech.attendance.enums.PunchType;
import com.stpl.tech.attendance.enums.SyncStatus;
import com.stpl.tech.attendance.exception.AttendanceException;
import com.stpl.tech.attendance.repository.AttendanceRecordRepository;
import com.stpl.tech.attendance.repository.AttendanceSyncRecordRepository;
import com.stpl.tech.attendance.service.AttendanceSyncProducerService;
import com.stpl.tech.attendance.service.ExternalAttendanceSyncService;
import com.stpl.tech.attendance.service.HttpService;
import com.stpl.tech.attendance.util.BiometricDeviceIdUtil;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Slf4j
@Service
@RequiredArgsConstructor
public class ExternalAttendanceSyncServiceImpl implements ExternalAttendanceSyncService {

    private final AttendanceRecordRepository attendanceRecordRepository;
    private final AttendanceSyncRecordRepository attendanceSyncRecordRepository;
    private final AttendanceSyncProducerService producerService;
    private final HttpService httpService;
    private final ExternalSyncConfig syncConfig;
    private final ObjectMapper objectMapper;
    private final BiometricDeviceIdUtil biometricDeviceIdUtil;
    private final UserCacheService userCacheService;

    @Override
    @Transactional
    public void queueAttendanceForSync(Long attendanceRecordId) {
        log.info("Queueing attendance record for external sync: {}", attendanceRecordId);
        
        // Check if already queued
        if (attendanceSyncRecordRepository.existsByAttendanceRecordId(attendanceRecordId)) {
            log.info("Attendance record {} already queued for sync", attendanceRecordId);
            return;
        }

        // Get attendance record
        AttendanceRecord record = attendanceRecordRepository.findById(attendanceRecordId)
            .orElseThrow(() -> new AttendanceException(AttendanceErrorCode.ATTENDANCE_PROCESSING_ERROR,"Attendance record not found: " + attendanceRecordId));

        // Create sync record
        AttendanceSyncRecord syncRecord = new AttendanceSyncRecord();
        syncRecord.setAttendanceRecordId(attendanceRecordId);
        syncRecord.setEmployeeId(record.getEmployeeId());
        syncRecord.setSyncStatus(SyncStatus.PENDING);
        syncRecord.setLastSyncedSrno(0L);
        attendanceSyncRecordRepository.save(syncRecord);

        // Create and send Kafka message
        AttendanceSyncMessage message = AttendanceSyncMessage.builder()
            .attendanceRecordId(attendanceRecordId)
            .employeeId(record.getEmployeeId())
            .punchTime(record.getPunchTime())
            .punchType(record.getPunchType().name())
            .biometricId(record.getBiometricDeviceId())
            .location(record.getGeoLocation())
            .srno(0L)
            .syncId(UUID.randomUUID().toString())
            .build();

        producerService.sendAttendanceSyncMessage(message);
        log.info("Attendance sync message queued for record: {}", attendanceRecordId);
    }

    /**
     * Extract biometric ID from device ID format: terminalId_unitType_unitId
     * Returns the unitId part as biometric ID for external service
     */
    private String extractBiometricId(String biometricDeviceId) {
        return biometricDeviceIdUtil.extractBiometricId(biometricDeviceId);
    }

    @Override
    @Transactional
    public void processAttendanceSync(AttendanceSyncMessage message) {
        log.info("Processing attendance sync for record: {}", message.getAttendanceRecordId());
        
        AttendanceSyncRecord syncRecord = attendanceSyncRecordRepository
            .findByAttendanceRecordId(message.getAttendanceRecordId())
            .orElseThrow(() -> new AttendanceException(AttendanceErrorCode.ATTENDANCE_PROCESSING_ERROR,"Sync record not found"));

        try {
            // Update sync status to in progress
            syncRecord.setSyncStatus(SyncStatus.IN_PROGRESS);
            syncRecord.setLastSyncAttempt(LocalDateTime.now());
            syncRecord.setSyncAttempts(syncRecord.getSyncAttempts() + 1);
            attendanceSyncRecordRepository.save(syncRecord);
            EmployeeBasicDetail employeeBasicDetail  = userCacheService.getUserById(syncRecord.getEmployeeId());

            // Get attendance record
            AttendanceRecord record = attendanceRecordRepository.findById(message.getAttendanceRecordId())
                .orElseThrow(() -> new AttendanceException(AttendanceErrorCode.ATTENDANCE_PROCESSING_ERROR,"Attendance record not found"));

            // Prepare data for external API
            Map<String, Object> attendanceData = prepareAttendanceData(record, employeeBasicDetail.getEmployeeCode());
            
            // Send to external API
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("callBackKey", "attendanceFromBiometric");
            requestBody.put("dataStr", objectMapper.writeValueAsString(attendanceData));
            requestBody.put("authKey", syncConfig.getAuthorizationKey());

            log.info("Sending attendance data to external API: {}", new Gson().toJson(requestBody));
            
            String response = httpService.post(
                syncConfig.getPostUrl(),
                requestBody,
                String.class
            ).block();

            // Update sync record on success
            syncRecord.setSyncStatus(SyncStatus.SUCCESS);
            syncRecord.setExternalResponse(response);
            syncRecord.setLastSyncedSrno(record.getId());
            attendanceSyncRecordRepository.save(syncRecord);

            log.info("Attendance sync completed successfully for record: {}", message.getAttendanceRecordId());

        } catch (Exception e) {
            log.error("Error processing attendance sync for record: {}", message.getAttendanceRecordId(), e);
            
            // Update sync record on failure
            syncRecord.setSyncStatus(SyncStatus.FAILED);
            syncRecord.setErrorMessage(e.getMessage());
            attendanceSyncRecordRepository.save(syncRecord);

            // Retry logic
            if (syncRecord.getSyncAttempts() < syncConfig.getMaxRetryAttempts()) {
                log.info("Scheduling retry for attendance sync record: {}", message.getAttendanceRecordId());
                scheduleRetry(message, syncRecord.getSyncAttempts());
            } else {
                log.error("Max retry attempts reached for attendance sync record: {}", message.getAttendanceRecordId());
            }
        }
    }

    private Map<String, Object> prepareAttendanceData(AttendanceRecord record, String empCode) {
        // Create the recordset array with the attendance record
        Map<String, Object> recordData = new HashMap<>();
        recordData.put("AttDate", record.getPunchTime().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")));
        recordData.put("EmpCode", empCode);
        recordData.put("checkin", record.getPunchTime().format(DateTimeFormatter.ofPattern("HH:mm:ss")));
        recordData.put("checkout", record.getPunchTime().format(DateTimeFormatter.ofPattern("HH:mm:ss")));
        recordData.put("BiometricId", record.getBiometricDeviceId());
        
        // Create the main data structure with recordset array
        Map<String, Object> data = new HashMap<>();
        data.put("recordset", List.of(recordData));
        data.put("genericTime", true);
        
        return data;
    }

    private void scheduleRetry(AttendanceSyncMessage message, int attemptCount) {
        // For now, we'll use a simple delay. In production, you might want to use a more sophisticated retry mechanism
        message.setRetryCount(attemptCount);
        
        // Schedule retry after delay
        new Thread(() -> {
            try {
                Thread.sleep(syncConfig.getRetryDelayMinutes() * 60 * 1000L);
                producerService.sendAttendanceSyncMessage(message);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("Retry scheduling interrupted for record: {}", message.getAttendanceRecordId());
            }
        }).start();
    }

    @Override
    public void fetchAndSyncAttendanceData() {
    }

    @Override
    public AttendanceSyncRecord getSyncStatus(Long attendanceRecordId) {
        return attendanceSyncRecordRepository.findByAttendanceRecordId(attendanceRecordId)
            .orElse(null);
    }

    @Override
    @Scheduled(fixedDelay = 600000) // Run every 10 minutes
    public void retryFailedSyncs() {
        log.info("Starting retry of failed sync records");
        
        List<AttendanceSyncRecord> failedRecords = attendanceSyncRecordRepository
            .findBySyncStatusAndSyncAttemptsLessThan(SyncStatus.FAILED, syncConfig.getMaxRetryAttempts());
        
        for (AttendanceSyncRecord record : failedRecords) {
            log.info("Retrying failed sync record: {}", record.getAttendanceRecordId());
            
            AttendanceSyncMessage message = AttendanceSyncMessage.builder()
                .attendanceRecordId(record.getAttendanceRecordId())
                .employeeId(record.getEmployeeId())
                .syncId(UUID.randomUUID().toString())
                .retryCount(record.getSyncAttempts())
                .build();
            
            producerService.sendAttendanceSyncMessage(message);
        }
    }

    @Override
    public SyncStatistics getSyncStatistics() {
        long total = attendanceSyncRecordRepository.count();
        long pending = attendanceSyncRecordRepository.countBySyncStatus(SyncStatus.PENDING);
        long successful = attendanceSyncRecordRepository.countBySyncStatus(SyncStatus.SUCCESS);
        long failed = attendanceSyncRecordRepository.countBySyncStatus(SyncStatus.FAILED);
        long inProgress = attendanceSyncRecordRepository.countBySyncStatus(SyncStatus.IN_PROGRESS);
        
        double successRate = total > 0 ? (double) successful / total * 100 : 0;
        
        return SyncStatistics.builder()
            .totalRecords(total)
            .pendingRecords(pending)
            .successfulRecords(successful)
            .failedRecords(failed)
            .inProgressRecords(inProgress)
            .successRate(successRate)
            .lastSyncTime(System.currentTimeMillis())
            .build();
    }

    @Override
    @Transactional
    public void resyncAttendanceData(LocalDate fromDate, LocalDate toDate, Integer employeeId) {
        log.info("Starting resync for date range: {} to {}, employee: {}", fromDate, toDate, employeeId);
        
        // Find attendance records in the date range
        List<AttendanceRecord> records = attendanceRecordRepository
            .findByEmployeeIdAndDateRange(employeeId, fromDate, toDate);
        
        log.info("Found {} attendance records to resync", records.size());
        
        for (AttendanceRecord record : records) {
            // Delete existing sync record if exists
            attendanceSyncRecordRepository.deleteByAttendanceRecordId(record.getId());
            
            // Queue for resync
            queueAttendanceForSync(record.getId());
        }
        
        log.info("Resync queued for {} attendance records", records.size());
    }

    @Override
    @Transactional
    public void resyncFailedRecords() {
        log.info("Starting resync of all failed records");
        
        List<AttendanceSyncRecord> failedRecords = attendanceSyncRecordRepository
            .findBySyncStatus(SyncStatus.FAILED);
        
        log.info("Found {} failed records to resync", failedRecords.size());
        
        for (AttendanceSyncRecord syncRecord : failedRecords) {
            // Reset sync record
            syncRecord.setSyncStatus(SyncStatus.PENDING);
            syncRecord.setSyncAttempts(0);
            syncRecord.setErrorMessage(null);
            syncRecord.setExternalResponse(null);
            attendanceSyncRecordRepository.save(syncRecord);
            
            // Get attendance record and queue for resync
            AttendanceRecord record = attendanceRecordRepository.findById(syncRecord.getAttendanceRecordId())
                .orElse(null);
            
            if (record != null) {
                AttendanceSyncMessage message = AttendanceSyncMessage.builder()
                    .attendanceRecordId(record.getId())
                    .employeeId(record.getEmployeeId())
                    .punchTime(record.getPunchTime())
                    .punchType(record.getPunchType().name())
                    .biometricId(record.getBiometricDeviceId())
                    .location(record.getGeoLocation())
                    .srno(0L)
                    .syncId(UUID.randomUUID().toString())
                    .build();
                
                producerService.sendAttendanceSyncMessage(message);
            }
        }
        
        log.info("Resync queued for {} failed records", failedRecords.size());
    }

    @Override
    public List<AttendanceSyncRecord> getSyncRecordsByDateRange(LocalDate fromDate, LocalDate toDate, Integer employeeId) {
        return attendanceSyncRecordRepository.findByDateRangeAndEmployeeId(fromDate, toDate, employeeId);
    }

    @Override
    public List<AttendanceSyncRecord> getFailedSyncRecords() {
        return attendanceSyncRecordRepository.findBySyncStatus(SyncStatus.FAILED);
    }
} 