package com.stpl.tech.attendance.dto.RosteringDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GenericFilterMetadataDTO {
    
    private List<FilterOption> availableFilters;
    private FilterAppliedFlags appliedFilters;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FilterOption {
        private String filterKey; // e.g., "unitIds", "cityNames", "shiftIds"
        private String filterName; // e.g., "Units", "Cities", "Shifts"
        private String dataType; // e.g., "INTEGER", "STRING"
        private List<FilterValueOptionDTO> valueOptions; // Linked value and display name pairs
        private String operator; // Default operator for this filter
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FilterAppliedFlags {
        private Map<String, Boolean> appliedFilters; // filterKey -> isApplied
        private Boolean anyFilterApplied;
    }
} 