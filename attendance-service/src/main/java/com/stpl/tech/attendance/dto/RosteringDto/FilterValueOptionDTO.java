package com.stpl.tech.attendance.dto.RosteringDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FilterValueOptionDTO {
    private String value; // The actual value (e.g., "1", "2", "3")
    private String displayName; // The display name (e.g., "Unit A", "Unit B", "Unit C")
} 