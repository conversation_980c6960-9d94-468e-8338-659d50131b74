package com.stpl.tech.attendance.notification.service;

import com.stpl.tech.attendance.approval.entity.ApprovalRequest;
import com.stpl.tech.attendance.approval.entity.ApprovalStep;
import com.stpl.tech.attendance.notification.dto.NotificationRequest;
import com.stpl.tech.attendance.notification.dto.NotificationResponse;
import com.stpl.tech.attendance.notification.dto.RosteringNotificationRequest;
import com.stpl.tech.attendance.notification.entity.Notification;
import com.stpl.tech.attendance.notification.entity.NotificationRecipient;
import com.stpl.tech.attendance.notification.entity.Notification.NotificationType;
import com.stpl.tech.attendance.notification.entity.NotificationRecipient.DeliveryStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

public interface NotificationService {
    
    /**
     * Sends a notification to one or more recipients
     * @param request The notification request
     */
    void sendNotification(NotificationRequest request);
    
    /**
     * Sends a workflow-related notification
     * @param workflowId The workflow ID
     * @param type The notification type
     * @param title The notification title
     * @param message The notification message
     * @param recipients List of recipient user IDs
     * @param metadata Additional metadata
     */
    void sendWorkflowNotification(
        String workflowId,
        NotificationType type,
        String title,
        String message,
        List<String> recipients,
        Map<String, Object> metadata
    );
    
    /**
     * Gets notifications for a user
     * @param userId The user ID
     * @param page Page number (0-based)
     * @param size Page size
     * @return List of notifications
     */
    List<NotificationResponse> getUserNotifications(String userId, int page, int size);
    
    /**
     * Marks a notification as read
     * @param notificationId The notification ID
     * @param userId The user ID
     */
    void markAsRead(String notificationId, String userId);
    
    /**
     * Gets the delivery status of a notification
     * @param notificationId The notification ID
     * @param userId The user ID
     * @return The delivery status
     */
    DeliveryStatus getDeliveryStatus(String notificationId, String userId);
    
    /**
     * Deletes a notification
     * @param notificationId The notification ID
     * @param userId The user ID
     */
    void deleteNotification(String notificationId, String userId);
    
    /**
     * Sends a notification for a new approval request
     * @param request The approval request
     */
    void sendApprovalRequestNotification(ApprovalRequest request);
    
    /**
     * Sends a notification for an approval step update
     * @param request The approval request
     * @param step The approval step
     */
    void sendApprovalStepNotification(ApprovalRequest request, ApprovalStep step);
    
    /**
     * Sends a notification for approval completion
     * @param request The approval request
     */
    void sendApprovalCompletionNotification(ApprovalRequest request);
    
    /**
     * Sends a notification for approval rejection
     * @param request The approval request
     */
    void sendApprovalRejectionNotification(ApprovalRequest request);
    
    /**
     * Sends a notification for approval completion
     * @param requestId The request ID
     * @param requesterId The requester ID
     * @param requestType The request type
     */
    void sendApprovalCompletionNotification(String requestId, String requesterId, String requestType);
    
    /**
     * Sends a notification for approval rejection
     * @param requestId The request ID
     * @param requesterId The requester ID
     * @param requestType The request type
     * @param comments The rejection comments
     */
    void sendApprovalRejectionNotification(String requestId, String requesterId, String requestType, String comments);

    Page<NotificationRecipient> getUserNotifications(String userId, Pageable pageable, String type);

    void bulkMarkAsRead(List<String> notificationIds, String userId);

    public NotificationResponse mapToResponse(NotificationRecipient notification);

    void sendRosteringNotification(RosteringNotificationRequest request,NotificationType notificationType,String title, String messagePrefix);
}