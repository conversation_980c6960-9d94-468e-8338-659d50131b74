package com.stpl.tech.attendance.repository;

import com.stpl.tech.attendance.entity.DailyAttendanceSummary;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Repository
public interface DailyAttendanceSummaryRepository extends JpaRepository<DailyAttendanceSummary, Long> {
    Optional<DailyAttendanceSummary> findByEmployeeIdAndAttendanceDate(Integer employeeId, LocalDate attendanceDate);
    
    @Query("SELECT d FROM DailyAttendanceSummary d WHERE d.employeeId = :employeeId " +
           "AND EXTRACT(MONTH FROM d.attendanceDate) = :month " +
           "AND EXTRACT(YEAR FROM d.attendanceDate) = :year " +
           "ORDER BY d.attendanceDate DESC")
    List<DailyAttendanceSummary> findByEmployeeIdAndMonthYear(
            @Param("employeeId") Integer employeeId,
            @Param("month") Integer month,
            @Param("year") Integer year);
    
    @Query("SELECT d FROM DailyAttendanceSummary d WHERE d.employeeId = :employeeId " +
           "AND EXTRACT(YEAR FROM d.attendanceDate) = :year " +
           "ORDER BY d.attendanceDate DESC")
    List<DailyAttendanceSummary> findByEmployeeIdAndYear(
            @Param("employeeId") Integer employeeId,
            @Param("year") Integer year);
} 