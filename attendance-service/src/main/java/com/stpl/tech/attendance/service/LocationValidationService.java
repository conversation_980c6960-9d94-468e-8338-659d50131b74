package com.stpl.tech.attendance.service;


import com.stpl.tech.attendance.cache.service.ApplicationDataCacheService;
import com.stpl.tech.attendance.cache.service.UnitCacheService;
import com.stpl.tech.attendance.entity.ApplicationData;
import com.stpl.tech.attendance.entity.ApplicationInstallationData;
import com.stpl.tech.attendance.exception.AuthenticationFailureException;
import com.stpl.tech.attendance.repository.ApplicationDataRepository;
import com.stpl.tech.attendance.repository.ApplicationInstallationDataRepository;
import com.stpl.tech.attendance.util.AppUtils;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

@Service
public class LocationValidationService {
    private static final Logger LOG = LoggerFactory.getLogger(LocationValidationService.class);

    @Autowired
    private ApplicationDataRepository applicationDataRepository;

    @Autowired
    private ApplicationDataCacheService applicationDataCacheService;

    @Autowired
    private UnitCacheService unitCacheService;

    @Autowired
    private ApplicationInstallationDataRepository installationDataRepository;

    /**
     * Validates the location and MAC address for a given application and unit
     * @param applicationId The application ID
     * @param macAddress The MAC address to validate
     * @param geoLocation The geolocation to validate (format: "latitude,longitude")
     * @param unitId The unit ID
     * @throws AuthenticationFailureException if validation fails
     */
    public void validateLocation(Integer applicationId, String macAddress, String geoLocation, Integer unitId)
            throws AuthenticationFailureException {
        LOG.info("application id : {} ", applicationId);
        
        // Try to get from cache first
        ApplicationData appData = applicationDataCacheService.getCachedApplicationData(applicationId);
        
        // If not in cache, fetch from database and cache it
        if (appData == null) {
            LOG.info("Cache miss for application data, fetching from database for application ID: {}", applicationId);
            appData = applicationDataRepository.findById(applicationId)
                .orElseThrow(() -> new AuthenticationFailureException("Invalid application"));
            
            // Cache the application data for future use
            applicationDataCacheService.cacheApplicationData(applicationId, appData);
            LOG.debug("Cached application data for application ID: {}", applicationId);
        } else {
            LOG.debug("Cache hit for application data, application ID: {}", applicationId);
        }

        // Validate MAC address if enabled
        if (AppUtils.getStatus(appData.getMacValidationEnabled())) {
            validateMacAddress(macAddress, unitId);
        }

        // Validate geolocation if enabled
        if (AppUtils.getStatus(appData.getGeoValidationEnabled())) {
            validateGeoLocation(geoLocation, unitId, appData.getGeoValidationRadius());
        }
    }

    /**
     * Validates the MAC address against registered devices for the unit
     * @param macAddress The MAC address to validate
     * @param unitId The unit ID
     * @throws AuthenticationFailureException if validation fails
     */
    private void validateMacAddress(String macAddress, Integer unitId)
            throws AuthenticationFailureException {
        
        if (macAddress == null || macAddress.trim().isEmpty()) {
            throw new AuthenticationFailureException("MAC address is required");
        }

        // Get registered MAC addresses for the unit
        List<ApplicationInstallationData> installations = 
            installationDataRepository.findByUnitIdAndStatus(unitId, "ACTIVE");

        boolean isValid = installations.stream()
            .anyMatch(installation -> 
                isMacAddressValid(installation.getMachineId(), macAddress));

        if (!isValid) {
            LOG.warn("MAC address validation failed for unit {} with MAC {}", unitId, macAddress);
            throw new AuthenticationFailureException(
                "MAC address validation failed. Device not registered or out of range.");
        }
    }

    /**
     * Validates the geolocation against the unit's location
     * @param geoLocation The geolocation to validate (format: "latitude,longitude")
     * @param unitId The unit ID
     * @param radius The allowed radius in meters
     * @throws AuthenticationFailureException if validation fails
     */
    private void validateGeoLocation(String geoLocation, Integer unitId, BigDecimal radius)
            throws AuthenticationFailureException {
        LOG.info("validating geoloation");
        if (geoLocation == null || geoLocation.trim().isEmpty()) {
            throw new AuthenticationFailureException("Geolocation is required");
        }

        // Get unit's location
        UnitBasicDetail unitBasicDetail = unitCacheService.getUnitBasicDetail(unitId);
        String lat = unitBasicDetail.getLatitude();
        String longitude = unitBasicDetail.getLongitude();

        if (lat == null || longitude == null) {
            throw new AuthenticationFailureException("Unit location not configured");
        }

        // Check if user's location is within allowed radius
        if (!isLocationInRange(Double.parseDouble(lat), Double.parseDouble(longitude), geoLocation, radius.doubleValue())) {
            LOG.error("Geolocation validation failed for unit {} with location {}", unitId, geoLocation);
            LOG.error("Unit location: {}, {}", lat, longitude);
//            throw new AuthenticationFailureException(
//                "Geolocation validation failed. Device out of allowed range.");
        }
    }

    /**
     * Validates if a MAC address matches the registered one
     * @param registeredMac The registered MAC address
     * @param currentMac The current MAC address to validate
     * @return true if MAC addresses match
     */
    private boolean isMacAddressValid(String registeredMac, String currentMac) {
        return registeredMac != null && registeredMac.equalsIgnoreCase(currentMac);
    }

    /**
     * Checks if a location is within the allowed radius of a unit
     * @param unitLat The unit's latitude
     * @param unitLon The unit's longitude
     * @param userLocation The user's location (format: "latitude,longitude")
     * @param radius The allowed radius in meters
     * @return true if location is within radius
     */
    private boolean isLocationInRange(Double unitLat, Double unitLon, String userLocation, Double radius) {
        try {
            // Parse locations (assuming format: "latitude,longitude")
            String[] userCoords = userLocation.split(",");

            double userLat = Double.parseDouble(userCoords[0]);
            double userLon = Double.parseDouble(userCoords[1]);

            // Calculate distance using Haversine formula
            double distance = calculateDistance(unitLat, unitLon, userLat, userLon);

            return distance <= radius;
        } catch (Exception e) {
            LOG.error("Error calculating location distance", e);
            return false;
        }
    }

    /**
     * Calculates the distance between two points using the Haversine formula
     * @param lat1 Latitude of first point
     * @param lon1 Longitude of first point
     * @param lat2 Latitude of second point
     * @param lon2 Longitude of second point
     * @return Distance in meters
     */
    private double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
        final int R = 6371000; // Earth's radius in meters

        double latDistance = Math.toRadians(lat2 - lat1);
        double lonDistance = Math.toRadians(lon2 - lon1);
        
        double a = Math.sin(latDistance / 2) * Math.sin(latDistance / 2)
                + Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2))
                * Math.sin(lonDistance / 2) * Math.sin(lonDistance / 2);
        
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        
        return R * c;
    }
} 