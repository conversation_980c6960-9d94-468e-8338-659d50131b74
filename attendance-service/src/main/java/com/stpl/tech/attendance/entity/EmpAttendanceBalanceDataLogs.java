package com.stpl.tech.attendance.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Entity representing employee leave data change logs
 * Maps to EMP_ATTENDANCE_BALANCE_DATA_LOGS table
 */
@Entity
@Table(name = "EMP_ATTENDANCE_BALANCE_DATA_LOGS")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmpAttendanceBalanceDataLogs {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;

    @Column(name = "EMP_ATTENDANCE_BALANCE_ID", nullable = false)
    private Long empAttendanceBalanceId;

    @Column(name = "ACTION", length = 45)
    private String action; // DEBIT/CREDIT

    @Column(name = "TYPE", length = 45)
    private String type; // LEAVE, COMP_OFF, LWP, WEEK_OFF

    @Column(name = "PREVIOUS_VALUE")
    private String previousValue; // Leave balance before the change

    @Column(name = "NEW_VALUE")
    private String newValue; // Leave balance after the change

    @Column(name = "UPDATED_BY", length = 45)
    private String updatedBy;

    @Column(name = "UPDATED_ON")
    private LocalDateTime updatedOn;

    @PrePersist
    protected void onCreate() {
        updatedOn = LocalDateTime.now();
    }
}
