package com.stpl.tech.attendance.service.RosteringService;

import com.stpl.tech.attendance.dto.RosteringDto.*;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.List;

public interface RosteringService {

    /**
     * Get rostering metadata flags
     * @param employeeId Employee ID from JWT context
     * @param unitId Unit ID from JWT context
     * @return Rostering metadata response with feature flags
     */
    RosteringMetadataResponseDTO getRosteringMetadata(Integer employeeId, Integer unitId);

    /**
     * Get cafe live dashboard data based on employee ID
     *
     * @param employeeId Employee ID to fetch dashboard data for
     * @return Cafe live dashboard response
     */
    RosterLiveDashboardResponseDTO getCafeLiveDashboard(Integer employeeId);

    /**
     * Get cafe live dashboard data with filters
     *
     * @param employeeId Employee ID to fetch dashboard data for
     * @param filterRequest Optional generic filter request
     * @return Cafe live dashboard response
     */
    RosterLiveDashboardResponseDTO getRosterLiveDashboardWithFilters(Integer employeeId, GenericFilterRequestDTO filterRequest);

    /**
     * Get shift employees data with generic filters
     * @param shiftIds List of shift IDs to filter by
     * @param date Business date for the query
     * @param userId User ID from JWT context
     * @param filterRequest Optional generic filter request
     * @return Shift employees response with filter metadata
     */
    ShiftEmployeesResponseDTO getShiftEmployeesForUser( Timestamp date, Integer userId, GenericFilterRequestDTO filterRequest);

    /**
     * Get employee shift data
     * @param empId Employee ID
     * @param startDate Optional start date filter
     * @param endDate Optional end date filter
     * @return Employee shift data response
     */
    EmployeeShiftDataResponseDTO getEmpUpcomingShiftData(Integer empId, LocalDate startDate, LocalDate endDate);

    /**
     * Get hierarchy employees with filters
     * @param userId User ID from JWT context
     * @param filterRequest Optional generic filter request
     * @param searchTerm Optional search term
     * @param page Page number (0-based)
     * @param size Page size
     * @return Hierarchy employees response with filter metadata
     */
    HierarchyEmployeesResponseDTO getHierarchyEmployeesWithFilters(Integer userId, GenericFilterRequestDTO filterRequest, String searchTerm, int page, int size);

    /**
     * Update employee shift mapping
     * @param request Update request
     * @return Updated employee shift mapping
     */
//    EmpShiftMappingDTO updateEmpShift(EmpShiftUpdateRequestDTO request);

    /**
     * Get cafe shift data
     * @param employeeId Employee ID from JWT context
     * @param unitId Unit ID from JWT context to filter data
     * @return List of cafe shift data
     */
    CafeShiftDataDTO getCafeShiftData(Integer employeeId, Integer unitId);
    // Additional utility methods

    /**
     * Create a new shift
     * @param shiftRequestDTO Shift request data
     * @param createdBy Employee ID from HttpServletRequest
     * @return Created shift
     */
    ShiftResponseDTO createShift(ShiftRequestDTO shiftRequestDTO, String createdBy);

    /**
     * Update an existing shift
     * @param shiftId Shift ID
     * @param shiftRequestDTO Updated shift data
     * @param updatedBy Employee ID from HttpServletRequest
     * @return Updated shift
     */
    ShiftResponseDTO updateShift(ShiftRequestDTO shiftRequestDTO, String updatedBy);

    /**
     * Delete a shift
     * @param shiftId Shift ID
     */
//    void deleteShift(Integer shiftId);

    /**
     * Get all shifts
     * @return List of shifts
     */
    List<ShiftResponseDTO> getAllShifts();

    /**
     * Create shift cafe mapping
     * @param request Shift cafe mapping request
     * @param createdBy Created by user
     * @return Created mapping response
     */
    ShiftCafeMappingResponseDTO createShiftCafeMapping(ShiftCafeMappingRequestDTO request);


    @Transactional(rollbackFor = Exception.class, readOnly = false)
    ShiftCafeMappingResponseDTO updateShiftCafeMapping(ShiftCafeMappingRequestDTO request);

    /**
     * Delete shift cafe mapping
     * @param shiftId Shift ID
     * @param unitId Unit ID
     */
//    void deleteShiftCafeMapping(Integer shiftId, Integer unitId);

    /**
     * Create employee shift mapping
     * @param request Employee shift mapping request
     * @return Created mapping
     */
//    EmpShiftMappingDTO createEmpShiftMapping(EmpShiftUpdateRequestDTO request,Integer userId);

    /**
     * Delete employee shift mapping
     * @param mappingId Mapping ID
     */
    void deleteEmpShiftMapping(Integer mappingId);

    /**
     * Get all units managed by the current user
     * @param userId User ID from JWT context
     * @return List of unit basic details managed by the user
     */
    List<com.stpl.tech.master.domain.model.UnitBasicDetail> getManagedUnits(Integer userId);

    /**
     * Create employee shift override mapping
     * @param request Employee shift update request
     */
    void createEmpShiftMappingOverriding(EmpShiftUpdateRequestDTO request);


}
