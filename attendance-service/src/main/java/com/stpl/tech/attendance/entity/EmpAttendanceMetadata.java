package com.stpl.tech.attendance.entity;

import com.stpl.tech.attendance.enums.AttendanceAttributeType;
import com.stpl.tech.attendance.enums.MappingStatus;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Entity representing the EMP_ATTENDANCE_METADATA table
 * Stores department-wise attendance configuration attributes
 */
@Entity
@Table(name = "EMP_ATTENDANCE_METADATA")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmpAttendanceMetadata {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "EMP_ATTENDANCE_METADATA_ID")
    private Long id;
    
    @Column(name = "DEPT_ID", nullable = false)
    private Integer deptId;
    
    @Column(name = "ATTRIBUTE_ID")
    private Integer attributeId;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "ATTRIBUTE_CODE", nullable = false, length = 45)
    private AttendanceAttributeType attributeCode;
    
    @Column(name = "ATTRIBUTE_TYPE", length = 45)
    private String attributeType;
    
    @Column(name = "ATTRIBUTE_VALUE", length = 45)
    private String attributeValue;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "MAPPING_STATUS", nullable = false, length = 45)
    private MappingStatus mappingStatus;
    
    @Column(name = "CREATED_BY")
    private String createdBy;
    
    @Column(name = "UPDATED_BY")
    private String updatedBy;
    
    @Column(name = "CREATED_AT")
    private LocalDateTime createdAt;
    
    @Column(name = "UPDATED_AT")
    private LocalDateTime updatedAt;
    
    /**
     * Get boolean value for boolean type attributes
     * @return Boolean value or null if not a boolean attribute
     */
    public Boolean getBooleanValue() {
        if (attributeCode != null && attributeCode.isBooleanType()) {
            return Boolean.parseBoolean(attributeValue);
        }
        return null;
    }
    
    /**
     * Get numeric value for numeric type attributes
     * @return BigDecimal value or null if not a numeric attribute
     */
    public BigDecimal getNumericValue() {
        if (attributeCode != null && attributeCode.isNumericType() && attributeValue != null) {
            try {
                return new BigDecimal(attributeValue);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }
    
    /**
     * Check if this is a default configuration (DEPT_ID = -1)
     * @return true if this is a default configuration
     */
    public boolean isDefaultConfiguration() {
        return deptId != null && deptId == -1;
    }
    
    /**
     * Check if this metadata is active
     * @return true if mapping status is ACTIVE
     */
    public boolean isActive() {
        return mappingStatus == MappingStatus.ACTIVE;
    }
}


