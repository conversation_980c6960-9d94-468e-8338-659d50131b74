package com.stpl.tech.attendance.model.response;

import com.stpl.tech.attendance.dto.EmployeeInfoDTO;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class ApprovalRequestResponse {
    private Long requestId;
    private EmployeeInfoDTO employeeInfo;
    private EmployeeInfoDTO createdBy;
    private EmployeeInfoDTO updatedBy;
    private String requestType;
    private String status;
    private String reason;
    private LocalDateTime requestDate;
    private LocalDateTime lastUpdatedDate;
    private String approverName;
    private String approverDesignation;
    private Integer unitId;
    private String unitName;
    private String remarks;
    private String metadata;
    private Integer currentStepNumber;
    private Long stepId;
    private String imageUrl;
    private String registrationImageUrl;
}