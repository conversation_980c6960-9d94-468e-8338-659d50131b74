package com.stpl.tech.attendance.service;

import com.stpl.tech.attendance.model.AttendancePunchRequest;
import com.stpl.tech.attendance.model.AttendancePunchResponse;
import com.stpl.tech.attendance.model.AttendanceResponse;
import com.stpl.tech.attendance.model.BulkAttendancePunchRequest;
import com.stpl.tech.attendance.model.BulkAttendancePunchResponse;

import java.time.LocalDate;
import java.util.List;

public interface AttendancePunchService {
    /**
     * Process attendance punch for an employee
     * @param request The attendance punch request containing employee and punch details
     * @return AttendancePunchResponse with punch status and details
     */
    AttendancePunchResponse processAttendancePunch(AttendancePunchRequest request);

    /**
     * Process attendance punch for an employee with multiple images support
     * @param request The attendance punch request containing employee and punch details with multiple images
     * @return AttendancePunchResponse with punch status and details
     */
    AttendancePunchResponse processAttendancePunch(AttendancePunchRequest request, boolean useMultiImage);

    /**
     * Process bulk attendance punch for multiple employees
     * @param request The bulk attendance punch request containing multiple attendance punch requests
     * @return BulkAttendancePunchResponse with overall status and individual results
     */
    BulkAttendancePunchResponse processBulkAttendancePunch(BulkAttendancePunchRequest request);

    /**
     * Get today's attendance for an employee
     * @param employeeId The employee ID
     * @return Today's attendance details
     */
    AttendanceResponse getTodayAttendance(Integer employeeId);

    /**
     * Get attendance for an employee by date/month/year
     * @param employeeId The employee ID
     * @param date Optional specific date
     * @param month Optional month
     * @param year Optional year
     * @return List of attendance records
     */
    List<AttendanceResponse> getAttendanceByDate(Integer employeeId, LocalDate date, Integer month, Integer year);

    /**
     * Get attendance history for an employee
     * @param employeeId The employee ID
     * @param date Optional specific date
     * @param month Optional month
     * @param year Optional year
     * @return List of attendance records
     */
    List<AttendanceResponse> getAttendanceHistory(Long employeeId, LocalDate date, Integer month, Integer year);

    /**
     * Get current day's attendance for an employee
     * @param employeeId The employee ID
     * @return Current day's attendance details
     */
    AttendanceResponse getCurrentDayAttendance(Long employeeId);
} 