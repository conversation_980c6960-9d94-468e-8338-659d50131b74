package com.stpl.tech.attendance.model.request;

import com.stpl.tech.attendance.dto.BiometricAdditionalImageDTO;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
public class BiometricRegistrationRequest {
    @NotBlank(message = "Employee ID is required")
    private String empId;
    
    @NotBlank(message = "Unit ID is required")
    private String unitId;
    
    @NotBlank(message = "Device ID is required")
    private String deviceId;
    
    @NotNull(message = "Latitude is required")
    private Double latitude;
    
    @NotNull(message = "Longitude is required")
    private Double longitude;
    
    @NotBlank(message = "Base64 image is required")
    private String base64Image;

    private String originalImage;

    private String verificationFace;

    private List<BiometricAdditionalImageDTO> additionalImages;
} 