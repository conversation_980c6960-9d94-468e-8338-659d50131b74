package com.stpl.tech.attendance.dto;

import com.stpl.tech.attendance.enums.BiometricStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmployeeDetailsDTO {
    private String empId;
    private String empName;
    private String empCode;
    private String empContact;
    private String empEmail;
    private BiometricStatus biometricRegistrationStatus;
    private boolean hasApprovalEligibilityMapping;
    
    /**
     * Employee attendance metadata containing various options and configurations
     */
    private EmployeeAttendanceMetadataDTO empAttendanceMetadata;

    private EmployeeShift employeeShift;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EmployeeShift{
        private Integer shiftId;
        private String shiftName;
        private LocalTime shiftStartTime;
        private LocalTime shiftEndTime;
    }
}
