package com.stpl.tech.attendance.approval.service;

import com.stpl.tech.attendance.approval.entity.ApprovalRequest;
import com.stpl.tech.attendance.approval.entity.ApprovalStep;
import com.stpl.tech.attendance.model.request.ApprovalRequestFilter;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;

public interface ApprovalEngineService {
    
    /**
     * Create a new approval request
     */
    ApprovalRequest createRequest(ApprovalRequest request);

    /**
     * Get pending approval requests for an approver
     * @param approverId The ID of the approver
     * @param pageable Pagination information
     * @return Page of pending approval requests
     */
    List<ApprovalRequest> getPendingApprovals(String approverId, List<Long> requesterIds, ApprovalRequestFilter approvalRequestFilter);

    /**
     * Get completed approval requests with pagination
     */
    Page<ApprovalRequest> getCompletedApprovals(String requesterId, Pageable pageable);

    /**
     * Get completed approval requests by approver ID with pagination
     */
    Page<ApprovalRequest> getCompletedApprovalsByApprover(Long approverId, Pageable pageable, List<Long> requesterIds, ApprovalRequestFilter approvalRequestFilter);

    /**
     * Process an approval step
     */
    ApprovalRequest processStep(Long requestId, Long stepId, com.stpl.tech.attendance.approval.dto.ApprovalDecision decision);
    
    /**
     * Get approval request by ID
     */
    ApprovalRequest getRequest(Long requestId);

    ApprovalStep getCurrentStep(Long requestId);

    List<ApprovalStep> getSteps(Long requestId);

    @Transactional
    ApprovalRequest cancelRequest(Long requestId, String reason);

    /**
     * Get approval step by ID
     */
    ApprovalStep getStep(Long stepId);
    
    /**
     * Get all approval steps for a request
     */
    List<ApprovalStep> getRequestSteps(Long requestId);

    Set<String> getUniqueDesignations();

} 