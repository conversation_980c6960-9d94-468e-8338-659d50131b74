package com.stpl.tech.attendance.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * DTO representing the result of a cache refresh operation
 */
@Data
@Builder
public class CacheRefreshResult {
    
    /**
     * Whether the refresh operation was successful
     */
    private boolean success;
    
    /**
     * Human-readable message describing the result
     */
    private String message;
    
    /**
     * Total number of units that were processed
     */
    private int totalUnits;
    
    /**
     * Number of units successfully refreshed
     */
    private int refreshedUnits;
    
    /**
     * Number of units that failed to refresh
     */
    private int failedUnits;
    
    /**
     * List of detailed error messages for failed units
     */
    private List<String> failedUnitDetails;
    
    /**
     * Duration of the refresh operation in milliseconds
     */
    private long durationMs;
    
    /**
     * Timestamp when the refresh operation completed
     */
    private long timestamp;
    
    /**
     * Builder method to set timestamp automatically
     */
    public static class CacheRefreshResultBuilder {
        public CacheRefreshResultBuilder success(boolean success) {
            this.success = success;
            this.timestamp = System.currentTimeMillis();
            return this;
        }
    }
}
