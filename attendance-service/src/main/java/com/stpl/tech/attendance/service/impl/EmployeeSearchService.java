package com.stpl.tech.attendance.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stpl.tech.attendance.cache.service.UnitCacheService;
import com.stpl.tech.attendance.cache.service.UserCacheService;
import com.stpl.tech.attendance.service.RedisMetricsService;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.util.AppConstants;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Service
public class EmployeeSearchService {

    private final RedisTemplate<String, Object> redisTemplate;
    private final RedisMetricsService redisMetricsService;
    private final ObjectMapper objectMapper;
    private final MeterRegistry meterRegistry;
    private final UserCacheService userCacheService;
    private final UnitCacheService unitCacheService;

    private static final String EMPLOYEE_CACHE_KEY_PREFIX = "employee:";
    private static final String EMPLOYEE_SEARCH_INDEX = "employee:search:index";
    private static final String EMPLOYEE_ACTIVE_CACHE = "employee:active:all";
    private static final Duration CACHE_TTL = Duration.ofHours(24);
    private static final Duration SEARCH_CACHE_TTL = Duration.ofMinutes(30);

    // In-memory search index for fast lookups
    private final Map<String, Set<String>> nameIndex = new ConcurrentHashMap<>();
    private final Map<String, String> empCodeIndex = new ConcurrentHashMap<>();
    private final Map<String, String> empIdIndex = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        warmUpCache();
    }

    public List<EmployeeBasicDetail> searchEmployees(String searchTerm) {
        Timer.Sample sample = redisMetricsService.startOperation();
        try {
            if (searchTerm == null || searchTerm.trim().isEmpty()) {
                return getAllActiveEmployees();
            }

            String normalizedSearchTerm = searchTerm.trim().toLowerCase();
            String searchKey = EMPLOYEE_SEARCH_INDEX + ":" + normalizedSearchTerm;

            // Check Redis cache first
            String cachedResults = (String) redisTemplate.opsForValue().get(searchKey);
            if (cachedResults != null) {
                redisMetricsService.recordCacheHit();
                return objectMapper.readValue(
                    cachedResults, 
                    new TypeReference<List<EmployeeBasicDetail>>() {}
                );
            }

            redisMetricsService.recordCacheMiss();
            List<EmployeeBasicDetail> results = performSearch(normalizedSearchTerm);

            if (!results.isEmpty()) {
                // Cache in Redis
                redisTemplate.opsForValue().set(
                    searchKey,
                    objectMapper.writeValueAsString(results),
                    SEARCH_CACHE_TTL
                );
            }

            return results;
        } catch (Exception e) {
            log.error("Error searching employees", e);
            return Collections.emptyList();
        } finally {
            redisMetricsService.recordOperation(sample, "searchEmployees");
        }
    }

    private List<EmployeeBasicDetail> performSearch(String searchTerm) {
        Set<String> matchingEmpIds = searchInIndexes(searchTerm);
        
        // Fetch matching employees from Hazelcast cache
        return matchingEmpIds.stream()
            .map(empId -> userCacheService.getAllUserCache().get(Integer.valueOf(empId)))
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }

    private Set<String> searchInIndexes(String searchTerm) {
        Set<String> matchingEmpIds = new HashSet<>();
        
        // Trim the search term to handle leading/trailing spaces
        searchTerm = searchTerm.trim();
        
        // If search term is empty after trimming, return empty set
        if (searchTerm.isEmpty()) {
            return matchingEmpIds;
        }

        // Search in name index
        String finalSearchTerm = searchTerm;
        nameIndex.forEach((name, empIds) -> {
            // Check if name starts with search term (case-insensitive)
            if (name.toLowerCase().startsWith(finalSearchTerm.toLowerCase())) {
                matchingEmpIds.addAll(empIds);
            }
            // Check if search term contains space (potential full name)
            else if (finalSearchTerm.contains(" ")) {
                // Split search term into words
                String[] searchWords = finalSearchTerm.toLowerCase().split("\\s+");
                // Check if all words in search term are present in the name
                boolean allWordsMatch = true;
                for (String word : searchWords) {
                    if (!name.toLowerCase().contains(word)) {
                        allWordsMatch = false;
                        break;
                    }
                }
                if (allWordsMatch) {
                    matchingEmpIds.addAll(empIds);
                }
            }
        });

        // Search in empCode index (exact match, case-insensitive)
        empCodeIndex.forEach((code, empId) -> {
            if (code.equalsIgnoreCase(finalSearchTerm)) {
                matchingEmpIds.add(empId);
            }
        });

        // Search in empId index (exact match, case-insensitive)
        empIdIndex.forEach((id, empId) -> {
            if (id.equalsIgnoreCase(finalSearchTerm)) {
                matchingEmpIds.add(empId);
            }
        });

        return matchingEmpIds;
    }

    @Scheduled(cron = "0 0 0 * * ?") // Run at midnight every day
    public void refreshCache() {
        log.info("Starting daily cache refresh");
        Timer.Sample sample = redisMetricsService.startOperation();

        try {
            // Clear existing caches
            clearCaches();

            // Fetch all active employees from Hazelcast
            List<EmployeeBasicDetail> activeEmployees = fetchActiveEmployees();
            
            // Build search indices
            buildSearchIndices(activeEmployees);

            log.info("Completed daily cache refresh for {} employees", activeEmployees.size());
        } catch (Exception e) {
            log.error("Error during cache refresh", e);
        } finally {
            redisMetricsService.recordOperation(sample, "refreshCache");
        }
    }

    private void buildSearchIndices(List<EmployeeBasicDetail> employees) {
        for (EmployeeBasicDetail employee : employees) {
            String empId = String.valueOf(employee.getId());
            
            // Index by name
            if (employee.getName() != null) {
                String[] nameParts = employee.getName().toLowerCase().split("\\s+");
                for (String part : nameParts) {
                    nameIndex.computeIfAbsent(part, k -> new HashSet<>()).add(empId);
                }
            }

            // Index by employee code
            if (employee.getEmployeeCode() != null) {
                empCodeIndex.put(employee.getEmployeeCode().toLowerCase(), empId);
            }

            // Index by employee ID
            empIdIndex.put(empId, empId);
        }
    }

    private void clearCaches() {
        // Clear Redis caches
        Set<String> keys = redisTemplate.keys(EMPLOYEE_SEARCH_INDEX + "*");
        if (keys != null && !keys.isEmpty()) {
            redisTemplate.delete(keys);
        }
        
        // Clear search indices
        nameIndex.clear();
        empCodeIndex.clear();
        empIdIndex.clear();

        unitCacheService.evictAllUnitBasicDetail();
        unitCacheService.evictUnitsCache();
    }

    private List<EmployeeBasicDetail> fetchActiveEmployees() {
        return userCacheService.getAllUserCache().values().stream()
                .filter(emp -> AppConstants.ACTIVE.equals(emp.getStatus().name()))
                .collect(Collectors.toList());
    }

    private void warmUpCache() {
        log.info("Starting cache warm-up");
        Timer.Sample sample = redisMetricsService.startOperation();

        try {
            List<EmployeeBasicDetail> activeEmployees = fetchActiveEmployees();
            buildSearchIndices(activeEmployees);
            log.info("Cache warm-up completed for {} employees", activeEmployees.size());
        } catch (Exception e) {
            log.error("Error during cache warm-up", e);
        } finally {
            redisMetricsService.recordOperation(sample, "warmUpCache");
        }
    }

    public List<EmployeeBasicDetail> getAllActiveEmployees() {
        try {
            String cachedResults = (String) redisTemplate.opsForValue().get(EMPLOYEE_ACTIVE_CACHE);
            if (cachedResults != null) {
                redisMetricsService.recordCacheHit();
                return objectMapper.readValue(
                    cachedResults,
                    new TypeReference<List<EmployeeBasicDetail>>() {}
                );
            }

            redisMetricsService.recordCacheMiss();
            List<EmployeeBasicDetail> activeEmployees = fetchActiveEmployees();
            
            if (!activeEmployees.isEmpty()) {
                redisTemplate.opsForValue().set(
                    EMPLOYEE_ACTIVE_CACHE,
                    objectMapper.writeValueAsString(activeEmployees),
                    CACHE_TTL
                );
            }

            return activeEmployees;
        } catch (Exception e) {
            log.error("Error getting all active employees", e);
            return Collections.emptyList();
        }
    }

    /**
     * Updates the search indices for a single employee
     * @param employee The employee to update in the indices
     */
    public void updateEmployeeIndices(EmployeeBasicDetail employee) {
        if (employee == null) {
            log.warn("Attempted to update indices for null employee");
            return;
        }

        String empId = String.valueOf(employee.getId());
        log.info("Updating search indices for employee: {}", empId);

        try {
            // Update name index
            if (employee.getName() != null) {
                String[] nameParts = employee.getName().toLowerCase().split("\\s+");
                for (String part : nameParts) {
                    nameIndex.computeIfAbsent(part, k -> new HashSet<>()).add(empId);
                }
            }

            // Update employee code index
            if (employee.getEmployeeCode() != null) {
                empCodeIndex.put(employee.getEmployeeCode().toLowerCase(), empId);
            }

            // Update employee ID index
            empIdIndex.put(empId, empId);

            // Clear Redis search cache for this employee
            clearEmployeeSearchCache(empId);
            
            log.info("Successfully updated search indices for employee: {}", empId);
        } catch (Exception e) {
            log.error("Error updating search indices for employee: {}", empId, e);
        }
    }

    /**
     * Removes an employee from all search indices
     * @param employeeId The ID of the employee to remove
     */
    public void removeEmployeeFromIndices(Integer employeeId) {
        if (employeeId == null) {
            log.warn("Attempted to remove null employee ID from indices");
            return;
        }

        String empId = String.valueOf(employeeId);
        log.info("Removing employee from search indices: {}", empId);

        try {
            // Remove from name index
            nameIndex.values().forEach(empIds -> empIds.remove(empId));
            // Clean up empty name entries
            nameIndex.entrySet().removeIf(entry -> entry.getValue().isEmpty());

            // Remove from employee code index
            empCodeIndex.entrySet().removeIf(entry -> entry.getValue().equals(empId));

            // Remove from employee ID index
            empIdIndex.remove(empId);

            // Clear Redis search cache for this employee
            clearEmployeeSearchCache(empId);

            log.info("Successfully removed employee from search indices: {}", empId);
        } catch (Exception e) {
            log.error("Error removing employee from search indices: {}", empId, e);
        }
    }

    private void clearEmployeeSearchCache(String empId) {
        try {
            // Clear any cached search results that might include this employee
            Set<String> keys = redisTemplate.keys(EMPLOYEE_SEARCH_INDEX + "*");
            if (keys != null && !keys.isEmpty()) {
                redisTemplate.delete(keys);
            }
            // Clear the active employees cache
            redisTemplate.delete(EMPLOYEE_ACTIVE_CACHE);
        } catch (Exception e) {
            log.error("Error clearing search cache for employee: {}", empId, e);
        }
    }

    /**
     * Check if an employee is active by verifying if their employee code exists in the active employee index
     * This is an optimized way to check active status without additional database calls
     * 
     * @param empId Employee ID to check
     * @return true if employee is active, false otherwise
     */
    public boolean isEmployeeActive(Integer empId) {
        if (empId == null) {
            return false;
        }
        
        try {
            // Get employee details from cache
            var employee = userCacheService.getUserById(empId);
            if (employee == null || employee.getEmployeeCode() == null) {
                log.debug("Employee {} not found or has no employee code", empId);
                return false;
            }
            
            boolean isActive = employee.getStatus().name().equals("ACTIVE");
            
            log.debug("Employee {} active status: {} (empCode: {})", empId, isActive, employee.getEmployeeCode());
            return isActive;
            
        } catch (Exception e) {
            log.error("Error checking active status for employee {}", empId, e);
            // Fallback: check status directly from employee object
            try {
                var employee = userCacheService.getUserById(empId);
                return employee != null && "ACTIVE".equals(employee.getStatus().name());
            } catch (Exception fallbackException) {
                log.error("Fallback active check failed for employee {}", empId, fallbackException);
                return false;
            }
        }
    }
} 