package com.stpl.tech.attendance.service;

import com.stpl.tech.attendance.dto.BiometricIdentificationRequestDTO;
import com.stpl.tech.attendance.dto.BiometricIdentificationResponseDTO;
import com.stpl.tech.attendance.dto.BiometricRegistrationDTO;
import com.stpl.tech.attendance.dto.BiometricRegistrationRequestDTO;
import com.stpl.tech.attendance.dto.BiometricRegistrationResponseDTO;
import com.stpl.tech.attendance.dto.BiometricServiceResponse;
import com.stpl.tech.attendance.dto.BiometricDeregistrationResponse;
import com.stpl.tech.attendance.enums.BiometricStatus;
import com.stpl.tech.attendance.dto.BiometricTempRegistrationRequestDTO;
import com.stpl.tech.attendance.dto.BiometricTempRegistrationResponseDTO;
import com.stpl.tech.attendance.dto.BiometricUpdateImagePathRequestDTO;
import com.stpl.tech.attendance.dto.BiometricUpdateImagePathResponseDTO;
import com.stpl.tech.attendance.dto.BiometricFaceActivationRequestDTO;
import com.stpl.tech.attendance.dto.BiometricMultiImageIdentificationRequestDTO;
import com.stpl.tech.attendance.dto.BiometricMultiImageIdentificationResponseDTO;

public interface BiometricService {
    /**
     * @deprecated Use {@link #registerFace(BiometricRegistrationRequestDTO)} instead
     */
    @Deprecated
    BiometricServiceResponse registerFace(String empId, String unitId, String base64Image);
    
    /**
     * Register a face using the new request/response format
     */
    BiometricRegistrationResponseDTO registerFace(BiometricRegistrationRequestDTO request);
    
    /**
     * Activate face for an employee using the new API
     */
    BiometricRegistrationResponseDTO activateFace(BiometricFaceActivationRequestDTO request);

    BiometricDeregistrationResponse deregisterFace(String biometricId, String unitId , Boolean deregistered);
    
    /**
     * @deprecated Use {@link #identifyFace(BiometricIdentificationRequestDTO)} instead
     */
    @Deprecated
    Integer getEmployeeIdFromImage(String base64image, Integer unitId);

    /**
     * Identify a face using the new request/response format
     */
    BiometricIdentificationResponseDTO identifyFace(BiometricIdentificationRequestDTO request);

    /**
     * Identify a face from multiple images using the new API
     */
    BiometricMultiImageIdentificationResponseDTO identifyFaceFromMultipleImages(BiometricMultiImageIdentificationRequestDTO request);

    void handleRegistrationStatusChange(String empId, BiometricStatus status);

    BiometricRegistrationDTO getBiometricRegistration(String empId);

    /**
     * Verifies temporary face registration with the biometric service
     */
    BiometricTempRegistrationResponseDTO verifyTempRegistration(BiometricTempRegistrationRequestDTO request);

    /**
     * Updates the image path for a biometric registration
     */
    BiometricUpdateImagePathResponseDTO updateImagePath(BiometricUpdateImagePathRequestDTO request);
    
    /**
     * Updates the image path for multiple images
     */
    BiometricUpdateImagePathResponseDTO updateImagePathMultiple(BiometricUpdateImagePathRequestDTO request);
} 