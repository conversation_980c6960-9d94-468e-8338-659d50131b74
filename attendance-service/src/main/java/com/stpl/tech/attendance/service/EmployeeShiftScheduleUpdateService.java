package com.stpl.tech.attendance.service;

import com.stpl.tech.attendance.entity.DailyAttendanceSummary;
import org.springframework.scheduling.annotation.Async;

import java.time.LocalDate;
import java.time.LocalDateTime;

public interface EmployeeShiftScheduleUpdateService {
    
    /**
     * Update shift schedule with actual attendance data
     * Called from AsyncOperationService when updating DailyAttendanceSummary
     */

    @Async("asyncTaskExecutor")
    void updateShiftScheduleWithAttendance(Integer empId, LocalDate businessDate,
                                           LocalDateTime checkInTime, LocalDateTime checkOutTime,
                                           DailyAttendanceSummary summary);
}