package com.stpl.tech.attendance.model;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

@Data
public class BulkAttendancePunchRequest {
    
    @NotEmpty(message = "Attendance punch requests list cannot be empty")
    @Size(max = 100, message = "Maximum 100 attendance punch requests allowed per bulk operation")
    @Valid
    private List<AttendancePunchRequest> attendancePunchRequests;

    private Boolean isOfflineMode = true; // Default to true - indicates if this bulk operation contains offline punches
} 