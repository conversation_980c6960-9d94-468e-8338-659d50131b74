package com.stpl.tech.attendance.cache.listener;

import com.stpl.tech.attendance.entity.EmpEligibilityMapping;
import com.stpl.tech.attendance.enums.MappingType;
import com.stpl.tech.attendance.service.EmpEligibilityService;
import com.stpl.tech.attendance.service.UnitResolutionService;
import com.stpl.tech.attendance.cache.service.UnitCacheService;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import java.time.LocalDate;
import java.util.HashSet;
import java.util.Set;
import java.util.List;

/**
 * Event listener for EmpEligibilityMapping entity changes
 * Handles cache eviction when eligibility mappings are created, updated, or deleted
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class EmpEligibilityMappingEventListener {

    private final EmpEligibilityService empEligibilityService;
    private final UnitResolutionService unitResolutionService;
    private final UnitCacheService unitCacheService;

    /**
     * Handle EmpEligibilityMapping entity creation
     */
    @EventListener
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void handleEmpEligibilityMappingCreated(EmpEligibilityMapping mapping) {
        log.debug("Handling EmpEligibilityMapping created event for employee: {}, mapping type: {}, value: {}", 
                mapping.getEmpId(), mapping.getMappingType(), mapping.getValue());
        
        evictRelatedCaches(mapping);
    }

    /**
     * Handle EmpEligibilityMapping entity update
     */
    @EventListener
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void handleEmpEligibilityMappingUpdated(EmpEligibilityMapping mapping) {
        log.debug("Handling EmpEligibilityMapping updated event for employee: {}, mapping type: {}, value: {}", 
                mapping.getEmpId(), mapping.getMappingType(), mapping.getValue());
        
        evictRelatedCaches(mapping);
    }

    /**
     * Handle EmpEligibilityMapping entity deletion
     */
    @EventListener
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void handleEmpEligibilityMappingDeleted(EmpEligibilityMapping mapping) {
        log.debug("Handling EmpEligibilityMapping deleted event for employee: {}, mapping type: {}, value: {}", 
                mapping.getEmpId(), mapping.getMappingType(), mapping.getValue());
        
        evictRelatedCaches(mapping);
    }

    /**
     * Evict all related caches when EmpEligibilityMapping changes
     */
    private void evictRelatedCaches(EmpEligibilityMapping mapping) {
        try {
            // Get affected units based on mapping type
            Set<Integer> affectedUnits = getAffectedUnits(mapping);
            
            // Update caches for affected units
            for (Integer unitId : affectedUnits) {
                // Update unit employees cache
                empEligibilityService.updateUnitEmployeesCache(unitId);
                
                // Evict unit resolution caches for current date (these are separate caches)
                unitResolutionService.evictUnitCaches(unitId, LocalDate.now());
                
                log.debug("Updated caches for unit: {}", unitId);
            }
            
            // Evict employee-specific caches
            try {
                Integer empId = Integer.parseInt(mapping.getEmpId());
                
                // Evict eligibility units cache for the employee
                if (empEligibilityService instanceof com.stpl.tech.attendance.service.impl.EmpEligibilityServiceImpl) {
                    ((com.stpl.tech.attendance.service.impl.EmpEligibilityServiceImpl) empEligibilityService)
                        .evictEligibilityUnitsCache(empId);
                }
                
                // Evict employee unit cache for current date
                unitResolutionService.evictEmployeeCaches(empId, LocalDate.now());
                
                log.debug("Evicted caches for employee: {}", empId);
            } catch (NumberFormatException e) {
                log.warn("Invalid employee ID format: {}", mapping.getEmpId());
            }
            
        } catch (Exception e) {
            log.error("Error updating caches for EmpEligibilityMapping change", e);
        }
    }

    /**
     * Get all units that might be affected by this mapping change
     */
    private Set<Integer> getAffectedUnits(EmpEligibilityMapping mapping) {
        Set<Integer> affectedUnits = new HashSet<>();
        
        try {
            if (mapping.getMappingType() == MappingType.UNIT) {
                // Direct unit mapping - only this unit is affected
                Integer unitId = Integer.parseInt(mapping.getValue());
                affectedUnits.add(unitId);
                
            } else if (mapping.getMappingType() == MappingType.CITY) {
                // City mapping - find all units in this city
                String cityValue = mapping.getValue();
                List<Integer> unitsInCity = unitCacheService.getUnitsByCity(cityValue);
                affectedUnits.addAll(unitsInCity);
                log.debug("City mapping change detected for city: {}. Found {} affected units.", cityValue, unitsInCity.size());
                
            } else if (mapping.getMappingType() == MappingType.REGION) {
                // Region mapping - find all units in this region
                String regionValue = mapping.getValue();
                List<Integer> unitsInRegion = unitCacheService.getUnitsByRegion(regionValue);
                affectedUnits.addAll(unitsInRegion);
                log.debug("Region mapping change detected for region: {}. Found {} affected units.", regionValue, unitsInRegion.size());
            }
            
        } catch (NumberFormatException e) {
            log.warn("Invalid unit ID format in mapping value: {}", mapping.getValue());
        }
        
        return affectedUnits;
    }
} 