package com.stpl.tech.attendance.controller;

import com.stpl.tech.attendance.constants.ApiConstants;
import com.stpl.tech.attendance.dto.CacheRefreshResult;
import com.stpl.tech.attendance.model.response.ApiResponse;
import com.stpl.tech.attendance.service.EmpEligibilityService;
import com.stpl.tech.attendance.cache.service.UnitCacheService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Controller for cache management operations
 * Provides endpoints for manual cache refresh and monitoring
 */
@Tag(name = "Cache Management", description = "Cache management and monitoring APIs")
@Slf4j
@RestController
@RequestMapping(ApiConstants.Paths.ATTENDANCE + "/cache")
@RequiredArgsConstructor
public class CacheManagementController extends BaseController {

    private final EmpEligibilityService empEligibilityService;
    private final UnitCacheService unitCacheService;

    @Operation(summary = "Manually refresh all unit employees cache", 
               description = "Triggers a manual refresh of all unit employees cache. This endpoint is useful for " +
                           "admin operations, testing, or when immediate cache refresh is needed. " +
                           "The operation will evict and refresh cache for all units.")
    @PostMapping("/refresh")
    public ResponseEntity<ApiResponse<CacheRefreshResult>> refreshAllUnitEmployeesCache() {
        log.info("Manual cache refresh requested via CacheManagementController");
        try {
            CacheRefreshResult result = empEligibilityService.manuallyRefreshAllUnitEmployeesCache();
            return success(result);
        } catch (Exception e) {
            log.error("Error during manual cache refresh via API", e);
            CacheRefreshResult errorResult = CacheRefreshResult.builder()
                .success(false)
                .message("Error during cache refresh: " + e.getMessage())
                .totalUnits(0)
                .refreshedUnits(0)
                .failedUnits(0)
                .durationMs(0)
                .build();
            return success(errorResult);
        }
    }

    @Operation(summary = "Refresh unit cache local maps", 
               description = "Refreshes the local unit cache maps from Hazelcast cache. This endpoint is useful when " +
                           "the main Hazelcast cache has been updated and you need to sync the local maps. " +
                           "This operation copies data from Hazelcast IMap to local HashMap instances to avoid " +
                           "cache eviction issues.")
    @PostMapping("/refresh/unit-cache")
    public ResponseEntity<ApiResponse<String>> refreshUnitCacheLocalMaps() {
        log.info("Manual unit cache local maps refresh requested via CacheManagementController");
        try {
            long startTime = System.currentTimeMillis();
            unitCacheService.refreshLocalMaps();
            long duration = System.currentTimeMillis() - startTime;
            
            String message = String.format("Unit cache local maps refreshed successfully in %d ms", duration);
            log.info(message);
            return success(message);
        } catch (Exception e) {
            log.error("Error during unit cache local maps refresh via API", e);
            return success("Error during unit cache refresh: " + e.getMessage());
        }
    }

    @Operation(summary = "Get cache status", 
               description = "Returns the current status of the cache system including " +
                           "scheduled refresh information and cache statistics.")
    @PostMapping("/status")
    public ResponseEntity<ApiResponse<Object>> getCacheStatus() {
        log.info("Cache status requested via CacheManagementController");
        try {
            // For now, return basic status. This can be enhanced later with more detailed cache information
            var status = new Object() {
                public final String status = "Cache system is running";
                public final String scheduledRefresh = "Every 2 hours";
                public final String lastRefresh = "Last refresh time would be logged in the scheduled method";
                public final boolean manualRefreshAvailable = true;
                public final boolean unitCacheLocalMapsAvailable = true;
            };
            return success(status);
        } catch (Exception e) {
            log.error("Error getting cache status", e);
            return created("Error getting cache status: " + e.getMessage());
        }
    }
}
