package com.stpl.tech.attendance.service;

import reactor.core.publisher.Mono;

import java.util.Map;

public interface HttpService {
    // Default timeout methods
    <T> Mono<T> get(String url, Class<T> responseType);
    <T> Mono<T> get(String url, Class<T> responseType, Map<String, String> headers);
    <T> Mono<T> get(String url, Class<T> responseType, Map<String, String> headers, Map<String, String> queryParams);
    
    <T, R> Mono<R> post(String url, T request, Class<R> responseType);
    <T, R> Mono<R> post(String url, T request, Class<R> responseType, Map<String, String> headers);
    <T, R> Mono<R> post(String url, T request, Class<R> responseType, Map<String, String> headers, Map<String, String> queryParams);
    
    <T> Mono<Void> put(String url, T request);
    <T> Mono<Void> put(String url, T request, Map<String, String> headers);
    
    Mono<Void> delete(String url);
    Mono<Void> delete(String url, Map<String, String> headers);
    Mono<Void> delete(String url, Map<String, String> headers, Map<String, String> queryParams);

    // Custom timeout methods
    <T> Mono<T> getWithTimeout(String url, Class<T> responseType, int timeoutMillis);
    <T> Mono<T> getWithTimeout(String url, Class<T> responseType, Map<String, String> headers, int timeoutMillis);
    <T> Mono<T> getWithTimeout(String url, Class<T> responseType, Map<String, String> headers, Map<String, String> queryParams, int timeoutMillis);
    
    <T, R> Mono<R> postWithTimeout(String url, T request, Class<R> responseType, int timeoutMillis);
    <T, R> Mono<R> postWithTimeout(String url, T request, Class<R> responseType, Map<String, String> headers, int timeoutMillis);
    
    <T> Mono<Void> putWithTimeout(String url, T request, int timeoutMillis);
    <T> Mono<Void> putWithTimeout(String url, T request, Map<String, String> headers, int timeoutMillis);
    
    Mono<Void> deleteWithTimeout(String url, int timeoutMillis);
    Mono<Void> deleteWithTimeout(String url, Map<String, String> headers, int timeoutMillis);
    Mono<Void> deleteWithTimeout(String url, Map<String, String> headers, Map<String, String> queryParams, int timeoutMillis);

    // No timeout methods (for long-running operations)
    <T> Mono<T> getWithoutTimeout(String url, Class<T> responseType);
    <T> Mono<T> getWithoutTimeout(String url, Class<T> responseType, Map<String, String> headers);
    <T> Mono<T> getWithoutTimeout(String url, Class<T> responseType, Map<String, String> headers, Map<String, String> queryParams);
    
    <T, R> Mono<R> postWithoutTimeout(String url, T request, Class<R> responseType);
    <T, R> Mono<R> postWithoutTimeout(String url, T request, Class<R> responseType, Map<String, String> headers);
    
    <T> Mono<Void> putWithoutTimeout(String url, T request);
    <T> Mono<Void> putWithoutTimeout(String url, T request, Map<String, String> headers);
    
    Mono<Void> deleteWithoutTimeout(String url);
    Mono<Void> deleteWithoutTimeout(String url, Map<String, String> headers);
    Mono<Void> deleteWithoutTimeout(String url, Map<String, String> headers, Map<String, String> queryParams);
} 