package com.stpl.tech.attendance.controller;

import com.stpl.tech.attendance.annotation.TrackRequest;
import com.stpl.tech.attendance.constants.ApiConstants;
import com.stpl.tech.attendance.dto.BiometricTempRegistrationRequestDTO;
import com.stpl.tech.attendance.dto.BiometricTempRegistrationResponseDTO;
import com.stpl.tech.attendance.context.RequestTrackingContext;
import com.stpl.tech.attendance.model.request.BiometricDeregistrationRequest;
import com.stpl.tech.attendance.model.request.BiometricRegistrationRequest;
import com.stpl.tech.attendance.model.request.BiometricTempVerificationRequest;
import com.stpl.tech.attendance.model.request.BiometricVerificationRequest;
import com.stpl.tech.attendance.model.response.ApiResponse;
import com.stpl.tech.attendance.model.response.BiometricRegistrationActionResponse;
import com.stpl.tech.attendance.model.response.BiometricRegistrationResponse;
import com.stpl.tech.attendance.service.BiometricRegistrationService;
import com.stpl.tech.attendance.service.BiometricService;
import com.stpl.tech.attendance.service.impl.AsyncOperationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Tag(name = "Biometric Registration", description = "Biometric registration and deregistration APIs")
@RestController
@RequestMapping(ApiConstants.Paths.BIOMETRIC)
@RequiredArgsConstructor
public class BiometricRegistrationController extends BaseController {

    private final BiometricRegistrationService biometricRegistrationService;
    private final BiometricService biometricService;
    private final AsyncOperationService asyncOperationService;


    @Operation(summary = "Register biometric data")
    @PostMapping("/register")
    public ResponseEntity<ApiResponse<BiometricRegistrationActionResponse>> registerBiometric(
            @Valid @RequestBody BiometricRegistrationRequest request) {
        BiometricRegistrationActionResponse response = biometricRegistrationService.registerBiometric(request);
        return success(response);
    }

    @Operation(summary = "Verify face images before registration")
    @TrackRequest(description = "Verify face images with registration")
    @PostMapping("/verify")
    public ResponseEntity<ApiResponse<BiometricRegistrationActionResponse>> verifyFace(
            @RequestBody BiometricVerificationRequest request) {
        BiometricRegistrationActionResponse response = biometricRegistrationService.verifyAndRegisterBiometric(request);
        asyncOperationService.saveAdditionalImagesAsync(
                    request.getAdditionalImages(),
                    response.getBiometricRegistrationId());
        
        // Set reference ID in context for automatic tracking by @RequestTrackingAspect
        if (response.getBiometricRegistrationId() != null) {
            RequestTrackingContext.setReferenceId(response.getBiometricRegistrationId().toString());
        }
        
        return success(response);
    }

    @Operation(summary = "Deregister biometric data")
    @PostMapping("/deregister")
    public ResponseEntity<ApiResponse<BiometricRegistrationActionResponse>> deregisterBiometric(
            @RequestBody BiometricDeregistrationRequest request) {
        BiometricRegistrationActionResponse response = biometricRegistrationService.deregisterBiometric(request.getEmpId());
        return success(response);
    }

    @Operation(summary = "Get biometric registration status")
    @GetMapping("/status/{empId}")
    public ResponseEntity<ApiResponse<BiometricRegistrationResponse>> getBiometricStatus(
            @PathVariable String empId) {
        BiometricRegistrationResponse response = biometricRegistrationService.getBiometricStatus(empId);
        return success(response);
    }

    @Operation(summary = "Verify temporary face registration")
    @PostMapping("/verify-temp")
    public ResponseEntity<ApiResponse<BiometricTempRegistrationResponseDTO>> verifyTempRegistration(
            @Valid @RequestBody BiometricTempRegistrationRequestDTO request) {
        BiometricTempRegistrationResponseDTO response = biometricService.verifyTempRegistration(request);
        return success(response);
    }
} 