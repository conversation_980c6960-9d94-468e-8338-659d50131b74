package com.stpl.tech.attendance.service.impl;

import com.stpl.tech.attendance.constants.AppConstants;
import com.stpl.tech.attendance.entity.EmpAttendanceBalanceDataLogs;
import com.stpl.tech.attendance.repository.EmpLeaveDataLogsRepository;
import com.stpl.tech.attendance.service.LeaveBalanceLoggingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

/**
 * Implementation of LeaveBalanceLoggingService for logging leave balance changes
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LeaveBalanceLoggingServiceImpl implements LeaveBalanceLoggingService {

    private final EmpLeaveDataLogsRepository empLeaveDataLogsRepository;

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public void logLeaveBalanceDeduction(Long empLeaveDataId, String leaveType, BigDecimal daysDeducted, String updatedBy) {
        try {
            EmpAttendanceBalanceDataLogs logEntry = EmpAttendanceBalanceDataLogs.builder()
                    .empAttendanceBalanceId(empLeaveDataId)
                    .action(AppConstants.DEBIT)
                    .type(leaveType.toUpperCase())
                    .updatedBy(updatedBy)
                    .build();

            EmpAttendanceBalanceDataLogs savedLog = empLeaveDataLogsRepository.save(logEntry);
            log.info("Logged leave balance deduction - ID: {}, Employee Leave Data ID: {}, Type: {}, Days: {}, Updated By: {}",
                    savedLog.getId(), empLeaveDataId, leaveType, daysDeducted, updatedBy);
        } catch (Exception e) {
            log.error("Failed to log leave balance deduction for employee leave data ID: {}, Type: {}, Days: {}",
                    empLeaveDataId, leaveType, daysDeducted, e);
            // Don't throw exception to avoid rolling back the main transaction
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public void logLeaveBalanceRestoration(Long empLeaveDataId, String leaveType, BigDecimal daysRestored, String updatedBy) {
        try {
            EmpAttendanceBalanceDataLogs logEntry = EmpAttendanceBalanceDataLogs.builder()
                    .empAttendanceBalanceId(empLeaveDataId)
                    .action(AppConstants.CREDIT)
                    .type(leaveType.toUpperCase())
                    .updatedBy(updatedBy)
                    .build();

            EmpAttendanceBalanceDataLogs savedLog = empLeaveDataLogsRepository.save(logEntry);
            log.info("Logged leave balance restoration - ID: {}, Employee Leave Data ID: {}, Type: {}, Days: {}, Updated By: {}",
                    savedLog.getId(), empLeaveDataId, leaveType, daysRestored, updatedBy);
        } catch (Exception e) {
            log.error("Failed to log leave balance restoration for employee leave data ID: {}, Type: {}, Days: {}",
                    empLeaveDataId, leaveType, daysRestored, e);
            // Don't throw exception to avoid rolling back the main transaction
        }
    }

    /**
     * Log a leave balance change with old and new count values
     * @param empLeaveDataId The employee leave data ID
     * @param leaveType The type of leave (LEAVE, COMP_OFF, LWP, WEEK_OFF)
     * @param oldCount The leave balance before the change
     * @param newCount The leave balance after the change
     * @param updatedBy The user who performed the operation
     */
    @Transactional(propagation = Propagation.REQUIRED)
    public void logLeaveBalanceChange(Long empLeaveDataId, String leaveType, BigDecimal oldCount, BigDecimal newCount, String updatedBy) {
        try {
            // Determine if this is a deduction or restoration based on count change
            String status = "UNKNOWN";

            if (oldCount != null && newCount != null) {
                if (newCount.compareTo(oldCount) < 0) {
                    status = "DEBIT";
                } else if (newCount.compareTo(oldCount) > 0) {
                    status = "CREDIT";
                }
            }

            EmpAttendanceBalanceDataLogs logEntry = EmpAttendanceBalanceDataLogs.builder()
                    .empAttendanceBalanceId(empLeaveDataId)
                    .action(status)
                    .type(leaveType.toUpperCase())
                    .previousValue(String.valueOf(oldCount))
                    .newValue(String.valueOf(newCount))
                    .updatedBy(updatedBy)
                    .build();

            EmpAttendanceBalanceDataLogs savedLog = empLeaveDataLogsRepository.save(logEntry);
            log.info("Logged leave balance change - ID: {}, Employee Leave Data ID: {}, Type: {}, Old Count: {}, New Count: {}, Updated By: {}",
                    savedLog.getId(), empLeaveDataId, leaveType, oldCount, newCount, updatedBy);
        } catch (Exception e) {
            log.error("Failed to log leave balance change for employee leave data ID: {}, Type: {}, Old Count: {}, New Count: {}",
                    empLeaveDataId, leaveType, oldCount, newCount, e);
            // Don't throw exception to avoid rolling back the main transaction
        }
    }
}
