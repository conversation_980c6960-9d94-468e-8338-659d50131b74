package com.stpl.tech.attendance.dto;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AttendanceSyncMessage {
    private Long attendanceRecordId;
    private Integer employeeId;
    private String employeeCode;
    private LocalDateTime punchTime;
    private String punchType;
    private String biometricId;
    private String location;
    private Long srno;
    private String syncId;
    private Integer retryCount = 0;
} 