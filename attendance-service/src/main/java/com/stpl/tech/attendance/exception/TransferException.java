package com.stpl.tech.attendance.exception;

import lombok.Getter;

@Getter
public class TransferException extends RuntimeException {
    private final TransferErrorCode errorCode;
    
    public TransferException(TransferErrorCode errorCode) {
        super(errorCode.getMessage());
        this.errorCode = errorCode;
    }
    
    public TransferException(TransferErrorCode errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
} 