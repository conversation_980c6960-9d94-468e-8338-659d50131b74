package com.stpl.tech.attendance.validation;

import com.stpl.tech.attendance.dto.RosteringDto.GenericFilterRequestDTO;
import com.stpl.tech.attendance.enums.FilterDataType;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class FilterValueValidator implements ConstraintValidator<ValidFilterValue, Map<String, GenericFilterRequestDTO.FilterValue>> {
    
    private static final List<String> VALID_OPERATORS = Arrays.asList(
        "IN", "EQUALS", "BETWEEN", "GREATER_THAN", "LESS_THAN", 
        "GREATER_THAN_EQUALS", "LESS_THAN_EQUALS", "NOT_IN", "NOT_EQUALS"
    );
    
    @Override
    public boolean isValid(Map<String, GenericFilterRequestDTO.FilterValue> filters, ConstraintValidatorContext context) {
        if (filters == null) {
            return true; // Null filters are allowed
        }
        
        for (Map.Entry<String, GenericFilterRequestDTO.FilterValue> entry : filters.entrySet()) {
            String filterKey = entry.getKey();
            GenericFilterRequestDTO.FilterValue filterValue = entry.getValue();
            
            if (filterValue == null) {
                addConstraintViolation(context, filterKey, "Filter value cannot be null");
                return false;
            }
            
            // Validate data type
            if (filterValue.getDataType() == null) {
                addConstraintViolation(context, filterKey, "Data type is required");
                return false;
            }
            
            // Validate operator
            if (filterValue.getOperator() == null || !VALID_OPERATORS.contains(filterValue.getOperator().toUpperCase())) {
                addConstraintViolation(context, filterKey, "Invalid operator. Must be one of: " + VALID_OPERATORS);
                return false;
            }
            
            // Validate values
            if (filterValue.getValues() == null || filterValue.getValues().isEmpty()) {
                addConstraintViolation(context, filterKey, "Values cannot be null or empty");
                return false;
            }
            
            // Validate data type compatibility with operator
            if (!isDataTypeCompatibleWithOperator(filterValue.getDataType(), filterValue.getOperator())) {
                addConstraintViolation(context, filterKey, 
                    "Operator '" + filterValue.getOperator() + "' is not compatible with data type '" + filterValue.getDataType() + "'");
                return false;
            }
            
            // Validate BETWEEN operator has exactly 2 values
            if ("BETWEEN".equalsIgnoreCase(filterValue.getOperator()) && filterValue.getValues().size() != 2) {
                addConstraintViolation(context, filterKey, "BETWEEN operator requires exactly 2 values");
                return false;
            }
        }
        
        return true;
    }
    
    private boolean isDataTypeCompatibleWithOperator(FilterDataType dataType, String operator) {
        switch (dataType) {
            case INTEGER:
            case DECIMAL:
                return Arrays.asList("IN", "EQUALS", "BETWEEN", "GREATER_THAN", "LESS_THAN", 
                                   "GREATER_THAN_EQUALS", "LESS_THAN_EQUALS", "NOT_IN", "NOT_EQUALS")
                           .contains(operator.toUpperCase());
            case STRING:
                return Arrays.asList("IN", "EQUALS", "NOT_IN", "NOT_EQUALS")
                           .contains(operator.toUpperCase());
            case DATE:
            case TIMESTAMP:
                return Arrays.asList("IN", "EQUALS", "BETWEEN", "GREATER_THAN", "LESS_THAN", 
                                   "GREATER_THAN_EQUALS", "LESS_THAN_EQUALS", "NOT_IN", "NOT_EQUALS")
                           .contains(operator.toUpperCase());
            case BOOLEAN:
                return Arrays.asList("EQUALS", "NOT_EQUALS")
                           .contains(operator.toUpperCase());
            default:
                return false;
        }
    }
    
    private void addConstraintViolation(ConstraintValidatorContext context, String filterKey, String message) {
        context.disableDefaultConstraintViolation();
        context.buildConstraintViolationWithTemplate(message)
               .addPropertyNode(filterKey)
               .addConstraintViolation();
    }
} 