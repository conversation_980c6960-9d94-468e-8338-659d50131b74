package com.stpl.tech.attendance.dto.RosteringDto;

import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShiftEmployeesWithFiltersRequestDTO {
    private Timestamp date;
    
    @Valid
    private GenericFilterRequestDTO filters;
} 