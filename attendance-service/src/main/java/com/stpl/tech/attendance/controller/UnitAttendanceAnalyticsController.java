package com.stpl.tech.attendance.controller;

import com.stpl.tech.attendance.constants.ApiConstants;
import com.stpl.tech.attendance.context.JwtContext;
import com.stpl.tech.attendance.dto.UnitAttendanceAnalyticsDTO;
import com.stpl.tech.attendance.model.response.ApiResponse;
import com.stpl.tech.attendance.service.UnitAttendanceAnalyticsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;

@RestController
@RequestMapping(ApiConstants.Paths.UNIT_ATTENDANCE_ANALYTICS)
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Unit Attendance Analytics", description = "Unit attendance analytics APIs")
public class UnitAttendanceAnalyticsController extends BaseController {
    
    private final UnitAttendanceAnalyticsService unitAttendanceAnalyticsService;
    
    /**
     * Get attendance analytics for all units managed by the current user for a specific date
     */
    @PostMapping("/manager")
    @Operation(summary = "Get manager unit analytics", description = "Get attendance analytics for all units managed by the current user for a specific date")
    public ResponseEntity<ApiResponse<UnitAttendanceAnalyticsDTO.ManagerAnalyticsResponseDTO>> getManagerUnitAnalytics(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate businessDate) {
        
        try {
            // Get user ID from JWT context
            Integer managerId = JwtContext.getInstance().getUserId();
            if (managerId == null) {
                log.error("User ID not found in JWT context");
                return ResponseEntity.badRequest().build();
            }
            
            log.info("Getting manager unit analytics for manager: {} on date: {}", managerId, businessDate);
            
            UnitAttendanceAnalyticsDTO.ManagerAnalyticsResponseDTO response = 
                unitAttendanceAnalyticsService.getManagerUnitAnalytics(managerId, businessDate);
            
            log.info("Manager unit analytics retrieved successfully for manager: {} on date: {}", managerId, businessDate);
            return success(response);
            
        } catch (Exception e) {
            log.error("Error getting manager unit analytics for date: {}", businessDate, e);
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * Get attendance analytics for a specific unit and date
     */
    @GetMapping("/unit/{unitId}")
    @Operation(summary = "Get unit analytics", description = "Get attendance analytics for a specific unit and date")
    public ResponseEntity<ApiResponse<UnitAttendanceAnalyticsDTO>> getUnitAnalytics(
            @PathVariable Integer unitId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate businessDate) {
        
        try {
            log.info("Getting unit analytics for unit: {} on date: {}", unitId, businessDate);
            
            UnitAttendanceAnalyticsDTO response = unitAttendanceAnalyticsService.getUnitAnalytics(unitId, businessDate);
            
            log.info("Unit analytics retrieved successfully for unit: {} on date: {}", unitId, businessDate);
            return success(response);
            
        } catch (Exception e) {
            log.error("Error getting unit analytics for unit: {} on date: {}", unitId, businessDate, e);
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * Get attendance analytics for today
     */
    @GetMapping("/manager/today")
    @Operation(summary = "Get manager analytics for today", description = "Get attendance analytics for all units managed by the current user for today")
    public ResponseEntity<ApiResponse<UnitAttendanceAnalyticsDTO.ManagerAnalyticsResponseDTO>> getManagerUnitAnalyticsToday() {
        log.info("Getting manager unit analytics for today");
        return getManagerUnitAnalytics(LocalDate.now());
    }
    
    /**
     * Get attendance analytics for yesterday
     */
    @GetMapping("/manager/yesterday")
    @Operation(summary = "Get manager analytics for yesterday", description = "Get attendance analytics for all units managed by the current user for yesterday")
    public ResponseEntity<ApiResponse<UnitAttendanceAnalyticsDTO.ManagerAnalyticsResponseDTO>> getManagerUnitAnalyticsYesterday() {
        log.info("Getting manager unit analytics for yesterday");
        return getManagerUnitAnalytics(LocalDate.now().minusDays(1));
    }
    
    /**
     * Get attendance analytics for a specific unit today
     */
    @GetMapping("/unit/{unitId}/today")
    @Operation(summary = "Get unit analytics for today", description = "Get attendance analytics for a specific unit for today")
    public ResponseEntity<ApiResponse<UnitAttendanceAnalyticsDTO>> getUnitAnalyticsToday(@PathVariable Integer unitId) {
        log.info("Getting unit analytics for unit: {} for today", unitId);
        return getUnitAnalytics(unitId, LocalDate.now());
    }
    
    /**
     * Get attendance analytics for a specific unit yesterday
     */
    @GetMapping("/unit/{unitId}/yesterday")
    @Operation(summary = "Get unit analytics for yesterday", description = "Get attendance analytics for a specific unit for yesterday")
    public ResponseEntity<ApiResponse<UnitAttendanceAnalyticsDTO>> getUnitAnalyticsYesterday(@PathVariable Integer unitId) {
        log.info("Getting unit analytics for unit: {} for yesterday", unitId);
        return getUnitAnalytics(unitId, LocalDate.now().minusDays(1));
    }
} 