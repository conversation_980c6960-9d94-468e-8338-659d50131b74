package com.stpl.tech.attendance.service;

import com.stpl.tech.attendance.dto.ApplyLeaveRequest;
import com.stpl.tech.attendance.dto.CancelRequestRequest;
import com.stpl.tech.attendance.dto.CancelRequestResponse;
import com.stpl.tech.attendance.dto.ApplyLeaveResponse;
import com.stpl.tech.attendance.dto.ApplyOdWfhRequest;
import com.stpl.tech.attendance.dto.ApplyOdWfhResponse;
import com.stpl.tech.attendance.dto.ApplyRegularisationRequest;
import com.stpl.tech.attendance.dto.ApplyRegularisationResponse;
import com.stpl.tech.attendance.dto.UploadLeaveDocumentRequest;
import com.stpl.tech.attendance.dto.UploadLeaveDocumentResponse;
import com.stpl.tech.attendance.dto.ApplyWeekOffRequest;
import com.stpl.tech.attendance.dto.ApplyWeekOffResponse;
import com.stpl.tech.attendance.enums.ApprovalStatus;

import java.util.List;

public interface EmployeeAttendanceService {
    
    /**
     * Apply leave for an employee with multiple dates and leave types
     * @param request The apply leave request containing dates, leave types, comments, and comments
     * @return ApplyLeaveResponse with success/fail message and created entry IDs
     */
    ApplyLeaveResponse applyLeave(ApplyLeaveRequest request);
    
    /**
     * Apply OD/WFH for an employee with multiple dates and check-in/check-out times
     * @param request The apply OD/WFH request containing dates, check-in/check-out times, comments, and documents
     * @return ApplyOdWfhResponse with success/fail message and created entry IDs
     */
    ApplyOdWfhResponse applyOdWfh(ApplyOdWfhRequest request);
    
    /**
     * Apply regularisation for an employee with date, check-in/check-out times, reason, and comments
     * @param request The apply regularisation request containing date, check-in/check-out times, reason, and comments
     * @return ApplyRegularisationResponse with success/fail message and created entry ID
     */
    ApplyRegularisationResponse applyRegularisation(ApplyRegularisationRequest request);

    /**
     * Upload leave document to S3 and return the document URL
     * @param request The upload document request containing base64 document, file name, and content type
     * @return UploadLeaveDocumentResponse with document URL and success status
     */
    UploadLeaveDocumentResponse uploadLeaveDocument(UploadLeaveDocumentRequest request);

    /**
     * Cancel an attendance request (pending or approved)
     * @param request The cancel request containing the request ID
     * @return CancelRequestResponse with success/fail message and status details
     */
    CancelRequestResponse cancelRequest(CancelRequestRequest request);

    /**
     * Apply week off for an employee
     * @param request The week off request containing date and temporary/permanent flag
     * @return ApplyWeekOffResponse with success/fail message and created entry details
     */
    ApplyWeekOffResponse applyWeekOff(ApplyWeekOffRequest request);

//    /**
//     * Handle approval status update for any type of attendance request
//     * @param statusEntryId The ID of the status entry from EMP_ATTENDANCE_STATUS table
//     * @param status The approval status (APPROVED or REJECTED)
//     * @param attendanceType The type of attendance (LEAVE, COMP_OFF, LWP)
//     * @param logMessage The log message for the approval type
//     */
//    void handleApprovalStatusUpdate(Long statusEntryId, ApprovalStatus status, String attendanceType, String logMessage);

    /**
     * Handle approval response for attendance requests with approval request information
     * @param approvalRequest The approval request containing reference ID and other details
     * @param status The approval status (APPROVED or REJECTED)
     * @param attendanceType The type of attendance (LEAVE, OD, WFH, REGULARISATION)
     */
    void handleApprovalResponse(com.stpl.tech.attendance.approval.entity.ApprovalRequest approvalRequest, ApprovalStatus status, String attendanceType);

} 