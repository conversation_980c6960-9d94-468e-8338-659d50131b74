package com.stpl.tech.attendance.config;

import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.core.LifecycleEvent;
import com.hazelcast.core.LifecycleListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicBoolean;

@Slf4j
@Component
public class HazelcastHealthIndicator implements HealthIndicator {

    private final HazelcastInstance hazelcastInstance;
    private final AtomicBoolean isConnected = new AtomicBoolean(false);

    public HazelcastHealthIndicator(@Qualifier("MasterHazelCastInstance") HazelcastInstance hazelcastInstance) {
        this.hazelcastInstance = hazelcastInstance;
        setupLifecycleListener();
    }

    private void setupLifecycleListener() {
        hazelcastInstance.getLifecycleService().addLifecycleListener(new LifecycleListener() {
            @Override
            public void stateChanged(LifecycleEvent event) {
                switch (event.getState()) {
                    case STARTED:
                        log.info("Hazelcast client connected to cluster");
                        isConnected.set(true);
                        break;
                    case SHUTDOWN:
                        log.warn("Hazelcast client disconnected from cluster");
                        isConnected.set(false);
                        break;
                    case CLIENT_CONNECTED:
                        log.info("Hazelcast client connected to cluster");
                        isConnected.set(true);
                        break;
                    case CLIENT_DISCONNECTED:
                        log.warn("Hazelcast client disconnected from cluster");
                        isConnected.set(false);
                        break;
                    default:
                        log.debug("Hazelcast client state changed: {}", event.getState());
                }
            }
        });
    }

    @Override
    public Health health() {
        try {
            if (!isConnected.get()) {
                return Health.down()
                        .withDetail("message", "Hazelcast client is not connected to cluster")
                        .build();
            }

            // Check if the client is still connected by trying to get cluster info
            hazelcastInstance.getCluster().getMembers();
            
            return Health.up()
                    .withDetail("clusterName", hazelcastInstance.getConfig().getClusterName())
                    .withDetail("instanceName", hazelcastInstance.getName())
                    .withDetail("memberCount", hazelcastInstance.getCluster().getMembers().size())
                    .build();
        } catch (Exception e) {
            log.error("Error checking Hazelcast health", e);
            return Health.down()
                    .withException(e)
                    .build();
        }
    }
} 