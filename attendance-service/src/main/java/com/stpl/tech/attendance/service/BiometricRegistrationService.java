package com.stpl.tech.attendance.service;

import com.stpl.tech.attendance.model.request.BiometricRegistrationRequest;
import com.stpl.tech.attendance.model.request.BiometricTempVerificationRequest;
import com.stpl.tech.attendance.model.request.BiometricVerificationRequest;
import com.stpl.tech.attendance.model.response.BiometricRegistrationActionResponse;
import com.stpl.tech.attendance.model.response.BiometricRegistrationResponse;
import com.stpl.tech.attendance.enums.ApprovalStatus;

public interface BiometricRegistrationService {
    BiometricRegistrationActionResponse registerBiometric(BiometricRegistrationRequest request);
    BiometricRegistrationActionResponse deregisterBiometric(String empId);
    BiometricRegistrationResponse getBiometricStatus(String empId);
    void handleApprovalResponse(Long approvalRequestId, ApprovalStatus status, String approverId) throws Exception;
    Integer getEmployeeIdFromImage(String biometricId, Integer unitId);
    /**
     * Verify temporary registration and proceed with regular registration if verification is successful
     * @param request The temporary verification request containing employee ID and face image
     * @return Response indicating the verification and registration status
     */
    BiometricRegistrationActionResponse verifyAndRegisterBiometric(BiometricVerificationRequest request);
    /**
     * Verify face images before registration
     * @param request The verification request containing both face images and registration details
     * @return Response indicating the verification status
     */

} 