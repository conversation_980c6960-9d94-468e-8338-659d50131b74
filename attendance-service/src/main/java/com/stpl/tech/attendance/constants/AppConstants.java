package com.stpl.tech.attendance.constants;

import java.util.List;

public class AppConstants {
    
    // Attendance Override Status Constants
    public static final String STATUS_ACTIVE = "ACTIVE";
    public static final String STATUS_PENDING = "PENDING";
    public static final String STATUS_APPROVED = "APPROVED";
    public static final String STATUS_REJECTED = "REJECTED";
    public static final String STATUS_CANCELLED = "CANCELLED";
    public static final String STATUS_CREATED = "CREATED";
    public static final String STATUS_INITIATED = "INITIATED";
    public static final String STATUS_CHANGED = "STATUS_CHANGE";


    public static final String LEAVE = "LEAVE";
    public static final String OD = "OD";
    public static final String WFH = "WFH";
    public static final String REGULARISATION = "REGULARISATION";
    public static final String DEBIT = "DEBIT";
    public static final String CREDIT = "CREDIT";
    public static final String SATURDAY = "SATURDAY";
    public static final String SUNDAY = "SUNDAY";
    public static final String RECURRING = "RECURRING";
    public static final String TEMPORARY = "TEMPORARY";
    public static final String WEEK_OFF = "WEEK_OFF";
    public static final String COMP_OFF = "COMP_OFF";
    public static final String LWP = "LWP";
    public static final List<String> LEAVE_TYPES = List.of(LEAVE, COMP_OFF);
    public static final String HOLIDAY = "HOLIDAY";
    public static final Integer SYSTEM_EMPLOYEE_ID = 120056;
    // colors
    public static final String YELLOW = "#DEC975"; // Yellow for pending Regularisation/OD/WFH
    public static final String BLUE = "#93B1CA"; // Blue for Leave/Holiday
    public static final String GREY = "#D1D1D135"; // Grey for Week Off
    public static final String GREEN = "#9FC3A5"; // Green for Present
    public static final String RED = "#F28B82"; // Red for Absent/Less Working Hours
    public static final String TRANSPARENT = "#00000000"; // Transparent for future dates
    
}
