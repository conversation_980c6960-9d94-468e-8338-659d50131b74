package com.stpl.tech.attendance.service.RosteringService;

import com.stpl.tech.attendance.dto.RosteringDto.EmpShiftUpdateRequestDTO;
import com.stpl.tech.attendance.dto.RosteringDto.EmpShiftUpdateResponseDTO;
import com.stpl.tech.attendance.dto.RosteringDto.ShiftCafeMappingRequestDTO;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * Service interface for Reladomo-based employee shift operations with bitemporal support
 * and shift coverage plan operations with unitemporal support
 */
public interface ReladomoService {

    /**
     * Update employee shift mappings based on empIds and shiftIds using Reladomo
     */
    EmpShiftUpdateResponseDTO updateEmpShifts(EmpShiftUpdateRequestDTO request);

    @Transactional(rollbackFor = Exception.class, readOnly = false)
    void terminateExistingMappings(Integer empId, LocalDateTime businessFrom, LocalDateTime processingFrom);

    @Transactional(rollbackFor = Exception.class, readOnly = false)
    void createReladomoBitemporalMapping(Integer empId, Integer shiftId, EmpShiftUpdateRequestDTO request, LocalDateTime businessFrom,
                                         LocalDateTime businessTo, LocalDateTime currentTime);
    /**
     * Update shift coverage plan mappings using unitemporal Reladomo operations
     */
    void updateShiftCoveragePlan(ShiftCafeMappingRequestDTO request);
}