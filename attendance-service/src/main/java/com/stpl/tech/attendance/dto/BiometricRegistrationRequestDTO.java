package com.stpl.tech.attendance.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BiometricRegistrationRequestDTO {
    private String model_id;
    private Parameters parameters;
    private InputData input_data;
    private String type;
    @Builder.Default
    private Map<String, Object> processing_options = new HashMap<>();
    @Builder.Default
    private List<ExtraRegData> extra_reg  = new ArrayList<>();

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Parameters {
        private Metadata metadata;
        private Integer employeeId;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Metadata {
        private Integer employeeId;
        private Integer unitId;
        private String imageUrl;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class InputData {
        private String data;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExtraRegData {
        /**
         * Base64 encoded image data
         */
        private String data;

        /**
         * Metadata containing image type and other information
         */
        private Map<String, Object> metadata;
    }
} 