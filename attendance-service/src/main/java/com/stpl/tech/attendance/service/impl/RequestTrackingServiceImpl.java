package com.stpl.tech.attendance.service.impl;

import com.stpl.tech.attendance.entity.RequestTracking;
import com.stpl.tech.attendance.repository.RequestTrackingRepository;
import com.stpl.tech.attendance.service.RequestTrackingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * Implementation of RequestTrackingService
 * Uses async operations and separate transactions to ensure tracking operations 
 * don't affect main business logic and don't slow down the system
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RequestTrackingServiceImpl implements RequestTrackingService {

    private final RequestTrackingRepository requestTrackingRepository;

    @Override
    @Async("asyncTaskExecutor")
    public void startTrackingAsync(String requestId, Integer unitId, String apiIdentifier) {
        try {
            // Create initial tracking record
            RequestTracking tracking = RequestTracking.builder()
                    .requestId(requestId)
                    .unitId(unitId)
                    .apiIdentifier(apiIdentifier)
                    .requestTime(LocalDateTime.now())
                    .build();
            
            requestTrackingRepository.save(tracking);
            log.debug("Started tracking request: {} for API: {}", requestId, apiIdentifier);
            
        } catch (Exception e) {
            // Log but don't throw - tracking failure should not affect main flow
            log.warn("Failed to start tracking request: {}, error: {}", requestId, e.getMessage());
        }
    }

    @Override
    @Async("asyncTaskExecutor")
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void markSuccessAsync(String requestId) {
        try {
            // Use atomic update to preserve existing reference ID
            RequestTracking tracking = requestTrackingRepository.findByRequestId(requestId)
                    .orElse(null);
            if (tracking == null) {
                log.warn("Tracking record not found for request: {}", requestId);
                return;
            }
            tracking.setCompletionTime(LocalDateTime.now());
            requestTrackingRepository.save(tracking);


            log.debug("Marked request as successful: {}", requestId);
                    
        } catch (Exception e) {
            // Log but don't throw - tracking failure should not affect main flow
            log.warn("Failed to mark request as successful: {}, error: {}", requestId, e.getMessage());
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    @Async("asyncTaskExecutor")
    public void markFailedAsync(String requestId, String errorMessage, String errorCode) {
        try {
            // Use atomic update to preserve existing reference ID
            requestTrackingRepository.updateCompletionTimeAndError(requestId, LocalDateTime.now(), errorMessage, errorCode);
            log.debug("Marked request as failed: {}, error: {}", requestId, errorCode);
                    
        } catch (Exception e) {
            // Log but don't throw - tracking failure should not affect main flow
            log.warn("Failed to mark request as failed: {}, error: {}", requestId, e.getMessage());
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    @Async("asyncTaskExecutor")
    public void updateReferenceIdAsync(String requestId, String referenceId) {
        try {
            // Use atomic update to preserve existing completion time
            requestTrackingRepository.updateReferenceIdOnly(requestId, referenceId);
            log.debug("Updated reference ID for request: {} to: {}", requestId, referenceId);
                    
        } catch (Exception e) {
            // Log but don't throw - tracking failure should not affect main flow
            log.warn("Failed to update reference ID for request: {}, error: {}", requestId, e.getMessage());
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    @Async("asyncTaskExecutor")
    public void completeWithReferenceIdAsync(String requestId, String referenceId) {
        try {
            // Use atomic update to set both completion time and reference ID
            requestTrackingRepository.updateCompletionTimeAndReferenceId(requestId, LocalDateTime.now(), referenceId);
            log.debug("Completed request: {} with reference ID: {}", requestId, referenceId);
                    
        } catch (Exception e) {
            // Log but don't throw - tracking failure should not affect main flow
            log.warn("Failed to complete request: {} with reference ID: {}, error: {}", requestId, referenceId, e.getMessage());
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    @Async("asyncTaskExecutor")
    public void completeWithReferenceIdWithLock(String requestId, String referenceId) {
        try {
            // Use pessimistic locking to prevent any concurrent modifications
            Optional<RequestTracking> trackingOpt = requestTrackingRepository.findByRequestIdWithLock(requestId);
            
            if (trackingOpt.isPresent()) {
                RequestTracking tracking = trackingOpt.get();
                
                // Update both fields atomically within the locked transaction
                tracking.setCompletionTime(LocalDateTime.now());
                tracking.setReferenceId(referenceId);
                
                requestTrackingRepository.save(tracking);
                log.debug("Completed request with lock: {} with reference ID: {}", requestId, referenceId);
            } else {
                log.warn("Request tracking record not found for ID: {}", requestId);
            }
                    
        } catch (Exception e) {
            // Log but don't throw - tracking failure should not affect main flow
            log.warn("Failed to complete request with lock: {} with reference ID: {}, error: {}", requestId, referenceId, e.getMessage());
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    @Async("asyncTaskExecutor")
    public void completeWithReferenceIdOptimistic(String requestId, String referenceId) {
        // This method is kept for interface compatibility but not recommended
        // Use completeWithReferenceIdWithLock instead for robust locking
        log.warn("Optimistic locking method called - this may cause data loss. Use completeWithReferenceIdWithLock instead.");
        completeWithReferenceIdWithLock(requestId, referenceId);
    }
}
