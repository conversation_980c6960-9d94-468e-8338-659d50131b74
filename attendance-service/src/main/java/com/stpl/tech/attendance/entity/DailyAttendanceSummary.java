package com.stpl.tech.attendance.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "daily_attendance_summary")
public class DailyAttendanceSummary {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "employee_id", nullable = false)
    private Integer employeeId;
    
    @Column(name = "attendance_date", nullable = false)
    private LocalDate attendanceDate;
    
    @Column(name = "first_check_in")
    private LocalDateTime firstCheckIn;
    
    @Column(name = "last_check_out")
    private LocalDateTime lastCheckOut;
    
    @Column(name = "total_hours")
    private BigDecimal totalHours = BigDecimal.ZERO;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private AttendanceStatus status;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false)
    private AttendanceType type = AttendanceType.NORMAL;
    
    @Column(name = "total_punches")
    private Integer totalPunches = 0;
    
    @Column(name = "reference_id")
    private Integer referenceId;

    @CreationTimestamp
    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
} 