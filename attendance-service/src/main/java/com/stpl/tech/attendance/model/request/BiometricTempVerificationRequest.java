package com.stpl.tech.attendance.model.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class BiometricTempVerificationRequest extends BiometricRegistrationRequest {
    @NotBlank(message = "Verification face image is required")
    private String verificationFace;
} 