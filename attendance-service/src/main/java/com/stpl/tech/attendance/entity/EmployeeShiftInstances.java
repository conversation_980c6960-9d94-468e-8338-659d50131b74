package com.stpl.tech.attendance.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Table(name = "employee_shift_instances")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmployeeShiftInstances {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "emp_id", nullable = false)
    private Integer empId;
    
    @Column(name = "business_date", nullable = false)
    private LocalDate businessDate;
    
    @Column(name = "shift_id", nullable = false)
    private Integer shiftId;
    
    @Column(name = "unit_id", nullable = false)
    private Integer unitId;
    
    @Column(name = "expected_start_time", nullable = false)
    private LocalDateTime expectedStartTime;
    
    @Column(name = "expected_end_time", nullable = false)
    private LocalDateTime expectedEndTime;
    
    @Column(name = "ideal_hours", nullable = false)
    private BigDecimal idealHours;
    
    @Column(name = "actual_start_time")
    private LocalDateTime actualStartTime;
    
    @Column(name = "actual_end_time")
    private LocalDateTime actualEndTime;
    
    @Column(name = "actual_hours")
    private BigDecimal actualHours;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private AttendanceStatus status = AttendanceStatus.ABSENT;

    @Column(name = "TYPE")
    private String type;
    
    @Column(name = "instance_status", length = 20, nullable = false)
    private String instanceStatus = "ACTIVE";
    
    @Column(name = "created_by")
    private String createdBy;
    
    @CreationTimestamp
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_by")
    private String updatedBy;
    
    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
} 