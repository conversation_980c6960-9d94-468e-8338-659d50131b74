package com.stpl.tech.attendance.cache.service;

import com.stpl.tech.attendance.dto.AttendanceMetadataResponse;
import com.stpl.tech.attendance.entity.EmpAttendanceMetadata;
import com.stpl.tech.attendance.enums.AttendanceAttributeType;
import com.stpl.tech.attendance.enums.LeaveCreditCycle;
import com.stpl.tech.attendance.enums.MappingStatus;
import com.stpl.tech.attendance.repository.EmpAttendanceMetadataRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Cache service for attendance metadata
 * Provides caching for department-wise attendance configuration values
 */
@Service
@Slf4j
public class AttendanceMetadataCacheService {

    @Autowired
    private  EmpAttendanceMetadataRepository metadataRepository;


    @Autowired
    @Lazy
    private  AttendanceMetadataCacheService self;
    
    private static final Integer DEFAULT_DEPT_ID = -1;
    
    /**
     * Cache key for department metadata
     */
    private static final String CACHE_NAME = "attendanceMetadata";
    
    /**
     * Get cached metadata value for a specific department and attribute
     * @param deptId Department ID
     * @param attributeCode Attribute code
     * @return Cached metadata response or null if not found
     */
    //@Cacheable(value = CACHE_NAME, key = "#deptId + '_' + #attributeCode")
    public AttendanceMetadataResponse getCachedMetadata(Integer deptId, AttendanceAttributeType attributeCode) {
        log.debug("Cache miss for metadata - Dept: {}, Attribute: {}", deptId, attributeCode);
        return null;
    }
    
    /**
     * Cache metadata value for a specific department and attribute
     * @param deptId Department ID
     * @param attributeCode Attribute code
     * @param metadataResponse Metadata response to cache
     * @return Cached metadata response
     */
    @CachePut(value = CACHE_NAME, key = "#deptId + '_' + #attributeCode")
    public AttendanceMetadataResponse cacheMetadata(Integer deptId, AttendanceAttributeType attributeCode, 
                                                  AttendanceMetadataResponse metadataResponse) {
        log.debug("Caching metadata for Dept: {}, Attribute: {}", deptId, attributeCode);
        return metadataResponse;
    }
    
    /**
     * Evict cache for a specific department and attribute
     * @param deptId Department ID
     * @param attributeCode Attribute code
     */
    @CacheEvict(value = CACHE_NAME, key = "#deptId + '_' + #attributeCode")
    public void evictMetadata(Integer deptId, AttendanceAttributeType attributeCode) {
        log.debug("Evicting cache for Dept: {}, Attribute: {}", deptId, attributeCode);
    }
    
    /**
     * Evict all cache for a specific department
     * @param deptId Department ID
     */
    @CacheEvict(value = CACHE_NAME, allEntries = true)
    public void evictAllMetadataForDept(Integer deptId) {
        log.debug("Evicting all cache for department: {}", deptId);
    }
    
    /**
     * Evict all cache entries
     */
    @CacheEvict(value = CACHE_NAME, allEntries = true)
    public void evictAllCache() {
        log.debug("Evicting all attendance metadata cache");
    }
    
    /**
     * Get metadata value for a department with fallback to default
     * @param deptId Department ID
     * @param attributeCode Attribute code
     * @return Metadata response with resolved value
     */
    public AttendanceMetadataResponse getMetadataValue(Integer deptId, AttendanceAttributeType attributeCode) {
        log.debug("Getting metadata value for Dept: {}, Attribute: {}", deptId, attributeCode);
        
        // First try to get from cache
        AttendanceMetadataResponse cachedResponse = self.getCachedMetadata(deptId, attributeCode);
        if (cachedResponse != null) {
            log.debug("Returning cached metadata for Dept: {}, Attribute: {}", deptId, attributeCode);
            return cachedResponse;
        }
        
        // If not in cache, resolve the value
        AttendanceMetadataResponse resolvedResponse = resolveMetadataValue(deptId, attributeCode);
        
        // Cache the resolved value
        if (resolvedResponse != null) {
            self.cacheMetadata(deptId, attributeCode, resolvedResponse);
        }
        
        return resolvedResponse;
    }
    
    /**
     * Resolve metadata value by first checking department-specific, then default
     * @param deptId Department ID
     * @param attributeCode Attribute code
     * @return Resolved metadata response
     */
    private AttendanceMetadataResponse resolveMetadataValue(Integer deptId, AttendanceAttributeType attributeCode) {
        // First try to find department-specific metadata
        Optional<EmpAttendanceMetadata> deptMetadata = metadataRepository
                .findByDeptIdAndAttributeCodeAndMappingStatus(deptId, attributeCode, MappingStatus.ACTIVE);
        
        if (deptMetadata.isPresent()) {
            log.debug("Found department-specific metadata for Dept: {}, Attribute: {}", deptId, attributeCode);
            return createMetadataResponse(deptMetadata.get(), false);
        }
        
        // If not found, try to find default metadata (DEPT_ID = -1)
        Optional<EmpAttendanceMetadata> defaultMetadata = metadataRepository
                .findDefaultMetadataByAttributeCode(attributeCode, MappingStatus.ACTIVE);
        
        if (defaultMetadata.isPresent()) {
            log.debug("Using default metadata for Dept: {}, Attribute: {}", deptId, attributeCode);
            return createMetadataResponse(defaultMetadata.get(), true);
        }
        
        log.warn("No metadata found for Dept: {}, Attribute: {}", deptId, attributeCode);
        return null;
    }
    
    /**
     * Create metadata response from entity
     * @param metadata Metadata entity
     * @param isDefault Whether this is a default value
     * @return Metadata response
     */
    private AttendanceMetadataResponse createMetadataResponse(EmpAttendanceMetadata metadata, boolean isDefault) {
        if (metadata.getAttributeCode().isBooleanType()) {
            Boolean booleanValue = metadata.getBooleanValue();
            return AttendanceMetadataResponse.forBoolean(
                metadata.getDeptId(), 
                metadata.getAttributeCode(), 
                booleanValue, 
                isDefault
            );
        } else if (metadata.getAttributeCode().isNumericType()) {
            BigDecimal numericValue = metadata.getNumericValue();
            return AttendanceMetadataResponse.forNumeric(
                metadata.getDeptId(), 
                metadata.getAttributeCode(), 
                numericValue, 
                isDefault
            );
        } else if (metadata.getAttributeCode().isCycleType()) {
            try {
                LeaveCreditCycle cycleValue = LeaveCreditCycle.valueOf(metadata.getAttributeValue());
                return AttendanceMetadataResponse.forCycle(
                    metadata.getDeptId(), 
                    metadata.getAttributeCode(), 
                    cycleValue, 
                    isDefault
                );
            } catch (IllegalArgumentException e) {
                log.warn("Invalid cycle value: {} for attribute: {}", metadata.getAttributeValue(), metadata.getAttributeCode());
                return AttendanceMetadataResponse.forString(
                    metadata.getDeptId(), 
                    metadata.getAttributeCode(), 
                    metadata.getAttributeValue(), 
                    isDefault
                );
            }
        } else if (metadata.getAttributeCode().isWeekendDaysType()) {
            return AttendanceMetadataResponse.forWeekendDays(
                metadata.getDeptId(), 
                metadata.getAttributeCode(), 
                metadata.getAttributeValue(), 
                isDefault
            );
        } else {
            return AttendanceMetadataResponse.forString(
                metadata.getDeptId(), 
                metadata.getAttributeCode(), 
                metadata.getAttributeValue(), 
                isDefault
            );
        }
    }
    
    /**
     * Get all metadata for a department with fallback to defaults
     * @param deptId Department ID
     * @return Map of attribute code to metadata response
     */
    public Map<AttendanceAttributeType, AttendanceMetadataResponse> getAllMetadataForDept(Integer deptId) {
        log.debug("Getting all metadata for department: {}", deptId);
        
        Map<AttendanceAttributeType, AttendanceMetadataResponse> result = new HashMap<>();
        
        // Get all active metadata for the department
        List<EmpAttendanceMetadata> deptMetadata = metadataRepository
                .findByDeptIdAndMappingStatus(deptId, MappingStatus.ACTIVE);
        
        // Get all default metadata
        List<EmpAttendanceMetadata> defaultMetadata = metadataRepository
                .findByDeptIdAndMappingStatus(DEFAULT_DEPT_ID, MappingStatus.ACTIVE);
        
        // Process default metadata first
        for (EmpAttendanceMetadata metadata : defaultMetadata) {
            AttendanceMetadataResponse response = createMetadataResponse(metadata, true);
            result.put(metadata.getAttributeCode(), response);
        }
        
        // Override with department-specific metadata
        for (EmpAttendanceMetadata metadata : deptMetadata) {
            AttendanceMetadataResponse response = createMetadataResponse(metadata, false);
            result.put(metadata.getAttributeCode(), response);
        }
        
        return result;
    }
    
    /**
     * Refresh cache for a specific department
     * @param deptId Department ID
     */
    public void refreshCacheForDept(Integer deptId) {
        log.info("Refreshing cache for department: {}", deptId);
        self.evictAllMetadataForDept(deptId);
        
        // Pre-load all metadata for the department
        Map<AttendanceAttributeType, AttendanceMetadataResponse> allMetadata = getAllMetadataForDept(deptId);
        for (Map.Entry<AttendanceAttributeType, AttendanceMetadataResponse> entry : allMetadata.entrySet()) {
            self.cacheMetadata(deptId, entry.getKey(), entry.getValue());
        }
    }
}


