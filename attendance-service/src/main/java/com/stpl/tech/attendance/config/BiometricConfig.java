package com.stpl.tech.attendance.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "biometric.service")
public class BiometricConfig {
    private String registrationUrl;
    private String templateApiUrl;
    private boolean mockEnabled;
} 