package com.stpl.tech.attendance.repository;

import com.stpl.tech.attendance.entity.EmpEligibilityMapping;
import com.stpl.tech.attendance.enums.EligibilityType;
import com.stpl.tech.attendance.enums.MappingStatus;
import com.stpl.tech.attendance.enums.MappingType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface EmpEligibilityMappingRepository extends JpaRepository<EmpEligibilityMapping, Long> {
    
    List<EmpEligibilityMapping> findByEmpIdAndEligibilityTypeAndStatus(
        String empId, 
        EligibilityType eligibilityType, 
        MappingStatus status
    );

    List<EmpEligibilityMapping> findByValueAndMappingTypeAndEligibilityTypeAndStatus(
        String value,
        MappingType mappingType,
        EligibilityType eligibilityType,
        MappingStatus status
    );

    List<EmpEligibilityMapping> findByValueInAndMappingTypeAndEligibilityTypeAndStatus(
        List<String> values,
        MappingType mappingType,
        EligibilityType eligibilityType,
        MappingStatus status
    );

    List<EmpEligibilityMapping> findByEmpIdInAndMappingTypeAndEligibilityTypeAndStatus(
            List<String> empIds,
            MappingType mappingType,
            EligibilityType eligibilityType,
            MappingStatus status
    );



    List<EmpEligibilityMapping> findByEmpIdAndValueAndMappingTypeAndEligibilityTypeAndStatus(
        String empId,
        String value,
        MappingType mappingType,
        EligibilityType eligibilityType,
        MappingStatus status
    );

    boolean existsByEmpIdAndValueAndMappingTypeAndEligibilityTypeAndStatus(
        String empId,
        String value,
        MappingType mappingType,
        EligibilityType eligibilityType,
        MappingStatus status
    );

    @Query("SELECT e FROM EmpEligibilityMapping e WHERE e.empId = :empId " +
           "AND e.eligibilityType = :eligibilityType " +
           "AND e.status = :status " +
           "AND (e.startDate IS NULL OR e.startDate <= :date) " +
           "AND (e.endDate IS NULL OR e.endDate >= :date)")
    List<EmpEligibilityMapping> findActiveMappingsForDate(
        @Param("empId") String empId,
        @Param("eligibilityType") EligibilityType eligibilityType,
        @Param("status") MappingStatus status,
        @Param("date") LocalDate date
    );

    @Query("SELECT e FROM EmpEligibilityMapping e WHERE e.value = :value " +
           "AND e.mappingType = :mappingType " +
           "AND e.eligibilityType = :eligibilityType " +
           "AND e.status = :status " +
           "AND (e.startDate IS NULL OR e.startDate <= :date) " +
           "AND (e.endDate IS NULL OR e.endDate >= :date)")
    List<EmpEligibilityMapping> findActiveMappingsForValueAndDate(
        @Param("value") String value,
        @Param("mappingType") MappingType mappingType,
        @Param("eligibilityType") EligibilityType eligibilityType,
        @Param("status") MappingStatus status,
        @Param("date") LocalDate date
    );
} 