package com.stpl.tech.attendance.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "EMP_ATTENDANCE_REQUEST")
public class EmployeeAttendanceRequest {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;
    
    @Column(name = "EMP_ID", nullable = false)
    private Integer empId;
    
    @Column(name = "DATE", nullable = false)
    private LocalDateTime date;
    
    @Column(name = "START_TIME")
    private LocalDateTime startTime;
    
    @Column(name = "END_TIME")
    private LocalDateTime endTime;
    
    @Column(name = "TYPE", length = 20)
    private String type;

    
    @Column(name = "STATUS", length = 20)
    private String status;
    
    @Column(name = "REASON", columnDefinition = "text")
    private String reason;
    
    @Column(name = "COMMENTS", columnDefinition = "text")
    private String comments;
    
    @Column(name = "CREATED_BY", length = 50)
    private String createdBy;
    
    @Column(name = "CREATION_TIME")
    private LocalDateTime creationTime;
    
    @Column(name = "UPDATED_BY", length = 50)
    private String updatedBy;
    
    @Column(name = "UPDATION_TIME")
    private LocalDateTime updationTime;

    @Column(name ="DOCUMENT")
    private String document;
} 