package com.stpl.tech.attendance.notification.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "NOTIFICATION_RECIPIENT")
public class NotificationRecipient {
    
    @Id
    @Column(name = "RECIPIENT_ID", length = 36)
    private String recipientId;
    
    @Column(name = "NOTIFICATION_ID", length = 36, nullable = false)
    private String notificationId;
    
    @Column(name = "USER_ID", length = 36, nullable = false)
    private String userId;
    
    @Column(name = "STATUS", length = 20)
    @Enumerated(EnumType.STRING)
    private DeliveryStatus status;
    
    @Column(name = "READ_DATE")
    private LocalDateTime readDate;
    
    @Column(name = "CREATED_DATE", nullable = false)
    private LocalDateTime createdDate;
    
    @Column(name = "CREATED_BY", length = 36, nullable = false)
    private String createdBy;
    
    @Column(name = "UPDATED_DATE", nullable = false)
    private LocalDateTime updatedDate;
    
    @Column(name = "UPDATED_BY", length = 36, nullable = false)
    private String updatedBy;

    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "NOTIFICATION_ID", insertable = false, updatable = false)
    private Notification notification;
    
    public enum DeliveryStatus {
        PENDING, DELIVERED, READ, FAILED
    }
} 