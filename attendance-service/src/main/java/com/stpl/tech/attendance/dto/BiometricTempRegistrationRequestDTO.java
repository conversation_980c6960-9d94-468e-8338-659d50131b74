package com.stpl.tech.attendance.dto;

import lombok.Builder;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@Builder
public class BiometricTempRegistrationRequestDTO {
    private String model_id;
    private Parameters parameters;
    private InputData input_data;
    private Map<String, Object> processing_options;
    private String verification_face;
    @Builder.Default
    private List<ExtraRegData> extra_reg = new ArrayList<>();
    


    @Data
    @Builder
    public static class Parameters {
        private Metadata metadata;
    }

    @Data
    @Builder
    public static class Metadata {
        private Integer employeeId;
    }

    @Data
    @Builder
    public static class InputData {
        private String data;
    }
    
    /**
     * Additional registration data structure
     * Maps to biometric service API format: { "data": "base64", "metadata": {"type": "left"} }
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExtraRegData {
        /**
         * Base64 encoded image data
         */
        private String data;
        
        /**
         * Metadata containing image type and other information
         */
        private Map<String, Object> metadata;
    }
} 