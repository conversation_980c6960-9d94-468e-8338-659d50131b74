package com.stpl.tech.attendance.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApplyRegularisationResponse {
    
    /**
     * Success or failure message
     */
    private String message;
    
    /**
     * Whether the operation was successful
     */
    private boolean success;
    
    /**
     * Created entry with date and ID
     */
    private CreatedEntry createdEntry;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CreatedEntry {
        /**
         * Date of the entry
         */
        private LocalDateTime date;
        
        /**
         * Created entry ID
         */
        private Long entryId;
    }
} 