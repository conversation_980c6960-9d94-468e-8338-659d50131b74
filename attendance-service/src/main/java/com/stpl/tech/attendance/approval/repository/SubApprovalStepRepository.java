package com.stpl.tech.attendance.approval.repository;

import com.stpl.tech.attendance.approval.entity.SubApprovalStep;
import com.stpl.tech.attendance.enums.ApprovalStepStatus;
import com.stpl.tech.attendance.model.request.ApprovalRequestFilter;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface SubApprovalStepRepository extends JpaRepository<SubApprovalStep, Long> {
    
    @Query("SELECT s FROM SubApprovalStep s WHERE s.approvalStep.id = :approvalStepId")
    List<SubApprovalStep> findByApprovalStepId(@Param("approvalStepId") Long approvalStepId);
    
    @Query("SELECT s FROM SubApprovalStep s WHERE s.approvalStep.id = :approvalStepId AND s.status = :status")
    List<SubApprovalStep> findByApprovalStepIdAndStatus(
        @Param("approvalStepId") Long approvalStepId, 
        @Param("status") ApprovalStepStatus status
    );
    
    @Query("SELECT s FROM SubApprovalStep s WHERE s.subTaskId = :subTaskId")
    Optional<SubApprovalStep> findBySubTaskId(@Param("subTaskId") String subTaskId);

    @Query(value = """
        SELECT sas.*
        FROM SUB_APPROVAL_STEP sas
        JOIN APPROVAL_STEP aps ON aps.STEP_ID = sas.STEP_ID
        JOIN APPROVAL_REQUEST ar ON ar.REQUEST_ID = aps.REQUEST_ID
        JOIN EMPLOYEE_DETAIL ed ON ed.EMP_ID = ar.REQUESTER_ID
        JOIN DESIGNATION d ON d.DESIGNATION_ID = ed.DESIGNATION_ID
        WHERE sas.APPROVER_ID = :approverId
          AND sas.STATUS = :status
          AND (:requesterIdsIsNull = TRUE OR ar.REQUESTER_ID IN (:requesterIds))
          AND (:typesIsNull = TRUE OR ar.REQUEST_TYPE IN (:types))
          AND (:designationsIsNull = TRUE OR d.DESIGNATION_NAME IN (:designations))
          AND (:statusesIsNull = TRUE OR ar.STATUS IN (:statuses))
          AND (:fromDate IS NULL OR ar.REQUEST_DATE >= :fromDate)
          AND (:toDate IS NULL OR ar.REQUEST_DATE <= :toDate)
        """, nativeQuery = true
    )
    List<SubApprovalStep> findByApproverIdAndStatusAndRequesterIds(
        @Param("approverId") Long approverId,
        @Param("status") String status,
        @Param("requesterIds") List<Long> requesterIds,
        @Param("requesterIdsIsNull") Boolean requesterIdsIsNull,
        @Param("types") List<String> types,
        @Param("typesIsNull") Boolean typesIsNull,
        @Param("designations") List<String> designations,
        @Param("designationsIsNull") Boolean designationsIsNull,
        @Param("statuses") List<String> statuses,
        @Param("statusesIsNull") Boolean statusesIsNull,
        @Param("fromDate") LocalDateTime fromDate,
        @Param("toDate") LocalDateTime toDate
    );

    @Query("SELECT s FROM SubApprovalStep s WHERE s.approverId = :approverId AND s.status IN ('APPROVED', 'REJECTED', 'SKIPPED')")
    Page<SubApprovalStep> findCompletedByApproverId(
        @Param("approverId") Long approverId,
        Pageable pageable
    );
} 