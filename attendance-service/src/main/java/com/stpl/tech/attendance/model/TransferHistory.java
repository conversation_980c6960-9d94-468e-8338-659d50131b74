package com.stpl.tech.attendance.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Table(name = "TRANSFER_HISTORY")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransferHistory {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long transferHistoryId;
    
    @Column(name = "TRANSFER_REQUEST_ID", nullable = false)
    private Long transferRequestId;
    
    @Column(name = "EMP_ID", nullable = false)
    private String empId;
    
    @Column(name = "SOURCE_UNIT_ID", nullable = false)
    private String sourceUnitId;
    
    @Column(name = "DESTINATION_UNIT_ID", nullable = false)
    private String destinationUnitId;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "TRANSFER_TYPE", nullable = false)
    private TransferType transferType;
    
    @Column(name = "START_DATE", nullable = false)
    private LocalDate startDate;
    
    @Column(name = "END_DATE")
    private LocalDate endDate;
    
    @Column(name = "COMMENT")
    private String comment;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "STATUS", nullable = false)
    private TransferRequestStatus status;
    
    @Column(name = "TRANSFER_HISTORY_STATUS", nullable = false)
    private String transferHistoryStatus;
    
    @Column(name = "CREATED_DATE", nullable = false)
    private LocalDateTime createdDate;
    
    @Column(name = "CREATED_BY", nullable = false)
    private String createdBy;
    
    @Column(name = "UPDATED_DATE", nullable = false)
    private LocalDateTime updatedDate;
    
    @Column(name = "UPDATED_BY", nullable = false)
    private String updatedBy;
    
    @Column(name = "DELETED_DATE")
    private LocalDateTime deletedDate;
    
    @Column(name = "DELETED_BY")
    private String deletedBy;
} 