package com.stpl.tech.attendance.dto;

import com.stpl.tech.attendance.model.TransferType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDate;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransferRequestDTO {
    @NotBlank(message = "Employee ID is required")
    private String empId;

    private String sourceUnitId;
    
    @NotBlank(message = "Destination unit ID is required")
    private String destinationUnitId;
    
    @NotNull(message = "Transfer type is required")
    private TransferType transferType;
    
    @NotNull(message = "Start date is required")
    private LocalDate startDate;
    
    private LocalDate endDate;
    
    @NotBlank(message = "Transfer reason is required")
    private String reason;
    
    private String comment;

} 