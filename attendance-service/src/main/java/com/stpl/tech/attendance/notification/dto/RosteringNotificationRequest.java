package com.stpl.tech.attendance.notification.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RosteringNotificationRequest {
    private Integer empId;
    private Integer shiftId;
    private String shiftName;
    private Integer unitId;
    private String unitName;
    private LocalDateTime effectiveFrom;
    private LocalDateTime effectiveTo;
    private String action; // "OVERRIDE_CREATED", "SHIFT_ASSIGNED"
    private String updatedBy;
} 