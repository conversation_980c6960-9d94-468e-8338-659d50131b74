package com.stpl.tech.attendance.repository;

import com.stpl.tech.attendance.entity.AttendanceImage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AttendanceImageRepository extends JpaRepository<AttendanceImage, Long> {
    
    /**
     * Find all images for a specific attendance record
     * @param attendanceId The attendance record ID
     * @return List of attendance images
     */
    List<AttendanceImage> findByAttendanceId(Long attendanceId);
    
    /**
     * Find all images for a specific attendance record ordered by creation time
     * @param attendanceId The attendance record ID
     * @return List of attendance images ordered by creation time
     */
    List<AttendanceImage> findByAttendanceIdOrderByCreatedAtAsc(Long attendanceId);
} 