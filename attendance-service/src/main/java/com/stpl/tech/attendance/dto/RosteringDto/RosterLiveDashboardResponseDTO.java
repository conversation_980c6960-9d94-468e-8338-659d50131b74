package com.stpl.tech.attendance.dto.RosteringDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RosterLiveDashboardResponseDTO {
    private DashboardViewDTO dashboardView;
    private GenericFilterMetadataDTO filterMetadata;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DashboardViewDTO {
        private DateRangeDTO date;
        private CafeDashboardStatsDTO cafeDashboard;
        private List<ShiftInfoDTO> shifts;
    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DateRangeDTO {
        private LocalDateTime date; // Current attendance date for live dashboard
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CafeDashboardStatsDTO {
        private Integer actual;
        private Integer ideal;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ShiftInfoDTO {
        private Integer shiftId;
        private LocalDateTime startTime; // Attendance date + shift start time
        private LocalDateTime endTime;   // Attendance date + shift end time
        private Integer numberOfEmployees;
        private String shiftName;
    }
}
