package com.stpl.tech.attendance.exception;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum TransferErrorCode {
    TRANSFER_NOT_FOUND("Transfer request not found"),
    TRANSFER_ALREADY_PROCESSED("Transfer request has already been processed"),
    INVALID_START_DATE("Start date is required"),
    INVALID_END_DATE("End date is required for temporary transfers"),
    INVALID_DATE_RANGE("End date must be after start date"),
    SAME_UNIT_TRANSFER("Source and destination units cannot be the same"),
    INVALID_EMPLOYEE("Invalid employee"),
    INVALID_UNIT("Invalid unit"),
    UNAUTHORIZED("Unauthorized to perform this action"),
    INVALID_STATUS("Invalid transfer status"),
    PENDING_TRANSFER_EXISTS("Employee already has a pending transfer request"),
    INVALID_TRANSFER_REASON("Invalid transfer reason. Please select from the available transfer reasons");
    
    private final String message;
} 