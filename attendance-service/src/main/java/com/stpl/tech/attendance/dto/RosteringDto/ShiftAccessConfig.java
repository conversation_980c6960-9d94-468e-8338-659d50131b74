package com.stpl.tech.attendance.dto.RosteringDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Configuration class for shift access permissions
 * Contains boolean flags for different rostering features
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShiftAccessConfig {
    
    /**
     * Flag to enable/disable all shift management features
     */
    private boolean allShiftManagement;
    
    /**
     * Flag to enable/disable unit-specific shift management features
     */
    private boolean unitShiftManagement;
    
    /**
     * Flag to enable/disable live dashboard view
     */
    private boolean liveDashboardView;
    
    /**
     * Flag to enable/disable shift dashboard view
     */
    private boolean shiftDashboardView;
    
    /**
     * Flag to enable/disable employee dashboard view
     */
    private boolean employeeDashboardView;

    private boolean analyticsDashboardView;

    private boolean hierarchyView;
} 