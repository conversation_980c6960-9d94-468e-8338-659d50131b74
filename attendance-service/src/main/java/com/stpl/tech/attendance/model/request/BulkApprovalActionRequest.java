package com.stpl.tech.attendance.model.request;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
public class BulkApprovalActionRequest {
    @NotNull(message = "Approver ID is required")
    private Long approverId;

    @NotEmpty(message = "At least one request must be specified")
    @Valid
    private List<RequestAction> requests;

    @Data
    public static class RequestAction {
        @NotNull(message = "Request ID is required")
        private Long requestId;

        @NotNull(message = "Step ID is required")
        private Long stepId;

        @NotNull(message = "Action is required")
        private String action;

        private String remarks;
    }
}