package com.stpl.tech.attendance.service;

import com.stpl.tech.attendance.dto.AttendanceSyncMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class AttendanceSyncConsumerService {

    private final ExternalAttendanceSyncService externalAttendanceSyncService;

    @KafkaListener(
        topics = "${kafka.topic.attendance-sync}",
        groupId = "${spring.kafka.consumer.group-id}",
        containerFactory = "kafkaListenerContainerFactory"
    )
    public void consumeAttendanceSyncMessage(
            @Payload AttendanceSyncMessage message,
            @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
            @Header(KafkaHeaders.RECEIVED_PARTITION) Integer partition,
            @Header(KafkaHeaders.OFFSET) Long offset,
            Acknowledgment acknowledgment) {
        
        log.info("Received attendance sync message: recordId={}, employeeId={}, topic={}, partition={}, offset={}", 
            message.getAttendanceRecordId(), message.getEmployeeId(), topic, partition, offset);
        
        try {
            externalAttendanceSyncService.processAttendanceSync(message);
            // Manually acknowledge the message after successful processing
            acknowledgment.acknowledge();
            log.info("Successfully processed and acknowledged attendance sync message for record: {}", 
                message.getAttendanceRecordId());
        } catch (Exception e) {
            log.error("Error processing attendance sync message for record: {}", 
                message.getAttendanceRecordId(), e);
            // Don't acknowledge the message so it can be retried
            // The message will be retried based on Kafka retry configuration
            throw e;
        }
    }
} 