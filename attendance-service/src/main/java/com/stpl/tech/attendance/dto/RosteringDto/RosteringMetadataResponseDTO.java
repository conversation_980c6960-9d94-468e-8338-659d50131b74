package com.stpl.tech.attendance.dto.RosteringDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RosteringMetadataResponseDTO {
    
    /**
     * Flag to enable/disable all shift management features
     */
    private Boolean allShiftManagement = true;
    
    /**
     * Flag to enable/disable unit-specific shift management features
     */
    private Boolean unitShiftManagement = true;
    
    /**
     * Flag to enable/disable live dashboard view
     */
    private Boolean liveDashboardView = true;
    
    /**
     * Flag to enable/disable shift dashboard view
     */
    private Boolean shiftDashboardView = true;
    
    /**
     * Flag to enable/disable employee dashboard view
     */
    private Boolean employeeDashboardView = true;

    private Boolean analyticsDashboardView = true;

    private Boolean hierarchyView = true;
} 