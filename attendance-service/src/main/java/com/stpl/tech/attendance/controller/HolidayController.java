package com.stpl.tech.attendance.controller;

import com.stpl.tech.attendance.dto.HolidayResponse;
import com.stpl.tech.attendance.entity.EmpHoliday;
import com.stpl.tech.attendance.model.response.ApiResponse;
import com.stpl.tech.attendance.service.HolidayService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Controller for holiday management operations
 */
@RestController
@RequestMapping("/api/v1/attendance/holidays")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Holiday Management", description = "APIs for managing system holidays")
public class HolidayController extends BaseController {
    
    private final HolidayService holidayService;
    
    /**
     * Get all holidays for a specific financial year
     * @param financialYear Financial year (e.g., "2024-25")
     * @return List of holidays
     */
    @GetMapping("/financial-year/{financialYear}")
    @Operation(summary = "Get holidays for a specific financial year")
    public ResponseEntity<ApiResponse<List<HolidayResponse>>> getHolidaysByFinancialYear(
            @PathVariable String financialYear) {
        log.info("Getting holidays for financial year: {}", financialYear);
        
        List<HolidayResponse> holidays = holidayService.getHolidaysByFinancialYear(financialYear);
        
        log.info("Retrieved {} holidays for financial year: {}", holidays.size(), financialYear);
        return success(holidays);
    }
    
    /**
     * Get holidays for the current financial year
     * @return List of holidays for current financial year
     */
    @GetMapping("/current")
    @Operation(summary = "Get holidays for the current financial year")
    public ResponseEntity<ApiResponse<List<HolidayResponse>>> getCurrentFinancialYearHolidays() {
        log.info("Getting holidays for current financial year");
        
        List<HolidayResponse> holidays = holidayService.getCurrentFinancialYearHolidays();
        
        log.info("Retrieved {} holidays for current financial year", holidays.size());
        return success(holidays);
    }

    

    


    /**
     * Create or update a holiday
     * @param holiday Holiday entity to save
     * @return Saved holiday entity
     */
    @PostMapping
    @Operation(summary = "Create or update a holiday")
    public ResponseEntity<ApiResponse<EmpHoliday>> createHoliday(@Valid @RequestBody EmpHoliday holiday) {
        log.info("Creating/updating holiday: {} on date: {}", holiday.getHolidayName(), holiday.getHolidayDate());
        
        EmpHoliday savedHoliday = holidayService.saveHoliday(holiday);
        
        log.info("Holiday saved successfully with ID: {}", savedHoliday.getId());
        return created(savedHoliday);
    }
    
    /**
     * Delete a holiday by ID
     * @param holidayId Holiday ID to delete
     * @return Success response
     */
    @DeleteMapping("/{holidayId}")
    @Operation(summary = "Delete a holiday by ID")
    public ResponseEntity<ApiResponse<String>> deleteHoliday(@PathVariable Long holidayId) {
        log.info("Deleting holiday with ID: {}", holidayId);
        
        holidayService.deleteHoliday(holidayId);
        
        log.info("Holiday deleted successfully");
        return success("Holiday deleted successfully");
    }
    
    /**
     * Get all available financial years
     * @return List of financial years
     */
    @GetMapping("/financial-years")
    @Operation(summary = "Get all available financial years")
    public ResponseEntity<ApiResponse<List<String>>> getAllFinancialYears() {
        log.info("Getting all available financial years");
        
        List<String> financialYears = holidayService.getAllFinancialYears();
        
        log.info("Retrieved {} financial years", financialYears.size());
        return success(financialYears);
    }
    
    /**
     * Get holidays by name pattern
     * @param namePattern Holiday name pattern
     * @return List of holidays matching the pattern
     */
    @GetMapping("/search")
    @Operation(summary = "Search holidays by name pattern")
    public ResponseEntity<ApiResponse<List<HolidayResponse>>> getHolidaysByNamePattern(
            @RequestParam String namePattern) {
        log.info("Searching holidays by name pattern: {}", namePattern);
        
        List<HolidayResponse> holidays = holidayService.getHolidaysByNamePattern(namePattern);
        
        log.info("Found {} holidays matching pattern: {}", holidays.size(), namePattern);
        return success(holidays);
    }
    
    /**
     * Refresh cache for a specific financial year
     * @param financialYear Financial year to refresh
     * @return Success response
     */
    @PostMapping("/cache/refresh/{financialYear}")
    @Operation(summary = "Refresh cache for a specific financial year")
    public ResponseEntity<ApiResponse<String>> refreshFinancialYearCache(@PathVariable String financialYear) {
        log.info("Refreshing cache for financial year: {}", financialYear);
        
        holidayService.refreshFinancialYearCache(financialYear);
        
        log.info("Cache refreshed successfully for financial year: {}", financialYear);
        return success("Cache refreshed successfully for financial year: " + financialYear);
    }
    
    /**
     * Evict all holiday cache entries
     * @return Success response
     */
    @PostMapping("/cache/evict-all")
    @Operation(summary = "Evict all holiday cache entries")
    public ResponseEntity<ApiResponse<String>> evictAllCache() {
        log.info("Evicting all holiday cache entries");
        
        holidayService.evictAllCache();
        
        log.info("All holiday cache entries evicted successfully");
        return success("All holiday cache entries evicted successfully");
    }
    
    /**
     * Evict cache for a specific financial year
     * @param financialYear Financial year
     * @return Success response
     */
    @PostMapping("/cache/evict/{financialYear}")
    @Operation(summary = "Evict cache for a specific financial year")
    public ResponseEntity<ApiResponse<String>> evictFinancialYearCache(@PathVariable String financialYear) {
        log.info("Evicting cache for financial year: {}", financialYear);
        
        holidayService.evictFinancialYearCache(financialYear);
        
        log.info("Cache evicted successfully for financial year: {}", financialYear);
        return success("Cache evicted successfully for financial year: " + financialYear);
    }
}
