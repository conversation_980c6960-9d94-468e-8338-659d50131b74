package com.stpl.tech.attendance.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Annotation to mark methods that should be tracked for request monitoring
 * 
 * Usage:
 * @TrackRequest
 * public ResponseEntity<?> myApiMethod() { ... }
 * 
 * This will automatically track:
 * - Request start time
 * - Request completion time
 * - Any errors that occur
 * - Unit ID from JWT context
 * - Request ID from MDC context
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface TrackRequest {
    
    /**
     * Optional description for the tracked request
     * Useful for identifying what type of operation is being tracked
     */
    String description() default "";
    
    /**
     * Whether to track successful requests (default: true)
     * Set to false if you only want to track failures
     */
    boolean trackSuccess() default true;
    
    /**
     * Whether to track failed requests (default: true)
     * Set to false if you only want to track successes
     */
    boolean trackFailure() default true;
}
