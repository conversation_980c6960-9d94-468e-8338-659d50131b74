package com.stpl.tech.attendance.controller;

import com.stpl.tech.attendance.dto.SyncStatistics;
import com.stpl.tech.attendance.entity.AttendanceSyncRecord;
import com.stpl.tech.attendance.enums.SyncStatus;
import com.stpl.tech.attendance.service.ExternalAttendanceSyncService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/v1/attendance/sync")
@RequiredArgsConstructor
public class AttendanceSyncController {

    private final ExternalAttendanceSyncService externalAttendanceSyncService;

    @GetMapping("/status/{attendanceRecordId}")
    public ResponseEntity<AttendanceSyncRecord> getSyncStatus(@PathVariable Long attendanceRecordId) {
        AttendanceSyncRecord status = externalAttendanceSyncService.getSyncStatus(attendanceRecordId);
        if (status == null) {
            return ResponseEntity.notFound().build();
        }
        return ResponseEntity.ok(status);
    }

    @GetMapping("/statistics")
    public ResponseEntity<SyncStatistics> getSyncStatistics() {
        SyncStatistics statistics = externalAttendanceSyncService.getSyncStatistics();
        return ResponseEntity.ok(statistics);
    }

    @PostMapping("/retry-failed")
    public ResponseEntity<String> retryFailedSyncs() {
        externalAttendanceSyncService.retryFailedSyncs();
        return ResponseEntity.ok("Retry process initiated");
    }

    @PostMapping("/trigger-sync")
    public ResponseEntity<String> triggerSync() {
        externalAttendanceSyncService.fetchAndSyncAttendanceData();
        return ResponseEntity.ok("Sync process initiated");
    }

    /**
     * Resync attendance data for a specific date range and employee
     */
    @PostMapping("/resync")
    public ResponseEntity<String> resyncAttendanceData(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fromDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate toDate,
            @RequestParam(required = false) Integer employeeId) {
        
        log.info("Resync request received for date range: {} to {}, employee: {}", fromDate, toDate, employeeId);
        
        if (employeeId == null) {
            // Resync for all employees in date range
            // This would need to be implemented based on your requirements
            return ResponseEntity.badRequest().body("Employee ID is required for resync");
        }
        
        externalAttendanceSyncService.resyncAttendanceData(fromDate, toDate, employeeId);
        return ResponseEntity.ok("Resync process initiated for employee: " + employeeId);
    }

    /**
     * Resync all failed records
     */
    @PostMapping("/resync-failed")
    public ResponseEntity<String> resyncFailedRecords() {
        log.info("Resync failed records request received");
        externalAttendanceSyncService.resyncFailedRecords();
        return ResponseEntity.ok("Failed records resync process initiated");
    }

    /**
     * Get sync records by date range and employee
     */
    @GetMapping("/records")
    public ResponseEntity<List<AttendanceSyncRecord>> getSyncRecords(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fromDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate toDate,
            @RequestParam(required = false) Integer employeeId) {
        
        List<AttendanceSyncRecord> records = externalAttendanceSyncService
            .getSyncRecordsByDateRange(fromDate, toDate, employeeId);
        return ResponseEntity.ok(records);
    }

    /**
     * Get all failed sync records
     */
    @GetMapping("/failed-records")
    public ResponseEntity<List<AttendanceSyncRecord>> getFailedSyncRecords() {
        List<AttendanceSyncRecord> failedRecords = externalAttendanceSyncService.getFailedSyncRecords();
        return ResponseEntity.ok(failedRecords);
    }

    /**
     * Get sync status summary for a date range
     */
    @GetMapping("/summary")
    public ResponseEntity<Map<String, Object>> getSyncSummary(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fromDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate toDate,
            @RequestParam(required = false) Integer employeeId) {
        
        List<AttendanceSyncRecord> records = externalAttendanceSyncService
            .getSyncRecordsByDateRange(fromDate, toDate, employeeId);
        
        long total = records.size();
        long successful = records.stream().filter(r -> r.getSyncStatus() == SyncStatus.SUCCESS).count();
        long failed = records.stream().filter(r -> r.getSyncStatus() == SyncStatus.FAILED).count();
        long pending = records.stream().filter(r -> r.getSyncStatus() == SyncStatus.PENDING).count();
        long inProgress = records.stream().filter(r -> r.getSyncStatus() == SyncStatus.IN_PROGRESS).count();
        
        Map<String, Object> summary = new HashMap<>();
        summary.put("totalRecords", total);
        summary.put("successfulRecords", successful);
        summary.put("failedRecords", failed);
        summary.put("pendingRecords", pending);
        summary.put("inProgressRecords", inProgress);
        summary.put("successRate", total > 0 ? (double) successful / total * 100 : 0);
        summary.put("fromDate", fromDate);
        summary.put("toDate", toDate);
        summary.put("employeeId", employeeId);
        
        return ResponseEntity.ok(summary);
    }
} 