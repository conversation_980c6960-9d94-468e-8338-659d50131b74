package com.stpl.tech.attendance.service.impl;

import com.stpl.tech.attendance.cache.service.UserCacheService;
import com.stpl.tech.attendance.entity.EmpAttendanceBalanceData;
import com.stpl.tech.attendance.enums.AttendanceAttributeType;
import com.stpl.tech.attendance.exception.BusinessException;
import com.stpl.tech.attendance.repository.EmpAttendanceBalanceDataRepository;
import com.stpl.tech.attendance.repository.EmpAttendanceMetadataRepository;
import com.stpl.tech.attendance.repository.EmpAttendanceReserveDataRepository;
import com.stpl.tech.attendance.repository.EmpHolidayRepository;
import com.stpl.tech.attendance.repository.EmployeeAttendanceRequestRepository;
import com.stpl.tech.attendance.service.AttendanceMetadataService;
import com.stpl.tech.attendance.service.HolidayService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class AttendanceRequestUtil {
    private final EmployeeAttendanceRequestRepository employeeAttendanceRequestRepository;
    private final UserCacheService userCacheService;
    private final AttendanceMetadataService attendanceMetadataService;
    private final EmpAttendanceBalanceDataRepository empAttendanceBalanceDataRepository;
    private final EmpAttendanceMetadataRepository empAttendanceMetadataRepository;
    private final HolidayService holidayService;
    private final EmpHolidayRepository empHolidayRepository;

    public static Map<String, BigDecimal> createLeaveDetailsFromDatabase(EmpAttendanceBalanceData empLeaveData, EmpAttendanceReserveDataRepository empLeaveDetailDataRepository) {
        Map<String, BigDecimal> leaveDetails = new HashMap<>();

        if (empLeaveData != null) {
            // Calculate available count as: EMP_ATTENDANCE_BALANCE_DATA count - EMP_ATTENDANCE_RESERVE_DATA reserved count
            leaveDetails.put("LEAVE", calculateAvailableLeaveCount(empLeaveData.getLeaveCount(), empLeaveData.getId(), "LEAVE", empLeaveDetailDataRepository));
            leaveDetails.put("COMP_OFF", calculateAvailableLeaveCount(empLeaveData.getCompOffCount(), empLeaveData.getId(), "COMP_OFF", empLeaveDetailDataRepository));
            //leaveDetails.put("LWP", calculateAvailableLeaveCount(empLeaveData.getLwpCount(), empLeaveData.getId(), "LWP", empLeaveDetailDataRepository));
        } else {
            // Fallback to default values if no data found
            leaveDetails.put("LEAVE", BigDecimal.ZERO);
            leaveDetails.put("COMP_OFF", BigDecimal.ZERO);
            //leaveDetails.put("LWP", BigDecimal.ZERO);
        }

        return leaveDetails;
    }

    private static BigDecimal calculateAvailableLeaveCount(BigDecimal totalCount, Long empLeaveDataId, String leaveType, EmpAttendanceReserveDataRepository empLeaveDetailDataRepository) {
        if (totalCount == null) {
            return BigDecimal.ZERO;
        }

        try {
            // Get reserved count from EMP_ATTENDANCE_RESERVE_DATA table
            BigDecimal reservedCount = empLeaveDetailDataRepository.getReservedCountByEmpAttendanceBalanceIdAndType(empLeaveDataId, leaveType);

            // If reserved count is null, treat as 0
            if (reservedCount == null) {
                reservedCount = BigDecimal.ZERO;
            }

            // Calculate available count: total - reserved
            BigDecimal availableCount = totalCount.subtract(reservedCount);

            // Ensure available count is not negative
            return availableCount.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : availableCount;

        } catch (Exception e) {
            // If there's any error fetching reserved count, return total count (treating reserved as 0)
            return totalCount;
        }
    }


    public List<LocalDateTime> filterLeaveDatesByWeekOffAndHolidays(List<LocalDateTime> leaveDates, Integer empId) {
        if (leaveDates == null || leaveDates.isEmpty()) {
            return new ArrayList<>();
        }
        
        // Filter out weekend and holiday dates (don't throw exception, just filter)
        List<LocalDateTime> filteredDates = new ArrayList<>();
        List<LocalDateTime> filteredOutDates = new ArrayList<>();
        
        for (LocalDateTime date : leaveDates) {
            if (!isWeekendOrHoliday(date, empId)) {
                filteredDates.add(date);
            } else {
                filteredOutDates.add(date);
            }
        }
        
        // Log which dates were filtered out for transparency
        if (!filteredOutDates.isEmpty()) {
            log.info("Filtered out {} weekend/holiday dates for employee {}: {}", 
                    filteredOutDates.size(), empId, filteredOutDates);
        }
        
        return filteredDates;
    }


    private void validateLeaveDates(List<LocalDateTime> leaveDates, Integer empId) {
        for (LocalDateTime date : leaveDates) {
            if (isWeekendOrHoliday(date, empId)) {
                String dayName = date.getDayOfWeek().name();
                if (isWeekendDay(date, empId)) {
                    throw new BusinessException("Leave can't be applied on weekends/weekOffs");
                } else {
                    throw new BusinessException("Leave can't be applied on holiday: " + date.toLocalDate());
                }
            }
        }
    }
    
    /**
     * Check if a date is weekend or holiday for the employee
     * @param date Date to check
     * @param empId Employee ID
     * @return true if the date is weekend or holiday
     */
    private boolean isWeekendOrHoliday(LocalDateTime date, Integer empId) {
        Integer deptId = getEmployeeDepartmentId(empId);
        boolean isFixedWeekOff = isFixedWeekOffEnabled(deptId);
        
        if (isFixedWeekOff) {
            // FIXED_WEEK_OFF = true: Skip weekends AND holidays
            return isWeekendDay(date, empId) || isHoliday(date);
        } else {
            // FIXED_WEEK_OFF = false: Skip NOTHING - include week-off days AND holidays
            return false;
        }
    }
    
    /**
     * Check if a date is a weekend day for the employee
     * @param date Date to check
     * @param empId Employee ID
     * @return true if the date is a weekend day
     */
    private boolean isWeekendDay(LocalDateTime date, Integer empId) {
        Integer deptId = getEmployeeDepartmentId(empId);
        
        // Check if FIXED_WEEK_OFF is enabled
        boolean isFixedWeekOff = isFixedWeekOffEnabled(deptId);
        
        if (isFixedWeekOff) {
            // Use WEEKEND_DAYS from metadata - ALWAYS skip these
            String[] weekendDays = getWeekendDaysFromMetadata(deptId);
            return isDateInWeekendDays(date, weekendDays);
        } else {
            // FIXED_WEEK_OFF = false: DON'T skip week_off_day from EMP_ATTENDANCE_BALANCE_DATA
            // Return false to treat week-off day as a regular working day for leave
            return false;
        }
    }
    
    /**
     * Check if a date is a holiday
     * @param date Date to check
     * @return true if the date is a holiday
     */
    private boolean isHoliday(LocalDateTime date) {
        return holidayService.isHoliday(date.toLocalDate());
    }
    
    /**
     * Get employee's department ID
     * @param empId Employee ID
     * @return Department ID
     */
    private Integer getEmployeeDepartmentId(Integer empId) {
        try {
            var employee = userCacheService.getUserById(empId);
            return employee != null ? employee.getDepartmentId() : null;
        } catch (Exception e) {
            // Log error and return null - will fall back to default metadata
            return null;
        }
    }
    
    /**
     * Check if fixed week-off is enabled for the department
     * @param deptId Department ID
     * @return true if fixed week-off is enabled
     */
    private boolean isFixedWeekOffEnabled(Integer deptId) {
        try {
            var metadata = empAttendanceMetadataRepository.findByDeptIdAndAttributeCodeAndMappingStatus(
                deptId, AttendanceAttributeType.FIXED_WEEK_OFF, com.stpl.tech.attendance.enums.MappingStatus.ACTIVE
            );
            
            if (metadata.isPresent()) {
                return Boolean.parseBoolean(metadata.get().getAttributeValue());
            }
            
            // Fall back to default metadata (DEPT_ID = -1)
            var defaultMetadata = empAttendanceMetadataRepository.findByDeptIdAndAttributeCodeAndMappingStatus(
                -1, AttendanceAttributeType.FIXED_WEEK_OFF, com.stpl.tech.attendance.enums.MappingStatus.ACTIVE
            );
            
            return defaultMetadata.isPresent() && Boolean.parseBoolean(defaultMetadata.get().getAttributeValue());
        } catch (Exception e) {
            // Log error and return false - will use balance data approach
            return false;
        }
    }
    
    /**
     * Get weekend days from metadata
     * @param deptId Department ID
     * @return Array of weekend days
     */
    private String[] getWeekendDaysFromMetadata(Integer deptId) {
        try {
            var metadata = empAttendanceMetadataRepository.findByDeptIdAndAttributeCodeAndMappingStatus(
                deptId, AttendanceAttributeType.WEEKEND_DAYS, com.stpl.tech.attendance.enums.MappingStatus.ACTIVE
            );
            
            if (metadata.isPresent() && metadata.get().getAttributeValue() != null) {
                return metadata.get().getAttributeValue().split(",");
            }
            
            // Fall back to default metadata (DEPT_ID = -1)
            var defaultMetadata = empAttendanceMetadataRepository.findByDeptIdAndAttributeCodeAndMappingStatus(
                -1, AttendanceAttributeType.WEEKEND_DAYS, com.stpl.tech.attendance.enums.MappingStatus.ACTIVE
            );
            
            if (defaultMetadata.isPresent() && defaultMetadata.get().getAttributeValue() != null) {
                return defaultMetadata.get().getAttributeValue().split(",");
            }
        } catch (Exception e) {
            // Log error and continue
        }
        
        // Default fallback
        return new String[]{"SATURDAY", "SUNDAY"};
    }

    /**
     * Check if a date falls on any of the weekend days
     * @param date Date to check
     * @param weekendDays Array of weekend day names
     * @return true if the date is a weekend day
     */
    private boolean isDateInWeekendDays(LocalDateTime date, String[] weekendDays) {
        if (weekendDays == null || weekendDays.length == 0) {
            return false;
        }
        
        String dayName = date.getDayOfWeek().name();
        for (String weekendDay : weekendDays) {
            if (weekendDay.trim().equalsIgnoreCase(dayName)) {
                return true;
            }
        }
        return false;
    }

    public boolean isWeekOff(LocalDate date, Integer employeeId, Integer departmentId) {
        try {
            // Check if fixed week-off is enabled for the department
            boolean isFixedWeekOffEnabled = attendanceMetadataService.isFixedWeekendAllowed(departmentId);

            if (isFixedWeekOffEnabled) {
                // Get weekend days from metadata
                String[] weekendDays = attendanceMetadataService.getWeekendDays(departmentId);
                DayOfWeek dayOfWeek = date.getDayOfWeek();
                String dayName = dayOfWeek.name();

                // Check if the day is in the weekend days list
                for (String weekendDay : weekendDays) {
                    if (weekendDay.trim().equalsIgnoreCase(dayName)) {
                        return true;
                    }
                }
                return false;
            } else {
                // If fixed week-off is not enabled, use default Saturday/Sunday logic
                DayOfWeek dayOfWeek = date.getDayOfWeek();
                String weekOffDay = empAttendanceBalanceDataRepository.findByEmpId(employeeId).get().getWeekOffDay();
                if(weekOffDay != null){
                    return dayOfWeek.name().equalsIgnoreCase(weekOffDay);
                }else{
                    return false;
                }
            }
        } catch (Exception e) {
            log.error("Error checking week off for department: {} and date: {}", departmentId, date, e);
            return false;
        }
    }

    public boolean isHoliday(LocalDate date, Integer departmentId) {
        try {
            // Check if holidays are allowed for the department
            boolean isHolidayAllowed = attendanceMetadataService.isHolidayAllowed(departmentId);

            if (isHolidayAllowed) {
                // Check if the date exists in EMP_HOLIDAYS table
                boolean isHoliday = empHolidayRepository.existsByHolidayDate(date);
                log.debug("Holiday check for date: {} and department: {} - Result: {}", date, departmentId, isHoliday);
                return isHoliday;
            } else {
                // If holidays are not allowed for the department, return false
                log.debug("Holidays not allowed for department: {}, returning false for date: {}", departmentId, date);
                return false;
            }
        } catch (Exception e) {
            log.error("Error checking holiday for department: {} and date: {}", departmentId, date, e);
            // Fallback to false (no holiday)
            return false;
        }
    }
}
