package com.stpl.tech.attendance.exception;

import com.stpl.tech.attendance.model.response.ApiResponse;
import com.stpl.tech.attendance.model.response.ErrorResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(AuthenticationException.class)
    public ResponseEntity<Map<String, Object>> handleAuthenticationException(AuthenticationException ex) {
        log.error("Authentication failed: {}", ex.getMessage());
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        Map<String, String> error = new HashMap<>();
        error.put("code", "AUTH_001");
        error.put("message", ex.getMessage());
        response.put("error", error);
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
    }

    @ExceptionHandler(AuthorizationException.class)
    public ResponseEntity<Map<String, Object>> handleAuthorizationException(AuthorizationException ex) {
        log.error("Authorization failed: {}", ex.getMessage());
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        Map<String, String> error = new HashMap<>();
        error.put("code", "AUTH_002");
        error.put("message", ex.getMessage());
        response.put("error", error);
        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
    }

    @ExceptionHandler(ValidationException.class)
    public ResponseEntity<Map<String, Object>> handleValidationException(ValidationException ex) {
        log.error("Validation failed: {}", ex.getMessage());
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        Map<String, Object> error = new HashMap<>();
        error.put("code", "VAL_001");
        error.put("message", ex.getMessage());
        if (ex.getDetails() != null) {
            error.put("details", ex.getDetails());
        }
        response.put("error", error);
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ApiResponse<ErrorResponse>> handleValidationExceptions(MethodArgumentNotValidException ex) {
        Map<String, String> errors = new HashMap<>();
        ex.getBindingResult().getAllErrors().forEach((error) -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            errors.put(fieldName, errorMessage);
        });
        
        ErrorResponse error = new ErrorResponse("Validation failed", "VALIDATION_ERROR", errors);
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.<ErrorResponse>builder()
                        .success(false)
                        .data(error)
                        .build());
    }

    @ExceptionHandler(BusinessException.class)
    public ResponseEntity<ApiResponse<ErrorResponse>> handleBusinessException(BusinessException ex) {
        log.error("Business validation failed: {}", ex.getMessage());
        ErrorResponse error = new ErrorResponse(ex.getMessage(), "BIZ_001");
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.<ErrorResponse>builder()
                        .success(false)
                        .data(error)
                        .build());
    }

    @ExceptionHandler(AttendanceValidationException.class)
    public ResponseEntity<ApiResponse<ErrorResponse>> handleAttendanceValidationException(AttendanceValidationException ex) {
        log.error("Attendance validation error: ", ex);
        ErrorResponse error = new ErrorResponse(ex.getMessage(), "VALIDATION_ERROR");
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.<ErrorResponse>builder()
                        .success(false)
                        .data(error)
                        .build());
    }

    @ExceptionHandler(BiometricRegistrationException.class)
    public ResponseEntity<ApiResponse<ErrorResponse>> handleBiometricRegistrationException(BiometricRegistrationException ex) {
        log.error("Biometric registration error: ", ex);
        ErrorResponse error = new ErrorResponse(ex.getMessage(), ex.getErrorCode().getCode());
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.<ErrorResponse>builder()
                        .success(false)
                        .data(error)
                        .build());
    }

    @ExceptionHandler(TransferException.class)
    public ResponseEntity<ApiResponse<ErrorResponse>> handleTransferException(TransferException ex) {
        log.error("Biometric registration error: ", ex);
        ErrorResponse error = new ErrorResponse(ex.getMessage(), ex.getErrorCode().name());
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.<ErrorResponse>builder()
                        .success(false)
                        .data(error)
                        .build());
    }

    @ExceptionHandler(AttendanceException.class)
    public ResponseEntity<ApiResponse<ErrorResponse>> handleAttendanceException(AttendanceException ex) {
        log.error("Attendance error: ", ex);
        ErrorResponse error = new ErrorResponse(ex.getMessage(), ex.getErrorCode().getCode());
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.<ErrorResponse>builder()
                        .success(false)
                        .data(error)
                        .build());
    }

    @ExceptionHandler(ApprovalException.class)
    public ResponseEntity<ApiResponse<ErrorResponse>> handleApprovalException(ApprovalException ex) {
        log.error("Approval error: ", ex);
        ErrorResponse error = new ErrorResponse(ex.getMessage(), ex.getErrorCode().getCode());
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.<ErrorResponse>builder()
                        .success(false)
                        .data(error)
                        .build());
    }

    @ExceptionHandler(AuthenticationFailureException.class)
    public ResponseEntity<ApiResponse<ErrorResponse>> handleAuthenticationFailureException(AuthenticationFailureException ex) {
        log.error("Authentication failed: {}", ex.getMessage());
        ErrorResponse error = new ErrorResponse(ex.getMessage(),"AUTH_001" );
        return ResponseEntity
                .status(HttpStatus.UNAUTHORIZED)
                .body(ApiResponse.<ErrorResponse>builder()
                        .success(false)
                        .data(error)
                        .build());
    }

    @ExceptionHandler(CafeLiveDashboardException.class)
    public ResponseEntity<ApiResponse<ErrorResponse>> handleCafeLiveDashboardException(CafeLiveDashboardException ex) {
        log.error("Cafe live dashboard error: ", ex);
        ErrorResponse error = new ErrorResponse(ex.getMessage(), ex.getErrorCode());
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.<ErrorResponse>builder()
                        .success(false)
                        .data(error)
                        .build());
    }

    @ExceptionHandler(FilterValidationException.class)
    public ResponseEntity<ApiResponse<ErrorResponse>> handleFilterValidationException(FilterValidationException ex) {
        log.error("Filter validation error: ", ex);
        ErrorResponse error = new ErrorResponse(ex.getMessage(), ex.getErrorCode());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.<ErrorResponse>builder()
                        .success(false)
                        .data(error)
                        .build());
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<ApiResponse<ErrorResponse>> handleGenericException(Exception ex) {
        log.error("Unexpected error occurred: ", ex);
        ErrorResponse error = new ErrorResponse("An unexpected error occurred", "INTERNAL_SERVER_ERROR");
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.<ErrorResponse>builder()
                        .success(false)
                        .data(error)
                        .build());
    }
}