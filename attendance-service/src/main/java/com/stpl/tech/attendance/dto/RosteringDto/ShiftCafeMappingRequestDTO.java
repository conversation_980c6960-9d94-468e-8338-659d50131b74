package com.stpl.tech.attendance.dto.RosteringDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;

import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShiftCafeMappingRequestDTO {
    @NotNull(message = "Shift ID is required")
    private Integer shiftId;

    @NotNull(message = "Unit ID is required")
    private Integer unitId;

    private Integer shiftCafeMappingId;

    private Map<String, Integer> dayWiseIdealCount;

    private Integer idealCount;

    private boolean allDaySame;

    private String action ;// update or delete on the basis of this we'll decide what to do
} 