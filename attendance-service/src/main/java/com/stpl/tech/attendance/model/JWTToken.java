package com.stpl.tech.attendance.model;

import com.stpl.tech.master.core.external.acl.service.TokenDao;
import io.jsonwebtoken.Claims;
import lombok.Data;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Data
public class JWTToken implements TokenDao {
    protected int unitId;
    protected String issuer;
    protected int userId;
    protected int terminalId;
    protected String macAddress;
    protected String geoLocation;
    private Long expirationTime;
    private String sessionKey;

    /**
     * Creates a map of claims for JWT token generation
     * @return Map containing all claims to be included in the JWT
     */
    public Map<String, Object> createClaims() {
        // Setting JWT Claims
        Map<String, Object> authClaims = new HashMap<String, Object>();
        authClaims.put("sessionKey", this.getSessionKey());
        authClaims.put("unitId", this.getUnitId());
        authClaims.put("issuer", this.getIssuer());
        authClaims.put("userId", this.getUserId());
        authClaims.put("terminalId", this.getTerminalId());
        authClaims.put("macAddress", this.getMacAddress());
        authClaims.put("geoLocation", this.getGeoLocation());
        authClaims.put("expirationTime", this.getExpirationTime());
        return authClaims;
    }

    /**
     * Parses claims from a JWT token into this object
     * @param claims The JWT claims to parse
     */
    public void parseClaims(Claims claims) {
        // Handle null checks for all claims with appropriate default values
        this.sessionKey = claims.get("sessionKey", String.class);
        this.unitId = claims.get("unitId") != null ? claims.get("unitId", Integer.class) : -1;
        this.userId = claims.get("userId") != null ? claims.get("userId", Integer.class) : -1;
        this.issuer = claims.get("issuer", String.class);
        this.terminalId = claims.get("terminalId") != null ? claims.get("terminalId", Integer.class) : -1;
        this.macAddress = claims.get("macAddress") != null ? claims.get("macAddress", String.class) : null;
        this.geoLocation = claims.get("geoLocation") !=null ?claims.get("geoLocation", String.class) : null;
        
        // Handle expiration time with null check
        if (claims.get("expirationTime") != null) {
            this.expirationTime = claims.get("expirationTime", Long.class);
        } else if (claims.get("iat", Date.class) != null) {
            this.expirationTime = claims.get("iat", Date.class).getTime();
        } else {
            this.expirationTime = System.currentTimeMillis()+ 2000; // Default to current time if both are null
        }
    }
} 