package com.stpl.tech.attendance.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CancelRequestResponse {
    private String message;
    private Long requestId;
    private String previousStatus;
    private String newStatus;
    
    /**
     * ID of the cancellation approval request (only populated when requireApproval is true)
     */
    private Long cancellationApprovalId;
}
