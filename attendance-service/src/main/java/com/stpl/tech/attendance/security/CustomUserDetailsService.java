package com.stpl.tech.attendance.security;

import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import lombok.RequiredArgsConstructor;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Service
@RequiredArgsConstructor
public class CustomUserDetailsService implements UserDetailsService {
    
    private final JdbcTemplate jdbcTemplate;
    private final HazelcastInstance hazelcastInstance;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        // Try to get roles from cache first
        IMap<String, Set<GrantedAuthority>> rolesCache = hazelcastInstance.getMap("user-roles-cache");
        Set<GrantedAuthority> authorities = rolesCache.get(username);

        if (authorities == null) {
            // Cache miss, load from database
            authorities = loadUserAuthorities(username);
            // Store in cache with TTL
            rolesCache.put(username, authorities, 1, TimeUnit.HOURS);
        }

        // Create UserDetails with the authorities
        return new User(username, 
                       "{noop}password", // You'll get the actual password from your auth service
                       authorities);
    }

    private Set<GrantedAuthority> loadUserAuthorities(String username) {
        Set<GrantedAuthority> authorities = new HashSet<>();
        
        // Get user roles
        String roleQuery = """
            SELECT urd.ROLE_NAME, urd.ROLE_STATUS 
            FROM USER_ROLE_DATA urd
            WHERE urd.ROLE_STATUS = 'ACTIVE'
            AND urd.APPLICATION_ID = ?
            """;

        List<Map<String, Object>> roleResults = jdbcTemplate.queryForList(roleQuery, 
            new Object[]{getApplicationId()});

        for (Map<String, Object> role : roleResults) {
            String roleName = (String) role.get("ROLE_NAME");
            authorities.add(new SimpleGrantedAuthority("ROLE_" + roleName));

            // Get actions for this role
            String actionQuery = """
                SELECT ad.ACTION_CODE, ad.ACTION_TYPE
                FROM ROLE_ACTION_MAPPING ram
                JOIN ACTION_DETAIL ad ON ram.ACTION_DETAIL_ID = ad.ACTION_DETAIL_ID
                WHERE ram.ROLE_ID = ?
                AND ram.MAPPING_STATUS = 'ACTIVE'
                AND ad.ACTION_STATUS = 'ACTIVE'
                """;

            List<Map<String, Object>> actionResults = jdbcTemplate.queryForList(actionQuery,
                new Object[]{role.get("ROLE_ID")});

            for (Map<String, Object> action : actionResults) {
                String actionCode = (String) action.get("ACTION_CODE");
                authorities.add(new SimpleGrantedAuthority(actionCode));
            }
        }

        return authorities;
    }

    private Integer getApplicationId() {
        // Return your application ID
        return 1; // Replace with your actual application ID
    }
}