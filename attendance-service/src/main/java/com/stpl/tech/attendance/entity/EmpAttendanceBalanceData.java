package com.stpl.tech.attendance.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Entity representing employee leave balance data
 * Maps to EMP_ATTENDANCE_BALANCE_DATA table
 */
@Entity
@Table(name = "EMP_ATTENDANCE_BALANCE_DATA")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmpAttendanceBalanceData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "EMP_ATTENDANCE_BALANCE_DATA_ID")
    private Long id;

    @Column(name = "EMP_ID", nullable = false)
    private Integer empId;

    @Column(name = "LEAVE_COUNT", precision = 10, scale = 2)
    private BigDecimal leaveCount;

    @Column(name = "COMP_OFF_COUNT", precision = 10, scale = 2)
    private BigDecimal compOffCount;

    @Column(name = "LWP_COUNT", precision = 10, scale = 2)
    private BigDecimal lwpCount;

    @Column(name = "WEEK_OFF_DAY")
    private String weekOffDay;

    @Column(name = "CREATED_ON")
    private LocalDateTime createdOn;

    @Column(name = "UPDATED_ON")
    private LocalDateTime updatedOn;

    @Column(name = "CREATED_BY")
    private String createdBy;

    @Column(name = "UPDATED_BY")
    private String updatedBy;

    @Version
    @Column(name = "VERSION")
    private Integer version;

    @PrePersist
    protected void onCreate() {
        createdOn = LocalDateTime.now();
        updatedOn = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedOn = LocalDateTime.now();
    }
} 