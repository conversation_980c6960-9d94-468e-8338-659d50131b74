package com.stpl.tech.attendance.service.impl;

import com.stpl.tech.attendance.cache.service.BiometricCache;
import com.stpl.tech.attendance.config.BiometricConfig;
import com.stpl.tech.attendance.config.BiometricServiceEndpoints;
import com.stpl.tech.attendance.dto.BiometricDeregistrationResponse;
import com.stpl.tech.attendance.dto.BiometricFaceActivationRequestDTO;
import com.stpl.tech.attendance.dto.BiometricIdentificationRequestDTO;
import com.stpl.tech.attendance.dto.BiometricIdentificationResponseDTO;
import com.stpl.tech.attendance.dto.BiometricRegistrationDTO;
import com.stpl.tech.attendance.dto.BiometricRegistrationRequestDTO;
import com.stpl.tech.attendance.dto.BiometricRegistrationResponseDTO;
import com.stpl.tech.attendance.dto.BiometricServiceResponse;
import com.stpl.tech.attendance.dto.BiometricTempRegistrationRequestDTO;
import com.stpl.tech.attendance.dto.BiometricTempRegistrationResponseDTO;
import com.stpl.tech.attendance.dto.BiometricUpdateImagePathRequestDTO;
import com.stpl.tech.attendance.dto.BiometricUpdateImagePathResponseDTO;
import com.stpl.tech.attendance.enums.BiometricErrorCode;
import com.stpl.tech.attendance.enums.BiometricStatus;
import com.stpl.tech.attendance.exception.AttendanceValidationException;
import com.stpl.tech.attendance.exception.BiometricRegistrationException;
import com.stpl.tech.attendance.repository.BiometricRegistrationRepository;
import com.stpl.tech.attendance.service.BiometricService;
import com.stpl.tech.attendance.service.HttpService;
import com.stpl.tech.attendance.service.RedisMetricsService;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import com.stpl.tech.attendance.dto.BiometricMultiImageIdentificationRequestDTO;
import com.stpl.tech.attendance.dto.BiometricMultiImageIdentificationResponseDTO;

@Slf4j
@Service
@RequiredArgsConstructor
public class BiometricServiceImpl implements BiometricService {

    private final HttpService httpService;
    private final BiometricConfig biometricConfig;
    private final BiometricServiceEndpoints endpoints;
    private final MeterRegistry meterRegistry;
    private final RedisMetricsService redisMetricsService;
    private final RedisTemplate<String, Object> redisTemplate;
    private final BiometricRegistrationRepository biometricRegistrationRepository;
    private final BiometricCache biometricCache;

    @Value("${biometric.service.mock.enabled:false}")
    private boolean mockEnabled;

    @Value("${biometric.service.api-key:Ch@@yos@2025@}")
    private String apiKey;

    private static final String BIOMETRIC_CACHE_KEY_PREFIX = "biometric:registration:";
    private static final String EMPLOYEE_CACHE_KEY_PREFIX = "employee:";

    @Override
  /*  @CircuitBreaker(name = "biometricService", fallbackMethod = "registerFaceFallback")
    @Retry(name = "biometricService", fallbackMethod = "registerFaceFallback")*/
    public BiometricServiceResponse registerFace(String empId, String unitId, String base64Image) {
        Timer.Sample sample = Timer.start(meterRegistry);
        log.info("Registering face for employee: {} in unit: {}", empId, unitId);
        
        try {
            if (mockEnabled) {
                return createMockRegistrationResponse(empId);
            }

            Map<String, String> headers = createHeaders();
            Map<String, Object> request = createRegistrationRequest(empId, unitId, base64Image);

            BiometricServiceResponse response = httpService.post(
                    endpoints.getRegistrationUrl(),
                    request,
                    BiometricServiceResponse.class,
                    headers
            ).block();

            if (response.isSuccess()) {
                // Invalidate cache after successful registration
                biometricCache.removeBiometricRegistration(empId);
            }

            sample.stop(meterRegistry.timer("biometric.registration.time",
                    "operation", "register",
                    "status", response.isSuccess() ? "success" : "failure"));

            return response;
        } catch (Exception e) {
            sample.stop(meterRegistry.timer("biometric.registration.time",
                    "operation", "register",
                    "status", "error"));
            throw e;
        }
    }

    @Override
   /* @CircuitBreaker(name = "biometricService", fallbackMethod = "registerFaceWithDTOFallback")
    @Retry(name = "biometricService", fallbackMethod = "registerFaceWithDTOFallback")*/
    public BiometricRegistrationResponseDTO registerFace(BiometricRegistrationRequestDTO request) {
        Timer.Sample sample = Timer.start(meterRegistry);
        log.info("Registering face for employee: {} in unit: {}", 
                request.getParameters().getMetadata().getEmployeeId(),
                request.getParameters().getMetadata().getUnitId());
        
        try {
            if (mockEnabled) {
                return createMockRegistrationResponseDTO(request);
            }

            Map<String, String> headers = createHeaders();

            BiometricRegistrationResponseDTO response = httpService.post(
                    endpoints.getRegistrationUrl(),
                    request,
                    BiometricRegistrationResponseDTO.class,
                    headers
            ).block();

            sample.stop(meterRegistry.timer("biometric.registration.time",
                    "operation", "register_dto",
                    "status", "success".equals(response.getStatus()) ? "success" : "failure"));

            return response;
        } catch (Exception e) {
            sample.stop(meterRegistry.timer("biometric.registration.time",
                    "operation", "register_dto",
                    "status", "error"));
            throw e;
        }
    }

    @Override
    /*@CircuitBreaker(name = "biometricService", fallbackMethod = "deregisterFaceFallback")
    @Retry(name = "biometricService", fallbackMethod = "deregisterFaceFallback")*/
    public BiometricDeregistrationResponse deregisterFace(String empId, String unitId , Boolean deregistered) {
        Timer.Sample sample = Timer.start(meterRegistry);
        log.info("Deregistering face with empId: {} from unit: {}", empId, unitId);

        try {
            if (mockEnabled) {
                log.info("Mock deregistration successful for empId: {}", empId);
                return BiometricDeregistrationResponse.builder()
                    .status("success")
                    .updated(1)
                    .redis_removed(1)
                    .build();
            }

            Map<String, String> headers = createHeaders();
            BiometricRegistrationRequestDTO request = BiometricRegistrationRequestDTO.builder()
                    .parameters(BiometricRegistrationRequestDTO.Parameters.builder()
                            .employeeId(Integer.parseInt(empId)).build()).type(Boolean.TRUE.equals(deregistered) ? "REMOVED" : "REJECTED").build();
            BiometricDeregistrationResponse response = httpService.post(
                endpoints.getDeRegisterFaceUrl(),
                    request,
                BiometricDeregistrationResponse.class,
                headers
            ).block();

            sample.stop(meterRegistry.timer("biometric.deregistration.time",
                    "operation", "deregister",
                    "status", "success".equals(response.getStatus()) ? "success" : "failure"));

            return response;
        } catch (Exception e) {
            sample.stop(meterRegistry.timer("biometric.deregistration.time",
                    "operation", "deregister",
                    "status", "error"));
            throw e;
        }
    }

    @Override
    /*@CircuitBreaker(name = "biometricService", fallbackMethod = "getEmployeeIdFromImageFallback")
    @Retry(name = "biometricService", fallbackMethod = "getEmployeeIdFromImageFallback")*/
    public Integer getEmployeeIdFromImage(String base64image, Integer unitId) {
        Timer.Sample sample = Timer.start(meterRegistry);
        try {
            log.info("Looking up employee ID for base64 image in unit: {}", unitId);

            BiometricIdentificationRequestDTO request = createIdentificationRequest(base64image, unitId);
            BiometricIdentificationResponseDTO response = identifyFace(request);

            validateIdentificationResponse(response, unitId);

            Integer employeeId = Integer.parseInt(response.getResult().getEmployeeId());
            log.info("Found employee ID: {} for unit: {}", employeeId, unitId);

            sample.stop(meterRegistry.timer("biometric.identification.time",
                    "operation", "get_employee_id",
                    "status", "success"));

            return employeeId;
        } catch (Exception e) {
            sample.stop(meterRegistry.timer("biometric.identification.time",
                    "operation", "get_employee_id",
                    "status", "error"));
            throw new AttendanceValidationException("Failed to get employee ID from biometric service: " + e.getMessage());
        }
    }

    @Override
    /*@CircuitBreaker(name = "biometricService", fallbackMethod = "identifyFaceFallback")
    @Retry(name = "biometricService", fallbackMethod = "identifyFaceFallback")*/
    public BiometricIdentificationResponseDTO identifyFace(BiometricIdentificationRequestDTO request) {
        Timer.Sample sample = Timer.start(meterRegistry);
        try {
            log.info("Identifying face for unit: {}", request.getParameters().getMetadata().getUnitId());

            if (mockEnabled) {
                return createMockIdentificationResponse();
            }

            Map<String, String> headers = createHeaders();

            BiometricIdentificationResponseDTO response = httpService.post(
                    endpoints.getIdentifyFaceUrl(),
                    request,
                    BiometricIdentificationResponseDTO.class,
                    headers
            ).block();

            sample.stop(meterRegistry.timer("biometric.identification.time",
                    "operation", "identify_face",
                    "status", "success".equals(response.getStatus()) ? "success" : "failure"));

            return response;
        } catch (Exception e) {
            sample.stop(meterRegistry.timer("biometric.identification.time",
                    "operation", "identify_face",
                    "status", "error"));
            throw e;
        }
    }

    // Fallback methods
    private BiometricServiceResponse registerFaceFallback(String empId, String unitId, String base64Image, Exception e) {
        log.error("Fallback: Failed to register face for employee: {} in unit: {}", empId, unitId, e);
        return BiometricServiceResponse.builder()
                .success(false)
                .errorMessage("Service temporarily unavailable. Please try again later.")
                .build();
    }

    private BiometricRegistrationResponseDTO registerFaceWithDTOFallback(BiometricRegistrationRequestDTO request, Exception e) {
        log.error("Fallback: Failed to register face for employee: {} in unit: {}",
                request.getParameters().getMetadata().getEmployeeId(),
                request.getParameters().getMetadata().getUnitId(), e);
        return BiometricRegistrationResponseDTO.builder()
                .status("error")
                .processing_time(0.0)
                .result(null)
                .error("Service temporarily unavailable. Please try again later.")
                .build();
    }

    private BiometricDeregistrationResponse deregisterFaceFallback(String biometricId, String unitId, Exception e) {
        log.error("Fallback: Failed to deregister face with biometricId: {} from unit: {}", biometricId, unitId, e);
        return BiometricDeregistrationResponse.builder()
            .status("error")
            .updated(0)
            .redis_removed(0)
            .build();
    }

    private Integer getEmployeeIdFromImageFallback(String base64image, Integer unitId, Exception e) {
        log.error("Fallback: Failed to get employee ID for unit: {}", unitId, e);
        throw new AttendanceValidationException("Service temporarily unavailable. Please try again later.");
    }

    private BiometricIdentificationResponseDTO identifyFaceFallback(BiometricIdentificationRequestDTO request, Exception e) {
        log.error("Fallback: Failed to identify face for unit: {}",
                request.getParameters().getMetadata().getUnitId(), e);
        throw new AttendanceValidationException("Service temporarily unavailable. Please try again later.");
    }

    // Helper methods
    private Map<String, String> createHeaders() {
        Map<String, String> headers = new HashMap<>();
        headers.put("X-API-KEY", apiKey);
        return headers;
    }

    private Map<String, Object> createRegistrationRequest(String empId, String unitId, String base64Image) {
        Map<String, Object> request = new HashMap<>();
        request.put("empId", empId);
        request.put("unitId", unitId);
        request.put("image", base64Image);
        return request;
    }

    private Map<String, String> createDeregistrationParams(String biometricId, String unitId) {
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("employeeId", biometricId);
        queryParams.put("unitId", unitId);
        return queryParams;
    }

    private BiometricIdentificationRequestDTO createIdentificationRequest(String base64image, Integer unitId) {
        return BiometricIdentificationRequestDTO.builder()
                .model_id("attendance")
                .parameters(BiometricIdentificationRequestDTO.Parameters.builder()
                        .metadata(BiometricIdentificationRequestDTO.Metadata.builder()
                                .unitId(unitId)
                                .build())
                        .build())
                .input_data(BiometricIdentificationRequestDTO.InputData.builder()
                        .data(base64image)
                        .build())
                .processing_options(new HashMap<>())
                .build();
    }

    private void validateIdentificationResponse(BiometricIdentificationResponseDTO response, Integer unitId) {
        if (response == null || response.getStatus() == null || !response.getStatus().equals("success")) {
            log.error("Invalid response from biometric service for unit: {}", unitId);
            throw new AttendanceValidationException("Failed to get employee ID from biometric service");
        }
    }

    private BiometricServiceResponse createMockRegistrationResponse(String empId) {
        return BiometricServiceResponse.builder()
                .success(true)
                .biometricId(UUID.randomUUID().toString())
                .userId(empId)
                .build();
    }

    private BiometricRegistrationResponseDTO createMockRegistrationResponseDTO(BiometricRegistrationRequestDTO request) {
        return BiometricRegistrationResponseDTO.builder()
                .status("success")
                .processing_time(0.3898959159851074)
                .result(BiometricRegistrationResponseDTO.Result.builder()
                        .BiometricId(UUID.randomUUID().toString())
                        .userId(request.getParameters().getMetadata().getEmployeeId().toString())
                        .build())
                .error(null)
                .build();
    }

    private BiometricRegistrationResponseDTO createMockRegistrationResponseDTO(BiometricFaceActivationRequestDTO request) {
        return BiometricRegistrationResponseDTO.builder()
                .status("success")
                .processing_time(0.3898959159851074)
                .result(BiometricRegistrationResponseDTO.Result.builder()
                        .BiometricId(UUID.randomUUID().toString())
                        .userId(request.getEmployeeId().toString())
                        .build())
                .error(null)
                .build();
    }

    private BiometricIdentificationResponseDTO createMockIdentificationResponse() {
        return BiometricIdentificationResponseDTO.builder()
                .status("success")
                .processing_time(0.28275203704833984)
                .result(BiometricIdentificationResponseDTO.Result.builder()
                        .userId("68304c6cab6de461c3ac5df6")
                        .employeeId("120056")
                        .score(0.0)
                        .message("Face recognised successfully")
                        .build())
                .error(null)
                .requestId("12312312")
                .build();
    }

    @Override
    public BiometricRegistrationDTO getBiometricRegistration(String empId) {
        return biometricCache.getBiometricRegistration(empId);
    }

    @Override
    public void handleRegistrationStatusChange(String empId, BiometricStatus status) {
        biometricCache.handleRegistrationStatusChange(empId, status);
    }

    @Override
    public BiometricTempRegistrationResponseDTO verifyTempRegistration(BiometricTempRegistrationRequestDTO request) {
        Timer.Sample sample = Timer.start(meterRegistry);
        try {
            log.info("Verifying temporary registration for employee: {}", 
                    request.getParameters().getMetadata().getEmployeeId());

            if (mockEnabled) {
                return createMockTempRegistrationResponse(request);
            }

            Map<String, String> headers = createHeaders();

            BiometricTempRegistrationResponseDTO response = httpService.post(
                    endpoints.getRegisterTempUrl(),
                    request,
                    BiometricTempRegistrationResponseDTO.class,
                    headers
            ).block();

            sample.stop(meterRegistry.timer("biometric.temp_registration.time",
                    "operation", "verify_temp",
                    "status", "success".equals(response.getStatus()) ? "success" : "failure"));

            return response;
        } catch (Exception e) {
            sample.stop(meterRegistry.timer("biometric.temp_registration.time",
                    "operation", "verify_temp",
                    "status", "error"));
            throw e;
        }
    }

    private BiometricTempRegistrationResponseDTO createMockTempRegistrationResponse(BiometricTempRegistrationRequestDTO request) {
        return BiometricTempRegistrationResponseDTO.builder()
                .status("success")
                .processing_time(31.337493419647217)
                .result(BiometricTempRegistrationResponseDTO.Result.builder()
                        .message("Face verification successful")
                        .employee_id(request.getParameters().getMetadata().getEmployeeId())
                        .session_id(UUID.randomUUID().toString())
                        .confidence(-1.19209289551e-07)
                        .verification_status("success")
                        .temp_registration_removed(true)
                        .matched_existing_face(true)
                        .is_temp_registration(false)
                        .embedding_type("temporary")
                        .build())
                .error(null)
                .build();
    }

    @Override
    public BiometricUpdateImagePathResponseDTO updateImagePath(BiometricUpdateImagePathRequestDTO request) {
        Timer.Sample sample = Timer.start(meterRegistry);
        try {
            log.info("Updating image path for reference ID: {}", request.getRefId());

            if (mockEnabled) {
                return createMockUpdateImagePathResponse(request);
            }

            Map<String, String> headers = createHeaders();

            BiometricUpdateImagePathResponseDTO response = httpService.post(
                    endpoints.getUpdateImgPathUrl(),
                    request,
                    BiometricUpdateImagePathResponseDTO.class,
                    headers
            ).block();

            sample.stop(meterRegistry.timer("biometric.update_image_path.time",
                    "operation", "update_image_path",
                    "status", "success".equals(response.getStatus()) ? "success" : "failure"));

            return response;
        } catch (Exception e) {
            sample.stop(meterRegistry.timer("biometric.update_image_path.time",
                    "operation", "update_image_path",
                    "status", "error"));
            throw e;
        }
    }

    private BiometricUpdateImagePathResponseDTO createMockUpdateImagePathResponse(BiometricUpdateImagePathRequestDTO request) {
        return BiometricUpdateImagePathResponseDTO.builder()
                .status("success")
                .message("Inference event image path updated successfully")
                .refId(request.getRefId())
                .imageUrl(request.getImageUrl())
                .build();
    }

    @Override
    public BiometricRegistrationResponseDTO activateFace(BiometricFaceActivationRequestDTO request) {
        Timer.Sample sample = Timer.start(meterRegistry);
        try {
            log.info("Activating face for employee: {}", request.getEmployeeId());

            if (mockEnabled) {
                return createMockRegistrationResponseDTO(request);
            }

            Map<String, String> headers = createHeaders();
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("employeeId", request.getEmployeeId());

            BiometricRegistrationResponseDTO response = httpService.post(
                    endpoints.getActivateFaceUrl(),
                    requestBody,
                    BiometricRegistrationResponseDTO.class,
                    headers
            ).block();

            sample.stop(meterRegistry.timer("biometric.activation.time",
                    "operation", "activate_face",
                    "status", "success".equals(response.getStatus()) ? "success" : "failure"));

            return response;
        } catch (Exception e) {
            sample.stop(meterRegistry.timer("biometric.activation.time",
                    "operation", "activate_face",
                    "status", "error"));
            log.error("Failed to activate face for employee: {}", request.getEmployeeId(), e);
            throw new BiometricRegistrationException(BiometricErrorCode.REGISTRATION_PROCESSING_ERROR,
                    "Failed to activate face: " + e.getMessage());
        }
    }

    @Override
    /*@CircuitBreaker(name = "biometricService", fallbackMethod = "identifyFaceFromMultipleImagesFallback")
    @Retry(name = "biometricService", fallbackMethod = "identifyFaceFromMultipleImagesFallback")*/
    public BiometricMultiImageIdentificationResponseDTO identifyFaceFromMultipleImages(BiometricMultiImageIdentificationRequestDTO request) {
        Timer.Sample sample = Timer.start(meterRegistry);
        try {
            log.info("Identifying face from multiple images for unit: {}", request.getParameters().getMetadata().getUnitId());

            if (mockEnabled) {
                return createMockMultiImageIdentificationResponse(request);
            }

            Map<String, String> headers = createHeaders();

            BiometricMultiImageIdentificationResponseDTO response = httpService.post(
                    endpoints.getIdentifyMultipleFacesUrl(),
                    request,
                    BiometricMultiImageIdentificationResponseDTO.class,
                    headers
            ).block();

            sample.stop(meterRegistry.timer("biometric.multi_identification.time",
                    "operation", "identify_multiple_faces",
                    "status", "success".equals(response.getStatus()) ? "success" : "failure"));

            return response;
        } catch (Exception e) {
            sample.stop(meterRegistry.timer("biometric.multi_identification.time",
                    "operation", "identify_multiple_faces",
                    "status", "error"));
            throw e;
        }
    }

    @Override
    /*@CircuitBreaker(name = "biometricService", fallbackMethod = "updateImagePathMultipleFallback")
    @Retry(name = "biometricService", fallbackMethod = "updateImagePathMultipleFallback")*/
    public BiometricUpdateImagePathResponseDTO updateImagePathMultiple(BiometricUpdateImagePathRequestDTO request) {
        Timer.Sample sample = Timer.start(meterRegistry);
        try {
            log.info("Updating image paths for {} images", request.getImages() != null ? request.getImages().size() : 0);

            if (mockEnabled) {
                return createMockUpdateImagePathMultipleResponse(request);
            }

            Map<String, String> headers = createHeaders();

            BiometricUpdateImagePathResponseDTO response = httpService.post(
                    endpoints.getUpdateImgPathV2Url(),
                    request,
                    BiometricUpdateImagePathResponseDTO.class,
                    headers
            ).block();

            sample.stop(meterRegistry.timer("biometric.update_image_path_multiple.time",
                    "operation", "update_image_path_multiple",
                    "status", "success".equals(response.getStatus()) ? "success" : "failure"));

            return response;
        } catch (Exception e) {
            sample.stop(meterRegistry.timer("biometric.update_image_path_multiple.time",
                    "operation", "update_image_path_multiple",
                    "status", "error"));
            log.error("Error while syncing multi images for record {}", request.getRefId(), e);
            //throw e;
        }
        return new BiometricUpdateImagePathResponseDTO();
    }

    // Helper methods for new functionality
    private BiometricMultiImageIdentificationResponseDTO createMockMultiImageIdentificationResponse(BiometricMultiImageIdentificationRequestDTO request) {
        // Return the first image as identified for mock
        String identifiedImageId = request.getInput_data().getData().isEmpty() ? "mock_id" : request.getInput_data().getData().get(0).getId();
        
        return BiometricMultiImageIdentificationResponseDTO.builder()
                .status("success")
                .processing_time(0.28275203704833984)
                .result(BiometricMultiImageIdentificationResponseDTO.Result.builder()
                        .userId("68304c6cab6de461c3ac5df6")
                        .employeeId("120056")
                        .score(0.95)
                        .message("Face recognised successfully from multiple images")
                        .status("success")
                        .imageId(identifiedImageId)
                        .build())
                .error(null)
                .requestId("12312312")
                .build();
    }

    private BiometricUpdateImagePathResponseDTO createMockUpdateImagePathMultipleResponse(BiometricUpdateImagePathRequestDTO request) {
        return BiometricUpdateImagePathResponseDTO.builder()
                .status("success")
                .message("Multiple inference event image paths updated successfully")
                .refId(request.getRefId())
                .imageUrl(request.getImageUrl())
                .build();
    }

    // Fallback methods for new functionality
    private BiometricMultiImageIdentificationResponseDTO identifyFaceFromMultipleImagesFallback(BiometricMultiImageIdentificationRequestDTO request, Exception e) {
        log.error("Fallback: Failed to identify face from multiple images for unit: {}",
                request.getParameters().getMetadata().getUnitId(), e);
        throw new AttendanceValidationException("Service temporarily unavailable. Please try again later.");
    }

    private BiometricUpdateImagePathResponseDTO updateImagePathMultipleFallback(BiometricUpdateImagePathRequestDTO request, Exception e) {
        log.error("Fallback: Failed to update image paths for {} images", 
                request.getImages() != null ? request.getImages().size() : 0, e);
        return BiometricUpdateImagePathResponseDTO.builder()
                .status("error")
                .message("Service temporarily unavailable. Please try again later.")
                .refId(request.getRefId())
                .imageUrl(request.getImageUrl())
                .build();
    }
} 