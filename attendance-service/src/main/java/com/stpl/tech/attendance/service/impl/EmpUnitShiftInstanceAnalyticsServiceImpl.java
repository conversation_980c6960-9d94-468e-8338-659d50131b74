package com.stpl.tech.attendance.service.impl;

import com.stpl.tech.attendance.cache.service.UnitCacheService;
import com.stpl.tech.attendance.cache.service.UserCacheService;
import com.stpl.tech.attendance.dto.EmpUnitShiftInstanceAnalyticsDTO;
import com.stpl.tech.attendance.dto.RosteringDto.FilterValueOptionDTO;
import com.stpl.tech.attendance.dto.RosteringDto.GenericFilterMetadataDTO;
import com.stpl.tech.attendance.dto.RosteringDto.GenericFilterRequestDTO;
import com.stpl.tech.attendance.entity.EmployeeShiftInstances;
import com.stpl.tech.attendance.entity.EmployeeShiftUnitAttendance;
import com.stpl.tech.attendance.repository.EmployeeShiftInstancesRepository;
import com.stpl.tech.attendance.repository.EmployeeShiftUnitAttendanceRepository;
import com.stpl.tech.attendance.service.EmpUnitShiftInstanceAnalyticsService;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class EmpUnitShiftInstanceAnalyticsServiceImpl implements EmpUnitShiftInstanceAnalyticsService {

    private final EmployeeShiftInstancesRepository employeeShiftInstancesRepository;
    private final EmployeeShiftUnitAttendanceRepository employeeShiftUnitAttendanceRepository;
    private final UnitCacheService unitCacheService;
    private final UserCacheService userCache;
    private final MetadataServiceImpl metadataService;

    @Override
    @Transactional(readOnly = true)
    public EmpUnitShiftInstanceAnalyticsDTO  getEmpUnitShiftInstanceAnalytics(Integer managerId, LocalDate date, LocalTime startTime, LocalTime endTime, GenericFilterRequestDTO filterRequest) {
        log.info("Getting employee unit shift instance analytics for manager: {} on date: {} from {} to {} with filters: {}", managerId, date, startTime, endTime, filterRequest);

        // 1. Get managed units for the manager
        List<Integer> managedUnitIds = metadataService.getManagedUnitIds(managerId);
        if (managedUnitIds.isEmpty()) {
            log.warn("No managed units found for manager: {}", managerId);
            return createEmptyResponse(managerId, date, startTime, endTime);
        }

        // 2. Apply filters to managed units
        List<Integer> filteredUnitIds = applyFilters(managedUnitIds, filterRequest);
        if (filteredUnitIds.isEmpty()) {
            log.warn("No units match the applied filters for manager: {}", managerId);
            return createEmptyResponse(managerId, date, startTime, endTime);
        }

        // 3. Create datetime objects for the time range
        LocalDateTime startDateTime = LocalDateTime.of(date, startTime);
        LocalDateTime endDateTime = LocalDateTime.of(date, endTime);

        // 4. Get ideal employees (active shift instances) for the specific date and time range
        List<EmployeeShiftInstances> idealShiftInstances = employeeShiftInstancesRepository
                .findActiveShiftInstancesByUnitIdsAndDate(filteredUnitIds, date, startDateTime);

        // 5. Get actual employees (from unit attendance) for the specific date and time range
        List<EmployeeShiftUnitAttendance> actualUnitAttendance = employeeShiftUnitAttendanceRepository
                .findByActualUnitIdsAndBusinessDateAndTimeRange(filteredUnitIds, date, startDateTime, endDateTime);

        // 6. Build response
        return buildAnalyticsResponse(managerId, date, startTime, endTime, idealShiftInstances, actualUnitAttendance);
    }

    @Override
    @Transactional(readOnly = true)
    public GenericFilterMetadataDTO getFilterMetadata(Integer managerId) {
        log.info("Getting filter metadata for manager: {}", managerId);

        try {
            // Get all units where this employee is a manager
            List<Integer> managedUnitIds = metadataService.getManagedUnitIds(managerId);
            
            if (managedUnitIds.isEmpty()) {
                log.info("No managed units found for manager: {}", managerId);
                return GenericFilterMetadataDTO.builder()
                    .availableFilters(new ArrayList<>())
                    .appliedFilters(GenericFilterMetadataDTO.FilterAppliedFlags.builder()
                        .appliedFilters(new HashMap<>())
                        .anyFilterApplied(false)
                        .build())
                    .build();
            }

            // Get unit details for managed units
            List<UnitBasicDetail> managedUnits = managedUnitIds.stream()
                .map(unitCacheService::getUnitBasicDetail)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

            // Build available filters
            List<GenericFilterMetadataDTO.FilterOption> availableFilters = new ArrayList<>();

            // Unit filter
            List<FilterValueOptionDTO> unitValueOptions = managedUnits.stream()
                .map(unit -> FilterValueOptionDTO.builder()
                    .value(String.valueOf(unit.getId()))
                    .displayName(unit.getName())
                    .build())
                .collect(Collectors.toList());

            availableFilters.add(GenericFilterMetadataDTO.FilterOption.builder()
                .filterKey("unitIds")
                .filterName("Units")
                .dataType("INTEGER")
                .valueOptions(unitValueOptions)
                .operator("IN")
                .build());

            // City filter
            List<String> cities = managedUnits.stream()
                .map(UnitBasicDetail::getCity)
                .filter(Objects::nonNull)
                .distinct()
                .sorted()
                .collect(Collectors.toList());

            List<FilterValueOptionDTO> cityValueOptions = cities.stream()
                .map(city -> FilterValueOptionDTO.builder()
                    .value(city)
                    .displayName(city)
                    .build())
                .collect(Collectors.toList());

            availableFilters.add(GenericFilterMetadataDTO.FilterOption.builder()
                .filterKey("cityNames")
                .filterName("Cities")
                .dataType("STRING")
                .valueOptions(cityValueOptions)
                .operator("IN")
                .build());

            log.info("Found {} units and {} cities for manager: {}", unitValueOptions.size(), cityValueOptions.size(), managerId);
            
            return GenericFilterMetadataDTO.builder()
                .availableFilters(availableFilters)
                .appliedFilters(GenericFilterMetadataDTO.FilterAppliedFlags.builder()
                    .appliedFilters(new HashMap<>())
                    .anyFilterApplied(false)
                    .build())
                .build();

        } catch (Exception e) {
            log.error("Error getting filter metadata for manager: {}", managerId, e);
            throw new RuntimeException("Failed to get filter metadata", e);
        }
    }

    @Override
    @Transactional
    public void updateUnitAttendance(Long shiftInstanceId, Integer actualUnitId, LocalDateTime punchTime) {
        log.debug("Updating unit attendance for shift instance: {}, unit: {}, time: {}", shiftInstanceId, actualUnitId, punchTime);

        // Find existing unit attendance record
        Optional<EmployeeShiftUnitAttendance> existingRecord = employeeShiftUnitAttendanceRepository
                .findByShiftInstanceIdAndActualUnitId(shiftInstanceId, actualUnitId);

        EmployeeShiftUnitAttendance unitAttendance;
        if (existingRecord.isEmpty()) {
            // First punch at this unit - create new record
            unitAttendance = EmployeeShiftUnitAttendance.builder()
                    .shiftInstanceId(shiftInstanceId)
                    .actualUnitId(actualUnitId)
                    .actualStartTime(punchTime)
                    .build();
            log.debug("Created new unit attendance record for shift instance: {}, unit: {}", shiftInstanceId, actualUnitId);
        } else {
            // Subsequent punch at this unit - update end time
            unitAttendance = existingRecord.get();
            unitAttendance.setActualEndTime(punchTime);
            log.debug("Updated unit attendance end time for shift instance: {}, unit: {}", shiftInstanceId, actualUnitId);
        }

        employeeShiftUnitAttendanceRepository.save(unitAttendance);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Integer> getManagedUnitIdsWithFilters(Integer managerId,List<Integer> managedUnitIds , GenericFilterRequestDTO filterRequest) {
        log.debug("Getting managed unit IDs with filters for manager: {} with filters: {}", managerId, filterRequest);

        if (managedUnitIds.isEmpty()) {
            log.warn("No managed units found for manager: {}", managerId);
            return new ArrayList<>();
        }

        // 2. Apply filters to managed units
        return applyFilters(managedUnitIds, filterRequest);
    }

    @Override
    @Transactional(readOnly = true)
    public int getIdealEmployeeCount(List<Integer> unitIds, LocalDate date, LocalDateTime currentTime) {
        log.debug("Getting ideal employee count for units: {} on date: {} at time: {}", unitIds, date, currentTime);
        
        List<EmployeeShiftInstances> idealShiftInstances = employeeShiftInstancesRepository
                .findActiveShiftInstancesByUnitIdsAndDate(unitIds, date, currentTime);
        
        return idealShiftInstances.size();
    }

    @Override
    @Transactional(readOnly = true)
    public int getActualEmployeeCount(List<Integer> unitIds, LocalDate date,LocalDateTime currentTime) {
        log.debug("Getting actual employee count for units: {} on date: {}", unitIds, date);
        
        Set<Integer> actualEmployeeIds = getActualEmployeeIds(unitIds, date,currentTime);
        return actualEmployeeIds.size();
    }

    @Override
    @Transactional(readOnly = true)
    public Set<Integer> getActualEmployeeIds(List<Integer> unitIds, LocalDate date,LocalDateTime currentTime) {
        log.debug("Getting actual employee IDs for units: {} on date: {}", unitIds, date);
        
        List<EmployeeShiftUnitAttendance> actualUnitAttendance = employeeShiftUnitAttendanceRepository
                .findByActualUnitIdsAndBusinessDateAndTimeRange(unitIds, date,currentTime);
        
        return actualUnitAttendance.stream()
                .map(attendance -> {
                    Optional<EmployeeShiftInstances> shiftInstance = employeeShiftInstancesRepository
                            .findById(attendance.getShiftInstanceId());
                    return shiftInstance.map(EmployeeShiftInstances::getEmpId).orElse(null);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    @Override
    @Transactional(readOnly = true)
    public Set<Integer> getIdealEmployeeIds(List<Integer> unitIds, LocalDate date, LocalDateTime currentTime) {
        log.debug("Getting ideal employee IDs for units: {} on date: {} at time: {}", unitIds, date, currentTime);
        
        List<EmployeeShiftInstances> idealShiftInstances = employeeShiftInstancesRepository
                .findActiveShiftInstancesByUnitIdsAndDate(unitIds, date, currentTime);
        
        return idealShiftInstances.stream()
                .map(EmployeeShiftInstances::getEmpId)
                .collect(Collectors.toSet());
    }

    private List<Integer> applyFilters(List<Integer> managedUnitIds, GenericFilterRequestDTO filterRequest) {
        if (filterRequest == null || filterRequest.getFilters() == null || filterRequest.getFilters().isEmpty()) {
            return managedUnitIds;
        }

        List<Integer> filteredUnitIds = new ArrayList<>(managedUnitIds);

        // Apply unit filter
        if (filterRequest.getFilters().containsKey("unitIds")) {
            GenericFilterRequestDTO.FilterValue unitFilter = filterRequest.getFilters().get("unitIds");
            if (unitFilter != null && unitFilter.getValues() != null && !unitFilter.getValues().isEmpty()) {
                List<Integer> selectedUnits = unitFilter.getValues().stream()
                        .map(Integer::parseInt)
                        .collect(Collectors.toList());
                filteredUnitIds = filteredUnitIds.stream()
                        .filter(selectedUnits::contains)
                        .collect(Collectors.toList());
            }
        }

        // Apply city filter
        if (filterRequest.getFilters().containsKey("cityNames")) {
            GenericFilterRequestDTO.FilterValue cityFilter = filterRequest.getFilters().get("cityNames");
            if (cityFilter != null && cityFilter.getValues() != null && !cityFilter.getValues().isEmpty()) {
                List<String> selectedCities = cityFilter.getValues();
                List<Integer> cityFilteredUnits = filteredUnitIds.stream()
                        .map(unitCacheService::getUnitBasicDetail)
                        .filter(Objects::nonNull)
                        .filter(unit -> selectedCities.contains(unit.getCity()))
                        .map(UnitBasicDetail::getId)
                        .collect(Collectors.toList());
                filteredUnitIds = filteredUnitIds.stream()
                        .filter(cityFilteredUnits::contains)
                        .collect(Collectors.toList());
            }
        }

        return filteredUnitIds;
    }

    private EmpUnitShiftInstanceAnalyticsDTO createEmptyResponse(Integer managerId, LocalDate date, LocalTime startTime, LocalTime endTime) {
        return EmpUnitShiftInstanceAnalyticsDTO.builder()
                .managerId(managerId)
                .dateAndTimeRange(EmpUnitShiftInstanceAnalyticsDTO.DateAndTimeRange.builder()
                        .date(date)
                        .startTime(startTime)
                        .endTime(endTime)
                        .build())
                .summary(EmpUnitShiftInstanceAnalyticsDTO.AttendanceSummary.builder()
                        .totalIdealEmployees(0L)
                        .totalActualEmployees(0L)
                        .attendanceRate(0.0)
                        .build())
                .idealEmployees(new ArrayList<>())
                .actualEmployees(new ArrayList<>())
                .absentEmployees(new ArrayList<>())
                .build();
    }

    private EmpUnitShiftInstanceAnalyticsDTO buildAnalyticsResponse(Integer managerId, LocalDate date, LocalTime startTime, LocalTime endTime,
                                                           List<EmployeeShiftInstances> idealShiftInstances,
                                                           List<EmployeeShiftUnitAttendance> actualUnitAttendance) {

        // Get unique employee IDs for counting
        Set<Integer> idealEmpIds = idealShiftInstances.stream()
                .map(EmployeeShiftInstances::getEmpId)
                .collect(Collectors.toSet());

        Set<Integer> actualEmpIds = actualUnitAttendance.stream()
                .map(actual -> {
                    Optional<EmployeeShiftInstances> shiftInstance = employeeShiftInstancesRepository
                            .findById(actual.getShiftInstanceId());
                    return shiftInstance.map(EmployeeShiftInstances::getEmpId).orElse(null);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // Calculate attendance rate
        double attendanceRate = idealEmpIds.isEmpty() ? 0.0 : 
                (double) actualEmpIds.size() / idealEmpIds.size() * 100;

        // Build summary
        EmpUnitShiftInstanceAnalyticsDTO.AttendanceSummary summary = EmpUnitShiftInstanceAnalyticsDTO.AttendanceSummary.builder()
                .totalIdealEmployees((long) idealEmpIds.size())
                .totalActualEmployees((long) actualEmpIds.size())
                .attendanceRate(attendanceRate)
                .build();

        // Build employee lists
        List<EmpUnitShiftInstanceAnalyticsDTO.EmployeeDetail> idealEmployeeDetails = buildIdealEmployeeDetails(idealShiftInstances);
        List<EmpUnitShiftInstanceAnalyticsDTO.EmployeeDetail> actualEmployeeDetails = buildActualEmployeeDetails(actualUnitAttendance);
        List<EmpUnitShiftInstanceAnalyticsDTO.EmployeeDetail> absentEmployeeDetails = buildAbsentEmployeeDetails(idealShiftInstances, actualEmpIds);

        return EmpUnitShiftInstanceAnalyticsDTO.builder()
                .managerId(managerId)
                .dateAndTimeRange(EmpUnitShiftInstanceAnalyticsDTO.DateAndTimeRange.builder()
                        .date(date)
                        .startTime(startTime)
                        .endTime(endTime)
                        .build())
                .summary(summary)
                .idealEmployees(idealEmployeeDetails)
                .actualEmployees(actualEmployeeDetails)
                .absentEmployees(absentEmployeeDetails)
                .build();
    }

    private List<EmpUnitShiftInstanceAnalyticsDTO.EmployeeDetail> buildIdealEmployeeDetails(List<EmployeeShiftInstances> shiftInstances) {
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss");
        
        return shiftInstances.stream()
                .map(shiftInstance -> {
                    EmployeeBasicDetail employee = userCache.getUserById(shiftInstance.getEmpId());
                    UnitBasicDetail unit = unitCacheService.getUnitBasicDetail(shiftInstance.getUnitId());
                    if (employee == null || unit == null) {
                        return null;
                    }

                    return EmpUnitShiftInstanceAnalyticsDTO.EmployeeDetail.builder()
                            .empId(shiftInstance.getEmpId())
                            .empName(employee.getName())
                            .empCode(employee.getEmployeeCode())
                            .unitId(shiftInstance.getUnitId())
                            .unitName(unit.getName())
                            .designation(employee.getDesignation())
                            .imageUrl(employee.getImagekey())
                            .expectedStartTime(shiftInstance.getExpectedStartTime().format(timeFormatter))
                            .expectedEndTime(shiftInstance.getExpectedEndTime().format(timeFormatter))
                            .actualStartTime(shiftInstance.getActualStartTime() != null ? 
                                    shiftInstance.getActualStartTime().format(timeFormatter) : null)
                            .actualEndTime(shiftInstance.getActualEndTime() != null ? 
                                    shiftInstance.getActualEndTime().format(timeFormatter) : null)
                            .build();
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private List<EmpUnitShiftInstanceAnalyticsDTO.EmployeeDetail> buildActualEmployeeDetails(List<EmployeeShiftUnitAttendance> unitAttendance) {
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss");
        
        return unitAttendance.stream()
                .map(attendance -> {
                    // Get shift instance to get employee details
                    Optional<EmployeeShiftInstances> shiftInstance = employeeShiftInstancesRepository
                            .findById(attendance.getShiftInstanceId());
                    if (shiftInstance.isEmpty()) {
                        return null;
                    }

                    EmployeeBasicDetail employee = userCache.getUserById(shiftInstance.get().getEmpId());
                    UnitBasicDetail expectedUnit = unitCacheService.getUnitBasicDetail(shiftInstance.get().getUnitId());
                    UnitBasicDetail actualUnit = unitCacheService.getUnitBasicDetail(attendance.getActualUnitId());
                    if (employee == null || expectedUnit == null || actualUnit == null) {
                        return null;
                    }

                    return EmpUnitShiftInstanceAnalyticsDTO.EmployeeDetail.builder()
                            .empId(shiftInstance.get().getEmpId())
                            .empName(employee.getName())
                            .empCode(employee.getEmployeeCode())
                            .unitId(shiftInstance.get().getUnitId())
                            .unitName(expectedUnit.getName())
                            .designation(employee.getDesignation())
                            .imageUrl(employee.getImagekey())
                            .expectedStartTime(shiftInstance.get().getExpectedStartTime().format(timeFormatter))
                            .expectedEndTime(shiftInstance.get().getExpectedEndTime().format(timeFormatter))
                            .actualStartTime(attendance.getActualStartTime() != null ? 
                                    attendance.getActualStartTime().format(timeFormatter) : null)
                            .actualEndTime(attendance.getActualEndTime() != null ? 
                                    attendance.getActualEndTime().format(timeFormatter) : null)
                            .actualUnitId(attendance.getActualUnitId())
                            .actualUnitName(actualUnit.getName())
                            .build();
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private List<EmpUnitShiftInstanceAnalyticsDTO.EmployeeDetail> buildAbsentEmployeeDetails(
            List<EmployeeShiftInstances> idealEmployees, 
            Set<Integer> actualEmpIds) {
        
        // Find employees who were scheduled but didn't attend
        return idealEmployees.stream()
                .filter(ideal -> !actualEmpIds.contains(ideal.getEmpId()))
                .map(shiftInstance -> {
                    EmployeeBasicDetail employee = userCache.getUserById(shiftInstance.getEmpId());
                    UnitBasicDetail unit = unitCacheService.getUnitBasicDetail(shiftInstance.getUnitId());
                    if (employee == null || unit == null) {
                        return null;
                    }

                    DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss");
                    return EmpUnitShiftInstanceAnalyticsDTO.EmployeeDetail.builder()
                            .empId(shiftInstance.getEmpId())
                            .empName(employee.getName())
                            .empCode(employee.getEmployeeCode())
                            .unitId(shiftInstance.getUnitId())
                            .unitName(unit.getName())
                            .expectedStartTime(shiftInstance.getExpectedStartTime().format(timeFormatter))
                            .expectedEndTime(shiftInstance.getExpectedEndTime().format(timeFormatter))
                            .build();
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
} 