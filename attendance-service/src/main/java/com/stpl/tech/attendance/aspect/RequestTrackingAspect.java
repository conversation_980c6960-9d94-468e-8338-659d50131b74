package com.stpl.tech.attendance.aspect;

import com.stpl.tech.attendance.annotation.TrackRequest;
import com.stpl.tech.attendance.context.JwtContext;
import com.stpl.tech.attendance.context.RequestTrackingContext;
import com.stpl.tech.attendance.interceptor.RequestIdInterceptor;
import com.stpl.tech.attendance.service.RequestTrackingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;
import java.lang.reflect.Method;

/**
 * AOP Aspect to automatically handle @TrackRequest annotation
 * 
 * This aspect:
 * 1. Intercepts methods annotated with @TrackRequest
 * 2. Starts tracking when method begins
 * 3. Marks success when method completes successfully
 * 4. Marks failure when method throws an exception
 * 5. Does everything asynchronously to avoid performance impact
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class RequestTrackingAspect {

    private final RequestTrackingService requestTrackingService;

    /**
     * Around advice for methods annotated with @TrackRequest
     * Automatically tracks request lifecycle without manual intervention
     */
    @Around("@annotation(trackRequest)")
    public Object trackRequest(ProceedingJoinPoint joinPoint, TrackRequest trackRequest) throws Throwable {
        
        // Get request ID and unit ID from context
        String requestId = RequestIdInterceptor.getCurrentRequestId();
        Integer unitId = JwtContext.getInstance().getUnitId();
        
        // Skip tracking if we don't have required context
        if (requestId == null || unitId == null) {
            log.debug("Skipping request tracking - missing requestId: {} or unitId: {}", requestId, unitId);
            return joinPoint.proceed();
        }
        
        // Extract API identifier from the method
        String apiIdentifier = extractApiIdentifier(joinPoint, trackRequest);
        
        // Start tracking asynchronously
        if (trackRequest.trackSuccess() || trackRequest.trackFailure()) {
            requestTrackingService.startTrackingAsync(requestId, unitId, apiIdentifier);
        }
        
        try {
            // Execute the actual method
            Object result = joinPoint.proceed();
            
            // Mark as successful asynchronously
            if (trackRequest.trackSuccess()) {
                // Check if reference ID is set in context
                String referenceId = RequestTrackingContext.getReferenceId();
                if (referenceId != null) {
                    // Update with both completion time and reference ID
                    requestTrackingService.completeWithReferenceIdWithLock(requestId, referenceId);
                    // Clear the reference ID after use
                    RequestTrackingContext.clearReferenceId();
                } else {
                    // Just mark as successful (no reference ID)
                    requestTrackingService.markSuccessAsync(requestId);
                }
            }
            
            return result;
            
        } catch (Exception e) {
            // Mark as failed asynchronously
            if (trackRequest.trackFailure()) {
                String errorMessage = e.getMessage() != null ? e.getMessage() : "Unknown error";
                String errorCode = e.getClass().getSimpleName();
                requestTrackingService.markFailedAsync(requestId, errorMessage, errorCode);
            }
            
            // Re-throw the exception to maintain normal flow
            throw e;
        }
    }
    
    /**
     * Extract API path from the method being called
     * Tries to get path from annotations, falls back to method name
     */
    private String extractApiIdentifier(ProceedingJoinPoint joinPoint, TrackRequest trackRequest) {
        try {
            // Try to get path from method annotations
            Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
            
            // Check for @PostMapping, @GetMapping, @PutMapping, @DeleteMapping
            PostMapping postMapping = method.getAnnotation(PostMapping.class);
            GetMapping getMapping = method.getAnnotation(GetMapping.class);
            PutMapping putMapping = method.getAnnotation(PutMapping.class);
            DeleteMapping deleteMapping = method.getAnnotation(DeleteMapping.class);
            
            String path = "";
            
            if (postMapping != null) {
                path = postMapping.value().length > 0 ? postMapping.value()[0] : "";
            } else if (getMapping != null) {
                path = getMapping.value().length > 0 ? getMapping.value()[0] : "";
            } else if (putMapping != null) {
                path = putMapping.value().length > 0 ? putMapping.value()[0] : "";
            } else if (deleteMapping != null) {
                path = deleteMapping.value().length > 0 ? deleteMapping.value()[0] : "";
            }
            
            // Get class-level @RequestMapping if available
            RequestMapping classMapping = method.getDeclaringClass().getAnnotation(RequestMapping.class);
            String classPath = "";
            if (classMapping != null && classMapping.value().length > 0) {
                classPath = classMapping.value()[0];
            }
            
            // Combine class path and method path
            String fullPath = (classPath + path).replaceAll("//+", "/");
            
            if (!fullPath.isEmpty()) {
                return fullPath;
            }
            
        } catch (Exception e) {
            log.debug("Could not extract API path, using method name: {}", e.getMessage());
        }
        
        // Fallback: use method name and description
        String methodName = joinPoint.getSignature().getName();
        String description = trackRequest.description();
        
        if (!description.isEmpty()) {
            return methodName + " (" + description + ")";
        }
        
        return methodName;
    }
}
