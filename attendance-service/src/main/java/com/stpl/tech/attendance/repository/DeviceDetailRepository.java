package com.stpl.tech.attendance.repository;

import com.stpl.tech.attendance.model.DeviceDetail;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface DeviceDetailRepository extends MongoRepository<DeviceDetail, String> {
    Optional<DeviceDetail> findByDeviceId(String deviceId);
} 