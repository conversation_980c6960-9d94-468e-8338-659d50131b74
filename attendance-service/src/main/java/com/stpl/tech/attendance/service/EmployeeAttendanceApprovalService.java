package com.stpl.tech.attendance.service;

import com.stpl.tech.attendance.approval.entity.ApprovalRequest;
import com.stpl.tech.attendance.model.response.EmployeeAttendanceApproval;
import com.stpl.tech.attendance.model.ApproverInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import com.stpl.tech.attendance.entity.EmployeeAttendanceRequest;

/**
 * Service interface for Employee Attendance Approval operations
 */
public interface EmployeeAttendanceApprovalService {
    
    /**
     * Creates EmployeeAttendanceApproval objects from ApprovalRequest
     * @param request The approval request
     * @return List of EmployeeAttendanceApproval objects with attendance details
     */
    List<EmployeeAttendanceApproval> createEmployeeAttendanceApprovals(ApprovalRequest request);
    
    /**
     * Determines the request type based on attendance type
     * @param attendanceType The attendance type from the database
     * @return The request type string
     */
    String determineRequestType(String attendanceType);
    
    /**
     * Get attendance history for the authenticated employee with pagination
     * @param pageable Pagination information
     * @return Page of EmployeeAttendanceRequest containing attendance records
     */
    Page<EmployeeAttendanceRequest> getAttendanceHistory(Pageable pageable);
    
    /**
     * Construct metadata for different request types
     * @param request The attendance request
     * @return Constructed metadata string
     */
    String constructMetadata(EmployeeAttendanceRequest request);

    List<ApproverInfo> getApproversForAttendanceRequest(String unitId, Integer empId, String approvalType);
}