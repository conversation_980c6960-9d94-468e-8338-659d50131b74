package com.stpl.tech.attendance.repository;

import com.stpl.tech.attendance.entity.EmpShiftOverride;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface EmpShiftOverrideRepository extends JpaRepository<EmpShiftOverride, Long> {
    
    /**
     * Find active overrides for a specific employee
     */
    List<EmpShiftOverride> findByEmpIdAndStatus(Integer empId, String status);
    
    /**
     * Find active overrides for a specific employee within a date range
     */
    @Query("SELECT e FROM EmpShiftOverride e WHERE e.empId = :empId AND e.status = :status " +
           "AND ((e.startDate <= :endDate AND e.endDate >= :startDate) OR " +
           "(e.startDate >= :startDate AND e.startDate <= :endDate) OR " +
           "(e.endDate >= :startDate AND e.endDate <= :endDate))")
    List<EmpShiftOverride> findOverlappingOverrides(@Param("empId") Integer empId, 
                                                   @Param("status") String status,
                                                   @Param("startDate") LocalDate startDate, 
                                                   @Param("endDate") LocalDate endDate);
    
    /**
     * Check if an override exists for the given employee and date range
     */
    boolean existsByEmpIdAndStartDateLessThanEqualAndEndDateGreaterThanEqualAndStatus(
            Integer empId, LocalDate startDate, LocalDate endDate, String status);

    EmpShiftOverride findByEmpIdAndStartDateLessThanEqualAndEndDateGreaterThanEqualAndStatus(Integer empId, LocalDate businessDate, LocalDate businessDate1, String active);
} 