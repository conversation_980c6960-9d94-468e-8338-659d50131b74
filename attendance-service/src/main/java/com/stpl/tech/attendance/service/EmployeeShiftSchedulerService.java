package com.stpl.tech.attendance.service;

import com.stpl.tech.attendance.dto.EmployeeShiftInstanceDetailDTO;
import com.stpl.tech.attendance.dto.EmployeeShiftInstancesDTO;
import com.stpl.tech.attendance.dto.EmployeeShiftInstancesDateResponseDTO;
import com.stpl.tech.attendance.dto.EmployeeShiftInstancesMonthAggregatedResponseDTO;
import com.stpl.tech.attendance.dto.EmployeeShiftInstancesMonthResponseDTO;
import com.stpl.tech.attendance.dto.EmployeeShiftInstancesMultiEmployeeResponseDTO;
import com.stpl.tech.attendance.dto.EmployeeShiftInstancesRangeResponseDTO;
import com.stpl.tech.attendance.dto.EmployeeShiftInstancesWeekResponseDTO;
import com.stpl.tech.attendance.dto.ShiftInstanceRecreationRequestDTO;
import com.stpl.tech.attendance.dto.ShiftInstanceRecreationResponseDTO;

import java.time.LocalDate;
import java.util.List;

public interface EmployeeShiftSchedulerService {
    
    /**
     * Create weekly shift schedules (called by cron job)
     */
    void createWeeklyShiftSchedules();
    
    /**
     * Manually recreate shift instances for specific date range and employees
     */
    ShiftInstanceRecreationResponseDTO recreateShiftInstances(ShiftInstanceRecreationRequestDTO request);
    
    /**
     * Get shift instances for an employee within a date range
     */
    EmployeeShiftInstancesRangeResponseDTO getShiftInstancesForEmployee(Integer empId, LocalDate startDate, LocalDate endDate);
    
    /**
     * Get shift instances for multiple employees within a date range
     */
    EmployeeShiftInstancesMultiEmployeeResponseDTO getShiftInstancesForEmployees(List<Integer> empIds, LocalDate startDate, LocalDate endDate);
    
    /**
     * Get shift instances for a specific date
     */
    EmployeeShiftInstancesDateResponseDTO getShiftInstancesForDate(LocalDate businessDate);
    
    /**
     * Get current week shift instances
     */
    EmployeeShiftInstancesWeekResponseDTO getCurrentWeekShiftInstances(Integer empId);
    
    /**
     * Get next week shift instances
     */
    EmployeeShiftInstancesWeekResponseDTO getNextWeekShiftInstances(Integer empId);
    
    /**
     * Get employee shift instances by month with detailed attendance information
     */
    EmployeeShiftInstancesMonthResponseDTO getEmployeeShiftInstancesByMonth(Integer empId, int year, int month);
    
    /**
     * Get aggregated employee shift instances by month with summary statistics
     */
    EmployeeShiftInstancesMonthAggregatedResponseDTO getEmployeeShiftInstancesAggregatedByMonth(Integer empId, int year, int month);
} 