package com.stpl.tech.attendance.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BiometricMultiImageIdentificationResponseDTO {
    private String status;
    private Double processing_time;
    private Result result;
    private String error;
    private String requestId;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private String userId;
        private String employeeId;
        private Double score;
        private String message;
        private String status;
        private String imageId;  // ID of the identified image
    }
} 