package com.stpl.tech.attendance.dto;

import com.google.firebase.database.annotations.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApplyWeekOffRequest {
    
    /**
     * Date for the week off application
     */
    @NotNull
    private LocalDateTime date;

    @NotNull
    private Integer empId;

    @NotNull
    private boolean forUpcomingWeeks ;
}
