package com.stpl.tech.attendance.security;

import com.stpl.tech.attendance.config.ConfigClientProperties;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

@Slf4j
@Component
@RequiredArgsConstructor
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private final ConfigClientProperties configClientProperties;
    private final CustomUserDetailsService userDetailsService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, Filter<PERSON>hai<PERSON> filterChain)
            throws ServletException, IOException {
        
        String authHeader = request.getHeader("Authorization");
        
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            filterChain.doFilter(request, response);
            return;
        }
        filterChain.doFilter(request, response);

//        try {
//            String jwt = authHeader.substring(7);
//            SecretKey key = Keys.hmacShaKeyFor(configClientProperties.getSecurity().getJwt().getSecret().getBytes(StandardCharsets.UTF_8));
//
//            Claims claims = Jwts.parser()
//                    .setSigningKey(key)
//                    .parseClaimsJws(jwt)
//                    .getBody();
//
//            String username = claims.getSubject();
//            @SuppressWarnings("unchecked")
//            List<String> authorities = claims.get("authorities", List.class);
//
//            UsernamePasswordAuthenticationToken auth = new UsernamePasswordAuthenticationToken(
//                username,
//                null,
//                authorities.stream()
//                    .map(SimpleGrantedAuthority::new)
//                    .collect(Collectors.toList())
//            );
//
//            SecurityContextHolder.getContext().setAuthentication(auth);
//
//        } catch (Exception e) {
//            log.error("Cannot set user authentication: {}", e);
//        }
//
//        filterChain.doFilter(request, response);
    }
} 