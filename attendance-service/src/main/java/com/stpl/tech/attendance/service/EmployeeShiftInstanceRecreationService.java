package com.stpl.tech.attendance.service;

import java.time.LocalDate;

/**
 * Service interface for recreating employee shift instances
 */
public interface EmployeeShiftInstanceRecreationService {

    /**
     * Recreate shift instances for an employee from effective date onwards
     * @param empId Employee ID
     * @param effectiveDate Effective date from which to recreate instances
     * @param updatedBy User who made the change
     */
    void recreateShiftInstancesFromEffectiveDate(Integer empId, LocalDate effectiveDate, String updatedBy);

    /**
     * Recreate shift instances for an employee within a date range
     * @param empId Employee ID
     * @param startDate Start date (inclusive)
     * @param endDate End date (inclusive)
     * @param updatedBy User who made the change
     */
    void recreateShiftInstancesInDateRange(Integer empId, LocalDate startDate, LocalDate endDate, String updatedBy);

    /**
     * Mark shift instances as inactive for an employee from effective date onwards
     * @param empId Employee ID
     * @param effectiveDate Effective date from which to mark instances inactive
     */
    void markShiftInstancesInactiveFromEffectiveDate(Integer empId, LocalDate effectiveDate);

    /**
     * Check if shift instance can be recreated (no actual values set)
     * @param empId Employee ID
     * @param businessDate Business date
     * @return true if instance can be recreated, false otherwise
     */
    boolean canRecreateShiftInstance(Integer empId, LocalDate businessDate);

    void invalidateCacheForDateRange(Integer empId, LocalDate startDate, LocalDate endDate);
} 