package com.stpl.tech.attendance.cache.service;

import com.stpl.tech.attendance.entity.ApplicationData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

/**
 * Cache service for ApplicationData
 * Provides caching for application configuration data to improve performance
 * and reduce database calls for frequently accessed application settings
 */
@Service
@Slf4j
public class ApplicationDataCacheService {

    private static final String CACHE_NAME = "applicationData";

    /**
     * Get cached application data by application ID
     * @param applicationId The application ID to fetch
     * @return Cached ApplicationData or null if not found
     */
    @Cacheable(value = CACHE_NAME, key = "#applicationId")
    public ApplicationData getCachedApplicationData(Integer applicationId) {
        log.debug("Cache miss for application data - Application ID: {}", applicationId);
        return null;
    }

    /**
     * Cache application data for a specific application ID
     * @param applicationId The application ID
     * @param applicationData The ApplicationData to cache
     * @return Cached ApplicationData
     */
    @CachePut(value = CACHE_NAME, key = "#applicationId")
    public ApplicationData cacheApplicationData(Integer applicationId, ApplicationData applicationData) {
        log.debug("Caching application data for Application ID: {}", applicationId);
        return applicationData;
    }

    /**
     * Evict cache for a specific application ID
     * @param applicationId The application ID to evict from cache
     */
    @CacheEvict(value = CACHE_NAME, key = "#applicationId")
    public void evictApplicationData(Integer applicationId) {
        log.debug("Evicting cache for Application ID: {}", applicationId);
    }

    /**
     * Evict all application data cache entries
     */
    @CacheEvict(value = CACHE_NAME, allEntries = true)
    public void evictAllApplicationData() {
        log.debug("Evicting all application data cache");
    }

    /**
     * Update cache for application data
     * @param applicationId The application ID
     * @param applicationData The updated ApplicationData to cache
     */
    public void updateApplicationDataCache(Integer applicationId, ApplicationData applicationData) {
        log.info("Updating cache for application ID: {}", applicationId);
        cacheApplicationData(applicationId, applicationData);
    }

    /**
     * Check if application data exists in cache
     * @param applicationId The application ID to check
     * @return true if exists in cache, false otherwise
     */
    public boolean isApplicationDataCached(Integer applicationId) {
        ApplicationData cached = getCachedApplicationData(applicationId);
        return cached != null;
    }

    /**
     * Get cache statistics (useful for monitoring and debugging)
     * @return Cache statistics information
     */
    public String getCacheStatistics() {
        log.debug("Getting cache statistics for applicationData cache");
        return "ApplicationData cache is active with TTL of 24 hours";
    }
}
