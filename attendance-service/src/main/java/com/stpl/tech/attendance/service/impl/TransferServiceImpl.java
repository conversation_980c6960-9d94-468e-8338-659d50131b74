package com.stpl.tech.attendance.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stpl.tech.attendance.approval.entity.ApprovalRequest;
import com.stpl.tech.attendance.approval.service.ApprovalEngineService;
import com.stpl.tech.attendance.cache.TransferCacheService;
import com.stpl.tech.attendance.cache.service.UnitCacheService;
import com.stpl.tech.attendance.cache.service.UserCacheService;
import com.stpl.tech.attendance.context.JwtContext;
import com.stpl.tech.attendance.dto.EmployeeTransferMetadataDTO;
import com.stpl.tech.attendance.dto.RosteringDto.EmpShiftUpdateRequestDTO;
import com.stpl.tech.attendance.dto.TransferCancellationDTO;
import com.stpl.tech.attendance.dto.TransferReasonDTO;
import com.stpl.tech.attendance.dto.TransferRequestDTO;
import com.stpl.tech.attendance.dto.UnitEligibilityDTO;
import com.stpl.tech.attendance.entity.EmpEligibilityMapping;
import com.stpl.tech.attendance.entity.RosteringEntity.RosteringConstants;
import com.stpl.tech.attendance.entity.RosteringEntity.Shift;
import com.stpl.tech.attendance.enums.ApprovalStatus;
import com.stpl.tech.attendance.enums.ApprovalType;
import com.stpl.tech.attendance.enums.EligibilityType;
import com.stpl.tech.attendance.enums.MappingStatus;
import com.stpl.tech.attendance.enums.MappingType;
import com.stpl.tech.attendance.enums.TransferReason;
import com.stpl.tech.attendance.event.TransferEventPublisher;
import com.stpl.tech.attendance.exception.TransferErrorCode;
import com.stpl.tech.attendance.exception.TransferException;
import com.stpl.tech.attendance.model.ApproverInfo;
import com.stpl.tech.attendance.model.TransferHistory;
import com.stpl.tech.attendance.model.TransferRequest;
import com.stpl.tech.attendance.model.TransferRequestStatus;
import com.stpl.tech.attendance.model.TransferStatistics;
import com.stpl.tech.attendance.model.TransferType;
import com.stpl.tech.attendance.repository.EmpEligibilityMappingRepository;
import com.stpl.tech.attendance.repository.RosteringRepository.ShiftCafeMappingRepository;
import com.stpl.tech.attendance.repository.RosteringRepository.ShiftRepository;
import com.stpl.tech.attendance.repository.TransferHistoryRepository;
import com.stpl.tech.attendance.repository.TransferRequestRepository;
import com.stpl.tech.attendance.repository.TransferStatisticsRepository;
import com.stpl.tech.attendance.service.EmpEligibilityService;
import com.stpl.tech.attendance.service.RosteringService.ReladomoService;
import com.stpl.tech.attendance.service.RosteringService.RosteringService;
import com.stpl.tech.attendance.service.TransferService;
import com.stpl.tech.attendance.util.AttendanceDateUtil;
import com.stpl.tech.attendance.util.DateTimeUtil;
import com.stpl.tech.attendance.validation.TransferValidator;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.util.AppConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class TransferServiceImpl implements TransferService {
    private final TransferRequestRepository transferRequestRepository;

    private final TransferHistoryRepository transferHistoryRepository;
    private final TransferStatisticsRepository transferStatisticsRepository;

    private final TransferValidator validator;
    private final TransferEventPublisher eventPublisher;
    private final TransferCacheService cacheService;
    private final ApprovalEngineService approvalEngineService;
    private final EmpEligibilityService empEligibilityService;
    private final ObjectMapper objectMapper;
    private final UnitCacheService unitCacheService;
    private final UserCacheService userCacheService;
    private final EmpEligibilityMappingRepository empEligibilityMappingRepository;
    private final ShiftRepository shiftRepository;
    private final ShiftCafeMappingRepository shiftCafeMappingRepository;
    private final AttendanceDateUtil attendanceDateUtil;
    @Autowired
    @Lazy
    private  RosteringService rosteringService;
    private final ReladomoService reladomoService;

    @Override
    @Transactional
    public TransferRequest createTransfer(TransferRequestDTO dto) {
        log.info("Creating transfer request for employee: {}", dto.getEmpId());
        
        // Validate request
        validator.validate(dto);
        String initiaterId  =JwtContext.getInstance().getUserId().toString();
        // Create transfer request
        TransferRequest request = TransferRequest.builder()
                .empId(dto.getEmpId())
                .sourceUnitId(dto.getSourceUnitId())
                .destinationUnitId(dto.getDestinationUnitId())
                .transferType(dto.getTransferType())
                .startDate(dto.getStartDate())
                .endDate(dto.getEndDate())
                .reason(dto.getReason())
                .comment(dto.getComment())
                .initiatorId(initiaterId)
                .status(TransferRequestStatus.PENDING)
                .transferRequestStatus("ACTIVE")
                .createdDate(LocalDateTime.now())
                .createdBy(initiaterId)
                .updatedDate(LocalDateTime.now())
                .updatedBy(initiaterId)
                .build();

        // Save transfer request
        request = transferRequestRepository.save(request);
        
        // Create approval request
        ApprovalRequest approvalRequest = createApprovalRequest(request);
        request.setApprovalRequestId(approvalRequest.getId());
        request = transferRequestRepository.save(request);
        
        // Cache the request
        cacheService.cacheTransferRequest(request);
        
        // Publish event
        eventPublisher.notifyTransferCreated(request);
        
        return request;
    }

    @Override
    public TransferRequest getTransferRequest(String transferId) {
        // Try to get from cache first
        TransferRequest request = cacheService.getCachedTransferRequest(transferId);
        if (request != null) {
            return request;
        }
        
        // If not in cache, get from database
        request = transferRequestRepository.findById(transferId)
                .orElseThrow(() -> new TransferException(TransferErrorCode.TRANSFER_NOT_FOUND));
        
        // Cache the request
        cacheService.cacheTransferRequest(request);
        
        return request;
    }

    @Override
    @Transactional
    public TransferRequest saveTransfer(TransferRequest request) {
        request.setUpdatedDate(LocalDateTime.now());
        return transferRequestRepository.save(request);
    }

    @Override
    @Transactional
    public TransferRequest cancelTransfer(String transferId, TransferCancellationDTO dto) {
        TransferRequest request = getTransferRequest(transferId);
        
        if (request.getStatus() != TransferRequestStatus.PENDING) {
            throw new TransferException(TransferErrorCode.TRANSFER_ALREADY_PROCESSED);
        }
        
        request.setStatus(TransferRequestStatus.CANCELLED);
        request.setUpdatedBy(dto.getCancelledBy());
        request.setUpdatedDate(LocalDateTime.now());
        
        // Cancel approval request if exists
        if (request.getApprovalRequestId() != null) {
            approvalEngineService.cancelRequest(request.getApprovalRequestId(),dto.getReason());
        }
        
        // Save and update cache
        request = transferRequestRepository.save(request);
        cacheService.cacheTransferRequest(request);
        
        // Publish event
        eventPublisher.notifyTransferCancelled(request);
        
        return request;
    }

    @Override
    public Page<TransferRequest> getEmployeeTransfers(String empId, Pageable pageable) {
        return transferRequestRepository.findByEmpId(empId, pageable);
    }

    @Override
    public Page<TransferRequest> getUnitTransfers(String unitId, Pageable pageable) {
        return transferRequestRepository.findBySourceUnitIdOrDestinationUnitId(unitId, unitId, pageable);
    }

    @Override
    public List<TransferHistory> getTransferHistory(String transferId) {
        //return transferHistoryRepository.findByTransferRequestId(transferId);
       return new ArrayList<>();
    }

    @Override
    public TransferStatistics getUnitTransferStatistics(String unitId) {
        return transferStatisticsRepository.findByUnitId(unitId)
                .orElseGet(() -> createInitialStatistics(unitId));
    }

    @Override
    @Transactional
    public void handleTransferApproval(String transferId, ApprovalStatus status) {
        TransferRequest request = getTransferRequest(transferId);
        
        if (status == ApprovalStatus.APPROVED) {
            // Update eligibility mapping
            empEligibilityService.updateEligibilityMapping(
                request.getEmpId(),
                    request.getSourceUnitId(),
                request.getDestinationUnitId(),
                request.getTransferType(),
                    request.getStartDate(),
                    request.getEndDate()
            );
            
            // Map employee to shift in the destination unit
            mapEmployeeToShift(request);
            
            // Create transfer history
            createTransferHistory(request);
            
            request.setStatus(TransferRequestStatus.APPROVED);
        } else {
            request.setStatus(TransferRequestStatus.REJECTED);
        }
        
        // Save the updated request
        request = saveTransfer(request);
        
        // Update cache
        cacheService.cacheTransferRequest(request);
        
        // Publish event based on status
        if (status == ApprovalStatus.APPROVED) {
            eventPublisher.notifyTransferApproved(request);
        } else {
            eventPublisher.notifyTransferRejected(request);
        }
        // Refresh the unitEmployees cache for this unit to include the new employee
        empEligibilityService.refreshUnitEmployeesCache(Integer.valueOf(request.getDestinationUnitId()));
    }

    private com.stpl.tech.attendance.approval.entity.ApprovalRequest createApprovalRequest(TransferRequest request)  {
        LocalDateTime now = DateTimeUtil.now();
        Map<String, String> metadata = Map.of(
                "sourceUnitId", request.getSourceUnitId() != null ? request.getSourceUnitId() : "",
                "sourceUnitName",request.getSourceUnitId() != null ? unitCacheService.getUnitById(Integer.valueOf(request.getSourceUnitId())).getName() : "",
                "destinationUnitId", request.getDestinationUnitId(),
                "destinationUnitName", unitCacheService.getUnitById(Integer.valueOf(request.getDestinationUnitId())).getName(),
                "transferType", request.getTransferType().name(),
                "startDate", request.getStartDate().toString(),
                "endDate", request.getEndDate() == null ? "" : request.getEndDate().toString(),
                "reason", request.getReason(),
                "comment", request.getComment() != null ? request.getComment() : ""
        );
        try {
            return approvalEngineService.createRequest(
                    ApprovalRequest.builder()
                            .requestType(ApprovalType.EMPLOYEE_TRANSFER)
                            .requesterId(Long.valueOf(request.getEmpId()))
                            .status(ApprovalStatus.PENDING)
                            .currentStep(1)
                            .totalSteps(1)
                            .requestDate(now)
                            .createdDate(now)
                            .createdBy(JwtContext.getInstance().getUserId().toString())
                            .referenceId(Long.valueOf(request.getTransferRequestId()))
                            .unitId(Long.valueOf(request.getDestinationUnitId()))
                            .metadata(objectMapper.writeValueAsString(metadata))
                            .build());
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    private TransferHistory createTransferHistory(TransferRequest request) {
        TransferHistory history = TransferHistory.builder()
                .transferRequestId(request.getTransferRequestId())
                .empId(request.getEmpId())
                .sourceUnitId(request.getSourceUnitId())
                .destinationUnitId(request.getDestinationUnitId())
                .transferType(request.getTransferType())
                .startDate(request.getStartDate())
                .endDate(request.getEndDate())
                .comment(request.getComment())
                .status(request.getStatus())
                .transferHistoryStatus("ACTIVE")
                .createdDate(LocalDateTime.now())
                .createdBy(request.getUpdatedBy())
                .updatedDate(LocalDateTime.now())
                .updatedBy(request.getUpdatedBy())
                .build();
                
        return transferHistoryRepository.save(history);
    }

    private TransferStatistics createInitialStatistics(String unitId) {
        TransferStatistics statistics = TransferStatistics.builder()
                .unitId(unitId)
                .totalTransfers(0)
                .pendingTransfers(0)
                .approvedTransfers(0)
                .rejectedTransfers(0)
                .permanentTransfers(0)
                .temporaryTransfers(0)
                .transfersByMonth("{}")
                .transfersByDepartment("{}")
                .transferStatisticsStatus("ACTIVE")
                .createdDate(LocalDateTime.now())
                .createdBy("SYSTEM")
                .updatedDate(LocalDateTime.now())
                .updatedBy("SYSTEM")
                .build();

        return transferStatisticsRepository.save(statistics);
    }



    @Override
   public List<ApproverInfo> getApprovers(String sourceUnitId , String destinationUnitId , Integer empId){
       UnitBasicDetail destinationUnitDetail = unitCacheService.getUnitBasicDetail(Integer.parseInt(destinationUnitId));
       List<ApproverInfo> approvers = new ArrayList<>();
       approvers.add(new ApproverInfo(destinationUnitDetail.getUnitCafeManager(), true));
       approvers.add(new ApproverInfo(destinationUnitDetail.getCafeManagerId(), true));
       return approvers;
    }

    @Override
    public Optional<TransferRequestStatus> getLatestTransferStatus(String empId) {
        log.debug("Getting latest transfer status for employee: {}", empId);
        
        try {
            // Try to get from cache first
            Optional<TransferRequestStatus> cachedStatus = cacheService.getLatestTransferStatus(empId);
            if (cachedStatus.isPresent()) {
                return cachedStatus;
            }
            
            // If not in cache, get from database
            Optional<TransferRequest> latestTransfer = transferRequestRepository.findMostRecentTransferByEmpId(empId);
            
            if (latestTransfer.isPresent()) {
                TransferRequestStatus status = latestTransfer.get().getStatus();
                // Cache the result
                cacheService.cacheLatestTransferStatus(empId, status);
                return Optional.of(status);
            }
            
            // Cache null result to avoid repeated database calls
            cacheService.cacheLatestTransferStatus(empId, null);
            return Optional.empty();
            
        } catch (Exception e) {
            log.error("Error getting latest transfer status for employee: {}", empId, e);
            return Optional.empty();
        }
    }

    @Override
    @Transactional(readOnly = true)
    public EmployeeTransferMetadataDTO getEmployeeTransferMetadata(String empId) {
        log.info("Getting transfer metadata for employee: {}", empId);
        
        // Get employee basic details
        EmployeeBasicDetail employee = userCacheService.getUserById(Integer.parseInt(empId));
        if (employee == null) {
            log.warn("Employee not found with empId: {}", empId);
            throw new TransferException(TransferErrorCode.TRANSFER_NOT_FOUND);
        }
        
        // Get attendance eligible units
        List<UnitEligibilityDTO> attendanceEligibleUnits = getAttendanceEligibleUnits(empId);
        
        // Get transfer reasons
        List<TransferReasonDTO> transferReasons = getTransferReasons();
        
        // Get transfer types
        List<TransferType> transferTypes = Arrays.asList(TransferType.values());
        
        return EmployeeTransferMetadataDTO.builder()
                .empId(empId)
                .empName(employee.getName())
                .empCode(employee.getEmployeeCode())
                .attendanceEligibleUnits(attendanceEligibleUnits)
                .transferReasons(transferReasons)
                .transferTypes(transferTypes)
                .build();
    }
    
    /**
     * Get attendance eligible units for an employee
     */
    private List<UnitEligibilityDTO> getAttendanceEligibleUnits(String empId) {
        // Get all active attendance eligibility mappings for the employee
        List<EmpEligibilityMapping> eligibilityMappings = empEligibilityMappingRepository
            .findByEmpIdAndEligibilityTypeAndStatus(
                empId,
                EligibilityType.ATTENDANCE,
                MappingStatus.ACTIVE
            );
        
        // Filter mappings that are for units and are currently active (within date range)
        LocalDate currentDate = LocalDate.now();
        List<EmpEligibilityMapping> activeUnitMappings = eligibilityMappings.stream()
            .filter(mapping -> mapping.getMappingType() == MappingType.UNIT)
            .filter(mapping -> 
                (mapping.getStartDate() == null || mapping.getStartDate().isBefore(currentDate) || mapping.getStartDate().isEqual(currentDate)) &&
                (mapping.getEndDate() == null || mapping.getEndDate().isAfter(currentDate) || mapping.getEndDate().isEqual(currentDate))
            )
            .collect(Collectors.toList());
        
        // Convert to DTOs with unit details
        return activeUnitMappings.stream()
            .map(mapping -> {
                try {
                    Integer unitId = Integer.parseInt(mapping.getValue());
                    UnitBasicDetail unitDetail = unitCacheService.getUnitBasicDetail(unitId);
                    
                    if (unitDetail != null) {
                        return UnitEligibilityDTO.builder()
                            .unitId(unitId)
                            .unitName(unitDetail.getName())
                            .unitCode(unitDetail.getReferenceName())
                            .startDate(mapping.getStartDate())
                            .endDate(mapping.getEndDate())
                            .status(mapping.getStatus().name())
                            .build();
                    } else {
                        log.warn("Unit details not found for unitId: {}", unitId);
                        return null;
                    }
                } catch (NumberFormatException e) {
                    log.error("Invalid unit ID format in mapping: {}", mapping.getValue());
                    return null;
                }
            })
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }
    
    /**
     * Get predefined transfer reasons
     */
    private List<TransferReasonDTO> getTransferReasons() {
        return Arrays.stream(TransferReason.values())
            .map(reason -> TransferReasonDTO.builder()
                .name(reason.getDisplayName())
                .build())
            .collect(Collectors.toList());
    }

    /**
     * Map employee to shift in the destination unit based on transfer type
     * @param request Transfer request containing employee and unit details
     */
    private void mapEmployeeToShift(TransferRequest request) {
        try {
            Integer empId = Integer.valueOf(request.getEmpId());
            Integer destinationUnitId = Integer.valueOf(request.getDestinationUnitId());
            TransferType transferType = request.getTransferType();
            LocalDate startDate = request.getStartDate();
            LocalDate endDate = request.getEndDate();
            
            // Find the morning earliest shift for the destination unit
            Shift morningEarliestShift = findMorningEarliestShiftForUnit(destinationUnitId);
            
            if (morningEarliestShift == null) {
                log.warn("No morning earliest shift found for unit: {}. Using default shift.", destinationUnitId);
                morningEarliestShift = getDefaultShift();
            }
            LocalDate currentAttendanceDate = attendanceDateUtil.getCurrentAttendanceDate();
            LocalDateTime currentDateTime = DateTimeUtil.now();

            // Create permanent shift mapping
            LocalDateTime shiftStartToday = currentAttendanceDate.atTime(morningEarliestShift.getStartTime().toLocalTime());

            // Check if current time is past the shift start time for today
            if (currentDateTime.isAfter(shiftStartToday) && startDate.isEqual(currentAttendanceDate)) {
                startDate = currentAttendanceDate.plusDays(1);
                log.info("Shift {} has already started today at {}. Mapping employee from tomorrow.",
                        morningEarliestShift.getShiftName(), morningEarliestShift.getStartTime());
            }
            if (transferType == TransferType.PERMANENT) {

                createPermanentShiftMapping(empId, morningEarliestShift.getShiftId(), destinationUnitId, startDate);
                log.info("Created permanent shift mapping for employee {} in unit {} with shift {}", 
                         empId, destinationUnitId, morningEarliestShift.getShiftName());
            } else {
                if(startDate.isAfter(endDate)){
                    return;
                }
                // Create temporary shift override
                createTemporaryShiftOverride(empId, morningEarliestShift.getShiftId(), destinationUnitId, startDate, endDate);
                log.info("Created temporary shift override for employee {} in unit {} with shift {} from {} to {}", 
                         empId, destinationUnitId, morningEarliestShift.getShiftName(), startDate, endDate);
            }
            
        } catch (Exception e) {
            log.error("Failed to map employee {} to shift in unit {}: {}", 
                     request.getEmpId(), request.getDestinationUnitId(), e.getMessage(), e);
            // Don't throw exception to avoid rolling back the entire transfer approval
            // Just log the error and continue
        }
    }
    
    /**
     * Find the morning earliest shift for a given unit
     * @param unitId Unit ID
     * @return Morning earliest shift or null if not found
     */
    private Shift findMorningEarliestShiftForUnit(Integer unitId) {
        try {
            // Get all shifts mapped to this unit
            List<Integer> shiftIds = shiftCafeMappingRepository.findShiftIdsByUnitIdAndStatus(unitId, RosteringConstants.ACTIVE);
            
            if (shiftIds.isEmpty()) {
                log.warn("No shifts found for unit: {}", unitId);
                return null;
            }
            
            // Get shift details and find the one with earliest start time
            Optional<Shift> morningEarliestShift = shiftIds.stream()
                .map(shiftId -> shiftRepository.findById(shiftId))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .min((s1, s2) -> {
                    LocalTime startTime1 = s1.getStartTime().toLocalTime();
                    LocalTime startTime2 = s2.getStartTime().toLocalTime();
                    return startTime1.compareTo(startTime2);
                });
            
            return morningEarliestShift.orElse(null);
            
        } catch (Exception e) {
            log.error("Error finding morning earliest shift for unit {}: {}", unitId, e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * Get default shift as fallback
     * @return Default shift
     */
    private Shift getDefaultShift() {
        try {
            // Try to find a shift with "UNIVERSAL" or "DEFAULT" in the name
            Optional<Shift> defaultShift = shiftRepository.findByShiftNameContainingIgnoreCaseAndStatus("UNIVERSAL", RosteringConstants.ACTIVE)
                .stream()
                .findFirst();
            
            if (defaultShift.isPresent()) {
                return defaultShift.get();
            }
            
            // Fallback to first active shift
            List<Shift> activeShifts = shiftRepository.findByStatus(RosteringConstants.ACTIVE);
            if (!activeShifts.isEmpty()) {
                return activeShifts.get(0);
            }
            
            log.error("No active shifts found in the system");
            return null;
            
        } catch (Exception e) {
            log.error("Error getting default shift: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * Create permanent shift mapping for employee
     * @param empId Employee ID
     * @param shiftId Shift ID
     * @param unitId Unit ID
     * @param startDate Start date for the mapping
     */
    private void createPermanentShiftMapping(Integer empId, Integer shiftId, Integer unitId, LocalDate startDate) {
        try {
            // Note: We don't check for existing mappings here as the ReladomoService will handle
            // any existing mappings appropriately (terminating them and creating new ones)
            // This ensures all existing functionality like post-processing, notifications, and shift instance recreation is reused
            
            // Use existing ReladomoService to create permanent shift mapping
            // This ensures all existing functionality like post-processing, notifications, and shift instance recreation is reused
            EmpShiftUpdateRequestDTO request = EmpShiftUpdateRequestDTO.builder()
                .empId(empId)
                .shiftId(shiftId)
                .unitId(unitId)
                .businessFrom(startDate.atStartOfDay())
                .businessTo(null) // No end date for permanent mapping (infinity)
                .updateUpcomingShifts(true) // This will use ReladomoService.updateEmpShifts()
                .updatedBy(String.valueOf(AppConstants.SYSTEM_EMPLOYEE_ID))
                .build();
            
            reladomoService.updateEmpShifts(request);
            log.info("Successfully created permanent shift mapping for employee {} in unit {} with shift {} using ReladomoService", 
                     empId, unitId, shiftId);
            
        } catch (Exception e) {
            log.error("Error creating permanent shift mapping for employee {} in unit {}: {}", empId, unitId, e.getMessage(), e);
            throw new RuntimeException("Failed to create permanent shift mapping", e);
        }
    }
    
    /**
     * Create temporary shift override for employee
     * @param empId Employee ID
     * @param shiftId Shift ID
     * @param unitId Unit ID
     * @param startDate Start date for the override
     * @param endDate End date for the override
     */
    private void createTemporaryShiftOverride(Integer empId, Integer shiftId, Integer unitId, LocalDate startDate, LocalDate endDate) {
        try {
            // Use existing RosteringService to create temporary shift override
            // This ensures all existing functionality like post-processing, notifications, and shift instance recreation is reused
            EmpShiftUpdateRequestDTO request = EmpShiftUpdateRequestDTO.builder()
                .empId(empId)
                .shiftId(shiftId)
                .unitId(unitId)
                .businessFrom(startDate.atStartOfDay())
                .businessTo(endDate.atStartOfDay())
                .updateUpcomingShifts(false) // This will use RosteringService.createEmpShiftMappingOverriding()
                .updatedBy(String.valueOf(AppConstants.SYSTEM_EMPLOYEE_ID))
                .build();
            
            rosteringService.createEmpShiftMappingOverriding(request);
            log.info("Successfully created temporary shift override for employee {} in unit {} with shift {} from {} to {} using RosteringService", 
                     empId, unitId, shiftId, startDate, endDate);
            
        } catch (Exception e) {
            log.error("Error creating temporary shift override for employee {} in unit {}: {}", empId, unitId, e.getMessage(), e);
            throw new RuntimeException("Failed to create temporary shift override", e);
        }
    }
}