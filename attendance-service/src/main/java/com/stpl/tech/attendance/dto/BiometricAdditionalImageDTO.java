package com.stpl.tech.attendance.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for additional images during biometric registration
 * Contains type (e.g., "left", "center", "right") and base64 image data
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BiometricAdditionalImageDTO {
    
    /**
     * Type of the image (e.g., "left", "center", "right", "profile", etc.)
     */
    private String type;
    
    /**
     * Base64 encoded image data
     */
    private String base64Image;
}
