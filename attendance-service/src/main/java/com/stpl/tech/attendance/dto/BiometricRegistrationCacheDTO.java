package com.stpl.tech.attendance.dto;

import com.stpl.tech.attendance.entity.BiometricRegistration;
import com.stpl.tech.attendance.enums.BiometricStatus;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BiometricRegistrationCacheDTO implements Serializable {
    private Long id;
    private String empId;
    private String biometricId;
    private BiometricStatus status;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private String createdBy;
    private String updatedBy;

    public static BiometricRegistrationCacheDTO fromEntity(BiometricRegistration entity) {
        if (entity == null) {
            return null;
        }
        return BiometricRegistrationCacheDTO.builder()
                .id(entity.getId())
                .empId(entity.getEmpId())
                .biometricId(entity.getBiometricId())
                .status(entity.getStatus())
                .createdAt(entity.getCreatedAt())
                .updatedAt(entity.getUpdatedAt())
                .createdBy(entity.getCreatedBy())
                .updatedBy(entity.getUpdatedBy())
                .build();
    }

    public BiometricRegistration toEntity() {
        BiometricRegistration entity = new BiometricRegistration();
        entity.setId(this.id);
        entity.setEmpId(this.empId);
        entity.setBiometricId(this.biometricId);
        entity.setStatus(this.status);
        entity.setCreatedAt(this.createdAt);
        entity.setUpdatedAt(this.updatedAt);
        entity.setCreatedBy(this.createdBy);
        entity.setUpdatedBy(this.updatedBy);
        return entity;
    }
} 