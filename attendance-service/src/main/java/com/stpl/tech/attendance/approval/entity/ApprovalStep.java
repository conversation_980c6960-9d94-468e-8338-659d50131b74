package com.stpl.tech.attendance.approval.entity;

import com.stpl.tech.attendance.enums.ApprovalStepStatus;
import com.stpl.tech.attendance.enums.ApprovalStepType;
import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonBackReference;

@Entity
@Table(name = "APPROVAL_STEP")
@Data
public class ApprovalStep {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "STEP_ID")
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "REQUEST_ID", nullable = false)
    @JsonBackReference
    private ApprovalRequest approvalRequest;

    @Column(name = "STEP_NUMBER", nullable = false)
    private Integer stepNumber;

    @Column(name = "STEP_NAME", nullable = false)
    private String stepName;

    @Column(name = "STEP_TYPE", nullable = false)
    @Enumerated(EnumType.STRING)
    private ApprovalStepType stepType;

    @Column(name = "STATUS", nullable = false)
    @Enumerated(EnumType.STRING)
    private ApprovalStepStatus status;

    @Column(name = "TASK_ID", nullable = false)
    private String taskId;

    @Column(name = "REQUIRED_APPROVALS", nullable = false)
    private Integer requiredApprovals;

    @Column(name = "CURRENT_APPROVALS", nullable = false)
    private Integer currentApprovals;

    @Column(name = "ALLOW_PARTIAL_APPROVAL")
    private Boolean allowPartialApproval;

    @Column(name = "MINIMUM_APPROVALS")
    private Integer minimumApprovals;

    @Column(name = "REQUIRE_ALL_APPROVALS")
    private Boolean requireAllApprovals;

    @Column(name = "TOTAL_APPROVERS", nullable = false)
    private Integer totalApprovers;

    @Column(name = "LAST_APPROVER_ID")
    private Long lastApproverId;

    @Column(name = "LAST_APPROVAL_DATE")
    private LocalDateTime lastApprovalDate;

    @Column(name = "CREATED_DATE", nullable = false)
    private LocalDateTime createdDate;

    @Column(name = "CREATED_BY", nullable = false)
    private String createdBy;

    @Column(name = "UPDATED_DATE", nullable = false)
    private LocalDateTime updatedDate;

    @Column(name = "UPDATED_BY", nullable = false)
    private String updatedBy;


    @OneToMany(mappedBy = "approvalStep", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonBackReference
    private List<SubApprovalStep> subSteps;
} 