package com.stpl.tech.attendance.service.impl;

import com.stpl.tech.attendance.entity.AttendanceType;
import com.stpl.tech.attendance.entity.DailyAttendanceSummary;
import com.stpl.tech.attendance.entity.DailyAttendanceSummaryLogs;
import com.stpl.tech.attendance.repository.DailyAttendanceSummaryLogsRepository;
import com.stpl.tech.attendance.repository.DailyAttendanceSummaryRepository;
import com.stpl.tech.attendance.service.DailyAttendanceSummaryLogsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class DailyAttendanceSummaryLogsServiceImpl implements DailyAttendanceSummaryLogsService {
    
    private final DailyAttendanceSummaryLogsRepository logsRepository;
    private final DailyAttendanceSummaryRepository summaryRepository;
    
    @Override
    @Transactional
    public DailyAttendanceSummaryLogs logCurrentState(DailyAttendanceSummary summary, 
                                                    DailyAttendanceSummaryLogs.ActionStatus actionStatus, String actionType,
                                                    String updatedBy) {
        try {
            if (summary == null) {
                log.warn("Cannot log null DailyAttendanceSummary");
                return null;
            }
            DailyAttendanceSummaryLogs logEntry = new DailyAttendanceSummaryLogs();
            logEntry.setDailyAttendanceSummaryId(summary.getId());
            logEntry.setFirstCheckIn(summary.getFirstCheckIn());
            logEntry.setLastCheckOut(summary.getLastCheckOut());
            logEntry.setStatus(summary.getStatus() != null ? summary.getStatus().name() : null);
            logEntry.setActionType(actionType);
            logEntry.setActionStatus(actionStatus);
            logEntry.setUpdatedBy(updatedBy);
            logEntry.setUpdationTime(LocalDateTime.now());

            DailyAttendanceSummaryLogs savedLog = logsRepository.save(logEntry);
            log.info("Successfully logged {} action for DailyAttendanceSummary ID: {}",
                    actionStatus, summary.getId());

            return savedLog;
        } catch (Exception e) {
            assert summary != null;
            log.error("Error logging current state for DailyAttendanceSummary: {}", summary.getId(), e);
            throw new RuntimeException("Failed to log current state", e);
        }
    }
    
    @Override
    @Transactional
    public DailyAttendanceSummary restoreFromLogs(Long summaryId, String updatedBy) {
        try {
            // Find the most recent log entry for this summary
            DailyAttendanceSummaryLogs mostRecentLog = logsRepository.findFirstByDailyAttendanceSummaryIdOrderByCreatedAtDescIdDesc(summaryId);
            
            if (mostRecentLog == null) {
                log.warn("No log entries found for DailyAttendanceSummary ID: {}", summaryId);
                return null;
            }

            // Find the current summary to restore
            Optional<DailyAttendanceSummary> currentSummary = summaryRepository.findById(summaryId);
            if (currentSummary.isEmpty()) {
                log.warn("DailyAttendanceSummary not found for ID: {}", summaryId);
                return null;
            }
            
            DailyAttendanceSummary summary = currentSummary.get();
            
            // Restore the values from the log entry
            summary.setFirstCheckIn(mostRecentLog.getFirstCheckIn());
            summary.setLastCheckOut(mostRecentLog.getLastCheckOut());
            if (mostRecentLog.getStatus() != null) {
                // Parse the status string back to AttendanceStatus enum
                try {
                    summary.setStatus(com.stpl.tech.attendance.entity.AttendanceStatus.valueOf(mostRecentLog.getStatus()));
                    summary.setType(AttendanceType.NORMAL);
                } catch (IllegalArgumentException e) {
                    log.warn("Invalid status in log entry: {}", mostRecentLog.getStatus());
                }
            }
            // Save the restored summary
            DailyAttendanceSummary restoredSummary = summaryRepository.save(summary);

            // Log the restoration action
            updateOrCreateLog(restoredSummary, DailyAttendanceSummaryLogs.ActionStatus.CANCELLED, mostRecentLog.getActionType(), updatedBy,mostRecentLog.getStatus());
            
            log.info("Successfully restored DailyAttendanceSummary ID: {} from logs", summaryId);
            return restoredSummary;
            
        } catch (Exception e) {
            log.error("Error restoring DailyAttendanceSummary from logs: {}", summaryId, e);
            throw new RuntimeException("Failed to restore from logs", e);
        }
    }
    
    @Override
    @Transactional
    public DailyAttendanceSummaryLogs updateOrCreateLog(DailyAttendanceSummary summary,
                                                        DailyAttendanceSummaryLogs.ActionStatus actionStatus,
                                                        String actionType,
                                                        String updatedBy,String status) {
        try {
            if (summary == null) {
                log.warn("Cannot log null DailyAttendanceSummary");
                return null;
            }
            
            // Try to find existing log entry for this summary
            DailyAttendanceSummaryLogs existingLog = logsRepository
                .findFirstByDailyAttendanceSummaryIdOrderByCreatedAtDescIdDesc(summary.getId());
            
            DailyAttendanceSummaryLogs logEntry;
            if (existingLog != null) {
                // Update existing log entry
                logEntry = existingLog;
                logEntry.setFirstCheckIn(summary.getFirstCheckIn());
                logEntry.setLastCheckOut(summary.getLastCheckOut());
                logEntry.setStatus(summary.getStatus() != null ? summary.getStatus().name() : null);
                logEntry.setActionType(actionType);
                logEntry.setActionStatus(actionStatus);
                logEntry.setUpdatedBy(updatedBy);
                logEntry.setUpdationTime(LocalDateTime.now());
                
                log.debug("Updating existing log entry ID: {} for DailyAttendanceSummary ID: {}", 
                    logEntry.getId(), summary.getId());
            } else {
                // Create new log entry
                logEntry = new DailyAttendanceSummaryLogs();
                logEntry.setDailyAttendanceSummaryId(summary.getId());
                logEntry.setFirstCheckIn(summary.getFirstCheckIn());
                logEntry.setLastCheckOut(summary.getLastCheckOut());
                logEntry.setStatus(summary.getStatus() != null ? summary.getStatus().name() : null);
                logEntry.setActionType(actionType);
                logEntry.setActionStatus(actionStatus);
                logEntry.setUpdatedBy(updatedBy);
                logEntry.setUpdationTime(LocalDateTime.now());
                
                log.debug("Creating new log entry for DailyAttendanceSummary ID: {}", summary.getId());
            }

            DailyAttendanceSummaryLogs savedLog = logsRepository.save(logEntry);
            log.info("Successfully {} log entry for DailyAttendanceSummary ID: {} with action: {}",
                    existingLog != null ? "updated" : "created", summary.getId(), actionType);

            return savedLog;
        } catch (Exception e) {
            log.error("Error updating/creating log for DailyAttendanceSummary: {}", summary.getId(), e);
            throw new RuntimeException("Failed to update/create log", e);
        }
    }
    
    @Override
    public Optional<DailyAttendanceSummaryLogs> findMostRecentLog(Long summaryId) {
        try {
            DailyAttendanceSummaryLogs mostRecentLog = logsRepository
                .findFirstByDailyAttendanceSummaryIdOrderByCreatedAtDescIdDesc(summaryId);
            return Optional.ofNullable(mostRecentLog);
        } catch (Exception e) {
            log.error("Error finding most recent log for DailyAttendanceSummary: {}", summaryId, e);
            return Optional.empty();
        }
    }
}
