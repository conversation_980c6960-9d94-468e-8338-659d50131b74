package com.stpl.tech.attendance.workflow.listener;

import com.stpl.tech.attendance.approval.entity.ApprovalRequest;
import com.stpl.tech.attendance.approval.entity.ApprovalStep;
import com.stpl.tech.attendance.approval.entity.SubApprovalStep;
import com.stpl.tech.attendance.approval.repository.ApprovalRequestRepository;
import com.stpl.tech.attendance.approval.repository.ApprovalStepRepository;
import com.stpl.tech.attendance.approval.repository.SubApprovalStepRepository;
import com.stpl.tech.attendance.approval.service.ApproverAssignmentService;
import com.stpl.tech.attendance.cache.service.UserCacheService;
import com.stpl.tech.attendance.enums.ApprovalStepStatus;
import com.stpl.tech.attendance.enums.ApprovalStepType;
import com.stpl.tech.attendance.enums.ApprovalType;
import com.stpl.tech.attendance.enums.EligibilityType;
import com.stpl.tech.attendance.notification.dto.NotificationRequest;
import com.stpl.tech.attendance.notification.entity.Notification;
import com.stpl.tech.attendance.notification.service.NotificationService;
import com.stpl.tech.attendance.model.ApproverInfo;
import com.stpl.tech.attendance.service.EmpEligibilityService;
import com.stpl.tech.attendance.service.TransferService;
import com.stpl.tech.attendance.service.EmployeeAttendanceService;
import com.stpl.tech.attendance.service.impl.EmployeeAttendanceApprovalServiceImpl;
import com.stpl.tech.attendance.service.impl.AsyncOperationService;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.attendance.util.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.ExtensionElement;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.bpmn.model.UserTask;
import org.flowable.engine.ProcessEngine;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.TaskService;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.task.api.Task;
import org.flowable.task.service.delegate.DelegateTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component("approvalTaskListener")
public class ApprovalTaskListener implements TaskListener {

    @Autowired
    private ApproverAssignmentService approverAssignmentService;

    @Autowired
    private ProcessEngine processEngine;

    @Autowired
    private ApprovalStepRepository approvalStepRepository;

    @Autowired
    private ApprovalRequestRepository approvalRequestRepository;

    @Autowired
    private SubApprovalStepRepository subApprovalStepRepository;

    @Autowired
    private TaskService taskService;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private UserCacheService userCacheService;

    @Autowired
    private EmpEligibilityService empEligibilityService;

    @Autowired
    private TransferService transferService;

    @Autowired
    private EmployeeAttendanceService employeeAttendanceService;

    @Autowired
    private EmployeeAttendanceApprovalServiceImpl employeeAttendanceApprovalService;

    @Autowired
    private AsyncOperationService asyncOperationService;

    @Override
    public void notify(DelegateTask delegateTask) {
        String taskName = delegateTask.getName();
        String approverRole = "";
        
        // Default values for approval configuration
        Integer requiredApprovals = 1;  // Default: 1 approver
        Integer minimumApprovals = 1;   // Default: Need 1 approval
        Boolean requireAllApprovals = false;  // Default: Don't require all approvers
        Boolean allowPartialApproval = true;  // Default: Allow partial approvals
        Integer currentStep = 1;
        String approvalType = (String) delegateTask.getVariable("requestType");
        
        String processDefinitionId = delegateTask.getProcessDefinitionId();
        String taskDefinitionKey = delegateTask.getTaskDefinitionKey();
        RepositoryService repositoryService = processEngine.getRepositoryService();
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionId);
        FlowElement flowElement = bpmnModel.getFlowElement(taskDefinitionKey);

        if (flowElement instanceof UserTask) {
            UserTask userTask = (UserTask) flowElement;
            Map<String, List<ExtensionElement>> extensions = userTask.getExtensionElements();

            List<ExtensionElement> props = extensions.get("property");
            if (props != null) {
                for (ExtensionElement prop : props) {
                    String name = prop.getAttributeValue(null, "name");
                    String value = prop.getAttributeValue(null, "value");
                    switch (name) {
                        case "approverRole":
                            approverRole = value;
                            break;
                        case "requiredApprovals":
                            requiredApprovals = Integer.parseInt(value);
                            break;
                        case "minimumApprovals":
                            minimumApprovals = Integer.parseInt(value);
                            break;
                        case "requireAllApprovals":
                            requireAllApprovals = Boolean.parseBoolean(value);
                            break;
                        case "allowPartialApproval":
                            allowPartialApproval = Boolean.parseBoolean(value);
                            break;
                        case "currentStep":
                            currentStep = Integer.parseInt(value);
                            break;
                    }
                }
            }
        }

        // Create context for approver validation
        Map<String, Object> context = new HashMap<>();
        setContext(delegateTask,context,currentStep,approvalType);
        // Get approvers for this step
        List<ApproverInfo> approvers = getApproversForStep(context);
        List<String> approverIds = approvers.stream().map(approver -> approver.getId().toString()).collect(java.util.stream.Collectors.toList());
       setDeligateTaskVariables(delegateTask,minimumApprovals,requiredApprovals,approverIds,currentStep);

        ApprovalRequest approvalRequest = approvalRequestRepository.findById(Long.parseLong(delegateTask.getVariable("approvalRequestId").toString()))
                .orElseThrow(() -> new RuntimeException("Approval request not found"));

        // Create main approval step
        ApprovalStep mainStep = createMainApprovalStep(delegateTask, taskName, requiredApprovals, 
            minimumApprovals, requireAllApprovals, allowPartialApproval);

        // Create sub-steps for each approver
        createSubApprovalSteps(delegateTask, mainStep, approvers, approvalRequest);
    }


    private void setContext(DelegateTask delegateTask,Map<String, Object> context , Integer currentStep ,
                            String approvalType) {
        context.put("departmentId", delegateTask.getVariable("departmentId"));
        context.put("locationId", delegateTask.getVariable("locationId"));
        context.put("empId", delegateTask.getVariable("requesterId"));
        context.put("unitId", delegateTask.getVariable("unitId"));
        context.put("currentStep", currentStep);
        context.put("approvalType",approvalType);
        context.put("sourceUnitId",delegateTask.getVariable("sourceUnitId"));
        context.put("destinationUnitId",delegateTask.getVariable("destinationUnitId"));
    }

    private void setDeligateTaskVariables(DelegateTask delegateTask, Integer minimumApprovals , Integer requiredApprovals,
                                          List<String> approvers , Integer currentStep) {
        delegateTask.setVariable("approvers", approvers);

        // Set other required variables for multi-instance
        delegateTask.setVariable("requiredApprovals", requiredApprovals);
        delegateTask.setVariable("minimumApprovals", minimumApprovals);
        delegateTask.setVariable("nrOfInstances", approvers.size());
        delegateTask.setVariable("nrOfCompletedInstances", 0);
        delegateTask.setVariable("nrOfActiveInstances", approvers.size());
        delegateTask.setVariable("nrOfApprovedInstances", 0);
        delegateTask.setVariable("nrOfRejectedInstances", 0);
        delegateTask.setVariable("currentStep", currentStep);
    }

    private ApprovalStep createMainApprovalStep(DelegateTask delegateTask, String stepName,
            Integer requiredApprovals, Integer minimumApprovals,
            Boolean requireAllApprovals, Boolean allowPartialApproval) {
        
        Long approvalRequestId = Long.parseLong(delegateTask.getVariable("approvalRequestId").toString());
        String requesterId = delegateTask.getVariable("requesterId").toString();
        String createdBy = delegateTask.getVariable("createdBy").toString();
        String taskId = delegateTask.getId();
        
        ApprovalStep mainStep = new ApprovalStep();
        mainStep.setApprovalRequest(new ApprovalRequest(approvalRequestId,Long.parseLong(requesterId)));
        mainStep.setStepNumber(Integer.parseInt(delegateTask.getVariable("currentStep").toString()));
        mainStep.setStepName(stepName);
        mainStep.setStepType(ApprovalStepType.PARALLEL); // Default to parallel approval
        mainStep.setStatus(ApprovalStepStatus.PENDING);
        mainStep.setTaskId(taskId);
        mainStep.setRequiredApprovals(requiredApprovals);
        mainStep.setMinimumApprovals(minimumApprovals);
        mainStep.setCurrentApprovals(0);
        mainStep.setTotalApprovers((Integer) delegateTask.getVariable("nrOfActiveInstances"));
        mainStep.setRequireAllApprovals(requireAllApprovals);
        mainStep.setAllowPartialApproval(allowPartialApproval);
        mainStep.setCreatedDate(DateTimeUtil.now());
        mainStep.setCreatedBy(createdBy);
        
        return approvalStepRepository.save(mainStep);
    }

    private void createSubApprovalSteps(DelegateTask delegateTask, ApprovalStep mainStep,
                                        List<ApproverInfo> approvers , ApprovalRequest approvalRequest) {
        String requesterId = delegateTask.getVariable("requesterId").toString();
        String processInstanceId = delegateTask.getProcessInstanceId();
        EmployeeBasicDetail employeeBasicDetail = userCacheService.getUserById(approvalRequest.getRequesterId().intValue());
        boolean shouldSendNotification = true;
        if(approvalRequest.getRequestType() == ApprovalType.BIOMETRIC_REGISTRATION) {
            // For biometric registration requests, skip the initial notification
            // The notification should be sent after the actual biometric registration process completes
            // This prevents sending notifications prematurely when the approval workflow starts
            shouldSendNotification = false;
        }

        // Filter approvers who should receive notifications
        List<String> notificationRecipients = approvers.stream()
                .filter(ApproverInfo::getSendNotificationToApprovers) // Filter approvers who should receive notifications
                .map(approver -> approver.getId().toString())
                .collect(java.util.stream.Collectors.toList());
        if (!notificationRecipients.isEmpty()) {
            // Check if this is a cancellation request by examining the approval request metadata
            Map<String, Object> notificationMetadata = new HashMap<>();
            boolean isCancellationRequest = true;

            if (approvalRequest.getMetadata() != null) {
                try {
                    com.fasterxml.jackson.databind.ObjectMapper objectMapper = new com.fasterxml.jackson.databind.ObjectMapper();
                    Map<String, Object> metadataMap = objectMapper.readValue(approvalRequest.getMetadata(), Map.class);
                    String isCancellationFlag = (String) metadataMap.get("isCancellationRequest");
                    if ("true".equals(isCancellationFlag)) {
                        notificationMetadata.put("isCancelRequest", true);
                        notificationMetadata.put("originalRequestId", metadataMap.get("attendanceRecordId"));
                    }
                } catch (Exception e) {
                    // If metadata parsing fails, continue without cancellation metadata
                    log.warn("Failed to parse approval request metadata for cancellation check: {}", e.getMessage());
                }
            }

            // Send notification only to approvers who have sendNotification = true
            if (shouldSendNotification) {
                NotificationRequest notificationRequest = NotificationRequest.builder()
                        .type(Notification.NotificationType.APPROVAL_REQUEST)
                        .title("Approval Request Assigned For " + approvalRequest.getRequestType().name())
                        .message(employeeBasicDetail.getName() + " has requested for " + approvalRequest.getRequestType().name() + " approval.")
                        .priority(Notification.Priority.MEDIUM)
                        .metadata(new HashMap<>())
                        .recipientIds(notificationRecipients)
                        .requesterId(approvalRequest.getRequesterId().toString())
                        .build();
                // Send notification asynchronously
                asyncOperationService.sendNotificationAsync(notificationRequest);
            }
        }
        List<SubApprovalStep> subSteps = new ArrayList<>();
        for (ApproverInfo approverInfo : approvers) {
            SubApprovalStep subStep = new SubApprovalStep();
            subStep.setApprovalStep(mainStep);
            subStep.setApproverId(approverInfo.getId().longValue());
            subStep.setStatus(ApprovalStepStatus.PENDING);
            subStep.setCreatedDate(DateTimeUtil.now());
            subStep.setCreatedBy(approvalRequest.getCreatedBy());
            
            // Create Flowable sub-task
            Task subTask = taskService.newTask();
            subTask.setName("Approval Sub-Task for Step " + mainStep.getStepNumber());
            subTask.setAssignee(approverInfo.getId().toString());
            //subTask.setProcessInstanceId(processInstanceId);
            subTask.setParentTaskId(mainStep.getTaskId());
            taskService.saveTask(subTask);
            
            subStep.setSubTaskId(subTask.getId());
            subSteps.add(subStep);
        }
        
        // Save all sub-steps
        subApprovalStepRepository.saveAll(subSteps);
    }

    private List<ApproverInfo> getApproversForStep(Map<String, Object> context) {
        return switch (ApprovalType.valueOf((String) context.get("approvalType"))) {
            case EMPLOYEE_TRANSFER ->
                    transferService.getApprovers(context.get("unitId").toString(), context.get("unitId").toString(), Integer.valueOf(context.get("empId").toString()));
            case EMPLOYEE_LEAVE ->
                    employeeAttendanceApprovalService.getApproversForAttendanceRequest(context.get("unitId").toString(), Integer.valueOf(context.get("empId").toString()), "leave");
            case EMPLOYEE_OD ->
                    employeeAttendanceApprovalService.getApproversForAttendanceRequest(context.get("unitId").toString(), Integer.valueOf(context.get("empId").toString()), "OD");
            case EMPLOYEE_WFH ->
                    employeeAttendanceApprovalService.getApproversForAttendanceRequest(context.get("unitId").toString(), Integer.valueOf(context.get("empId").toString()), "WFH");
            case EMPLOYEE_REGULARISATION ->
                    employeeAttendanceApprovalService.getApproversForAttendanceRequest(context.get("unitId").toString(), Integer.valueOf(context.get("empId").toString()), "regularisation");
            case ATTENDANCE_REQUEST_CANCELLATION ->
                employeeAttendanceApprovalService.getApproversForAttendanceRequest(context.get("unitId").toString(), Integer.valueOf(context.get("empId").toString()), "cancellation");
            default ->
                    empEligibilityService.getApprovers(EligibilityType.APPROVAL, Integer.valueOf(context.get("unitId").toString()));
        };

    }
} 