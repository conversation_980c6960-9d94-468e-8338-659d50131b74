package com.stpl.tech.attendance.enums;

/**
 * Enum representing the leave credit cycle periods
 */
public enum LeaveCreditCycle {
    YEARLY,
    QUARTERLY,
    HALF_YEAR;
    
    /**
     * Get the number of months for this cycle
     * @return number of months in the cycle
     */
    public int getMonths() {
        return switch (this) {
            case YEARLY -> 12;
            case QUARTERLY -> 3;
            case HALF_YEAR -> 6;
        };
    }
    
    /**
     * Get the display name for the cycle
     * @return human readable cycle name
     */
    public String getDisplayName() {
        return switch (this) {
            case YEARLY -> "Yearly";
            case QUARTERLY -> "Quarterly";
            case HALF_YEAR -> "Half Year";
        };
    }
}


