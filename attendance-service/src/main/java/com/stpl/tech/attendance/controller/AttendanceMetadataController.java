//package com.stpl.tech.attendance.controller;
//
//import com.stpl.tech.attendance.dto.AttendanceMetadataResponse;
//import com.stpl.tech.attendance.entity.EmpAttendanceMetadata;
//import com.stpl.tech.attendance.enums.AttendanceAttributeType;
//import com.stpl.tech.attendance.model.response.ApiResponse;
//import com.stpl.tech.attendance.service.AttendanceMetadataService;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.http.ResponseEntity;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.List;
//import java.util.Map;
//
///**
// * Controller for attendance metadata operations
// */
//@RestController
//@RequestMapping("/api/v1/attendance/metadata")
//@RequiredArgsConstructor
//@Slf4j
//public class AttendanceMetadataController extends BaseController {
//
//    private final AttendanceMetadataService metadataService;
//
//    /**
//     * Get metadata value for a specific department and attribute
//     * @param deptId Department ID
//     * @param attributeCode Attribute code
//     * @return Metadata response
//     */
//    @GetMapping("/{deptId}/{attributeCode}")
//    public ResponseEntity<ApiResponse<AttendanceMetadataResponse>> getMetadataValue(
//            @PathVariable Integer deptId,
//            @PathVariable AttendanceAttributeType attributeCode) {
//        log.info("Getting metadata value for Dept: {}, Attribute: {}", deptId, attributeCode);
//
//        AttendanceMetadataResponse response = metadataService.getMetadataValue(deptId, attributeCode);
//
//        if (response == null) {
//            log.warn("No metadata found for Dept: {}, Attribute: {}", deptId, attributeCode);
//            return notFound("No metadata found for the specified department and attribute");
//        }
//
//        log.info("Metadata retrieved successfully for Dept: {}, Attribute: {}", deptId, attributeCode);
//        return success(response, "Metadata retrieved successfully");
//    }
//
//    /**
//     * Get all metadata for a department
//     * @param deptId Department ID
//     * @return Map of attribute code to metadata response
//     */
//    @GetMapping("/{deptId}")
//    public ResponseEntity<ApiResponse<Map<AttendanceAttributeType, AttendanceMetadataResponse>>> getAllMetadataForDept(
//            @PathVariable Integer deptId) {
//        log.info("Getting all metadata for department: {}", deptId);
//
//        Map<AttendanceAttributeType, AttendanceMetadataResponse> response = metadataService.getAllMetadataForDept(deptId);
//
//        log.info("All metadata retrieved successfully for department: {}", deptId);
//        return success(response, "All metadata retrieved successfully");
//    }
//
//    /**
//     * Check if leave is allowed for a department
//     * @param deptId Department ID
//     * @return Boolean response
//     */
//    @GetMapping("/{deptId}/leave-allowed")
//    public ResponseEntity<ApiResponse<Boolean>> isLeaveAllowed(@PathVariable Integer deptId) {
//        log.info("Checking if leave is allowed for department: {}", deptId);
//
//        boolean isAllowed = metadataService.isLeaveAllowed(deptId);
//
//        log.info("Leave allowed check completed for department: {}", deptId);
//        return success(isAllowed, "Leave allowed check completed");
//    }
//
//    /**
//     * Check if OD is allowed for a department
//     * @param deptId Department ID
//     * @return Boolean response
//     */
//    @GetMapping("/{deptId}/od-allowed")
//    public ResponseEntity<ApiResponse<Boolean>> isOdAllowed(@PathVariable Integer deptId) {
//        log.info("Checking if OD is allowed for department: {}", deptId);
//
//        boolean isAllowed = metadataService.isOdAllowed(deptId);
//
//        log.info("OD allowed check completed for department: {}", deptId);
//        return success(isAllowed, "OD allowed check completed");
//    }
//
//    /**
//     * Check if WFH is allowed for a department
//     * @param deptId Department ID
//     * @return Boolean response
//     */
//    @GetMapping("/{deptId}/wfh-allowed")
//    public ResponseEntity<ApiResponse<Boolean>> isWfhAllowed(@PathVariable Integer deptId) {
//        log.info("Checking if WFH is allowed for department: {}", deptId);
//
//        boolean isAllowed = metadataService.isWfhAllowed(deptId);
//
//        log.info("WFH allowed check completed for department: {}", deptId);
//        return success(isAllowed);
//    }
//
//    /**
//     * Check if regularisation is allowed for a department
//     * @param deptId Department ID
//     * @return Boolean response
//     */
//    @GetMapping("/{deptId}/regularisation-allowed")
//    public ResponseEntity<ApiResponse<Boolean>> isRegularisationAllowed(@PathVariable Integer deptId) {
//        log.info("Checking if regularisation is allowed for department: {}", deptId);
//
//        boolean isAllowed = metadataService.isRegularisationAllowed(deptId);
//
//        log.info("Regularisation allowed check completed for department: {}", deptId);
//        return success(isAllowed);
//    }
//
//    /**
//     * Get leave credit cycle for a department
//     * @param deptId Department ID
//     * @return String response
//     */
//    @GetMapping("/{deptId}/leave-credit-cycle")
//    public ResponseEntity<ApiResponse<String>> getLeaveCreditCycle(@PathVariable Integer deptId) {
//        log.info("Getting leave credit cycle for department: {}", deptId);
//
//        String cycle = metadataService.getLeaveCreditCycle(deptId);
//
//        if (cycle == null) {
//            log.warn("No leave credit cycle found for department: {}", deptId);
//            return notFound("No leave credit cycle found for the specified department");
//        }
//
//        log.info("Leave credit cycle retrieved successfully for department: {}", deptId);
//        return success(cycle, "Leave credit cycle retrieved successfully");
//    }
//
//    /**
//     * Get total number of leaves for a department
//     * @param deptId Department ID
//     * @return BigDecimal response
//     */
//    @GetMapping("/{deptId}/total-leaves")
//    public ResponseEntity<ApiResponse<java.math.BigDecimal>> getTotalNumberOfLeaves(@PathVariable Integer deptId) {
//        log.info("Getting total number of leaves for department: {}", deptId);
//
//        java.math.BigDecimal totalLeaves = metadataService.getTotalNumberOfLeaves(deptId);
//
//        if (totalLeaves == null) {
//            log.warn("No total leaves configuration found for department: {}", deptId);
//            return notFound("No total leaves configuration found for the specified department");
//        }
//
//        log.info("Total leaves retrieved successfully for department: {}", deptId);
//        return success(totalLeaves, "Total leaves retrieved successfully");
//    }
//
//    /**
//     * Create or update metadata
//     * @param metadata Metadata entity to save
//     * @return Saved metadata entity
//     */
//    @PostMapping
//    public ResponseEntity<ApiResponse<EmpAttendanceMetadata>> createMetadata(
//            @RequestBody EmpAttendanceMetadata metadata) {
//        log.info("Creating/updating metadata for Dept: {}, Attribute: {}",
//                metadata.getDeptId(), metadata.getAttributeCode());
//
//        EmpAttendanceMetadata savedMetadata = metadataService.saveMetadata(metadata);
//
//        log.info("Metadata saved successfully with ID: {}", savedMetadata.getId());
//        return created(savedMetadata, "Metadata saved successfully");
//    }
//
//    /**
//     * Bulk create or update metadata
//     * @param metadataList List of metadata to save
//     * @return List of saved metadata
//     */
//    @PostMapping("/bulk")
//    public ResponseEntity<ApiResponse<List<EmpAttendanceMetadata>>> createMetadataBulk(
//            @RequestBody List<EmpAttendanceMetadata> metadataList) {
//        log.info("Bulk creating/updating {} metadata records", metadataList.size());
//
//        List<EmpAttendanceMetadata> savedMetadata = metadataService.saveMetadataBulk(metadataList);
//
//        log.info("Bulk metadata save completed successfully");
//        return created(savedMetadata, "Bulk metadata save completed successfully");
//    }
//
//    /**
//     * Get all metadata entities for a department
//     * @param deptId Department ID
//     * @return List of metadata entities
//     */
//    @GetMapping("/{deptId}/entities")
//    public ResponseEntity<ApiResponse<List<EmpAttendanceMetadata>>> getAllMetadataEntitiesForDept(
//            @PathVariable Integer deptId) {
//        log.info("Getting all metadata entities for department: {}", deptId);
//
//        List<EmpAttendanceMetadata> response = metadataService.getAllMetadataEntitiesForDept(deptId);
//
//        log.info("All metadata entities retrieved successfully for department: {}", deptId);
//        return success(response, "All metadata entities retrieved successfully");
//    }
//
//    /**
//     * Refresh cache for a specific department
//     * @param deptId Department ID
//     * @return Success response
//     */
//    @PostMapping("/{deptId}/refresh-cache")
//    public ResponseEntity<ApiResponse<String>> refreshCacheForDept(@PathVariable Integer deptId) {
//        log.info("Refreshing cache for department: {}", deptId);
//
//        metadataService.refreshCacheForDept(deptId);
//
//        log.info("Cache refreshed successfully for department: {}", deptId);
//        return success("Cache refreshed successfully", "Cache refreshed successfully");
//    }
//
//    /**
//     * Refresh all cache
//     * @return Success response
//     */
//    @PostMapping("/refresh-all-cache")
//    public ResponseEntity<ApiResponse<String>> refreshAllCache() {
//        log.info("Refreshing all attendance metadata cache");
//
//        metadataService.refreshAllCache();
//
//        log.info("All cache refreshed successfully");
//        return success("All cache refreshed successfully");
//    }
//}
//
//
