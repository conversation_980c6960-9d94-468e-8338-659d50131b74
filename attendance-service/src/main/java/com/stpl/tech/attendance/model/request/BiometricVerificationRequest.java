package com.stpl.tech.attendance.model.request;

import com.stpl.tech.attendance.dto.BiometricAdditionalImageDTO;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class BiometricVerificationRequest extends BiometricRegistrationRequest {
    @NotBlank(message = "Verification face image is required")
    private String verificationFace;

} 