package com.stpl.tech.attendance.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "external.sync")
public class ExternalSyncConfig {
    private String authorizationKey ;
    private String postUrl;
    private String dateTimeColumn;
    private String tableName;
    private String srnoColumn;
    private String userIdColumn;
    private String locationColumnName;
    private String joinsIfAny;
    

    // Sync configuration
    private int maxRetryAttempts = 3;
    private int retryDelayMinutes = 5;
    private int batchSize = 100;
    private boolean enableSync = true;
} 