package com.stpl.tech.attendance.service;

import java.time.LocalDateTime;
import org.springframework.web.multipart.MultipartFile;

public interface S3Service {
    /**
     * Upload base64 image to S3 and return CloudFront URL
     * @param base64Image The base64 encoded image
     * @param employeeId The employee ID
     * @param timestamp The timestamp for unique filename
     * @return The CloudFront URL of the uploaded image
     */

    String uploadBase64Image(String base64Image,String basePath, String key);
    
    /**
     * Upload leave document to S3 directly from MultipartFile (more efficient)
     * @param file The MultipartFile to upload
     * @param basePath The base path in S3
     * @param key The unique key for the document
     * @return The CloudFront URL of the uploaded document
     */
    String uploadLeaveDocument(MultipartFile file, String basePath, String key);
    
    String getCloudfrontUrl(String key);
    
    /**
     * Get CloudFront URL for leave documents using the dedicated domain
     * @param key The document key
     * @return The CloudFront URL for leave documents
     */
    String getLeaveDocumentCloudfrontUrl(String key);
    
    void deleteImage(String key);
} 