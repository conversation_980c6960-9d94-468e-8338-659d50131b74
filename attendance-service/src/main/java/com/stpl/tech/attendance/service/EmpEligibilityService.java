package com.stpl.tech.attendance.service;

import com.stpl.tech.attendance.enums.EligibilityType;
import com.stpl.tech.attendance.model.ApproverInfo;
import com.stpl.tech.attendance.model.TransferType;

import java.util.List;
import java.time.LocalDate;

public interface EmpEligibilityService {
    
    /**
     * Check if an employee is eligible to punch attendance at a specific unit
     * @param empId Employee ID
     * @param unitId Unit ID
     * @param city City
     * @return true if employee is eligible, false otherwise
     */
    boolean isEligibleForAttendance(String empId, String unitId);

    /**
     * Get all approvers for a specific eligibility type and unit
     * @param eligibilityType Type of eligibility (e.g., APPROVAL)
     * @param unitId Unit ID
     * @return List of employee IDs who are approvers
     */
    List<ApproverInfo> getApprovers(EligibilityType eligibilityType, Integer unitId);

    /**
     * Create attendance eligibility mapping for an employee at a specific unit
     * @param empId Employee ID
     * @param unitId Unit ID
     * @return true if mapping was created, false if it already exists
     */
    boolean createAttendanceEligibilityMapping(String empId, String unitId);

    /**
     * Update employee eligibility mapping based on transfer type
     * @param empId Employee ID
     * @param sourceUnitId Source unit ID (current unit)
     * @param destinationUnitId Destination unit ID (new unit)
     * @param transferType Type of transfer (PERMANENT, TEMPORARY, etc.)
     * @param startDate Start date of the transfer (required for temporary transfers)
     * @param endDate End date of the transfer (required for temporary transfers)
     */
    void updateEligibilityMapping(String empId, String sourceUnitId, String destinationUnitId, 
                                TransferType transferType, LocalDate startDate, LocalDate endDate);

    /**
     * Get units for employee based on eligibility mappings (CITY and REGION)
     * @param empId Employee ID
     * @return List of unit IDs that the employee can manage based on eligibility mappings
     */
    java.util.List<Integer> getUnitsFromEligibilityMappings(Integer empId);

    /**
     * Get employee IDs eligible for a specific unit based on eligibility mappings
     * This is a reverse cache that considers all mapping types (UNIT, CITY, REGION)
     * @param unitId Unit ID
     * @return List of employee IDs who are eligible for the unit
     */
    java.util.List<Integer> getEmployeesForUnit(Integer unitId);

    /**
     * Evict unit employees cache for a specific unit
     * @param unitId Unit ID
     */
    void evictUnitEmployeesCache(Integer unitId);

    /**
     * Evict all unit employees cache entries
     */
    void evictAllUnitEmployeesCache();

    /**
     * Update unit employees cache for a specific unit
     * @param unitId Unit ID
     */
    void updateUnitEmployeesCache(Integer unitId);

    /**
     * Update all unit employees cache entries
     */
    void updateAllUnitEmployeesCache();

    /**
     * Refresh unit employees cache for a specific unit
     * This method is specifically called after biometric registration approval
     * to ensure the cache is updated with the new employee eligibility
     * @param unitId Unit ID
     */
    void refreshUnitEmployeesCache(Integer unitId);

    /**
     * Manually refresh all unit employees cache
     * This method can be called via API or other services
     * @return CacheRefreshResult containing refresh statistics
     */
    com.stpl.tech.attendance.dto.CacheRefreshResult manuallyRefreshAllUnitEmployeesCache();

}