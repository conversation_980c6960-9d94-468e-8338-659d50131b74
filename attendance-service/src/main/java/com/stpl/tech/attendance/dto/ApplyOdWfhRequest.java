package com.stpl.tech.attendance.dto;

import com.mongodb.lang.Nullable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApplyOdWfhRequest {

    @Nullable
    private Integer empId; // (if null then take from jwt for self application)
    /**
     * List of OD/WFH entries with date, check-in, and check-out times
     */
    private List<OdWfhEntry> odWfhEntries;

    
    /**
     * Documents related to the OD/WFH application (file paths or document references)
     */
    @Nullable
    private String documents;

    //OD,WFH
    private String type;

    private Boolean overrideRequest;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OdWfhEntry {
        /**0
         * Date for the OD/WFH entry
         */
        private LocalDateTime date;
        
        /**
         * Check-in time for the day
         */
        private LocalDateTime checkIn;
        
        /**
         * Check-out time for the day
         */
        private LocalDateTime checkOut;

        private String comments;
    }
}