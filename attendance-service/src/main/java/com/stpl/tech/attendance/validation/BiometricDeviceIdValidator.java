package com.stpl.tech.attendance.validation;

import com.stpl.tech.attendance.util.BiometricDeviceIdUtil;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class BiometricDeviceIdValidator implements ConstraintValidator<ValidBiometricDeviceId, String> {

    private final BiometricDeviceIdUtil biometricDeviceIdUtil;

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null || value.isEmpty()) {
            return true; // Allow null/empty values, they will be generated
        }
        return biometricDeviceIdUtil.isValidBiometricDeviceId(value);
    }
} 