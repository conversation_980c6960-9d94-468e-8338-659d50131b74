package com.stpl.tech.attendance.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stpl.tech.attendance.approval.entity.ApprovalRequest;
import com.stpl.tech.attendance.approval.service.ApprovalEngineService;
import com.stpl.tech.attendance.approval.repository.ApprovalRequestRepository;
import com.stpl.tech.attendance.cache.service.UserCacheService;
import com.stpl.tech.attendance.constants.AppConstants;
import com.stpl.tech.attendance.context.JwtContext;
import com.stpl.tech.attendance.dto.EmployeeInfoDTO;
import com.stpl.tech.attendance.entity.EmployeeAttendanceRequest;
import com.stpl.tech.attendance.enums.ApprovalStatus;
import com.stpl.tech.attendance.enums.ApprovalType;
import com.stpl.tech.attendance.exception.BusinessException;
import com.stpl.tech.attendance.model.response.EmployeeAttendanceApproval;
import com.stpl.tech.attendance.model.ApproverInfo;
import com.stpl.tech.attendance.repository.EmployeeAttendanceRequestRepository;
import com.stpl.tech.attendance.service.EmployeeAttendanceApprovalService;
import com.stpl.tech.attendance.service.EmployeeAttendanceRequestDetailService;
import com.stpl.tech.attendance.util.DateTimeUtil;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import com.stpl.tech.attendance.entity.EmployeeAttendanceRequestDetail;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * Implementation of EmployeeAttendanceApprovalService
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EmployeeAttendanceApprovalServiceImpl implements EmployeeAttendanceApprovalService {

    private final EmployeeAttendanceRequestRepository employeeAttendanceRepository;
    private final ApprovalRequestRepository approvalRequestRepository;
    private final UserCacheService userCacheService;
    private final ObjectMapper objectMapper;
    private final EmployeeAttendanceRequestDetailService employeeAttendanceRequestDetailService;

    @Override
    public List<EmployeeAttendanceApproval> createEmployeeAttendanceApprovals(ApprovalRequest request) {
        // Find attendance records for this employee
        List<EmployeeAttendanceRequest> attendanceRecords = employeeAttendanceRepository.findByEmpId(request.getRequesterId().intValue());
        
        if (attendanceRecords.isEmpty()) {
            // Get employee info for the createdBy field from approval request
            EmployeeInfoDTO createdByEmployee = null;
            if (request.getCreatedBy() != null && !request.getCreatedBy().trim().isEmpty()) {
                try {
                    Integer createdByEmpId = Integer.valueOf(request.getCreatedBy());
                    createdByEmployee = getEmployeeInfo(createdByEmpId);
                } catch (NumberFormatException e) {
                    log.warn("Invalid createdBy employee ID: {}", request.getCreatedBy());
                }
            }
            
            EmployeeAttendanceApproval approval = EmployeeAttendanceApproval.builder()
                    .requestType(request.getRequestType().name())
                    .status(request.getStatus().name())
                    .reason(request.getMetadata())
                    .metadata(EmployeeAttendanceApproval.EmployeeAttendanceMetadata.builder()
                            .comments(request.getMetadata())
                            .createdBy(createdByEmployee)
                            .documents("") // empty for now
                            .build())
                    .build();
            
            return List.of(approval);
        }
        
        // For completed approvals, fetch records with APPROVED status from EMP_ATTENDANCE_REQUEST
        // For pending approvals, fetch records with PENDING status from EMP_ATTENDANCE_REQUEST
        String filterStatus = "APPROVED".equals(request.getStatus().name()) ? "APPROVED" : "PENDING";
        
        // Create EmployeeAttendanceApproval objects for each attendance record
        return attendanceRecords.stream()
                .filter(attendance -> attendance.getStatus().equals(filterStatus))
                .map(attendance -> {
                    // Determine request type based on attendance type
                    String requestType = determineRequestType(attendance.getType());
                    
                    // Get employee info for the createdBy field
                    EmployeeInfoDTO createdByEmployee = null;
                    if (attendance.getCreatedBy() != null && !attendance.getCreatedBy().trim().isEmpty()) {
                        try {
                            Integer createdByEmpId = Integer.valueOf(attendance.getCreatedBy());
                            createdByEmployee = getEmployeeInfo(createdByEmpId);
                        } catch (NumberFormatException e) {
                            log.warn("Invalid createdBy employee ID: {}", attendance.getCreatedBy());
                        }
                    }
                    
                    return EmployeeAttendanceApproval.builder()
                            .requestType(requestType)
                            .startTime(attendance.getStartTime())
                            .endTime(attendance.getEndTime())
                            .status(attendance.getStatus())
                            .reason(attendance.getReason())
                            .metadata(EmployeeAttendanceApproval.EmployeeAttendanceMetadata.builder()
                                    .comments(attendance.getComments())
                                    .startTime(attendance.getStartTime())
                                    .endTime(attendance.getEndTime())
                                    .createdBy(createdByEmployee)
                                    .documents("") // empty for now
                                    .build())
                            .build();
                })
                .collect(Collectors.toList());
    }

    @Override
    public String determineRequestType(String attendanceType) {
        if (attendanceType == null) {
            return "REGULARISATION_APPROVAL";
        }

        return switch (attendanceType.toUpperCase()) {
            case "LEAVE" -> "LEAVE_APPROVAL";
            case "COMP_OFF" -> "COMP_OFF_APPROVAL";
            case "LWP" -> "LWP_APPROVAL";
            case "OD" -> "OD_APPROVAL";
            case "WFH" -> "WFH_APPROVAL";
            default -> "REGULARISATION_APPROVAL";
        };
    }

    /**
     * Helper method to get employee information
     * @param empId The employee ID
     * @return EmployeeInfoDTO with employee details
     */
    private EmployeeInfoDTO getEmployeeInfo(Integer empId) {
        EmployeeBasicDetail employeeBasicDetail = userCacheService.getUserById(empId);
        return EmployeeInfoDTO.builder()
                .empId(empId)
                .employeeName(employeeBasicDetail.getName())
                .employeeCode(employeeBasicDetail.getEmployeeCode())
                .employeeDesignation(employeeBasicDetail.getDesignation())
                .build();
    }

    /**
     * Creates approval request for cancellation requests
     * @param requestId The ID of the request to be cancelled
     * @param attendanceRecord The attendance record to be cancelled
     * @return ApprovalRequest for cancellation
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public ApprovalRequest createCancellationApprovalRequest(Long requestId, EmployeeAttendanceRequest attendanceRecord) throws JsonProcessingException {
        try {
            // Get employee details to find reporting manager
            Integer empId = attendanceRecord.getEmpId();
            EmployeeBasicDetail employee = userCacheService.getUserById(empId);
            String leaveDayCount = "0";
            if(List.of(AppConstants.LEAVE, AppConstants.COMP_OFF).contains(attendanceRecord.getType())){
                // Find the approval request by referenceId (which is the attendance record ID)
                ApprovalRequest approvalRequests = approvalRequestRepository.findByReferenceIdAndRequestTypeAndStatus(requestId,ApprovalType.ATTENDANCE_REQUEST_CANCELLATION,List.of(ApprovalStatus.PENDING, ApprovalStatus.APPROVED));
                if (approvalRequests != null) {
                    throw new BusinessException("Cancellation request already exists for this attendance record");
                }
                List<EmployeeAttendanceRequestDetail> detailRecords = employeeAttendanceRequestDetailService.getDetailsByAttendanceRequestId(requestId);
                leaveDayCount = String.valueOf(employeeAttendanceRequestDetailService.calculateLeaveDeduction(detailRecords));
            }


            // Get reporting manager as approver
            List<ApproverInfo> approvers = getApproversForAttendanceRequest(employee.getLocCode().toString(), empId, "cancellation");

            if (approvers.isEmpty()) {
                throw new BusinessException("No reporting manager found for employee: " + empId);
            }

            // Create metadata map for cancellation request
            Map<String, String> metadata = new HashMap<>();
            metadata.put("leaveType", attendanceRecord.getType());
            metadata.put("leaveDayCount", leaveDayCount);
            metadata.put("attendanceRecordId", requestId.toString());
            if(attendanceRecord.getType().equalsIgnoreCase(AppConstants.LEAVE) || attendanceRecord.getType().equalsIgnoreCase(AppConstants.COMP_OFF)){
                String leaveDatesJson = createLeaveDatesJson(attendanceRecord.getId());
                metadata.put("leaveDates", leaveDatesJson);
            } else {
                metadata.put("toDate", attendanceRecord.getEndTime() != null ? attendanceRecord.getEndTime().toString() : "");
                metadata.put("fromDate", attendanceRecord.getStartTime() != null ? attendanceRecord.getStartTime().toString() : "");
            }
            metadata.put("isCancellationRequest", "true");
            metadata.put("reason", "Cancellation");
            metadata.put("approvers", approvers.stream().map(approver -> approver.getId().toString()).collect(Collectors.joining(",")));

            LocalDateTime now = DateTimeUtil.now();

            return ApprovalRequest.builder()
                    .requestType(ApprovalType.ATTENDANCE_REQUEST_CANCELLATION)
                    .requesterId(empId.longValue())
                    .status(ApprovalStatus.PENDING)
                    .currentStep(1)
                    .totalSteps(1)
                    .requestDate(now)
                    .createdDate(now)
                    .createdBy(empId.toString())
                    .referenceId(attendanceRecord.getId())
                    .unitId(Long.valueOf(JwtContext.getInstance().getUnitId()))
                    .metadata(objectMapper.writeValueAsString(metadata))
                    .build();
        } catch (JsonProcessingException e) {
            throw new BusinessException("Failed to create cancellation approval request metadata: " + e.getMessage());
        }
    }

    private String createLeaveDatesJson(Long attendanceRequestId) throws JsonProcessingException {
        List<EmployeeAttendanceRequestDetail> detailRecords =
                employeeAttendanceRequestDetailService.getDetailsByAttendanceRequestId(attendanceRequestId);

        // Create Map<LocalDateTime, String> for date-wise leave types
        Map<LocalDateTime, String> leaveDatesMap = new HashMap<>();
        for (EmployeeAttendanceRequestDetail detail : detailRecords) {
            leaveDatesMap.put(detail.getDate(), detail.getRequestType());
        }

        // Sort the dates to ensure they appear in chronological order in metadata
        // Create a sorted map using TreeMap to maintain date order
        Map<LocalDateTime, String> sortedLeaveDatesMap = new TreeMap<>(leaveDatesMap);

        // Convert the sorted Map to JSON string for metadata
        return objectMapper.writeValueAsString(sortedLeaveDatesMap);
    }

    /**
     * Get attendance history for the authenticated employee with pagination
     * @param pageable Pagination information
     * @return Page of EmployeeAttendanceRequest containing attendance records
     */
    @Override
    public Page<EmployeeAttendanceRequest> getAttendanceHistory(Pageable pageable) {
        try {
            // Get employee ID from JWT context
            Integer empId = JwtContext.getInstance().getUserId();
            if (empId == null) {
                throw new RuntimeException("Employee ID not found in authentication context");
            }
            
            log.info("Getting attendance history for employee: {} with pagination: page={}, size={}", 
                    empId, pageable.getPageNumber(), pageable.getPageSize());
            
            // Get attendance records with leave details for optimal LEAVE type handling
            Page<Object[]> attendanceRecordsWithDetails = 
                employeeAttendanceRepository.findByEmpIdWithLeaveDetails(empId, pageable);
            
            if (attendanceRecordsWithDetails.isEmpty()) {
                log.info("No attendance records found for employee: {}", empId);
                return new PageImpl<>(new ArrayList<>(), pageable, 0);
            }
            
            List<EmployeeAttendanceRequest> attendanceRecords = attendanceRecordsWithDetails.getContent().stream()
                .map(result -> (EmployeeAttendanceRequest) result[0])
                .collect(Collectors.toList());
            
            log.info("Successfully retrieved {} attendance records for employee: {} (showing {} of {} total)", 
                    attendanceRecords.size(), empId, attendanceRecords.size(), attendanceRecordsWithDetails.getTotalElements());
            
            // Create a new Page with the extracted objects
            return new PageImpl<>(attendanceRecords, pageable, attendanceRecordsWithDetails.getTotalElements());
            
        } catch (RuntimeException e) {
            // Re-throw RuntimeException as-is
            throw e;
        } catch (Exception e) {
            log.error("Error getting attendance history for employee", e);
            throw new RuntimeException("Failed to get attendance history: " + e.getMessage());
        }
    }

    /**
     * Construct metadata for different request types
     * @param request The attendance request
     * @return Constructed metadata string
     */
    public String constructMetadata(EmployeeAttendanceRequest request) {
        try {
            if ("LEAVE".equals(request.getType())) {
                return constructLeaveMetadata(request);
            } else {
                return constructOtherTypeMetadata(request);
            }
        } catch (Exception e) {
            log.error("Error constructing metadata for request ID: {}", request.getId(), e);
            return request.getReason() != null ? request.getReason() : "";
        }
    }

    /**
     * Construct metadata for LEAVE type requests
     * @param request The attendance request
     * @return Constructed leave metadata string
     */
    private String constructLeaveMetadata(EmployeeAttendanceRequest request) throws JsonProcessingException {
        // Get leave details from EMP_ATTENDANCE_REQUEST_DETAIL table
        List<EmployeeAttendanceRequestDetail> leaveDetails = 
            employeeAttendanceRepository.findLeaveDetailsByRequestId(request.getId());
        
        log.debug("Found {} leave detail records for request ID: {}", leaveDetails.size(), request.getId());
        
        Map<String, String> leaveDates = new HashMap<>();
        double totalDayCount = 0.0;
        
        for (EmployeeAttendanceRequestDetail detail : leaveDetails) {
            String dateKey = detail.getDate().toLocalDate().toString() + "T00:00";
            leaveDates.put(dateKey, detail.getRequestType());
            
            log.debug("Processing leave detail: date={}, requestType={}", dateKey, detail.getRequestType());
            
            // Calculate day count - using actual database values
            if ("FULL DAY".equals(detail.getRequestType())) {
                totalDayCount += 1.0;
                log.debug("Added 1.0 for FULL DAY");
            } else if ("FIRST HALF".equals(detail.getRequestType()) || "SECOND HALF".equals(detail.getRequestType())) {
                totalDayCount += 0.5;
                log.debug("Added 0.5 for HALF DAY");
            } else {
                log.debug("Unknown request type: {}, adding 0.0", detail.getRequestType());
            }
        }
        
        log.debug("Total leave day count calculated: {} for request ID: {}", totalDayCount, request.getId());
        
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("reason", request.getReason() != null ? request.getReason() : "Leave application");
        metadata.put("comments", request.getComments() != null ? request.getComments() : "");
        metadata.put("documents", request.getDocument() != null ? request.getDocument() : "");
        metadata.put("leaveType", request.getType());
        metadata.put("leaveDates", objectMapper.writeValueAsString(leaveDates));
        metadata.put("leaveDayCount", String.valueOf(totalDayCount));
        
        return objectMapper.writeValueAsString(metadata);
    }

    /**
     * Construct metadata for other type requests (OD, WFH, REGULARISATION)
     * @param request The attendance request
     * @return Constructed metadata string
     */
    private String constructOtherTypeMetadata(EmployeeAttendanceRequest request) throws JsonProcessingException {
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("reason", request.getReason() != null ? request.getReason() : "");
        metadata.put("toDate", request.getEndTime() != null ? request.getEndTime().toString() : "");
        metadata.put("comments", request.getComments() != null ? request.getComments() : "");
        metadata.put("fromDate", request.getStartTime() != null ? request.getStartTime().toString() : "");
        metadata.put("documents", request.getDocument() != null ? request.getDocument() : "");
        
        return objectMapper.writeValueAsString(metadata);
    }

//    /**
//     * Map attendance request type to ApprovalType for cancellation flows
//     */
//    private ApprovalType resolveCancellationApprovalType(String attendanceType) {
//        return switch (attendanceType.trim().toUpperCase()) {
//            case "LEAVE", "COMP_OFF", "LWP" -> ApprovalType.EMPLOYEE_LEAVE;
//            case "OD" -> ApprovalType.EMPLOYEE_OD;
//            case "WFH" -> ApprovalType.EMPLOYEE_WFH;
//            default -> ApprovalType.EMPLOYEE_REGULARISATION;
//        };
//    }

    @Override
    public List<ApproverInfo> getApproversForAttendanceRequest(String unitId, Integer empId, String approvalType) {
        log.info("Getting approvers for {} approval - Unit: {}, Employee: {}", approvalType, unitId, empId);

        try {
            // Get employee details to find reporting manager
            EmployeeBasicDetail employee = userCacheService.getUserById(empId);
            if (employee == null) {
                log.warn("Employee not found with ID: {}", empId);
                return new ArrayList<>();
            }

            // Get reporting manager as approver
            List<ApproverInfo> approvers = new ArrayList<>();
            if (employee.getReportingManagerId() != null) {
                // Add reporting manager with notification enabled
                approvers.add(new ApproverInfo(employee.getReportingManagerId(), true));
                
                // Add default approver with notification enabled
                approvers.add(new ApproverInfo(140199, false));
                
                // Add HR executive if available with notification enabled
                if(employee.getHrExecutive() != null){
                    approvers.add(new ApproverInfo(Integer.parseInt(employee.getHrExecutive()), false));
                }
                log.info("Found reporting manager {} for employee {}", employee.getReportingManagerId(), empId);
            } else {
                log.warn("No reporting manager found for employee: {}", empId);
            }

            return approvers;

        } catch (Exception e) {
            log.error("Error getting approvers for {} approval - Unit: {}, Employee: {}", approvalType, unitId, empId, e);
            return new ArrayList<>();
        }
    }

} 