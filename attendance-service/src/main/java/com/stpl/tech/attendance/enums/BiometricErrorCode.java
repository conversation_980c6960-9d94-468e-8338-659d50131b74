package com.stpl.tech.attendance.enums;

import lombok.Getter;

@Getter
public enum BiometricErrorCode {
    EMPLOYEE_NOT_FOUND("BIO_001", "Employee not found"),
    ALREADY_REGISTERED("BIO_002", "Employee already has an active biometric registration"),
    PENDING_REGISTRATION("BIO_012", "Pending biometric registration found for employee"),
    APPROVAL_METADATA_ERROR("BIO_003", "Failed to create approval request metadata"),
    REGISTRATION_PROCESSING_ERROR("BIO_004", "Failed to process biometric registration"),
    NO_ACTIVE_REGISTRATION("BIO_005", "No active biometric registration found for employee"),
    DEREGISTRATION_SERVICE_ERROR("BIO_006", "Failed to deregister from biometric service"),
    DEREGISTRATION_PROCESSING_ERROR("BIO_007", "Failed to process biometric deregistration"),
    INVALID_IMAGE_FORMAT("BIO_008", "Invalid image format provided"),
    IMAGE_UPLOAD_ERROR("BIO_009", "Failed to upload image to storage"),
    INVALID_BIOMETRIC_DATA("BIO_010", "Invalid biometric data provided"),
    FACE_ALREADY_EXISTS("BIO_011","Face already exists for another employee" ),
    BIOMETRIC_IN_PROGRESS("BIO_012","Biometric Not Yet Approved for employee");

    private final String code;
    private final String defaultMessage;

    BiometricErrorCode(String code, String defaultMessage) {
        this.code = code;
        this.defaultMessage = defaultMessage;
    }
} 