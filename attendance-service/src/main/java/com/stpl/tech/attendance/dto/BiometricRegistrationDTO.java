package com.stpl.tech.attendance.dto;

import com.stpl.tech.attendance.cache.service.UnitCacheService;
import com.stpl.tech.attendance.entity.BiometricRegistration;
import com.stpl.tech.attendance.enums.BiometricStatus;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BiometricRegistrationDTO implements Serializable {
    private Long id;
    private String empId;
    private String unitId;
    private String unitName;
    private String deviceId;
    private String biometricId;
    private String biometricUserId;
    private String imageUrl;
    private Double latitude;
    private Double longitude;
    private BiometricStatus status;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private String createdBy;
    private String updatedBy;

    public static BiometricRegistrationDTO fromEntity(BiometricRegistration entity, UnitCacheService unitCacheService) {
        if (entity == null) {
            return null;
        }
        return BiometricRegistrationDTO.builder()
                .id(entity.getId())
                .empId(entity.getEmpId())
                .unitId(entity.getUnitId())
                .unitName(unitCacheService.getUnitById(Integer.valueOf(entity.getUnitId())).getName())
                .deviceId(entity.getDeviceId())
                .biometricId(entity.getBiometricId())
                .biometricUserId(entity.getBiometricUserId())
                .imageUrl(entity.getImageUrl())
                .latitude(entity.getLatitude())
                .longitude(entity.getLongitude())
                .status(entity.getStatus())
                .createdAt(entity.getCreatedAt())
                .updatedAt(entity.getUpdatedAt())
                .createdBy(entity.getCreatedBy())
                .updatedBy(entity.getUpdatedBy())
                .build();
    }

    public BiometricRegistration toEntity() {
        BiometricRegistration entity = new BiometricRegistration();
        entity.setId(this.id);
        entity.setEmpId(this.empId);
        entity.setUnitId(this.unitId);
        entity.setDeviceId(this.deviceId);
        entity.setBiometricId(this.biometricId);
        entity.setBiometricUserId(this.biometricUserId);
        entity.setImageUrl(this.imageUrl);
        entity.setLatitude(this.latitude);
        entity.setLongitude(this.longitude);
        entity.setStatus(this.status);
        entity.setCreatedAt(this.createdAt);
        entity.setUpdatedAt(this.updatedAt);
        entity.setCreatedBy(this.createdBy);
        entity.setUpdatedBy(this.updatedBy);
        return entity;
    }
} 