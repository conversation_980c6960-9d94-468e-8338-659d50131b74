package com.stpl.tech.attendance.validation;

import com.stpl.tech.attendance.dto.TransferRequestDTO;
import com.stpl.tech.attendance.enums.TransferReason;
import com.stpl.tech.attendance.exception.TransferException;
import com.stpl.tech.attendance.exception.TransferErrorCode;
import com.stpl.tech.attendance.model.TransferRequest;
import com.stpl.tech.attendance.model.TransferRequestStatus;
import com.stpl.tech.attendance.model.TransferType;
import com.stpl.tech.attendance.repository.TransferRequestRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Component
@RequiredArgsConstructor
public class TransferValidator {
    
    private final TransferRequestRepository transferRequestRepository;
    
    // Valid transfer reasons are now managed by TransferReason enum
    
    public void validate(TransferRequestDTO request) {
        validateDates(request);
        validateUnits(request);
        validateEmployee(request);
        validateNoPendingTransfers(request);
        validateTransferReason(request);
    }
    
    private void validateDates(TransferRequestDTO request) {
        if (request.getStartDate() == null) {
            throw new TransferException(TransferErrorCode.INVALID_START_DATE);
        }
        
        if (request.getTransferType() == TransferType.TEMPORARY && request.getEndDate() == null) {
            throw new TransferException(TransferErrorCode.INVALID_END_DATE);
        }
        
        if (request.getEndDate() != null && request.getEndDate().isBefore(request.getStartDate())) {
            throw new TransferException(TransferErrorCode.INVALID_DATE_RANGE);
        }
    }
    
    private void validateUnits(TransferRequestDTO request) {
        if (Objects.nonNull(request.getSourceUnitId()) &&  request.getSourceUnitId().equals(request.getDestinationUnitId())) {
            throw new TransferException(TransferErrorCode.SAME_UNIT_TRANSFER);
        }
    }
    
    private void validateEmployee(TransferRequestDTO request) {
        // Add employee validation logic here
        // For example: check if employee exists, is active, etc.
    }
    
    private void validateNoPendingTransfers(TransferRequestDTO request) {
        // Check if employee already has a pending transfer request
        List<TransferRequest> pendingTransfers = transferRequestRepository.findByEmpIdAndStatus(
            request.getEmpId(), 
            TransferRequestStatus.PENDING
        );
        
        if (!pendingTransfers.isEmpty()) {
            throw new TransferException(TransferErrorCode.PENDING_TRANSFER_EXISTS);
        }
    }
    
    private void validateTransferReason(TransferRequestDTO request) {
        if (request.getReason() == null || request.getReason().trim().isEmpty()) {
            throw new TransferException(TransferErrorCode.INVALID_TRANSFER_REASON);
        }
        
        if (!TransferReason.isValid(request.getReason().trim())) {
            throw new TransferException(TransferErrorCode.INVALID_TRANSFER_REASON);
        }
    }
} 