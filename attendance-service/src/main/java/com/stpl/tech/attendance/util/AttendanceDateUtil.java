package com.stpl.tech.attendance.util;

import com.stpl.tech.attendance.config.AttendanceConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Component
public class AttendanceDateUtil {

    @Autowired
    private AttendanceConfig attendanceConfig;

    /**
     * Calculate attendance date based on 4 AM to 3:59 AM business day logic
     * @param punchTime The punch time to calculate attendance date for
     * @return The attendance date
     */
    public LocalDate getAttendanceDate(LocalDateTime punchTime) {
        if (punchTime.getHour() < attendanceConfig.getDayStartHour()) {
            return punchTime.toLocalDate().minusDays(1);
        }
        return punchTime.toLocalDate();
    }

    /**
     * Calculate current attendance date based on current time
     * @return Current attendance date
     */
    public LocalDate getCurrentAttendanceDate() {
        LocalDateTime now = LocalDateTime.now();
        return getAttendanceDate(now);
    }

    /**
     * Calculate attendance date for a given timestamp
     * @param timestamp The timestamp to calculate attendance date for
     * @return The attendance date
     */
    public LocalDate getAttendanceDateForTimestamp(java.sql.Timestamp timestamp) {
        LocalDateTime dateTime = timestamp.toLocalDateTime();
        return getAttendanceDate(dateTime);
    }
} 