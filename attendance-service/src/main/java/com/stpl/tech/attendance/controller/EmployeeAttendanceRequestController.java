package com.stpl.tech.attendance.controller;

import com.stpl.tech.attendance.config.EnvironmentProperties;
import com.stpl.tech.attendance.constants.ApiConstants;
import com.stpl.tech.attendance.dto.ApplyLeaveRequest;
import com.stpl.tech.attendance.dto.ApplyLeaveResponse;
import com.stpl.tech.attendance.dto.ApplyOdWfhRequest;
import com.stpl.tech.attendance.dto.ApplyOdWfhResponse;
import com.stpl.tech.attendance.dto.ApplyRegularisationRequest;
import com.stpl.tech.attendance.dto.ApplyRegularisationResponse;
import com.stpl.tech.attendance.dto.CancelRequestRequest;
import com.stpl.tech.attendance.dto.CancelRequestResponse;
import com.stpl.tech.attendance.dto.UploadLeaveDocumentRequest;
import com.stpl.tech.attendance.dto.UploadLeaveDocumentResponse;
import com.stpl.tech.attendance.dto.ApplyWeekOffRequest;
import com.stpl.tech.attendance.dto.ApplyWeekOffResponse;
import com.stpl.tech.attendance.model.response.ApiResponse;
import com.stpl.tech.attendance.service.EmployeeAttendanceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@Tag(name = "Employee Attendance", description = "Employee attendance management APIs")
@Slf4j
@RestController
@RequestMapping(ApiConstants.Paths.ATTENDANCE + "/employee-attendance-request")
@RequiredArgsConstructor
public class EmployeeAttendanceRequestController extends BaseController {

    private final EmployeeAttendanceService employeeAttendanceService;
    private final EnvironmentProperties environmentProperties;

    @Operation(summary = "Apply leave for employee")
    @PostMapping("/applyLeave")
    public ResponseEntity<ApiResponse<ApplyLeaveResponse>> applyLeave(
            @Valid @RequestBody ApplyLeaveRequest request) {
        log.info("Applying leave for employee with {} dates", request.getLeaveDates().size());
        
        ApplyLeaveResponse response = employeeAttendanceService.applyLeave(request);
        return success(response);
    }
    
    @Operation(summary = "Apply OD/WFH for employee")
    @PostMapping("/applyOdWfh")
    public ResponseEntity<ApiResponse<ApplyOdWfhResponse>> applyOdWfh(
            @Valid @RequestBody ApplyOdWfhRequest request) {
        log.info("Applying OD/WFH for employee with {} entries", request.getOdWfhEntries().size());
        
        ApplyOdWfhResponse response = employeeAttendanceService.applyOdWfh(request);
        return success(response);
    }
    
    @Operation(summary = "Apply regularisation for employee")
    @PostMapping("/applyRegularisation")
    public ResponseEntity<ApiResponse<ApplyRegularisationResponse>> applyRegularisation(
            @Valid @RequestBody ApplyRegularisationRequest request) {
        log.info("Applying regularisation for employee on date: {}", request.getDate());
        
        ApplyRegularisationResponse response = employeeAttendanceService.applyRegularisation(request);
        return success(response);
    }

    @Operation(summary = "Cancel attendance request", description = "Cancel an attendance request")
    @PostMapping("/cancelRequest")
    public ResponseEntity<ApiResponse<CancelRequestResponse>> cancelRequest(
            @Valid @RequestBody CancelRequestRequest request) {
        log.info("Cancelling attendance request with ID: {} with requireApproval: {}", 
                request.getRequestId(), request.getRequireApproval());
        
        CancelRequestResponse response = employeeAttendanceService.cancelRequest(request);
        return success(response);
    }

    @Operation(summary = "Upload leave document")
    @PostMapping(value = "/uploadLeaveDocument", consumes = "multipart/form-data")
    public ResponseEntity<ApiResponse<UploadLeaveDocumentResponse>> uploadLeaveDocument(
            @RequestParam("file") MultipartFile file) {
        log.info("Uploading leave document with file: {}", file.getOriginalFilename());
        if(file.getSize() > environmentProperties.getMaxFileSize()){
            throw new RuntimeException("File size exceeds maximum limit of 2MB");
        }
        UploadLeaveDocumentRequest request = UploadLeaveDocumentRequest.builder()
                .file(file)
                .build();
        UploadLeaveDocumentResponse response = employeeAttendanceService.uploadLeaveDocument(request);
        return success(response);
    }
    
    @Operation(summary = "Apply week off for employee")
    @PostMapping("/applyWeekOff")
    public ResponseEntity<ApiResponse<ApplyWeekOffResponse>> applyWeekOff(
            @Valid @RequestBody ApplyWeekOffRequest request) {
        log.info("Applying week off for employee with date: {} and forUpcomingWeeks: {}", 
                request.getDate(), request.isForUpcomingWeeks());
        
        ApplyWeekOffResponse response = employeeAttendanceService.applyWeekOff(request);
        return success(response);
    }

} 