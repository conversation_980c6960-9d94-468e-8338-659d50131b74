package com.stpl.tech.attendance.service;

/**
 * Service interface for async request tracking
 * Provides methods to track request processing status without blocking the main flow
 */
public interface RequestTrackingService {

    /**
     * Start tracking a new request asynchronously
     * Creates initial tracking record with request start time
     */
    void startTrackingAsync(String requestId, Integer unitId, String apiIdentifier);

    /**
     * Mark request as successfully completed asynchronously
     * Updates completion time and preserves any existing reference ID
     */
    void markSuccessAsync(String requestId);

    /**
     * Mark request as failed asynchronously
     * Updates completion time and error details, preserves any existing reference ID
     */
    void markFailedAsync(String requestId, String errorMessage, String errorCode);

    /**
     * Update reference ID asynchronously
     * Useful for linking tracking record to business entities
     */
    void updateReferenceIdAsync(String requestId, String referenceId);

    /**
     * Complete request with reference ID in a single atomic operation
     * Prevents race conditions between completion and reference ID updates
     */
    void completeWithReferenceIdAsync(String requestId, String referenceId);

    /**
     * Complete request with reference ID using pessimistic locking
     * Most robust method - prevents any concurrent modifications
     */
    void completeWithReferenceIdWithLock(String requestId, String referenceId);

    /**
     * Complete request with reference ID using optimistic locking
     * Retries on version conflicts
     */
    void completeWithReferenceIdOptimistic(String requestId, String referenceId);
}
