package com.stpl.tech.attendance.cache.service.impl;

import com.stpl.tech.attendance.cache.service.BiometricCacheOperations;
import com.stpl.tech.attendance.cache.service.UnitCacheService;
import com.stpl.tech.attendance.dto.BiometricRegistrationDTO;
import com.stpl.tech.attendance.entity.BiometricRegistration;
import com.stpl.tech.attendance.enums.BiometricStatus;
import com.stpl.tech.attendance.repository.BiometricRegistrationRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * Implementation of BiometricCacheOperations that handles Spring cache annotations.
 * This service is separate from BiometricCacheImpl to avoid Spring proxy issues.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BiometricCacheOperationsImpl implements BiometricCacheOperations {

    private final BiometricRegistrationRepository biometricRegistrationRepository;
    private final UnitCacheService unitCacheService;

    @Override
    @CachePut(value = "biometricRegistrations", key = "#empId")
    public BiometricRegistrationDTO updateBiometricRegistration(String empId) {
        log.debug("Updating biometric registration cache for employee: {}", empId);
        Optional<BiometricRegistration> registrationOptional = biometricRegistrationRepository.findByEmpIdAndStatusIn(
                empId, List.of(BiometricStatus.APPROVED, BiometricStatus.PENDING));
        return registrationOptional.map(biometricRegistration -> 
                BiometricRegistrationDTO.fromEntity(biometricRegistration, unitCacheService))
                .orElse(null);
    }

    @Override
    @CacheEvict(value = "biometricRegistrations", key = "#empId")
    public void removeBiometricRegistration(String empId) {
        log.debug("Removing biometric registration from cache for employee: {}", empId);
        // The @CacheEvict annotation handles the cache removal
    }
} 