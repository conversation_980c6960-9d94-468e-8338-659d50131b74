package com.stpl.tech.attendance.cache.service.impl;

import com.hazelcast.core.HazelcastInstance;
import com.stpl.tech.attendance.cache.service.ExternalAPITokenCache;
import com.stpl.tech.attendance.cache.constants.CacheConstants;
import com.stpl.tech.attendance.util.ACLUtil;
import jakarta.annotation.PostConstruct;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Map;

@Log4j2
@Service
public class ExternalAPITokenCacheImpl implements ExternalAPITokenCache {

    @Autowired
    @Qualifier(value = "MasterHazelCastInstance")
    private HazelcastInstance instance;
    
    @Autowired
    private ACLUtil aclUtil;
    
    private Map<String, com.stpl.tech.master.core.external.partner.service.impl.ExternalAPIToken> tokenMap;

    @PostConstruct
    public void createCache() {
        log.info("POST-CONSTRUCT ExternalAPITokenCache - STARTED");
        long time = System.currentTimeMillis();
        tokenMap = instance.getMap(CacheConstants.EXT_API_TOKENS);
        log.info("POST-CONSTRUCT ExternalAPITokenCache took {} ms", System.currentTimeMillis() - time);
    }

    @Override
    public boolean isValidKey(String key) {
        return tokenMap.containsKey(key);
    }

    @Override
    public boolean checkAccess(String key, String requestUrl, String requestMethod) {
        com.stpl.tech.master.core.external.partner.service.impl.ExternalAPIToken externalAPIToken = tokenMap.get(key);
        return aclUtil.checkPermission(externalAPIToken.getAccessAPIs(),
                aclUtil.convertURIToModule(requestUrl), requestMethod);
    }
} 