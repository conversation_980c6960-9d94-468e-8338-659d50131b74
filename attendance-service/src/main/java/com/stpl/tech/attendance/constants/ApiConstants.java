package com.stpl.tech.attendance.constants;

import java.math.BigDecimal;

public final class ApiConstants {
    private ApiConstants() {
        // Private constructor to prevent instantiation
    }

    // Base API path without version
    public static final String API_BASE = "/api";

    // Universal Shift Constants
    public static final Integer UNIVERSAL_SHIFT_ID = 4; // Universal shift ID
    public static final BigDecimal DEFAULT_UNIVERSAL_SHIFT_HOURS = BigDecimal.valueOf(9.0); // Default 9 hours for universal shift
    public static final Integer CAFE_MANAGER_DESIGNATION_ID = 1003; // Default 9 hours for universal shift
    public static  final Integer DEFAULT_UNIT_ID = 26091;
    public static  final Integer CAFE_DEPARTMENT_ID = 26091;

    // Version constants for different controllers
    public static final class Versions {
        private Versions() {}
        
        public static final String V1 = "v1";
        public static final String V2 = "v2";
        public static final String V3 = "v3";
    }

    // API paths for different controllers
    public static final class Paths {

        private Paths() {}

        public static final String ATTENDANCE = API_BASE + "/" + Versions.V1 + "/attendance";
        public static final String APPROVALS = API_BASE + "/" + Versions.V1 + "/approvals";
        public static final String BIOMETRIC = API_BASE + "/" + Versions.V1 + "/biometric";
        public static final String METADATA = API_BASE + "/" + Versions.V1 + "/metadata";
        public static final String NOTIFICATIONS = API_BASE + "/" + Versions.V1 + "/notifications";
        public static final String TRANSFER = API_BASE + "/" + Versions.V1 + "/transfers";
        public static final String ROSTER = API_BASE + "/" + Versions.V1 + "/roster";
        public static final String UNIT_ATTENDANCE_ANALYTICS = API_BASE + "/" + Versions.V1 + "/unit-attendance-analytics";
        public static final String EMPLOYEE_SHIFT_INSTANCES = API_BASE + "/" + Versions.V1 + "/employee-shift-instances";
    }

    // Helper method to build versioned path
    public static String buildPath(String version, String resource) {
        return API_BASE + "/" + version + "/" + resource;
    }
} 