package com.stpl.tech.attendance.context;

import lombok.experimental.UtilityClass;

/**
 * Context to store reference ID for request tracking
 * This allows the @RequestTrackingAspect to automatically update
 * the tracking record with both completion time and reference ID
 */
@UtilityClass
public class RequestTrackingContext {
    
    private static final ThreadLocal<String> referenceIdHolder = new ThreadLocal<>();
    
    /**
     * Set the reference ID for the current request
     * @param referenceId The business reference ID (e.g., attendance request ID)
     */
    public static void setReferenceId(String referenceId) {
        referenceIdHolder.set(referenceId);
    }
    
    /**
     * Get the reference ID for the current request
     * @return The reference ID or null if not set
     */
    public static String getReferenceId() {
        return referenceIdHolder.get();
    }
    
    /**
     * Clear the reference ID for the current request
     * Should be called after the aspect processes the tracking
     */
    public static void clearReferenceId() {
        referenceIdHolder.remove();
    }
}
