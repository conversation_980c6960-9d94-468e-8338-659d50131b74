package com.stpl.tech.attendance.dto.RosteringDto;

import com.stpl.tech.attendance.enums.FilterDataType;
import com.stpl.tech.attendance.validation.ValidFilterValue;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GenericFilterRequestDTO {
    
    @Valid
    @ValidFilterValue
    private Map<String, FilterValue> filters;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FilterValue {
        @NotNull(message = "Data type is required")
        private FilterDataType dataType;
        
        @NotEmpty(message = "Values cannot be empty")
        private List<String> values;
        
        @NotNull(message = "Operator is required")
        private String operator; // "IN", "EQUALS", "BETWEEN", "GREATER_THAN", "LESS_THAN", etc.
    }
} 