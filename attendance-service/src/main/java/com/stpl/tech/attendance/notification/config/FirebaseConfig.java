package com.stpl.tech.attendance.notification.config;

import com.google.auth.oauth2.GoogleCredentials;
import com.google.firebase.FirebaseApp;
import com.google.firebase.FirebaseOptions;
import com.google.firebase.messaging.FirebaseMessaging;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.util.ResourceUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;

@Slf4j
@Configuration
@Primary
public class FirebaseConfig {

    @Value("${firebase.credentials.path}")
    private String firebaseCredentialsPath;

    @Bean(name = "notificationFirebaseApp")
    @Primary
    public FirebaseApp firebaseApp() {
        try {
            if (FirebaseApp.getApps().isEmpty()) {
                log.info("Initializing Firebase with credentials from: {}", firebaseCredentialsPath);
                
                // Try multiple ways to load the file
                InputStream serviceAccount;
                try {
                    // First try as classpath resource
                    Resource resource = new ClassPathResource(firebaseCredentialsPath);
                    if (resource.exists()) {
                        log.info("Found credentials file as classpath resource");
                        serviceAccount = resource.getInputStream();
                    } else {
                        // Then try as file
                        File file = ResourceUtils.getFile("classpath:" + firebaseCredentialsPath);
                        log.info("Found credentials file as file: {}", file.getAbsolutePath());
                        serviceAccount = new FileInputStream(file);
                    }
                } catch (IOException e) {
                    log.error("Failed to load credentials file: {}", e.getMessage());
                    throw e;
                }

                GoogleCredentials credentials = GoogleCredentials.fromStream(serviceAccount);
                log.info("Successfully loaded Firebase credentials");

                FirebaseOptions options = FirebaseOptions.builder()
                    .setCredentials(credentials)
                    .build();
                log.info("Successfully built Firebase options");

                return FirebaseApp.initializeApp(options);
            }
            log.info("Firebase app already initialized, returning existing instance");
            return FirebaseApp.getInstance();
        } catch (IOException e) {
            log.error("Error initializing Firebase: {}", e.getMessage(), e);
            throw new RuntimeException("Error initializing Firebase", e);
        }
    }

    @Bean(name = "notificationFirebaseMessaging")
    @Primary
    public FirebaseMessaging firebaseMessaging(FirebaseApp firebaseApp) {
        log.info("Creating Firebase Messaging instance");
        return FirebaseMessaging.getInstance(firebaseApp);
    }
} 