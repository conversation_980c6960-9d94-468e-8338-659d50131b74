package com.stpl.tech.attendance.util;

import java.time.LocalDateTime;

public class AppUtils {
    
    private AppUtils() {
        // Private constructor to prevent instantiation
    }
    
    public static LocalDateTime getCurrentDateTime() {
        return DateTimeUtil.now();
    }

    /**
     * Converts a string status to boolean
     * @param status The status string to convert
     * @return true if status is "Y" or "YES", false otherwise
     */
    public static boolean getStatus(String status) {
        if (status == null) {
            return false;
        }
        return status.equalsIgnoreCase("Y") || status.equalsIgnoreCase("YES");
    }
} 