package com.stpl.tech.attendance.entity;

import lombok.Getter;

public class EmployeeAttendanceEnum {

    // ---------------------------------------
    // Regularisation reasons for attendance override
    // ---------------------------------------
    @Getter
    public enum RegularisationReason {
        FORGOT_TO_PUNCH("FORGOT TO PUNCH"),
        TECHNICAL_ISSUE("TECHNICAL ISSUE"),
        SUPPORT_TO_OTHER_CAFE("SUPPORT TO OTHER CAFE"),
        OTHER_ISSUE("OTHER ISSUE");

        private final String value;

        RegularisationReason(String value) {
            this.value = value;
        }

    }

    // ---------------------------------------
    // Day types for leave applications
    // ---------------------------------------
    @Getter
    public enum DayType {
        FULL_DAY("FULL DAY"),
        FIRST_HALF("FIRST HALF"),
        SECOND_HALF("SECOND HALF");

        private final String value;

        DayType(String value) {
            this.value = value;
        }

    }

    // ---------------------------------------
    // OD/WFH options for attendance override
    // ---------------------------------------
    @Getter
    public enum AttendanceType {
        OD("OD"),
        WFH("WFH");

        private final String value;

        AttendanceType(String value) {
            this.value = value;
        }

    }

    // ---------------------------------------
    // Leave types for leave applications
    // ---------------------------------------
    @Getter
    public enum LeaveType {
        LEAVE("LEAVE"),
        COMP_OFF("COMP_OFF"),
        LWP("LWP");

        private final String value;

        LeaveType(String value) {
            this.value = value;
        }

    }
}

