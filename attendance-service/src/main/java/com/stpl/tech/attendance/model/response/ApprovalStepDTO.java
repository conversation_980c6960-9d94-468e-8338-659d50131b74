package com.stpl.tech.attendance.model.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.stpl.tech.attendance.approval.entity.ApprovalStep;
import com.stpl.tech.attendance.approval.entity.SubApprovalStep;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ApprovalStepDTO {
    private Long id;
    private Integer stepNumber;
    private String stepName;
    private String stepType;
    private String status;
    private String taskId;
    private Integer requiredApprovals;
    private Integer currentApprovals;
    private Boolean allowPartialApproval;
    private Integer minimumApprovals;
    private Boolean requireAllApprovals;
    private Integer totalApprovers;
    private Long lastApproverId;
    private String lastApproverName;
    private LocalDateTime lastApprovalDate;
    private LocalDateTime createdDate;
    private String createdBy;
    private LocalDateTime updatedDate;
    private String updatedBy;
    private List<SubApprovalStepDTO> subSteps;

    public static ApprovalStepDTO fromEntity(ApprovalStep step) {
        ApprovalStepDTO dto = new ApprovalStepDTO();
        dto.setId(step.getId());
        dto.setStepNumber(step.getStepNumber());
        dto.setStepName(step.getStepName());
        dto.setStepType(step.getStepType().name());
        dto.setStatus(step.getStatus().name());
        dto.setTaskId(step.getTaskId());
        dto.setRequiredApprovals(step.getRequiredApprovals());
        dto.setCurrentApprovals(step.getCurrentApprovals());
        dto.setAllowPartialApproval(step.getAllowPartialApproval());
        dto.setMinimumApprovals(step.getMinimumApprovals());
        dto.setRequireAllApprovals(step.getRequireAllApprovals());
        dto.setTotalApprovers(step.getTotalApprovers());
        dto.setLastApproverId(step.getLastApproverId());
        dto.setLastApprovalDate(step.getLastApprovalDate());
        dto.setCreatedDate(step.getCreatedDate());
        dto.setCreatedBy(step.getCreatedBy());
        dto.setUpdatedDate(step.getUpdatedDate());
        dto.setUpdatedBy(step.getUpdatedBy());
        
        if (step.getSubSteps() != null) {
            dto.setSubSteps(step.getSubSteps().stream()
                .map(SubApprovalStepDTO::fromEntity)
                .collect(Collectors.toList()));
        }
        
        return dto;
    }
} 