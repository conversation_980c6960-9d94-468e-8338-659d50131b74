package com.stpl.tech.attendance.dto;

import com.stpl.tech.attendance.model.TransferType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmployeeTransferMetadataDTO {
    private String empId;
    private String empName;
    private String empCode;
    private List<UnitEligibilityDTO> attendanceEligibleUnits;
    private List<TransferReasonDTO> transferReasons;
    private List<TransferType> transferTypes;
} 