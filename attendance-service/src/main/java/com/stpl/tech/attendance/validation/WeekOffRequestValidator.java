package com.stpl.tech.attendance.validation;

import com.stpl.tech.attendance.cache.service.UserCacheService;
import com.stpl.tech.attendance.constants.AppConstants;
import com.stpl.tech.attendance.dto.ApplyWeekOffRequest;
import com.stpl.tech.attendance.entity.EmployeeAttendanceRequest;
import com.stpl.tech.attendance.exception.BusinessException;
import com.stpl.tech.attendance.repository.EmployeeAttendanceRequestRepository;
import com.stpl.tech.attendance.service.AttendanceMetadataService;
import com.stpl.tech.attendance.service.impl.AttendanceRequestUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * Validator for week-off request validation.
 * Handles all week-off-specific validation logic including date overlaps,
 * and employee eligibility validations.
 */
@Slf4j
@Component
public class WeekOffRequestValidator extends AttendanceRequestValidator<ApplyWeekOffRequest> {


    protected WeekOffRequestValidator(AttendanceRequestUtil attendanceRequestUtil, UserCacheService userCacheService, AttendanceMetadataService attendanceMetadataService, EmployeeAttendanceRequestRepository employeeAttendanceRequestRepository) {
        super(attendanceRequestUtil, userCacheService, attendanceMetadataService, employeeAttendanceRequestRepository);
    }

    @Override
    public void validate(ApplyWeekOffRequest request) {
        log.info("Validating week-off request for employee");
        
        // Get employee ID from JWT context for validation
        Integer empId = validateEmployeeContext();
        
        // Validate basic request structure
        validateBasicRequest(request);
        
        // NEW: Validate payroll processing date for week-off date
        validatePayrollProcessingDates(Collections.singletonList(request.getDate()));

        // Validate employee eligibility for week-off
        validateFromMetadata(empId, AppConstants.WEEK_OFF);
        
        // Check if any dates already have existing entries of any type
        List<LocalDateTime> datesToCheck = Collections.singletonList(request.getDate());
        validateNoDateRangeOverlaps(empId, datesToCheck);
        
        // For week-off requests, also check if any existing requests overlap with the date
        validateLeaveDateRangeOverlaps(empId, datesToCheck);
        
        log.info("Week-off request validation completed successfully for employee: {}", empId);
    }
    
    /**
     * Validates the basic structure of the week-off request
     * 
     * @param request The week-off request to validate
     * @throws BusinessException if basic validation fails
     */
    private void validateBasicRequest(ApplyWeekOffRequest request) {
        if (request.getDate() == null) {
            log.error("No week-off date provided in request");
            throw new BusinessException("Week-off date is required");
        }
        // if this type already exists then throw error
        EmployeeAttendanceRequest existingRequest = employeeAttendanceRequestRepository.findByEmpIdAndDateAndType(request.getEmpId(), request.getDate(), AppConstants.WEEK_OFF);
        if (existingRequest != null) {
            log.error("Week-off already exists for the date: {}", request.getDate());
            throw new BusinessException("Week-off already exists for the date: " + request.getDate());
        }

        if (request.getEmpId() == null) {
            log.error("Employee ID is required for week-off request");
            throw new BusinessException("Employee ID is required");
        }
    }
}
