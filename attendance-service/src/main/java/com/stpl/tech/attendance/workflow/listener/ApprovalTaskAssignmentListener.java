package com.stpl.tech.attendance.workflow.listener;

import com.stpl.tech.attendance.approval.entity.ApprovalStep;
import com.stpl.tech.attendance.approval.repository.ApprovalStepRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.TaskService;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.identitylink.api.IdentityLink;
import org.flowable.task.service.delegate.DelegateTask;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Set;

@Slf4j
@Component
@RequiredArgsConstructor
public class ApprovalTaskAssignmentListener implements TaskListener {

    private final ApprovalStepRepository approvalStepRepository;
    private final TaskService taskService;

    @Override
    public void notify(DelegateTask delegateTask) {
        log.info("Task assignment event received for task: {}", delegateTask.getId());
        
        // Get the process variables
        String requestId = (String) delegateTask.getVariable("requestId");
        Integer currentStep = (Integer) delegateTask.getVariable("currentStep");
        
        // Get the approval step
        ApprovalStep step = approvalStepRepository.findByRequestIdAndStepNumber(Long.valueOf(requestId), currentStep)
            .orElseThrow(() -> new RuntimeException("Approval step not found for request: " + requestId));
            
        // Get the candidate groups
        Set<IdentityLink> identityLinks = delegateTask.getCandidates();
        if (identityLinks == null || identityLinks.isEmpty()) {
            log.warn("No candidate groups found for task: {}", delegateTask.getId());
            return;
        }
        
        // Get the role from the first candidate group
        String role = identityLinks.stream()
            .map(IdentityLink::getGroupId)
            .filter(Objects::nonNull)
            .findFirst()
            .orElse(null);
            
        if (role == null) {
            log.warn("No role found in candidate groups for task: {}", delegateTask.getId());
            return;
        }
        
        // TODO: Get the actual approver ID based on the role
        // This should be implemented based on your user/role management system
        String approverId = getApproverForRole(role);
        
        // Set the assignee
        delegateTask.setAssignee(approverId);
        
        // Update the approval step
        //step.setApproverId(Long.valueOf(approverId));
        step.setTaskId(delegateTask.getId());
        approvalStepRepository.save(step);
        
        log.info("Task {} assigned to approver {} with role {}", delegateTask.getId(), approverId, role);
    }
    
    private String getApproverForRole(String role) {
        // TODO: Implement logic to get approver ID based on role
        // This could involve:
        // 1. Looking up users with the given role
        // 2. Considering the request context (location, department, etc.)
        // 3. Checking user availability/load
        // 4. Applying any business rules for approver selection
        
        // For now, return a placeholder
        return role + "_ID";
    }
} 