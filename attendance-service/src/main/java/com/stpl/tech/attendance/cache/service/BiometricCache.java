package com.stpl.tech.attendance.cache.service;

import com.stpl.tech.attendance.dto.BiometricRegistrationDTO;
import com.stpl.tech.attendance.enums.BiometricStatus;

/**
 * Interface for managing biometric registration cache operations.
 * Provides methods for caching, retrieving, and managing biometric registration data.
 */
public interface BiometricCache {

    void cacheBiometricRegistration(BiometricRegistrationDTO registration);

    /**
     * Retrieves a biometric registration from cache or database.
     *
     * @param empId the employee ID
     * @return the biometric registration DTO, or null if not found
     */
    BiometricRegistrationDTO getBiometricRegistration(String empId);

    /**
     * Updates the cache with new biometric registration data using @CachePut.
     * This method will always update the cache regardless of whether the key exists.
     *
     * @param empId the employee ID
     * @return the updated BiometricRegistrationDTO, or null if not found
     */
    BiometricRegistrationDTO updateBiometricRegistration(String empId);

    /**
     * Invalidates the cache for a specific employee's biometric registration.
     *
     * @param empId the employee ID
     */
    //void invalidateBiometricCache(String empId);

    void removeBiometricRegistration(String empId);

    /**
     * Handles cache updates when a registration status changes.
     *
     * @param empId the employee ID
     * @param status the new biometric status
     */
    void handleRegistrationStatusChange(String empId, BiometricStatus status);

    /**
     * Refreshes the entire biometric registration cache.
     * This method is typically called on a schedule.
     */
//    void refreshBiometricCache();
//
//    /**
//     * Warms up the cache with frequently accessed registrations.
//     * This method is typically called on application startup.
//     */
//    void warmUpCache();
}