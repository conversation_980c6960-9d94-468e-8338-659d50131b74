package com.stpl.tech.attendance.service.impl;

import com.stpl.tech.attendance.cache.constants.CacheConstants;
import com.stpl.tech.attendance.cache.service.UserCacheService;
import com.stpl.tech.attendance.constants.ApiConstants;
import com.stpl.tech.attendance.entity.EmpEligibilityMapping;
import com.stpl.tech.attendance.entity.EmployeeShiftInstances;
import com.stpl.tech.attendance.entity.RosteringEntity.RosteringConstants;
import com.stpl.tech.attendance.enums.EligibilityType;
import com.stpl.tech.attendance.enums.MappingStatus;
import com.stpl.tech.attendance.enums.MappingType;
import com.stpl.tech.attendance.repository.EmpEligibilityMappingRepository;
import com.stpl.tech.attendance.repository.EmployeeShiftInstancesRepository;
import com.stpl.tech.attendance.service.UnitResolutionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.StopWatch;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Implementation of UnitResolutionService for resolving unit IDs for employees
 * 
 * Cache Features:
 * 1. Unit-level caching with key: "unitId_businessDate" for getEmployeesForUnit()
 * 2. Employee-level caching with key: "empId_businessDate" for getUnitIdForEmployee()
 * 3. Automatic daily cache invalidation at midnight for both caches
 * 4. Manual cache eviction when employee shift mapping changes
 * 
 * Cache Usage Example:
 * When employee shift mapping changes, call:
 * - evictEmployeeCaches(empId, businessDate) for specific employee/date
 * - evictUnitCaches(unitId, businessDate) for specific unit/date
 * - evictAllCaches() to clear all cache entries
 * 
 * Example integration in EmployeeShiftScheduleUpdateServiceImpl:
 * ```java
 * // After updating employee shift instance
 * employeeShiftInstancesRepository.save(instance);
 * 
 * // Evict both caches for the affected employee and date
 * unitResolutionService.evictEmployeeCaches(instance.getEmpId(), instance.getBusinessDate());
 * ```
 * 
 * Daily Cache Invalidation:
 * - Runs automatically at 12:00 AM daily for both caches
 * - Configurable via cache.invalidation.cron property
 * - Ensures fresh data is loaded each day
 * 
 * Self-Invocation Fix:
 * This service uses @Lazy annotation to inject a proxy for self-invocation,
 * ensuring @Cacheable annotations work properly when methods are called from within the same class.
 * This approach avoids the overhead of repeated ApplicationContext.getBean() calls.
 * 
 * Active Employee Filtering:
 * - Uses EmployeeSearchService to filter out inactive employees
 * - Leverages empCodeIndex which only contains active employees
 * - Optimized to avoid unnecessary database calls for inactive employees
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UnitResolutionServiceImpl implements UnitResolutionService {

    private final EmpEligibilityMappingRepository empEligibilityMappingRepository;
    private final EmployeeShiftInstancesRepository employeeShiftInstancesRepository;
    private final UserCacheService userCacheService;
    private final EmployeeSearchService employeeSearchService;
    
    // Lazy proxy for self-invocation to avoid overhead
    @Autowired
    @Lazy
    private UnitResolutionService selfProxy;

    // Local cache for unit employees to avoid Redis overhead for bulk operations
    // Key: "unitId_businessDate", Value: List<Integer> employeeIds
    private final Map<String, List<Integer>> localUnitEmployeesCache = new ConcurrentHashMap<>();

    @Override
    @Cacheable(value = CacheConstants.EMPLOYEE_UNIT_CACHE, key = "#empId + '_' + #businessDate")
    public Integer getUnitIdForEmployee(Integer empId, LocalDate businessDate) {
        log.debug("Getting unit ID for employee {} on date {}", empId, businessDate);

        // First try to get unit ID from emp shift mapping
        Integer unitIdFromShiftMapping = getUnitIdFromEmpShiftMapping(empId, businessDate);
        if (unitIdFromShiftMapping != null) {
            log.debug("Found unit ID {} from emp shift mapping for employee {} on date {}", 
                    unitIdFromShiftMapping, empId, businessDate);
            return unitIdFromShiftMapping;
        }

        // Fallback to emp eligibility mapping
        Integer unitIdFromEligibility = getUnitIdFromEmpEligibilityMapping(empId, businessDate);
        log.debug("Using unit ID {} from emp eligibility mapping for employee {} on date {}", 
                unitIdFromEligibility, empId, businessDate);
        if(unitIdFromEligibility == null) {
            Integer locCode = userCacheService.getUserById(empId).getLocCode();
            return locCode != null && locCode > 0 ? locCode : ApiConstants.DEFAULT_UNIT_ID;

        }
        return unitIdFromEligibility;
    }

    @Override
    public Integer getUnitIdFromEmpShiftMapping(Integer empId, LocalDate businessDate) {
        try {
            // Use EmployeeShiftInstancesRepository to get unit ID from shift instances
            Optional<EmployeeShiftInstances> shiftInstance = employeeShiftInstancesRepository
                .findByEmpIdAndBusinessDateAndInstanceStatus(empId, businessDate, "ACTIVE");
            
            if (shiftInstance.isPresent()) {
                EmployeeShiftInstances instance = shiftInstance.get();
                Integer unitId = instance.getUnitId();
                log.debug("Found unit ID {} from employee shift instance for employee {} on date {}", 
                        unitId, empId, businessDate);
                return unitId;
            }
            
            log.debug("No active employee shift instance found for employee {} on date {}", empId, businessDate);
            return null;
            
        } catch (Exception e) {
            log.error("Error getting unit ID from employee shift instance for employee {} on date {}", 
                    empId, businessDate, e);
            return null;
        }
    }

    @Override
    public Integer getUnitIdFromEmpEligibilityMapping(Integer empId, LocalDate businessDate) {
        try {
            // Get emp eligibility mapping for this employee and date
            List<EmpEligibilityMapping> eligibilityMappings = empEligibilityMappingRepository
                    .findByEmpIdInAndMappingTypeAndEligibilityTypeAndStatus(
                            List.of(String.valueOf(empId)),
                            MappingType.UNIT,
                            EligibilityType.ATTENDANCE,
                            MappingStatus.ACTIVE
                    );

            // Find the latest active mapping for this employee
            Optional<EmpEligibilityMapping> activeMapping = eligibilityMappings.stream()
                    .filter(mapping -> {
                        // Check if this mapping is for the specific employee
                        if (!String.valueOf(empId).equals(mapping.getEmpId())) {
                            return false;
                        }
                        
                        // Check if mapping is active for the business date
                        return isMappingActive(mapping, businessDate);
                    })
                    .max((mapping1, mapping2) -> {
                        // Compare by updatedAt first, then by createdAt if updatedAt is null
                        LocalDateTime date1 = mapping1.getUpdatedAt() != null ? mapping1.getUpdatedAt() : mapping1.getCreatedAt();
                        LocalDateTime date2 = mapping2.getUpdatedAt() != null ? mapping2.getUpdatedAt() : mapping2.getCreatedAt();
                        
                        if (date1 == null && date2 == null) return 0;
                        if (date1 == null) return -1;
                        if (date2 == null) return 1;
                        
                        return date1.compareTo(date2);
                    });

            if (activeMapping.isPresent()) {
                Integer unitId = Integer.parseInt(activeMapping.get().getValue());
                log.debug("Found unit ID {} from emp eligibility mapping for employee {} on date {}", 
                        unitId, empId, businessDate);
                return unitId;
            }

            log.warn("No active unit eligibility mapping found for employee {} on date {}, using default unit 26091", 
                    empId, businessDate);
            return null; // Default unit ID

        } catch (Exception e) {
            log.error("Error getting unit ID from emp eligibility mapping for employee {} on date {}, using default unit 26091", 
                    empId, businessDate, e);
            return null; // Default unit ID
        }
    }

    /**
     * Check if a mapping is currently active based on date range
     */
    private boolean isMappingActive(EmpEligibilityMapping mapping, LocalDate currentDate) {
        // Check start date: mapping is active if start date is null, before current date, or equal to current date
        boolean startDateValid = mapping.getStartDate() == null ||
                                mapping.getStartDate().isBefore(currentDate) ||
                                mapping.getStartDate().isEqual(currentDate);

        // Check end date: mapping is active if end date is null or after current date
        boolean endDateValid = mapping.getEndDate() == null ||
                              mapping.getEndDate().isAfter(currentDate);

        return startDateValid && endDateValid;
    }

    /**
     * Get employees from employee shift instances for the given units and business date
     */
    private Set<Integer> getEmployeesFromEmpShiftMapping(List<Integer> unitIds, LocalDate businessDate) {
        try {
            Set<Integer> employeeIds = new HashSet<>();
            
            // Use EmployeeShiftInstancesRepository to get employees from shift instances
            for (Integer unitId : unitIds) {
                List<EmployeeShiftInstances> shiftInstances = employeeShiftInstancesRepository
                    .findByUnitIdAndBusinessDateAndInstanceStatus(unitId, businessDate, RosteringConstants.ACTIVE);
                
                if (!shiftInstances.isEmpty()) {
                    // Add all employee IDs from active shift instances for this unit
                    for (EmployeeShiftInstances instance : shiftInstances) {
                        if ("ACTIVE".equals(instance.getInstanceStatus())) {
                            employeeIds.add(instance.getEmpId());
                        }
                    }
                }
            }
            
            log.debug("Found {} employees from employee shift instances for {} units on date {}", 
                    employeeIds.size(), unitIds.size(), businessDate);
            return employeeIds;
            
        } catch (Exception e) {
            log.error("Error getting employees from employee shift instances for units {} on date {}", 
                    unitIds, businessDate, e);
            return new HashSet<>();
        }
    }

    /**
     * Get employees from emp eligibility mapping for the given units and business date
     */
    private Set<Integer> getEmployeesFromEmpEligibilityMapping(List<Integer> unitIds, LocalDate businessDate) {
        try {
            // Get emp eligibility mappings for these units
            List<EmpEligibilityMapping> eligibilityMappings = empEligibilityMappingRepository
                    .findByValueInAndMappingTypeAndEligibilityTypeAndStatus(
                            unitIds.stream().map(String::valueOf).collect(Collectors.toList()),
                            MappingType.UNIT,
                            EligibilityType.ATTENDANCE,
                            MappingStatus.ACTIVE
                    );

            // Filter active mappings and get unique employee IDs
            Set<Integer> employeeIds = eligibilityMappings.stream()
                    .filter(mapping -> isMappingActive(mapping, businessDate))
                    .map(EmpEligibilityMapping::getEmpId)
                    .map(Integer::parseInt)
                    .collect(Collectors.toSet());

            log.debug("Found {} employees from emp eligibility mapping for {} units on date {}", 
                    employeeIds.size(), unitIds.size(), businessDate);
            return employeeIds;

        } catch (Exception e) {
            log.error("Error getting employees from emp eligibility mapping for units {} on date {}", 
                    unitIds, businessDate, e);
            return new HashSet<>();
        }
    }

    /**
     * Get employees based on locCode from user cache for the given units
     */
    private Set<Integer> getEmployeesFromLocCode(List<Integer> unitIds) {
        try {
            Set<Integer> employeeIds = new HashSet<>();
            
            // This would require a method to get all users from cache and filter by locCode
            // For now, we'll return empty set as this is a fallback mechanism
            // TODO: Implement proper locCode-based employee lookup if needed
            
            log.debug("Found {} employees from locCode for {} units", employeeIds.size(), unitIds.size());
            return employeeIds;

        } catch (Exception e) {
            log.error("Error getting employees from locCode for units {}", unitIds, e);
            return new HashSet<>();
        }
    }

    @Override
    public Set<Integer> getEmployeesForUnits(List<Integer> unitIds, LocalDate businessDate) {
        return getEmployeesForUnits(unitIds, businessDate, false);
    }

    /**
     * Check if an employee is active using the EmployeeSearchService
     * This is an optimized way to filter out inactive employees without additional database calls
     * 
     * @param empId Employee ID to check
     * @return true if employee is active, false otherwise
     */
    private boolean isEmployeeActive(Integer empId) {
        return employeeSearchService.isEmployeeActive(empId);
    }

    /**
     * Filter out inactive employees from a set of employee IDs
     * 
     * @param employeeIds Set of employee IDs to filter
     * @return Set of only active employee IDs
     */
    private Set<Integer> filterActiveEmployees(Set<Integer> employeeIds) {
        if (employeeIds == null || employeeIds.isEmpty()) {
            return new HashSet<>();
        }
        
        Set<Integer> activeEmployees = employeeIds.stream()
                .filter(this::isEmployeeActive)
                .collect(Collectors.toSet());
        
        log.debug("Filtered {} active employees from {} total employees", 
                activeEmployees.size(), employeeIds.size());
        
        return activeEmployees;
    }

    /**
     * Get employees for a single unit with local caching and active employee filtering
     * @param unitId Unit ID
     * @param businessDate Business date
     * @return Set of active employee IDs
     */
    public List<Integer> getEmployeesForUnit(Integer unitId, LocalDate businessDate) {
        log.debug("Getting employees for unit {} on date {}", unitId, businessDate);

        // Check local cache first for better performance
        String cacheKey = unitId + "_" + businessDate;
        List<Integer> cachedEmployees = localUnitEmployeesCache.get(cacheKey);
        if (cachedEmployees != null) {
            log.debug("Found {} employees in local cache for unit {} on date {}",
                    cachedEmployees.size(), unitId, businessDate);
            return cachedEmployees;
        }
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        // If not in local cache, compute and cache the result
        List<Integer> employees = computeEmployeesForUnit(unitId, businessDate);
        stopWatch.stop();
        log.info("Time taken to fetch employees for unit {} on date {} is {} ms", unitId, businessDate, stopWatch.getTime());
        // Store in local cache for future lookups
        localUnitEmployeesCache.put(cacheKey, employees);

        log.debug("Computed and cached {} employees for unit {} on date {}",
                employees.size(), unitId, businessDate);
        return employees;
    }

    /**
     * Compute employees for unit without caching (actual business logic)
     */
    private List<Integer> computeEmployeesForUnit(Integer unitId, LocalDate businessDate) {
        log.debug("Computing employees for unit {} on date {} (cache miss)", unitId, businessDate);
        if (unitId == null) {
            log.warn("No unit ID provided for employee lookup");
            return new ArrayList<>();
        }

        try {
            Set<Integer> employeeIds = new HashSet<>();
            
            // Get all potential employees from all sources
            Set<Integer> allPotentialEmployees = new HashSet<>();
            
            // Get employees from employee shift instances
            Set<Integer> employeesFromShiftMapping = getEmployeesFromEmpShiftMapping(List.of(unitId), businessDate);
            allPotentialEmployees.addAll(employeesFromShiftMapping);
            
            // Get employees from eligibility mapping
            Set<Integer> employeesFromEligibility = getEmployeesFromEmpEligibilityMapping(List.of(unitId), businessDate);
            allPotentialEmployees.addAll(employeesFromEligibility);
            
            // Get employees from locCode (if no employees found from other sources)
            if (allPotentialEmployees.isEmpty()) {
                Set<Integer> employeesFromLocCode = getEmployeesFromLocCode(List.of(unitId));
                allPotentialEmployees.addAll(employeesFromLocCode);
            }
            
            // Filter out inactive employees first for better performance
            Set<Integer> activePotentialEmployees = filterActiveEmployees(allPotentialEmployees);
            
            // For each active potential employee, determine their primary unit
            for (Integer empId : activePotentialEmployees) {
                Integer primaryUnitId = selfProxy.getUnitIdForEmployee(empId, businessDate);
                
                // Only include employee if their primary unit matches the requested unit
                if (primaryUnitId != null && primaryUnitId.equals(unitId)) {
                    employeeIds.add(empId);
                    log.debug("Active employee {} assigned to primary unit {}", empId, primaryUnitId);
                } else {
                    log.debug("Active employee {} primary unit {} not matching requested unit {}, excluding", 
                            empId, primaryUnitId, unitId);
                }
            }

            log.debug("Total {} active employees found for unit {} on date {}", 
                    employeeIds.size(), unitId, businessDate);
            return employeeIds.stream().toList();

        } catch (Exception e) {
            log.error("Error fetching employees for unit: {} on date {}", unitId, businessDate, e);
            throw new RuntimeException("Failed to fetch employees for the specified unit", e);
        }
    }

    /**
     * Evict cache for a specific unit and date when employee shift mapping changes
     * @param unitId Unit ID
     * @param businessDate Business date
     */
    public void evictUnitEmployeesCache(Integer unitId, LocalDate businessDate) {
        log.debug("Evicting local cache for unit {} on date {}", unitId, businessDate);
        String cacheKey = unitId + "_" + businessDate;
        localUnitEmployeesCache.remove(cacheKey);
    }

    /**
     * Evict all unit employees cache entries
     */
    public void evictAllUnitEmployeesCache() {
        log.debug("Evicting all local unit employees cache");
        localUnitEmployeesCache.clear();
    }

    /**
     * Evict cache for a specific employee and date when employee shift mapping changes
     * @param empId Employee ID
     * @param businessDate Business date
     */
    @CacheEvict(value = CacheConstants.EMPLOYEE_UNIT_CACHE, key = "#empId + '_' + #businessDate")
    public void evictEmployeeUnitCache(Integer empId, LocalDate businessDate) {
        log.debug("Evicting cache for employee {} on date {}", empId, businessDate);
    }

    /**
     * Evict all employee unit cache entries
     */
    @CacheEvict(value = CacheConstants.EMPLOYEE_UNIT_CACHE, allEntries = true)
    public void evictAllEmployeeUnitCache() {
        log.debug("Evicting all employee unit cache");
    }

    /**
     * Evict both caches for a specific employee and date when employee shift mapping changes
     * @param empId Employee ID
     * @param businessDate Business date
     */
    @CacheEvict(value = {CacheConstants.UNIT_EMPLOYEES_CACHE, CacheConstants.EMPLOYEE_UNIT_CACHE}, 
                key = "#empId + '_' + #businessDate")
    public void evictEmployeeCaches(Integer empId, LocalDate businessDate) {
        log.debug("Evicting both caches for employee {} on date {}", empId, businessDate);
    }

    /**
     * Evict both caches for a specific unit and date when employee shift mapping changes
     * @param unitId Unit ID
     * @param businessDate Business date
     */
    @CacheEvict(value = CacheConstants.EMPLOYEE_UNIT_CACHE, key = "#unitId + '_' + #businessDate")
    public void evictUnitCaches(Integer unitId, LocalDate businessDate) {
        log.debug("Evicting both caches for unit {} on date {}", unitId, businessDate);
        // Also evict from local cache
        evictUnitEmployeesCache(unitId, businessDate);
    }

    /**
     * Evict all cache entries for both caches
     */
    @CacheEvict(value = CacheConstants.EMPLOYEE_UNIT_CACHE, allEntries = true)
    public void evictAllCaches() {
        log.debug("Evicting all cache entries for both unit employees and employee unit caches");
        // Also clear local cache
        evictAllUnitEmployeesCache();
    }

    /**
     * Scheduled task to invalidate both caches daily at 12:00 AM
     * This ensures fresh data is loaded each day
     * 
     * Cron expression: "0 0 0 * * ?" means:
     * - 0: seconds (0)
     * - 0: minutes (0) 
     * - 0: hours (0 = midnight)
     * - *: every day of month
     * - *: every month
     * - ?: any day of week
     */
    @Scheduled(cron = "0 0 0 * * ?") // Run daily at midnight
    @CacheEvict(value = CacheConstants.EMPLOYEE_UNIT_CACHE, allEntries = true)
    public void scheduledCacheInvalidation() {
        log.info("Scheduled daily cache invalidation for both unit employees and employee unit caches at {}", LocalDateTime.now());
        // Also clear local cache
        evictAllUnitEmployeesCache();
    }

//    /**
//     * Alternative scheduled task that can be configured via properties
//     * Default: runs daily at 2:00 AM (configurable via cache.invalidation.cron)
//     *
//     * To configure in application.properties:
//     * cache.invalidation.cron=0 0 2 * * ?  # Run at 2 AM daily
//     * cache.invalidation.enabled=true        # Enable/disable the task
//     */
//    @Scheduled(cron = "${cache.invalidation.cron:0 0 2 * * ?}")
//    @CacheEvict(value = {CacheConstants.UNIT_EMPLOYEES_CACHE, CacheConstants.EMPLOYEE_UNIT_CACHE}, allEntries = true)
//    public void configurableCacheInvalidation() {
//        log.info("Configurable scheduled cache invalidation for both unit employees and employee unit caches at {}", LocalDateTime.now());
//    }

    /**
     * Get employees for units with control over whether to include secondary mappings
     * @param unitIds List of unit IDs
     * @param businessDate Business date
     * @param includeSecondaryMappings If true, include employees from all sources. If false, only include employees in their primary unit.
     * @return Set of active employee IDs
     */
    public Set<Integer> getEmployeesForUnits(List<Integer> unitIds, LocalDate businessDate, boolean includeSecondaryMappings) {
        if (unitIds == null || unitIds.isEmpty()) {
            log.warn("No unit IDs provided for employee lookup");
            return new HashSet<>();
        }

        try {
            Set<Integer> employeeIds = new HashSet<>();

            if (includeSecondaryMappings) {
                // Original logic: Include employees from all sources
                // First priority: Get employees from employee shift instances
                Set<Integer> employeesFromShiftMapping = getEmployeesFromEmpShiftMapping(unitIds, businessDate);
                if (!employeesFromShiftMapping.isEmpty()) {
                    log.debug("Found {} employees from employee shift instances for {} units on date {}", 
                            employeesFromShiftMapping.size(), unitIds.size(), businessDate);
                    employeeIds.addAll(employeesFromShiftMapping);
                }

                // Second priority: Get employees from emp eligibility mapping
                Set<Integer> employeesFromEligibility = getEmployeesFromEmpEligibilityMapping(unitIds, businessDate);
                if (!employeesFromEligibility.isEmpty()) {
                    log.debug("Found {} employees from emp eligibility mapping for {} units on date {}", 
                            employeesFromEligibility.size(), unitIds.size(), businessDate);
                    employeeIds.addAll(employeesFromEligibility);
                }

                // Last fallback: Get employees based on locCode from user cache
                if (employeeIds.isEmpty()) {
                    Set<Integer> employeesFromLocCode = getEmployeesFromLocCode(unitIds);
                    if (!employeesFromLocCode.isEmpty()) {
                        log.debug("Found {} employees from locCode for {} units", 
                                employeesFromLocCode.size(), unitIds.size());
                        employeeIds.addAll(employeesFromLocCode);
                    }
                }
                
                // Filter out inactive employees for better performance
                employeeIds = filterActiveEmployees(employeeIds);
            } else {
                // New logic: Use single unit method for each unit to leverage caching
                // This already includes active employee filtering in getEmployeesForUnit
                for (Integer unitId : unitIds) {
                    List<Integer> unitEmployees = selfProxy.getEmployeesForUnit(unitId, businessDate);
                    employeeIds.addAll(unitEmployees);
                }
            }

            log.debug("Total {} active employees found for {} units on date {} (includeSecondaryMappings: {})", 
                    employeeIds.size(), unitIds.size(), businessDate, includeSecondaryMappings);
            return employeeIds;

        } catch (Exception e) {
            log.error("Error fetching employees for units: {} on date {}", unitIds, businessDate, e);
            throw new RuntimeException("Failed to fetch employees for the specified units", e);
        }
    }
} 