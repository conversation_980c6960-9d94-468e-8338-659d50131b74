package com.stpl.tech.attendance.model;

/**
 * Model class to hold approver information including ID and notification preference
 */
public class ApproverInfo {
    private Integer id;
    private Boolean sendNotificationToApprovers;

    public ApproverInfo() {
    }

    public ApproverInfo(Integer id, Boolean sendNotificationToApprovers) {
        this.id = id;
        this.sendNotificationToApprovers = sendNotificationToApprovers;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Boolean getSendNotificationToApprovers() {
        return sendNotificationToApprovers;
    }

    public void setSendNotificationToApprovers(Boolean sendNotificationToApprovers) {
        this.sendNotificationToApprovers = sendNotificationToApprovers;
    }

    @Override
    public String toString() {
        return "ApproverInfo{" +
                "id='" + id + '\'' +
                ", sendNotificationToApprovers=" + sendNotificationToApprovers +
                '}';
    }
}
