package com.stpl.tech.attendance.controller;

import com.google.gson.Gson;
import com.stpl.tech.attendance.constants.ApiConstants;
import com.stpl.tech.attendance.context.RequestTrackingContext;
import com.stpl.tech.attendance.model.AttendancePunchRequest;
import com.stpl.tech.attendance.model.AttendancePunchResponse;
import com.stpl.tech.attendance.model.AttendanceResponse;
import com.stpl.tech.attendance.model.BulkAttendancePunchRequest;
import com.stpl.tech.attendance.model.BulkAttendancePunchResponse;
import com.stpl.tech.attendance.model.response.ApiResponse;
import com.stpl.tech.attendance.service.AttendancePunchService;
import com.stpl.tech.attendance.annotation.TrackRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.List;

@Tag(name = "Attendance", description = "Attendance management APIs")
@Slf4j
@RestController
@RequestMapping(ApiConstants.Paths.ATTENDANCE)
@RequiredArgsConstructor
public class AttendanceController extends BaseController {

    @Autowired
    private AttendancePunchService attendancePunchService;



    @Operation(summary = "Mark attendance punch")
    @TrackRequest(description = "Mark attendance punch")
    @PostMapping("/punch")
    public ResponseEntity<ApiResponse<AttendancePunchResponse>> markAttendance(
            @Valid @RequestBody AttendancePunchRequest request) {
        AttendancePunchResponse response = attendancePunchService.processAttendancePunch(request, false);
        return success(response);
    }

    @Operation(summary = "Mark attendance punch with multiple images")
    @TrackRequest(description = "Mark attendance punch with images")
    @PostMapping("/punch-with-images")
    public ResponseEntity<ApiResponse<AttendancePunchResponse>> markAttendanceWithImages(
             @RequestBody AttendancePunchRequest request) {
        log.info("Processing attendance punch with multiple images for employee: {}", request.getEmployeeId());
        AttendancePunchResponse response = attendancePunchService.processAttendancePunch(request, true);
        
        // Set reference ID in context for automatic tracking by @RequestTrackingAspect
        if (response.getAttendanceRequestId() != null) {
            RequestTrackingContext.setReferenceId(response.getAttendanceRequestId());
        }
        
        return success(response);
    }

    @Operation(summary = "Mark bulk attendance punch")
    @TrackRequest(description = "Bulk attendance punch")
    @PostMapping("/bulk-punch")
    public ResponseEntity<ApiResponse<BulkAttendancePunchResponse>> markBulkAttendance(
            @Valid @RequestBody BulkAttendancePunchRequest request) {
        log.info("Processing bulk attendance punch request: {}", request);
        BulkAttendancePunchResponse response = attendancePunchService.processBulkAttendancePunch(request);
        return success(response);
    }

    @Operation(summary = "Get current day attendance")
    @GetMapping("/today/{employeeId}")
    public ResponseEntity<ApiResponse<AttendanceResponse>> getCurrentDayAttendance(
            @PathVariable Long employeeId) {
        AttendanceResponse response = attendancePunchService.getCurrentDayAttendance(employeeId);
        return success(response);
    }

    @Operation(summary = "Get attendance history")
    @GetMapping("/{employeeId}")
    public ResponseEntity<ApiResponse<List<AttendanceResponse>>> getAttendanceHistory(
            @PathVariable Long employeeId,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date,
            @RequestParam(required = false) Integer month,
            @RequestParam(required = false) Integer year) {
        List<AttendanceResponse> response = attendancePunchService.getAttendanceHistory(employeeId, date, month, year);
        return success(response);
    }
} 