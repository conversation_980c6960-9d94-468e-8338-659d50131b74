package com.stpl.tech.attendance.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmployeeShiftInstanceDetailDTO {
    private LocalDate businessDate;
    private ShiftData shiftData;
    private LocalDateTime checkInTime;
    private LocalDateTime checkOutTime;
    private BigDecimal totalWorkHours;
    private String status;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ShiftData {
        private Integer shiftId;
        private String shiftName;
        private LocalDateTime shiftStartTime;
        private LocalDateTime shiftEndTime;
    }
} 