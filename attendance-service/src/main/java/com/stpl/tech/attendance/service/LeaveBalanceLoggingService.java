package com.stpl.tech.attendance.service;

import java.math.BigDecimal;

/**
 * Service for logging leave balance changes
 */
public interface LeaveBalanceLoggingService {

    /**
     * Log a leave balance deduction (DEBIT operation)
     * @param empLeaveDataId The employee leave data ID
     * @param leaveType The type of leave (LEAVE, COMP_OFF, LWP, WEEK_OFF)
     * @param daysDeducted The number of days deducted
     * @param updatedBy The user who performed the operation
     */
    void logLeaveBalanceDeduction(Long empLeaveDataId, String leaveType, BigDecimal daysDeducted, String updatedBy);

    /**
     * Log a leave balance restoration (CREDIT operation)
     * @param empLeaveDataId The employee leave data ID
     * @param leaveType The type of leave (LEAVE, COMP_OFF, LWP, WEEK_OFF)
     * @param daysRestored The number of days restored
     * @param updatedBy The user who performed the operation
     */
    void logLeaveBalanceRestoration(Long empLeaveDataId, String leaveType, BigDecimal daysRestored, String updatedBy);

    /**
     * Log a leave balance change with old and new count values
     * @param empLeaveDataId The employee leave data ID
     * @param leaveType The type of leave (LEAVE, COMP_OFF, LWP, WEEK_OFF)
     * @param oldCount The leave balance before the change
     * @param newCount The leave balance after the change
     * @param updatedBy The user who performed the operation
     */
    void logLeaveBalanceChange(Long empLeaveDataId, String leaveType, BigDecimal oldCount, BigDecimal newCount, String updatedBy);
}
