package com.stpl.tech.attendance.service.impl;

import com.stpl.tech.attendance.entity.DailyAttendanceSummary;
import com.stpl.tech.attendance.entity.EmployeeShiftInstances;
import com.stpl.tech.attendance.entity.RosteringEntity.RosteringConstants;
import com.stpl.tech.attendance.entity.RosteringEntity.Shift;
import com.stpl.tech.attendance.enums.EmployeeShiftScheduleStatus;
import com.stpl.tech.attendance.repository.EmployeeShiftInstancesRepository;
import com.stpl.tech.attendance.service.EmployeeShiftScheduleUpdateService;
import com.stpl.tech.attendance.service.UnitResolutionService;
import com.stpl.tech.attendance.util.ShiftHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class EmployeeShiftScheduleUpdateServiceImpl implements EmployeeShiftScheduleUpdateService {
    
    private final EmployeeShiftInstancesRepository employeeShiftInstancesRepository;
    private final ShiftHelper shiftHelper;
    private final UnitResolutionService unitResolutionService;
    
    /**
     * Update shift schedule with actual attendance data
     * Called from AsyncOperationService when updating DailyAttendanceSummary
     */
    @Async("asyncTaskExecutor")
    @Override
    public void updateShiftScheduleWithAttendance(Integer empId, LocalDate businessDate,
                                                  LocalDateTime checkInTime, LocalDateTime checkOutTime,
                                                  DailyAttendanceSummary summary) {
        try {
            Optional<EmployeeShiftInstances> instanceOpt = employeeShiftInstancesRepository
                .findByEmpIdAndBusinessDateAndInstanceStatus(empId, businessDate , RosteringConstants.ACTIVE);
            
            if (instanceOpt.isEmpty()) {
                log.warn("No shift instance found for employee {} on date {}", empId, businessDate);
                // Create shifts for the entire week for this employee
                createShiftsForWeek(empId, businessDate);
                return;
            }
            
            EmployeeShiftInstances instance = instanceOpt.get();
            
            // Update actual times
            if (checkInTime != null) {
                instance.setActualStartTime(checkInTime);
                instance.setStatus(summary.getStatus());
            }
            
            if (checkOutTime != null) {
                instance.setActualEndTime(checkOutTime);
                instance.setStatus(summary.getStatus());
                
                // Calculate actual hours
                if (instance.getActualStartTime() != null) {
                    BigDecimal actualHours = calculateActualHours(
                        instance.getActualStartTime(), instance.getActualEndTime());
                    instance.setActualHours(actualHours);
                }
            }
            
            // Update status based on attendance
            //updateScheduleStatus(instance, checkInTime, checkOutTime);
            
            employeeShiftInstancesRepository.save(instance);
            log.debug("Updated shift instance for employee {} on date {}", empId, businessDate);
            
        } catch (Exception e) {
            log.error("Error updating shift instance for employee {} on date {}", empId, businessDate, e);
        }
    }
    
    /**
     * Create shifts for the remaining days of the current week for an employee starting from the given date
     * @param empId Employee ID
     * @param startDate Starting date for the week
     */
    private void createShiftsForWeek(Integer empId, LocalDate startDate) {
        log.info("Creating shifts for the remaining days of the week for employee {} starting from {}", empId, startDate);
        
        // Calculate the end date (end of the current week - Sunday)
        LocalDate endDate = startDate.with(java.time.DayOfWeek.SUNDAY);
        
        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            try {
                // Check if instance already exists
                if (employeeShiftInstancesRepository.existsByEmpIdAndBusinessDateAndInstanceStatus(
                        empId, date, RosteringConstants.ACTIVE)) {
                    log.debug("Shift instance already exists for employee {} on date {}", empId, date);
                    continue;
                }
                
                // Get shift for this employee and date using ShiftHelper
                Shift shift = shiftHelper.getShiftForEmployee(empId, date);
                if (shift == null) {
                    log.warn("No shift found for employee {} on date {}", empId, date);
                    continue;
                }
                
                // Get unit ID for employee
                Integer unitId = unitResolutionService.getUnitIdForEmployee(empId, date);
                
                // Calculate expected times using ShiftHelper for universal shift handling
                LocalDateTime expectedStartTime;
                LocalDateTime expectedEndTime;
                
                // Check if this is a universal shift and use appropriate calculation
                if (shiftHelper.isUniversalShift(shift)) {
                    expectedStartTime = shiftHelper.calculateExpectedStartTimeForUniversalShift(empId, date);
                    expectedEndTime = shiftHelper.calculateExpectedEndTimeForUniversalShift(empId, date);
                } else {
                    expectedStartTime = shiftHelper.calculateExpectedStartTime(shift, date);
                    expectedEndTime = shiftHelper.calculateExpectedEndTime(shift, date);
                }
                
                BigDecimal idealHours = shiftHelper.calculateIdealHours(expectedStartTime, expectedEndTime);
                
                // Create new instance
                EmployeeShiftInstances newInstance = EmployeeShiftInstances.builder()
                    .empId(empId)
                    .businessDate(date)
                    .shiftId(shift.getShiftId())
                    .unitId(unitId)
                    .expectedStartTime(expectedStartTime)
                    .expectedEndTime(expectedEndTime)
                    .idealHours(idealHours)
                    .instanceStatus(RosteringConstants.ACTIVE)
                    .createdBy("SYSTEM")
                    .status(com.stpl.tech.attendance.entity.AttendanceStatus.ABSENT)
                    .build();
                
                employeeShiftInstancesRepository.save(newInstance);
                log.debug("Created new shift instance for employee {} on date {} with shift {}", 
                         empId, date, shift.getShiftName());
                
            } catch (Exception e) {
                log.error("Error creating shift instance for employee {} on date {}", empId, date, e);
            }
        }
        
        log.info("Completed creating shifts for the week for employee {} from {} to {}", 
                empId, startDate, endDate);
    }
    
    /**
     * Update schedule status based on attendance
     */
 /*   private void updateScheduleStatus(EmployeeShiftInstances instance,
                                    LocalDateTime checkInTime, LocalDateTime checkOutTime) {
        if (checkInTime != null && checkOutTime != null) {
            instance.setStatus(EmployeeShiftScheduleStatus.COMPLETED);
        } else if (checkInTime != null) {
            instance.setStatus(EmployeeShiftScheduleStatus.IN_PROGRESS);
        } else if (checkOutTime != null) {
            instance.setStatus(EmployeeShiftScheduleStatus.PARTIAL);
        } else {
            // Check if it's past the expected end time
            if (LocalDateTime.now().isAfter(instance.getExpectedEndTime())) {
                instance.setStatus(EmployeeShiftScheduleStatus.ABSENT);
            }
        }
    }*/
    
    /**
     * Calculate actual hours between start and end time
     */
    private BigDecimal calculateActualHours(LocalDateTime startTime, LocalDateTime endTime) {
        Duration duration = Duration.between(startTime, endTime);
        return BigDecimal.valueOf(duration.toMinutes() / 60.0);
    }
} 