package com.stpl.tech.attendance.repository;

import com.stpl.tech.attendance.entity.EmpAttendanceMetadata;
import com.stpl.tech.attendance.enums.AttendanceAttributeType;
import com.stpl.tech.attendance.enums.MappingStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for EmpAttendanceMetadata entity
 */
@Repository
public interface EmpAttendanceMetadataRepository extends JpaRepository<EmpAttendanceMetadata, Long> {
    
    /**
     * Find active metadata by department ID and attribute code
     * @param deptId Department ID
     * @param attributeCode Attribute code to search for
     * @return Optional containing the metadata if found
     */
    Optional<EmpAttendanceMetadata> findByDeptIdAndAttributeCodeAndMappingStatus(
        Integer deptId, 
        AttendanceAttributeType attributeCode, 
        MappingStatus mappingStatus
    );
    
    /**
     * Find all active metadata for a specific department
     * @param deptId Department ID
     * @return List of active metadata for the department
     */
    List<EmpAttendanceMetadata> findByDeptIdAndMappingStatus(
        Integer deptId, 
        MappingStatus mappingStatus
    );
    
    /**
     * Find all active metadata for a specific attribute code across all departments
     * @param attributeCode Attribute code to search for
     * @return List of active metadata for the attribute code
     */
    List<EmpAttendanceMetadata> findByAttributeCodeAndMappingStatus(
        AttendanceAttributeType attributeCode, 
        MappingStatus mappingStatus
    );
    
    /**
     * Find default metadata (DEPT_ID = -1) for a specific attribute code
     * @param attributeCode Attribute code to search for
     * @return Optional containing the default metadata if found
     */
    @Query("SELECT m FROM EmpAttendanceMetadata m WHERE m.deptId = -1 " +
           "AND m.attributeCode = :attributeCode " +
           "AND m.mappingStatus = :mappingStatus")
    Optional<EmpAttendanceMetadata> findDefaultMetadataByAttributeCode(
        @Param("attributeCode") AttendanceAttributeType attributeCode,
        @Param("mappingStatus") MappingStatus mappingStatus
    );
    
    /**
     * Find metadata for a specific department and attribute code, 
     * including default metadata if department-specific not found
     * @param deptId Department ID
     * @param attributeCode Attribute code to search for
     * @return List containing department-specific metadata first, then default metadata
     */
    @Query("SELECT m FROM EmpAttendanceMetadata m WHERE " +
           "(m.deptId = :deptId OR m.deptId = -1) " +
           "AND m.attributeCode = :attributeCode " +
           "AND m.mappingStatus = :mappingStatus " +
           "ORDER BY m.deptId DESC")
    List<EmpAttendanceMetadata> findMetadataByDeptAndAttributeCode(
        @Param("deptId") Integer deptId,
        @Param("attributeCode") AttendanceAttributeType attributeCode,
        @Param("mappingStatus") MappingStatus mappingStatus
    );
    
    /**
     * Check if metadata exists for a specific department and attribute code
     * @param deptId Department ID
     * @param attributeCode Attribute code to check
     * @return true if metadata exists
     */
    boolean existsByDeptIdAndAttributeCodeAndMappingStatus(
        Integer deptId, 
        AttendanceAttributeType attributeCode, 
        MappingStatus mappingStatus
    );
    
    /**
     * Find all active metadata for multiple departments
     * @param deptIds List of department IDs
     * @return List of active metadata for the specified departments
     */
    List<EmpAttendanceMetadata> findByDeptIdInAndMappingStatus(
        List<Integer> deptIds, 
        MappingStatus mappingStatus
    );
}


