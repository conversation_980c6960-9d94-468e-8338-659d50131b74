package com.stpl.tech.attendance.config;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.io.IOException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

@Configuration
public class JacksonConfig {

    private static final String DATE_TIME_PATTERN = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";
    private static final String ISO_DATE_TIME_PATTERN = "yyyy-MM-dd'T'HH:mm:ss";
    private static final String DATE_TIME_WITH_SPACE_PATTERN = "yyyy-MM-dd HH:mm:ss";
    public static final String BUSINESS_DATETIME_PATTERN = "yyyy-MM-dd HH:mm:ss";
    public static final String BUSINESS_TIMEZONE = "Asia/Kolkata";

    /**
     * Custom LocalDate deserializer that handles date strings with time information
     */
    public static class CustomLocalDateDeserializer extends JsonDeserializer<LocalDate> {
        
        private final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        private final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DATE_TIME_WITH_SPACE_PATTERN);
        
        @Override
        public LocalDate deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
            String value = p.getValueAsString();
            
            if (value == null || value.trim().isEmpty()) {
                return null;
            }
            
            // Try to parse as date only first (yyyy-MM-dd)
            try {
                return LocalDate.parse(value, dateFormatter);
            } catch (DateTimeParseException e) {
                // If date parsing fails, try as datetime format (yyyy-MM-dd HH:mm:ss)
                try {
                    LocalDateTime dateTime = LocalDateTime.parse(value, dateTimeFormatter);
                    return dateTime.toLocalDate();
                } catch (DateTimeParseException dte) {
                    // If both fail, try with the default ISO format
                    try {
                        return LocalDate.parse(value);
                    } catch (DateTimeParseException dte2) {
                        throw new IOException("Unable to parse LocalDate: " + value, dte2);
                    }
                }
            }
        }
    }

    /**
     * Custom LocalDateTime deserializer that handles both ISO format strings and raw timestamps
     */
    public static class CustomLocalDateTimeDeserializer extends JsonDeserializer<LocalDateTime> {
        
        private final DateTimeFormatter isoFormatter = DateTimeFormatter.ofPattern(ISO_DATE_TIME_PATTERN);
        private final DateTimeFormatter spaceFormatter = DateTimeFormatter.ofPattern(DATE_TIME_WITH_SPACE_PATTERN);
        
        @Override
        public LocalDateTime deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
            String value = p.getValueAsString();
            
            if (value == null || value.trim().isEmpty()) {
                return null;
            }
            
            // Try to parse as ISO format with timezone first (e.g., "2025-07-29T08:00:00.000Z")
            try {
                Instant instant = Instant.parse(value);
                return instant.atZone(ZoneId.systemDefault()).toLocalDateTime();
            } catch (DateTimeParseException e) {
                // Try to parse as ISO format without timezone
                try {
                    return LocalDateTime.parse(value, isoFormatter);
                } catch (DateTimeParseException e2) {
                    // Try with space format (yyyy-MM-dd HH:mm:ss)
                    try {
                        return LocalDateTime.parse(value, spaceFormatter);
                    } catch (DateTimeParseException e3) {
                        // If ISO parsing fails, try as timestamp
                        try {
                            long timestamp = Long.parseLong(value);
                            return Instant.ofEpochMilli(timestamp)
                                    .atZone(ZoneId.systemDefault())
                                    .toLocalDateTime();
                        } catch (NumberFormatException nfe) {
                            // If all fail, try with the default ISO format
                            try {
                                return LocalDateTime.parse(value);
                            } catch (DateTimeParseException dte) {
                                throw new IOException("Unable to parse LocalDateTime: " + value, dte);
                            }
                        }
                    }
                }
            }
        }
    }

    @Bean
    @Primary
    public ObjectMapper objectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        
        // Configure LocalDateTime serialization and deserialization
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_TIME_PATTERN);
        
        javaTimeModule.addSerializer(LocalDateTime.class, 
            new LocalDateTimeSerializer(formatter));
        
        // Add custom deserializers that can handle multiple formats
        javaTimeModule.addDeserializer(LocalDateTime.class, 
            new CustomLocalDateTimeDeserializer());
        javaTimeModule.addDeserializer(LocalDate.class, 
            new CustomLocalDateDeserializer());
        
        objectMapper.registerModule(javaTimeModule);
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

        // Configure deserialization features
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.configure(DeserializationFeature.READ_UNKNOWN_ENUM_VALUES_AS_NULL, true);
        
        // Enable timestamp deserialization for LocalDateTime
        objectMapper.configure(DeserializationFeature.READ_DATE_TIMESTAMPS_AS_NANOSECONDS, false);
        
        return objectMapper;
    }
} 