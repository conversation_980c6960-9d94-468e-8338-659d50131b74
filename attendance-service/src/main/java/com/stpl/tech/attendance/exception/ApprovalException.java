package com.stpl.tech.attendance.exception;

import com.stpl.tech.attendance.enums.ApprovalErrorCode;
import lombok.Getter;

@Getter
public class ApprovalException extends RuntimeException {
    private final ApprovalErrorCode errorCode;

    public ApprovalException(ApprovalErrorCode errorCode) {
        super(errorCode.getDefaultMessage());
        this.errorCode = errorCode;
    }

    public ApprovalException(ApprovalErrorCode errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public ApprovalException(ApprovalErrorCode errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }
} 