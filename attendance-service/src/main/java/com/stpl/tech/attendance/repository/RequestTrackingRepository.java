package com.stpl.tech.attendance.repository;

import com.stpl.tech.attendance.entity.RequestTracking;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import jakarta.persistence.LockModeType;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository for managing generic request tracking data
 * Provides methods to track and query request processing status
 */
@Repository
public interface RequestTrackingRepository extends JpaRepository<RequestTracking, Long> {

    /**
     * Find tracking record by request ID
     */
    Optional<RequestTracking> findByRequestId(String requestId);

    /**
     * Update only completion time (preserves reference ID and other fields)
     */
    @Modifying(clearAutomatically = true)
    @Query(value = "UPDATE REQUEST_TRACKING SET COMPLETION_TIME = :completionTime, UPDATED_AT = CURRENT_TIMESTAMP WHERE REQUEST_ID = :requestId", nativeQuery = true)
    void updateCompletionTimeOnly(@Param("requestId") String requestId, @Param("completionTime") LocalDateTime completionTime);

    /**
     * Update completion time and error details (preserves reference ID)
     */
    @Modifying(clearAutomatically = true)
    @Query(value = "UPDATE REQUEST_TRACKING SET COMPLETION_TIME = :completionTime, ERROR_MESSAGE = :errorMessage, ERROR_CODE = :errorCode, UPDATED_AT = CURRENT_TIMESTAMP WHERE REQUEST_ID = :requestId", nativeQuery = true)
    void updateCompletionTimeAndError(@Param("requestId") String requestId, 
                                    @Param("completionTime") LocalDateTime completionTime,
                                    @Param("errorMessage") String errorMessage,
                                    @Param("errorCode") String errorCode);

    /**
     * Update only reference ID (preserves completion time and other fields)
     */
    @Modifying(clearAutomatically = true)
    @Query(value = "UPDATE REQUEST_TRACKING SET REFERENCE_ID = :referenceId, UPDATED_AT = CURRENT_TIMESTAMP WHERE REQUEST_ID = :requestId", nativeQuery = true)
    void updateReferenceIdOnly(@Param("requestId") String requestId, @Param("referenceId") String referenceId);

    /**
     * Update both completion time and reference ID atomically
     */
    @Modifying(clearAutomatically = true)
    @Query(value = "UPDATE REQUEST_TRACKING SET COMPLETION_TIME = :completionTime, REFERENCE_ID = :referenceId, UPDATED_AT = CURRENT_TIMESTAMP WHERE REQUEST_ID = :requestId", nativeQuery = true)
    void updateCompletionTimeAndReferenceId(@Param("requestId") String requestId, 
                                          @Param("completionTime") LocalDateTime completionTime,
                                          @Param("referenceId") String referenceId);

    /**
     * Find slow requests (response time > threshold in milliseconds)
     */
    @Query(value = "SELECT ID, REQUEST_ID, UNIT_ID, API_IDENTIFIER, REFERENCE_ID, REQUEST_TIME, COMPLETION_TIME, ERROR_MESSAGE, ERROR_CODE, UPDATED_AT FROM REQUEST_TRACKING WHERE COMPLETION_TIME IS NOT NULL AND TIMESTAMPDIFF(MILLISECOND, REQUEST_TIME, COMPLETION_TIME) > :thresholdMs ORDER BY REQUEST_TIME DESC", nativeQuery = true)
    List<RequestTracking> findSlowRequests(@Param("thresholdMs") Long thresholdMs);

    /**
     * Get API performance summary
     */
    @Query(value = """
        SELECT 
            API_IDENTIFIER,
            COUNT(*) as TOTAL_REQUESTS,
            COUNT(CASE WHEN ERROR_CODE IS NULL THEN 1 END) as SUCCESSFUL,
            COUNT(CASE WHEN ERROR_CODE IS NOT NULL THEN 1 END) as FAILED,
            AVG(TIMESTAMPDIFF(MILLISECOND, REQUEST_TIME, COMPLETION_TIME)) as AVG_RESPONSE_TIME
        FROM REQUEST_TRACKING 
        WHERE COMPLETION_TIME IS NOT NULL
        GROUP BY API_IDENTIFIER
        ORDER BY TOTAL_REQUESTS DESC
        """, nativeQuery = true)
    List<Object[]> getApiPerformanceSummary();

    /**
     * Find requests by business reference ID
     */
    @Query(value = "SELECT ID, REQUEST_ID, UNIT_ID, API_IDENTIFIER, REFERENCE_ID, REQUEST_TIME, COMPLETION_TIME, ERROR_MESSAGE, ERROR_CODE, UPDATED_AT FROM REQUEST_TRACKING WHERE REFERENCE_ID = :referenceId ORDER BY REQUEST_TIME DESC", nativeQuery = true)
    List<RequestTracking> findByReferenceId(@Param("referenceId") String referenceId);

    /**
     * Get unit performance summary
     */
    @Query(value = """
        SELECT 
            UNIT_ID,
            COUNT(*) as TOTAL_REQUESTS,
            COUNT(CASE WHEN ERROR_CODE IS NULL THEN 1 END) as SUCCESSFUL,
            COUNT(CASE WHEN ERROR_CODE IS NOT NULL THEN 1 END) as FAILED,
            AVG(TIMESTAMPDIFF(MILLISECOND, REQUEST_TIME, COMPLETION_TIME)) as AVG_RESPONSE_TIME
        FROM REQUEST_TRACKING 
        WHERE COMPLETION_TIME IS NOT NULL
        GROUP BY UNIT_ID
        ORDER BY TOTAL_REQUESTS DESC
        """, nativeQuery = true)
    List<Object[]> getUnitPerformanceSummary();

    /**
     * Find failed requests
     */
    @Query(value = "SELECT ID, REQUEST_ID, UNIT_ID, API_IDENTIFIER, REFERENCE_ID, REQUEST_TIME, COMPLETION_TIME, ERROR_MESSAGE, ERROR_CODE, UPDATED_AT FROM REQUEST_TRACKING WHERE ERROR_CODE IS NOT NULL ORDER BY REQUEST_TIME DESC", nativeQuery = true)
    List<RequestTracking> findFailedRequests();

    /**
     * Find tracking record by request ID with PESSIMISTIC WRITE lock
     * This prevents any other transaction from modifying the record
     * Other transactions will wait until this one completes
     */
    @Lock(LockModeType.PESSIMISTIC_WRITE)
    @Query("SELECT t FROM RequestTracking t WHERE t.requestId = :requestId")
    Optional<RequestTracking> findByRequestIdWithLock(@Param("requestId") String requestId);




}
