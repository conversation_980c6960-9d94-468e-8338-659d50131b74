package com.stpl.tech.attendance.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BiometricUpdateImagePathRequestDTO {
    private String refId;
    private String imageUrl;
    
    // For multiple images support
    private List<ImageData> images;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ImageData {
        private String id;           // Random ID for the image
        private String imageUrl;  // Base64 image data
        private String imageType;    // Image type (PROCESSED, CROPPED, etc.)
    }
} 