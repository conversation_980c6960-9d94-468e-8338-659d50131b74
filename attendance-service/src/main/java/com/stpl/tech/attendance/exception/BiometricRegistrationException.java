package com.stpl.tech.attendance.exception;

import com.stpl.tech.attendance.enums.BiometricErrorCode;
import lombok.Getter;

@Getter
public class BiometricRegistrationException extends RuntimeException {
    private final BiometricErrorCode errorCode;

    public BiometricRegistrationException(BiometricErrorCode errorCode) {
        super(errorCode.getDefaultMessage());
        this.errorCode = errorCode;
    }

    public BiometricRegistrationException(BiometricErrorCode errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public BiometricRegistrationException(BiometricErrorCode errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }
} 