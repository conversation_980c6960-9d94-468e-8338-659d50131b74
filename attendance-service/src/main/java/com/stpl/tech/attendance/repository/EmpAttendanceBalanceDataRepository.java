package com.stpl.tech.attendance.repository;

import com.stpl.tech.attendance.entity.EmpAttendanceBalanceData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Repository for managing employee leave balance data
 */
@Repository
public interface EmpAttendanceBalanceDataRepository extends JpaRepository<EmpAttendanceBalanceData, Long> {

    /**
     * Find leave balance data by employee ID
     * @param empId Employee ID
     * @return Optional containing leave balance data if found
     */
    Optional<EmpAttendanceBalanceData> findByEmpId(Integer empId);
}