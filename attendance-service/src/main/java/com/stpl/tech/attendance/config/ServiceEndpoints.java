package com.stpl.tech.attendance.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;

@Getter
public abstract class ServiceEndpoints {
    
    @Value("${service.base-url:http://34.131.135.245:8080}")
    protected String baseUrl;
    
    @Value("${service.api-version:v1}")
    protected String apiVersion;
    
    protected String buildUrl(String path) {
        return baseUrl + "/" + apiVersion + "/" + path;
    }
    
    protected String buildUrl(String path, String... segments) {
        StringBuilder fullPath = new StringBuilder(path);
        for (String segment : segments) {
            fullPath.append("/").append(segment);
        }
        return buildUrl(fullPath.toString());
    }

    @Value("${biometric.service.base-url:http://34.131.135.245:8080}")
    public abstract void setBaseUrl(String baseUrl);
}