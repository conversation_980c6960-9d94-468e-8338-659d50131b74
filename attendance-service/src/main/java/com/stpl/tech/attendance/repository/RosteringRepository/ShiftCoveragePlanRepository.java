package com.stpl.tech.attendance.repository.RosteringRepository;

import com.stpl.tech.attendance.entity.RosteringEntity.ShiftCoveragePlan;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface ShiftCoveragePlanRepository extends JpaRepository<ShiftCoveragePlan, Integer> {

    /**
     * Find all shift coverage plans by shift cafe mapping ID and status
     */
    List<ShiftCoveragePlan> findByShiftCafeMappingIdAndStatus(Integer shiftCafeMappingId, String status);
    
    /**
     * Find all shift coverage plans by shift cafe mapping ID
     */
    List<ShiftCoveragePlan> findByShiftCafeMappingId(Integer shiftCafeMappingId);

} 