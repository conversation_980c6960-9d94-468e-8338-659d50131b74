package com.stpl.tech.attendance.repository;

import com.stpl.tech.attendance.entity.AttendanceSyncRecord;
import com.stpl.tech.attendance.enums.SyncStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Repository
public interface AttendanceSyncRecordRepository extends JpaRepository<AttendanceSyncRecord, Long> {
    
    Optional<AttendanceSyncRecord> findByAttendanceRecordId(Long attendanceRecordId);
    
    boolean existsByAttendanceRecordId(Long attendanceRecordId);
    
    long countBySyncStatus(SyncStatus status);
    
    List<AttendanceSyncRecord> findBySyncStatus(SyncStatus status);
    
    @Query("SELECT ar FROM AttendanceSyncRecord ar WHERE ar.syncStatus = :status AND ar.syncAttempts < :maxAttempts")
    List<AttendanceSyncRecord> findBySyncStatusAndSyncAttemptsLessThan(
        @Param("status") SyncStatus status, 
        @Param("maxAttempts") Integer maxAttempts
    );
    
    @Query("SELECT ar FROM AttendanceSyncRecord ar WHERE ar.employeeId = :employeeId AND DATE(ar.createdAt) BETWEEN :fromDate AND :toDate ORDER BY ar.createdAt DESC")
    List<AttendanceSyncRecord> findByDateRangeAndEmployeeId(
        @Param("fromDate") LocalDate fromDate,
        @Param("toDate") LocalDate toDate,
        @Param("employeeId") Integer employeeId
    );
    
    void deleteByAttendanceRecordId(Long attendanceRecordId);
} 