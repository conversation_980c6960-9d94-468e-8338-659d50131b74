package com.stpl.tech.attendance.cache.listener;

import com.hazelcast.core.EntryEvent;
import com.hazelcast.map.listener.EntryAddedListener;
import com.hazelcast.map.listener.EntryRemovedListener;
import com.hazelcast.map.listener.EntryUpdatedListener;
import com.stpl.tech.attendance.cache.service.UserCacheService;
import com.stpl.tech.attendance.service.impl.EmployeeSearchService;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.domain.model.EmploymentStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class EmployeeCacheEventListener implements 
    EntryAddedListener<Integer, EmployeeBasicDetail>,
    EntryUpdatedListener<Integer, EmployeeBasicDetail>,
    EntryRemovedListener<Integer, EmployeeBasicDetail> {

    private final EmployeeSearchService employeeSearchService;
    private final UserCacheService userCacheService;

    @Autowired
    public EmployeeCacheEventListener(EmployeeSearchService employeeSearchService, UserCacheService userCacheService) {
        this.employeeSearchService = employeeSearchService;
        this.userCacheService = userCacheService;
    }

    @Override
    public void entryAdded(EntryEvent<Integer, EmployeeBasicDetail> event) {
        log.info("Employee cache entry added: {}", event.getKey());
        // Update search indices for new employee
        userCacheService.evictUserCache(event.getValue().getId());
        if(event.getValue().getStatus().equals(EmploymentStatus.ACTIVE)){
        employeeSearchService.updateEmployeeIndices(event.getValue());
    }
    }

    @Override
    public void entryUpdated(EntryEvent<Integer, EmployeeBasicDetail> event) {
        log.info("Employee cache entry updated: {}", event.getKey());
        // Update search indices for updated employee
        userCacheService.evictUserCache(event.getValue().getId());
        if(event.getValue().getStatus().equals(EmploymentStatus.ACTIVE)){
        employeeSearchService.updateEmployeeIndices(event.getValue());
    }
    }

    @Override
    public void entryRemoved(EntryEvent<Integer, EmployeeBasicDetail> event) {
        log.info("Employee cache entry removed: {}", event.getKey());
        // Remove employee from search indices
        userCacheService.evictUserCache(event.getValue().getId());
        employeeSearchService.removeEmployeeFromIndices(event.getKey());
    }


} 