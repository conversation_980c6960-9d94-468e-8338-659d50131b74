package com.stpl.tech.attendance.service;

import com.stpl.tech.attendance.dto.EmpUnitShiftInstanceAnalyticsDTO;
import com.stpl.tech.attendance.dto.RosteringDto.GenericFilterMetadataDTO;
import com.stpl.tech.attendance.dto.RosteringDto.GenericFilterRequestDTO;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Set;

public interface EmpUnitShiftInstanceAnalyticsService {
    
    /**
     * Get employee unit shift instance analytics for a manager for a specific date and time range
     * @param managerId The manager's employee ID
     * @param date The specific date
     * @param startTime Start time of the range
     * @param endTime End time of the range
     * @param filterRequest Optional filters to apply
     * @return Employee unit shift instance analytics with employee counts
     */
    EmpUnitShiftInstanceAnalyticsDTO getEmpUnitShiftInstanceAnalytics(Integer managerId, LocalDate date, LocalTime startTime, LocalTime endTime, GenericFilterRequestDTO filterRequest);
    
    /**
     * Get filter metadata for the analytics API
     * @param managerId The manager's employee ID
     * @return Filter metadata with available filters
     */
    GenericFilterMetadataDTO getFilterMetadata(Integer managerId);
    
    /**
     * Update unit attendance when processing attendance punch
     * @param shiftInstanceId The shift instance ID
     * @param actualUnitId The unit where employee actually attended
     * @param punchTime The punch time
     */
    void updateUnitAttendance(Long shiftInstanceId, Integer actualUnitId, java.time.LocalDateTime punchTime);
    
    /**
     * Get managed unit IDs for a manager with optional filters
     * @param managerId The manager's employee ID
     * @param filterRequest Optional filters to apply
     * @return List of filtered unit IDs
     */
    List<Integer> getManagedUnitIdsWithFilters(Integer managerId, List<Integer> managedUnitIds, GenericFilterRequestDTO filterRequest);
    
    /**
     * Get ideal employee count for specific units and date/time
     * @param unitIds List of unit IDs
     * @param date The specific date
     * @param currentTime Current time for active shift check
     * @return Count of ideal employees
     */
    int getIdealEmployeeCount(List<Integer> unitIds, LocalDate date, java.time.LocalDateTime currentTime);
    
    /**
     * Get actual employee count (unique employees who attended any unit) for specific units and date
     * @param unitIds List of unit IDs
     * @param date The specific date
     * @return Count of unique actual employees
     */
    int getActualEmployeeCount(List<Integer> unitIds, LocalDate date, LocalDateTime currentDate);
    
    /**
     * Get unique employee IDs who actually attended for specific units and date
     * @param unitIds List of unit IDs
     * @param date The specific date
     * @return Set of unique employee IDs who attended
     */
    Set<Integer> getActualEmployeeIds(List<Integer> unitIds, LocalDate date, LocalDateTime currentDate);
    
    /**
     * Get ideal employee IDs for specific units and date/time
     * @param unitIds List of unit IDs
     * @param date The specific date
     * @param currentTime Current time for active shift check
     * @return Set of unique ideal employee IDs
     */
    Set<Integer> getIdealEmployeeIds(List<Integer> unitIds, LocalDate date, java.time.LocalDateTime currentTime);
} 