package com.stpl.tech.attendance.event;

import com.stpl.tech.attendance.model.TransferRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class TransferEventPublisher {
    private final ApplicationEventPublisher eventPublisher;
    
    public void notifyTransferCreated(TransferRequest request) {
        publishEvent(request, TransferEvent.TransferEventType.CREATED);
    }
    
    public void notifyTransferApproved(TransferRequest request) {
        publishEvent(request, TransferEvent.TransferEventType.APPROVED);
    }
    
    public void notifyTransferRejected(TransferRequest request) {
        publishEvent(request, TransferEvent.TransferEventType.REJECTED);
    }
    
    public void notifyTransferCancelled(TransferRequest request) {
        publishEvent(request, TransferEvent.TransferEventType.CANCELLED);
    }
    
    private void publishEvent(TransferRequest request, TransferEvent.TransferEventType eventType) {
        TransferEvent event = new TransferEvent(request, eventType);
        eventPublisher.publishEvent(event);
        log.info("Published transfer event: {} for request: {}", eventType, request.getTransferRequestId());
    }
} 