package com.stpl.tech.attendance.model;

import com.stpl.tech.attendance.enums.SpecialCaseType;
import com.stpl.tech.attendance.enums.PunchType;
import com.stpl.tech.attendance.validation.ValidBiometricDeviceId;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class AttendancePunchRequest {

    private Integer employeeId;

    @NotNull(message = "Base64 image is required")
    private String base64Image;
    
    private String originalImage;

    @NotNull(message = "Unit ID is required")
    @Min(value = 1, message = "Unit ID must be greater than 0")
    private Integer unitId;

    private LocalDateTime punchTime;

    //@Pattern(regexp = "^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$", message = "Invalid MAC address format")
    private String macAddress;

    @NotNull(message = "Geo Location is required")
    private String geoLocation;

    private String createdBy;

    private String remarks;

    private Boolean isSpecialCase;

    private SpecialCaseType specialCaseType;

    private String biometricId;

    private PunchType punchType;

    private Boolean saveFailedImage = true; // Default to false
    
    //@ValidBiometricDeviceId
    private String biometricDeviceId; // Format: terminalId_unitType_unitId

    // For multi-image support
    private List<AttendanceImageRequest> additionalImages;
} 