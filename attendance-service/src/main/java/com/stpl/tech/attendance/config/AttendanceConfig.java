package com.stpl.tech.attendance.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.math.BigDecimal;

@Data
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "attendance")
public class AttendanceConfig {
    private int maxShiftHours = 15;
    private int maxPunchesAllowedPerDay = 5;
    private int duplicatePunchThresholdMinutes = 30;
    private int dayStartHour = 4; // 4 AM
    private int dayEndHour = 4; // 3:59:59 AM
    private BigDecimal minimumWorkHours = BigDecimal.valueOf(7);
} 