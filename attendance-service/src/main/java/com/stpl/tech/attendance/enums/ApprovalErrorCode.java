package com.stpl.tech.attendance.enums;

import lombok.Getter;

@Getter
public enum ApprovalErrorCode {
    APPROVAL_REQUEST_NOT_FOUND("APR_001", "Approval request not found"),
    INVALID_APPROVAL_STEP("APR_002", "Invalid approval step"),
    INVALID_APPROVAL_DECISION("APR_003", "Invalid approval decision"),
    APPROVAL_PROCESSING_ERROR("APR_004", "Failed to process approval request"),
    INVALID_APPROVER("APR_005", "Invalid approver for this request"),
    APPROVAL_STEP_NOT_FOUND("APR_006", "Approval step not found"),
    INVALID_APPROVAL_STATUS("APR_007", "Invalid approval status"),
    APPROVAL_UPDATE_ERROR("APR_008", "Failed to update approval request"),
    INVALID_APPROVAL_METADATA("APR_009", "Invalid approval metadata"),
    APPROVAL_DELETION_ERROR("APR_010", "Failed to delete approval request"),
    INVALID_APPROVAL_TYPE("APR_011", "Invalid approval type"),
    APPROVAL_HISTORY_ERROR("APR_012", "Failed to fetch approval history"),
    INVALID_APPROVAL_COMMENTS("APR_013", "Invalid approval comments"),
    APPROVAL_BULK_PROCESSING_ERROR("APR_014", "Failed to process bulk approval requests"),
    APPROVAL_WORKFLOW_ERROR("APR_015", "Approval workflow error");

    private final String code;
    private final String defaultMessage;

    ApprovalErrorCode(String code, String defaultMessage) {
        this.code = code;
        this.defaultMessage = defaultMessage;
    }
} 