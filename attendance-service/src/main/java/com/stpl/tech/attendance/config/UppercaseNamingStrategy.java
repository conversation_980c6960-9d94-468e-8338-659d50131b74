package com.stpl.tech.attendance.config;

import org.hibernate.boot.model.naming.Identifier;
import org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl;
import org.hibernate.engine.jdbc.env.spi.JdbcEnvironment;

import java.util.Locale;

public class UppercaseNamingStrategy extends PhysicalNamingStrategyStandardImpl {
    
    @Override
    public Identifier toPhysicalTableName(Identifier name, JdbcEnvironment context) {
        if (name == null) {
            return null;
        }
        return new Identifier(name.getText().toUpperCase(Locale.ROOT), name.isQuoted());
    }

    @Override
    public Identifier toPhysicalColumnName(Identifier name, JdbcEnvironment context) {
        if (name == null) {
            return null;
        }
        return new Identifier(name.getText().toUpperCase(Locale.ROOT), name.isQuoted());
    }

    @Override
    public Identifier toPhysicalSequenceName(Identifier name, JdbcEnvironment context) {
        if (name == null) {
            return null;
        }
        return new Identifier(name.getText().toUpperCase(Locale.ROOT), name.isQuoted());
    }

    @Override
    public Identifier toPhysicalCatalogName(Identifier name, JdbcEnvironment context) {
        if (name == null) {
            return null;
        }
        return new Identifier(name.getText().toUpperCase(Locale.ROOT), name.isQuoted());
    }

    @Override
    public Identifier toPhysicalSchemaName(Identifier name, JdbcEnvironment context) {
        if (name == null) {
            return null;
        }
        return new Identifier(name.getText().toUpperCase(Locale.ROOT), name.isQuoted());
    }
} 