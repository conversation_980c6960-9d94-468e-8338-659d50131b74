package com.stpl.tech.attendance.service.RosteringService.impl;

import com.gs.fw.common.mithra.MithraManager;
import com.gs.fw.common.mithra.MithraManagerProvider;
import com.gs.fw.common.mithra.MithraTransaction;
import com.stpl.tech.attendance.context.JwtContext;
import com.stpl.tech.attendance.domain.EmpShiftMapping;
import com.stpl.tech.attendance.domain.EmpShiftMappingFinder;
import com.stpl.tech.attendance.domain.EmpShiftMappingList;
import com.stpl.tech.attendance.domain.ShiftCoveragePlan;
import com.stpl.tech.attendance.domain.ShiftCoveragePlanFinder;
import com.stpl.tech.attendance.domain.ShiftCoveragePlanList;
import com.stpl.tech.attendance.dto.RosteringDto.EmpShiftUpdateRequestDTO;
import com.stpl.tech.attendance.dto.RosteringDto.EmpShiftUpdateResponseDTO;
import com.stpl.tech.attendance.dto.RosteringDto.ShiftCafeMappingRequestDTO;
import com.stpl.tech.attendance.entity.RosteringEntity.RosteringConstants;
import com.stpl.tech.attendance.exception.BusinessException;
import com.stpl.tech.attendance.entity.RosteringEntity.ShiftCafeMapping;
import com.stpl.tech.attendance.entity.RosteringEntity.Shift;
import com.stpl.tech.attendance.repository.RosteringRepository.ShiftCafeMappingRepository;
import com.stpl.tech.attendance.repository.RosteringRepository.ShiftRepository;
import com.stpl.tech.attendance.cache.service.EmployeeShiftDataCacheService;
import com.stpl.tech.attendance.service.EmployeeShiftInstanceRecreationService;
import com.stpl.tech.attendance.service.RosteringService.ReladomoService;
import com.stpl.tech.attendance.service.UnitResolutionService;
import com.stpl.tech.attendance.util.RosteringNotificationUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.common.network.Send;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Reladomo-based service for employee shift operations with bitemporal support
 * This service uses generated Reladomo classes for high-performance bitemporal operations
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ReladomoServiceImpl implements ReladomoService {
    private final ShiftCafeMappingRepository shiftCafeMappingRepository;
    private final ShiftRepository shiftRepository;
    private final EmployeeShiftInstanceRecreationService employeeShiftInstanceRecreationService;
    private final RosteringNotificationUtil rosteringNotificationUtil;
    private final EmployeeShiftDataCacheService employeeShiftDataCacheService;
    private final UnitResolutionService unitResolutionService;

@Override
@Transactional(rollbackFor = Exception.class, readOnly = false , propagation = Propagation.REQUIRED )
public EmpShiftUpdateResponseDTO updateEmpShifts(EmpShiftUpdateRequestDTO request) {
    log.info("Updating employee shift mapping with Reladomo bitemporal: empId={}, shiftId={}, unitId={}, businessFrom={}, businessTo={}",
            request.getEmpId(), request.getShiftId(), request.getUnitId(), request.getBusinessFrom(), request.getBusinessTo());

    // 1. Validate request
    validateShiftUpdateRequest(request);

    int totalUpdated = 0;

    // 2. Transaction 1 - Terminate existing mappings
    MithraTransaction tx1 = MithraManager.getInstance().startOrContinueTransaction();
    try {
        terminateExistingMappings(
                request.getEmpId(),
                request.getBusinessFrom(),
                LocalDateTime.now()
        );
        tx1.commit();
        log.info("Successfully terminated existing shift mappings for empId: {}", request.getEmpId());
    } catch (Exception e) {
        tx1.rollback();
        log.error("Failed to terminate existing shift mappings for empId: {}", request.getEmpId(), e);
        throw new BusinessException("Failed to terminate existing mappings", e);
    }

    // 3. Transaction 2 - Create new mapping
    MithraTransaction tx2 = MithraManager.getInstance().startOrContinueTransaction();
    try {
        createReladomoBitemporalMapping(
                request.getEmpId(),
                request.getShiftId(),
                request,
                request.getBusinessFrom(),
                request.getBusinessTo(),
                LocalDateTime.now()
        );
        tx2.commit();
        log.info("Successfully created new shift mapping for empId: {}", request.getEmpId());
        totalUpdated++;
    } catch (Exception e) {
        tx2.rollback();
        log.error("Failed to create new shift mapping for empId: {}", request.getEmpId(), e);
        throw new BusinessException("Failed to create new shift mapping", e);
    }

//     4. Trigger recreation of shift instances
    try {
        triggerShiftInstanceRecreation(
                request.getEmpId(),
                request.getBusinessFrom().toLocalDate(),
                request.getUpdatedBy()
        );
    } catch (Exception e) {
        log.warn("Failed to recreate shift instances for empId: {}", request.getEmpId(), e);
        // Do not throw
    }

//     5. Send notification
    try {
        rosteringNotificationUtil.sendRosteringNotification(request, RosteringConstants.SHIFT_ASSIGNED);
    } catch (Exception e) {
        log.warn("Failed to send rostering notification for empId: {}", request.getEmpId(), e);
        // Do not throw
    }

    // 6. Final Response
    return EmpShiftUpdateResponseDTO.builder()
            .success(true)
            .message("Shift created/updated successfully!")
            .updatedShifts(request.getShiftId())
            .updatedEmployee(request.getEmpId())
            .totalUpdatedMappings(totalUpdated)
            .build();
}

    /**
     * Trigger recreation of shift instances from effective date
     * @param empId Employee ID
     * @param effectiveDate Effective date from which to recreate instances
     * @param updatedBy User who made the change
     */
    private void triggerShiftInstanceRecreation(Integer empId, java.time.LocalDate effectiveDate, String updatedBy) {
        try {
            log.info("Triggering shift instance recreation for employee {} from effective date {}", empId, effectiveDate);
            employeeShiftInstanceRecreationService.recreateShiftInstancesFromEffectiveDate(empId, effectiveDate, updatedBy);
            
            // Refresh cache for affected date range
            refreshCacheForDateRange(empId, effectiveDate);
            
        } catch (Exception e) {
            log.error("Error triggering shift instance recreation for employee {} from effective date {}", 
                     empId, effectiveDate, e);
            // Don't throw exception as this is a side effect
        }
    }

    /**
     * Refresh cache for employee shift data from effective date onwards
     * This ensures that any cached data is invalidated and will be recalculated
     * when next requested
     * 
     * @param empId Employee ID
     * @param effectiveDate Effective date from which to refresh cache
     */
    private void refreshCacheForDateRange(Integer empId, java.time.LocalDate effectiveDate) {
        try {
            log.info("Refreshing cache for employee {} from effective date {}", empId, effectiveDate);
            
            // Calculate end date for cache refresh (e.g., 30 days from effective date)
            java.time.LocalDate endDate = effectiveDate.plusDays(7);
            
            // Evict cache for each date in the range
            for (java.time.LocalDate date = effectiveDate; !date.isAfter(endDate); date = date.plusDays(1)) {
                // Invalidate employee shift data cache
                employeeShiftDataCacheService.evictEmployeeShiftData(empId, date);
                
                // Invalidate unit resolution caches
                unitResolutionService.evictEmployeeCaches(empId, date);
                
                // Also invalidate unit cache for the employee's unit on this date
                Integer unitId = unitResolutionService.getUnitIdForEmployee(empId, date);
                if (unitId != null) {
                    unitResolutionService.evictUnitCaches(unitId, date);
                }
                
                log.debug("Evicted all caches for employee {} on date {}", empId, date);
            }
            
            log.info("Successfully refreshed all caches for employee {} from {} to {}", empId, effectiveDate, endDate);
            
        } catch (Exception e) {
            log.error("Error refreshing cache for employee {} from effective date {}", empId, effectiveDate, e);
            // Don't throw exception as this is a side effect
        }
    }

    /**
     * Perform bulk update using Reladomo with bitemporal pattern
     */
    private UpdateResult performReladomoBulkUpdate(EmpShiftUpdateRequestDTO request, MithraTransaction tx) {
        log.debug("Performing Reladomo bitemporal update for employee {}, shift {}, and unit {}",
                request.getEmpId(), request.getShiftId(), request.getUnitId());

        int totalUpdated = 0;
        LocalDateTime currentTime = LocalDateTime.now();
        LocalDateTime businessFrom = request.getBusinessFrom();
        LocalDateTime businessTo = request.getBusinessTo();

        Integer empId = request.getEmpId();
        Integer shiftId = request.getShiftId();

        // Terminate existing active mappings for this employee in the business date range
        terminateExistingMappings(empId, businessFrom, currentTime);

        // Create new bitemporal mapping using Reladomo
//        createReladomoBitemporalMapping(empId, shiftId, request, businessFrom, businessTo, currentTime);
        totalUpdated++;
        log.debug("Created Reladomo bitemporal mapping: empId={}, shiftId={}, unitId={}, businessFrom={}, businessTo={}",
                 empId, shiftId, request.getUnitId(), businessFrom, businessTo);

        return new UpdateResult(totalUpdated);
    }
    /**
     * Validate the update request
     */
    private void validateShiftUpdateRequest(EmpShiftUpdateRequestDTO request) {
        if (request == null) {
            throw new IllegalArgumentException("Request cannot be null");
        }

        if (request.getEmpId() == null) {
            throw new IllegalArgumentException("Employee ID is required");
        }

        if (request.getShiftId() == null) {
            throw new IllegalArgumentException("Shift ID is required");
        }

        if (request.getUnitId() == null) {
            throw new IllegalArgumentException("Unit ID is required");
        }

        if (request.getBusinessFrom() == null) {
            throw new IllegalArgumentException("Business from date is required");
        }

        // businessTo can be null (meaning infinity) in bitemporal scenarios
        if (request.getBusinessTo() != null && request.getBusinessFrom().isAfter(request.getBusinessTo())) {
            throw new IllegalArgumentException("Business from date cannot be after business to date");
        }

        // Validate that empId, shiftId, and unitId are positive
        if (request.getEmpId() <= 0) {
            throw new IllegalArgumentException("Employee ID must be a positive integer");
        }

        if (request.getShiftId() <= 0) {
            throw new IllegalArgumentException("Shift ID must be a positive integer");
        }

        if (request.getUnitId() <= 0) {
            throw new IllegalArgumentException("Unit ID must be a positive integer");
        }

        // Validate that shift has not already started for the business date
        validateShiftStartTime(request);

        log.debug("Update request validation passed for employee {}, shift {}, and unit {}",
                 request.getEmpId(), request.getShiftId(), request.getUnitId());
    }

    /**
     * Validate that the shift has not already started for the given business date
     * @param request The shift update request
     */
    private void validateShiftStartTime(EmpShiftUpdateRequestDTO request) {
        try {
            // Get shift details from repository
            com.stpl.tech.attendance.entity.RosteringEntity.Shift shift = shiftRepository.findById(request.getShiftId())
                    .orElseThrow(() -> new IllegalArgumentException("Shift not found with ID: " + request.getShiftId()));

            // Get the business date (date part only)
            LocalDate businessDate = request.getBusinessFrom().toLocalDate();
            
            // Create the actual shift start time for the business date
            LocalDateTime actualShiftStartTime = businessDate.atTime(shift.getStartTime().toLocalTime());
            
            // Get current time
            LocalDateTime currentTime = LocalDateTime.now();
            
            // Check if shift has already started
            if (currentTime.isAfter(actualShiftStartTime)) {
                String errorMessage = String.format(
                    "Shift cannot be mapped as it is already started for %s.",
                    businessDate.format(java.time.format.DateTimeFormatter.ofPattern("dd MMM yyyy"))
                );
                throw new BusinessException(errorMessage);
            }
            
            log.debug("Shift start time validation passed for shift {} on date {}", 
                     shift.getShiftName(), businessDate);
                     
        } catch (BusinessException e) {
            // Re-throw BusinessException as is
            throw e;
        } catch (Exception e) {
            log.error("Error validating shift start time for shiftId: {}", request.getShiftId(), e);
            throw new BusinessException("Error validating shift start time", e);
        }
    }

    /**
     * Helper class to hold update results
     */
    private static class UpdateResult {
        private final int totalUpdated;

        public UpdateResult(int totalUpdated) {
            this.totalUpdated = totalUpdated;
        }

        public int getTotalUpdated() {
            return totalUpdated;
        }
    }
    /**
     * Terminate existing active mappings for an employee using Reladomo
     * For bitemporal objects, we use terminate() method instead of manually setting processing dates
     */

    @Transactional(rollbackFor = Exception.class, readOnly = false )
    @Override
    public void terminateExistingMappings(Integer empId, LocalDateTime businessFrom, LocalDateTime processingFrom) {
        log.info("Terminating existing mappings for empId: {} at businessFrom: {}", empId, businessFrom);

        // Find ALL existing active mappings for this employee
        // We need to terminate ALL active mappings regardless of their business date
        // For bitemporal objects, we need to find records where processingDate is at infinity
        try {
            Timestamp infinityTimestamp = java.sql.Timestamp.valueOf(RosteringConstants.INFINITY_TIME);
            EmpShiftMappingList existingMappings = EmpShiftMappingFinder.findMany(
                    EmpShiftMappingFinder.empId().eq(empId)
                            .and(EmpShiftMappingFinder.businessDate().equalsEdgePoint())
                            .and(EmpShiftMappingFinder.processingDate().equalsEdgePoint())
                            .and(EmpShiftMappingFinder.processingDate().eq(infinityTimestamp)));

            log.info("Found {} existing mappings to terminate for empId: {}", existingMappings.size(), empId);

            // Convert businessFrom to start of day timestamp for proper date comparison
            Timestamp newBusinessFromTimestamp = Timestamp.valueOf(businessFrom.toLocalDate().atStartOfDay());
            // Terminate existing mappings and set their businessTo to the new businessFrom
            for (EmpShiftMapping existing : existingMappings) {
                // Log before terminating since we can't access properties after terminate()
                Integer mappingId = existing.getId();
                Integer shiftId = existing.getShiftId();
                Timestamp existingBusinessDate = existing.getBusinessDate();
                Timestamp existingBusinessTo = existing.getBusinessDateTo();
                Timestamp terminationTime = Timestamp.valueOf(processingFrom);
//                Timestamp terminationTime = Timestamp.valueOf(LocalDateTime.now().minusSeconds(1));
                log.info("Terminating mapping: empId={}, mappingId={}, shiftId={}, businessDate={}, currentBusinessTo={}, newBusinessTo={}",
                         empId, mappingId, shiftId, existingBusinessDate, existingBusinessTo, newBusinessFromTimestamp);

                // In Reladomo bitemporal pattern, we cannot directly update business dates on existing records
                // Instead, we terminate the existing record and let the new record handle the business date range
                // The business date range is handled by the new mapping that will be created

                // Terminate the existing mapping using Reladomo's terminate() method
//                 existing.setBusinessDateTo(Timestamp.valueOf(businessFrom));
                existing.terminate();
//                existing.terminateUntil(Timestamp.valueOf(businessFrom));

                log.debug("Successfully terminated mapping for empId: {}, mappingId: {}", empId, mappingId);
            }
        } catch (Exception e) {
            log.error("Error terminating existing mappings for empId: {}", empId, e);
        }
    }

    @Transactional(rollbackFor = Exception.class, readOnly = false , propagation = Propagation.REQUIRES_NEW)
    @Override
    public void createReladomoBitemporalMapping(Integer empId, Integer shiftId, EmpShiftUpdateRequestDTO request, LocalDateTime businessFrom,
                                                LocalDateTime businessTo, LocalDateTime currentTime) {

        log.debug("Creating Reladomo bitemporal mapping for empId: {}, shiftId: {}, unitId: {}, businessFrom: {}, businessTo: {}",
                empId, shiftId, request.getUnitId(), businessFrom, businessTo);
        try {

            // Convert business dates to start of day (00:00:00) for proper date-only storage
            LocalDateTime businessFromStartOfDay = businessFrom.toLocalDate().atStartOfDay();
            LocalDateTime businessToStartOfDay = businessTo != null ? businessTo.toLocalDate().atStartOfDay() : null;

            // Create new mapping with business date at start of day - Reladomo handles processing date automatically
            EmpShiftMapping newMapping = new EmpShiftMapping(Timestamp.valueOf(businessFromStartOfDay));
            // Set basic attributes
            newMapping.setEmpId(empId);
            newMapping.setShiftId(shiftId);
            newMapping.setUnitId(request.getUnitId());
            newMapping.setStatus(RosteringConstants.ACTIVE);

            // Set expected dates if provided
            if (request.getExpectedArrivalTime() != null) {
                newMapping.setExpectedStartDate(Timestamp.valueOf(request.getExpectedArrivalTime()));
            }
            if (businessTo != null) {
                newMapping.setExpectedEndDate(Timestamp.valueOf(businessTo));
            }

            // Set audit fields with start of day timestamps
            String updatedBy = request.getUpdatedBy() != null ? request.getUpdatedBy() : JwtContext.getInstance().getUserId().toString();
            newMapping.setBusinessDateFrom(Timestamp.valueOf(businessFromStartOfDay));
            
            // Set businessDateTo - if businessTo is null, set to infinity, otherwise use the provided date
            if (businessToStartOfDay != null) {
                newMapping.setBusinessDateTo(Timestamp.valueOf(businessToStartOfDay));
            } else {
                // Set to infinity when businessTo is null (meaning the mapping is valid until infinity)
                newMapping.setBusinessDateTo(Timestamp.valueOf(RosteringConstants.INFINITY_TIME));
            }
            newMapping.setCreatedBy(updatedBy);
            newMapping.setCreationTime(Timestamp.valueOf(currentTime));
            newMapping.setUpdatedBy(updatedBy);
            newMapping.setUpdationTime(Timestamp.valueOf(currentTime));

            // Insert the new row - Reladomo automatically handles processing dates
            newMapping.insert();

            log.info("Reladomo insert successful for empId={}, shiftId={}, unitId={}, businessFrom={} (start of day), businessTo={} (start of day)",
                    empId, shiftId, request.getUnitId(), businessFromStartOfDay, businessToStartOfDay);
        } catch (Exception e) {
            log.error("Error creating Reladomo bitemporal mapping for empId: {}, shiftId: {}, unitId: {}", 
                    empId, shiftId, request.getUnitId(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = false)
    public void updateShiftCoveragePlan(ShiftCafeMappingRequestDTO request) {
        log.info("Updating shift coverage plan with Reladomo unitemporal: shiftId={}, unitId={}",
                request.getShiftId(), request.getUnitId());
        try {
            // Find the shift cafe mapping to get the cafe_shift_mapping_id
            List<ShiftCafeMapping> shiftCafeMapping = shiftCafeMappingRepository.findByShiftIdAndUnitIdAndStatus(
                    request.getShiftId(), request.getUnitId());

            Integer shiftCafeMappingId;

            if (!shiftCafeMapping.isEmpty()) {
                // If exists → Use the existing shiftCafeMappingId
                shiftCafeMappingId = shiftCafeMapping.get(0).getShiftCafeMappingId();
                log.info("Found existing shift cafe mapping with ID: {}", shiftCafeMappingId);
            } else {
                // If doesn't exist → Create a new ShiftCafeMapping first
                log.info("No active shift cafe mapping found, creating new one for shiftId: {} and unitId: {}",
                        request.getShiftId(), request.getUnitId());

                String createdBy = JwtContext.getInstance().getUserId().toString();
                ShiftCafeMapping newMapping = ShiftCafeMapping.builder()
                    .shiftId(request.getShiftId())
                    .unitId(request.getUnitId())
                    .status(RosteringConstants.ACTIVE)
                    .createdBy(createdBy)
                    .updatedBy(createdBy)
                    .build();

//                newMapping = shiftCafeMappingRepository.save(newMapping);
                shiftCafeMappingId = newMapping.getShiftCafeMappingId();
                log.info("Created new shift cafe mapping with ID: {}", shiftCafeMappingId);
            }

            Integer userId = JwtContext.getInstance().getUserId();

            // Validate request
            validateShiftCoveragePlanRequest(request, userId);

            MithraTransaction tx = MithraManager.getInstance().startOrContinueTransaction();
            try {
                // Perform unitemporal update operations
                performUnitemporalShiftCoveragePlanUpdate(shiftCafeMappingId, request, userId);

                tx.commit();
                log.info("Successfully updated shift coverage plan using unitemporal operations for shiftCafeMappingId: {}", shiftCafeMappingId);
            } catch (Exception e) {
                tx.rollback();
                log.error("Error updating shift coverage plan with unitemporal operations", e);
                throw new BusinessException("Failed to update shift coverage plan", e);
            }
        } catch (Exception e) {
            log.error("Error updating shift coverage plan", e);
            throw new BusinessException("Failed to update shift coverage plan", e);
        }
    }

    /**
     * Perform unitemporal update using Reladomo for shift coverage plan with bulk insert optimization
     */
    private void performUnitemporalShiftCoveragePlanUpdate(Integer shiftCafeMappingId, ShiftCafeMappingRequestDTO request, Integer userId) {
        log.debug("Performing Reladomo unitemporal update for shift coverage plan: shiftCafeMappingId={}, dayWiseIdealCount={}, allDaySame={}, idealCount={}",
                shiftCafeMappingId, request.getDayWiseIdealCount(), request.isAllDaySame(), request.getIdealCount());

        Timestamp currentTime = new Timestamp(System.currentTimeMillis());

        // Start a single Reladomo transaction for all days
        MithraTransaction tx = MithraManager.getInstance().startOrContinueTransaction();
        try {
            Map<String, Integer> dayWiseIdealCount = new HashMap<>();
            
            // Handle allDaySame case - create map with same idealCount for all days
            if (request.isAllDaySame() && request.getIdealCount() != null) {
                log.debug("Processing allDaySame=true with idealCount={}", request.getIdealCount());
                dayWiseIdealCount.put(RosteringConstants.DAY_MONDAY, request.getIdealCount());
                dayWiseIdealCount.put(RosteringConstants.DAY_TUESDAY, request.getIdealCount());
                dayWiseIdealCount.put(RosteringConstants.DAY_WEDNESDAY, request.getIdealCount());
                dayWiseIdealCount.put(RosteringConstants.DAY_THURSDAY, request.getIdealCount());
                dayWiseIdealCount.put(RosteringConstants.DAY_FRIDAY, request.getIdealCount());
                dayWiseIdealCount.put(RosteringConstants.DAY_SATURDAY, request.getIdealCount());
                dayWiseIdealCount.put(RosteringConstants.DAY_SUNDAY, request.getIdealCount());
            } else if (request.getDayWiseIdealCount() != null && !request.getDayWiseIdealCount().isEmpty()) {
                // Use provided dayWiseIdealCount
                dayWiseIdealCount = request.getDayWiseIdealCount();
            } else {
                log.warn("No valid ideal count data provided for shiftCafeMappingId: {}", shiftCafeMappingId);
                tx.commit();
                return;
            }
            
            // Terminate all existing active records first
            terminateAllExistingShiftCoveragePlans(shiftCafeMappingId);
            
            // Create all new records in bulk
            createBulkShiftCoveragePlanMappings(shiftCafeMappingId, dayWiseIdealCount, currentTime, userId);
            
            tx.commit();
            log.info("Successfully updated all days for shiftCafeMappingId: {} using bulk operations", shiftCafeMappingId);
        } catch (Exception e) {
            tx.rollback();
            log.error("Error updating shift coverage plan for shiftCafeMappingId: {}", shiftCafeMappingId, e);
            throw e;
        }
    }

    /**
     * Update shift coverage plan for a specific day using unitemporal Reladomo operations
     */
    private void updateShiftCoveragePlanForDay(Integer shiftCafeMappingId, String day, Integer idealCount,
                                               Timestamp currentTime, Integer userId) {
        log.debug("Updating shift coverage plan for shiftCafeMappingId: {}, day: {}, idealCount: {}",
                shiftCafeMappingId, day, idealCount);

        try {
            // Find existing ACTIVE records (at infinity processing date) for this shift cafe mapping and day
            Timestamp infinityTimestamp = java.sql.Timestamp.valueOf(RosteringConstants.INFINITY_TIME);
            ShiftCoveragePlanList existingPlans = ShiftCoveragePlanFinder.findMany(
                    ShiftCoveragePlanFinder.shiftCafeMappingId().eq(shiftCafeMappingId).
                            and(ShiftCoveragePlanFinder.processingDate().eq(infinityTimestamp))); // Only find active records

            // Terminate existing active records
            terminateExistingShiftCoveragePlansForDay(existingPlans, shiftCafeMappingId, day);

            // Create new record
            createUnitemporalShiftCoveragePlanMapping(shiftCafeMappingId, day, idealCount, currentTime, userId);

        } catch (Exception e) {
            log.error("Error updating shift coverage plan for shiftCafeMappingId: {}, day: {}",
                    shiftCafeMappingId, day, e);
            throw e;
        }
    }

    /**
     * Validate the shift coverage plan update request
     */
    private void validateShiftCoveragePlanRequest(ShiftCafeMappingRequestDTO request, Integer userId) {
        if (request == null) {
            throw new IllegalArgumentException("Request cannot be null");
        }

        if (request.getShiftId() == null) {
            throw new IllegalArgumentException("Shift ID is required");
        }

        if (request.getUnitId() == null) {
            throw new IllegalArgumentException("Unit ID is required");
        }

        if (userId == null) {
            throw new IllegalArgumentException("User ID is required");
        }

        // Validate ideal count data
        if (request.isAllDaySame()) {
            if (request.getIdealCount() == null || request.getIdealCount() < 0) {
                throw new IllegalArgumentException("Ideal count must be a non-negative integer when allDaySame is true");
            }
        } else {
            if (request.getDayWiseIdealCount() == null || request.getDayWiseIdealCount().isEmpty()) {
                throw new IllegalArgumentException("Day wise ideal count is required when allDaySame is false");
            }

            // Validate each day's ideal count
            for (String dayStr : request.getDayWiseIdealCount().keySet()) {
                // Validate day name format
                if (!isValidDayName(dayStr)) {
                    throw new IllegalArgumentException("Invalid day format: " + dayStr + ". Use day names (" + 
                    RosteringConstants.DAY_MONDAY + ", " + RosteringConstants.DAY_TUESDAY + ", etc.)");
                }

                Integer idealCount = request.getDayWiseIdealCount().get(dayStr);
                if (idealCount < 0) {
                    throw new IllegalArgumentException("Ideal count must be a non-negative integer for day " + dayStr);
                }
            }
        }

        log.debug("Shift coverage plan update request validation passed for shiftId: {}, unitId: {}",
                 request.getShiftId(), request.getUnitId());
    }

    /**
     * Terminate all existing active shift coverage plans for a shift cafe mapping using unitemporal Reladomo
     * This is optimized for bulk operations - terminates all active records at once
     */
    private void terminateAllExistingShiftCoveragePlans(Integer shiftCafeMappingId) {
        log.debug("Terminating all existing active shift coverage plans for shiftCafeMappingId: {}", shiftCafeMappingId);
        try {
            // Find all existing ACTIVE records (at infinity processing date) for this shift cafe mapping
            Timestamp infinityTimestamp = java.sql.Timestamp.valueOf(RosteringConstants.INFINITY_TIME);
            ShiftCoveragePlanList existingPlans = ShiftCoveragePlanFinder.findMany(
                    ShiftCoveragePlanFinder.shiftCafeMappingId().eq(shiftCafeMappingId)
                            .and(ShiftCoveragePlanFinder.processingDate().eq(infinityTimestamp))); // Only find active records

            log.debug("Found {} existing active shift coverage plans to terminate for shiftCafeMappingId: {}", 
                     existingPlans.size(), shiftCafeMappingId);

            // Terminate all existing active records
            for (ShiftCoveragePlan existing : existingPlans) {
                // Log before terminating since we can't access properties after terminate()
                Integer planId = existing.getId();
                String existingDay = existing.getDay();
                
                log.debug("Terminating shift coverage plan: id={}, shiftCafeMappingId={}, day={}",
                         planId, shiftCafeMappingId, existingDay);
                
                MithraManagerProvider.getMithraManager().executeTransactionalCommand(tx -> {
                    log.debug("Before terminate: from=" + existing.getProcessingDateFrom() + ", to=" + existing.getProcessingDateTo());
                    existing.terminate(); // ✅ Should work if `to = infinity`
                    return null;
                });
                
                log.debug("Successfully terminated shift coverage plan: id={}, shiftCafeMappingId={}, day={}",
                        planId, shiftCafeMappingId, existingDay);
            }
            
            log.info("Successfully terminated {} existing shift coverage plans for shiftCafeMappingId: {}", 
                    existingPlans.size(), shiftCafeMappingId);
        } catch (Exception e) {
            log.error("Error terminating existing shift coverage plans for shiftCafeMappingId: {}", shiftCafeMappingId, e);
            throw e;
        }
    }

    /**
     * Terminate existing active shift coverage plans for a specific day using unitemporal Reladomo
     */
    private void terminateExistingShiftCoveragePlansForDay(ShiftCoveragePlanList existingPlans,
                                                           Integer shiftCafeMappingId, String day) {
        //log.debug("Terminating {} existing shift coverage plans for shiftCafeMappingId: {}, day: {}",
         //       existingPlans.size(), shiftCafeMappingId, day);
        try {
            // Since we already filtered for active records (at infinity), we can terminate all of them
            for (ShiftCoveragePlan existing : existingPlans) {
                // Log before terminating since we can't access properties after terminate()
                Integer planId = existing.getId();
                String existingDay = existing.getDay();
                //existing.setProcessingDateTo(new Timestamp(System.currentTimeMillis()));
                //existing.setProcessingDateTo(Timestamp.valueOf(LocalDateTime.now()));
//                ShiftCoveragePlanList shiftCoveragePlans = new ShiftCoveragePlanList();
//                shiftCoveragePlans.add(existing);
                log.info("From: " + existing.getProcessingDateFrom());
                log.info("To:   " + existing.getProcessingDateTo());
                MithraManagerProvider.getMithraManager().executeTransactionalCommand(tx -> {
                    log.info("Before terminate: from=" + existing.getProcessingDateFrom() + ", to=" + existing.getProcessingDateTo());
                    log.info("Before terminate: processdate=" + existing.getProcessingDate());

                    existing.terminate(); // ✅ Should work if `to = infinity`

                    return null;
                });
                //existing.terminate();
                log.debug("Successfully terminated shift coverage plan: id={}, shiftCafeMappingId={}, day={}",
                        planId, shiftCafeMappingId, existingDay);
            }
        } catch (Exception e) {
            log.error("Error terminating existing shift coverage plans for shiftCafeMappingId: {}, day: {}",
                    shiftCafeMappingId, day, e);
            throw e;
        }
    }

    /**
     * Create new unitemporal shift coverage plan mapping using Reladomo
     */
    private void createUnitemporalShiftCoveragePlanMapping(Integer shiftCafeMappingId, String day,
                                                          Integer idealCount, Timestamp currentTime, Integer userId) {
        log.debug("Creating Reladomo unitemporal shift coverage plan mapping for shiftCafeMappingId: {}, day: {}, idealCount: {}",
                shiftCafeMappingId, day, idealCount);
        try {
            // For unitemporal objects, we need to create with infinity processing date
            // This indicates it's the current active record
            Timestamp infinityTimestamp = java.sql.Timestamp.valueOf(RosteringConstants.INFINITY_TIME);
            ShiftCoveragePlan newPlan = new ShiftCoveragePlan(infinityTimestamp);

            // Set basic attributes
            newPlan.setShiftCafeMappingId(shiftCafeMappingId);
            newPlan.setDay(day); // Store day as string directly
            newPlan.setIdealCount(idealCount);
            newPlan.setStatus(RosteringConstants.ACTIVE);

            // Set audit fields
            String updatedBy = userId != null ? String.valueOf(userId) : "SYSTEM";
            newPlan.setCreatedBy(updatedBy);
            newPlan.setCreationTime(currentTime);
            newPlan.setUpdatedBy(updatedBy);
            newPlan.setUpdationTime(currentTime);

            ShiftCoveragePlanList shiftCoveragePlans = new ShiftCoveragePlanList();
            shiftCoveragePlans.add(newPlan);

            // Insert the new row - Reladomo automatically handles processing dates
            shiftCoveragePlans.insertAll();

            log.info("Reladomo insert successful for shift coverage plan: shiftCafeMappingId={}, day={}, idealCount={}",
                    shiftCafeMappingId, day, idealCount);
        } catch (Exception e) {
            log.error("Error creating Reladomo unitemporal shift coverage plan mapping for shiftCafeMappingId: {}, day: {}",
                    shiftCafeMappingId, day, e);
            throw e;
        }
    }

    /**
     * Create bulk shift coverage plan mappings using Reladomo for optimal performance
     * This method creates all day mappings in a single bulk insert operation
     */
    private void createBulkShiftCoveragePlanMappings(Integer shiftCafeMappingId, 
                                                    Map<String, Integer> dayWiseIdealCount,
                                                    Timestamp currentTime, Integer userId) {
        log.debug("Creating bulk Reladomo unitemporal shift coverage plan mappings for shiftCafeMappingId: {}, days: {}",
                shiftCafeMappingId, dayWiseIdealCount.keySet());

        try {
            // For unitemporal objects, we need to create with infinity processing date
            // This indicates it's the current active record
            Timestamp infinityTimestamp = java.sql.Timestamp.valueOf(RosteringConstants.INFINITY_TIME);
            
            // Create a list to hold all new plans for bulk insert
            ShiftCoveragePlanList newPlans = new ShiftCoveragePlanList();
            
            // Set audit fields once for all records
            String updatedBy = userId != null ? String.valueOf(userId) : "SYSTEM";
            
            // Create individual plan objects for each day
            for (Map.Entry<String, Integer> entry : dayWiseIdealCount.entrySet()) {
                String day = entry.getKey();
                Integer idealCount = entry.getValue();
                
                ShiftCoveragePlan newPlan = new ShiftCoveragePlan(infinityTimestamp);

                // Set basic attributes
                newPlan.setShiftCafeMappingId(shiftCafeMappingId);
                newPlan.setDay(day); // Store day as string directly
                newPlan.setIdealCount(idealCount);
                newPlan.setStatus(RosteringConstants.ACTIVE);

                // Set audit fields
                newPlan.setCreatedBy(updatedBy);
                newPlan.setCreationTime(currentTime);
                newPlan.setUpdatedBy(updatedBy);
                newPlan.setUpdationTime(currentTime);
                
                // Add to the list for bulk insert
                newPlans.add(newPlan);
                
                log.debug("Prepared shift coverage plan for bulk insert: shiftCafeMappingId={}, day={}, idealCount={}",
                        shiftCafeMappingId, day, idealCount);
            }

            // Perform bulk insert - Reladomo automatically handles processing dates
            newPlans.insertAll();

            log.info("Bulk Reladomo insert successful for {} shift coverage plans: shiftCafeMappingId={}, days={}",
                    newPlans.size(), shiftCafeMappingId, dayWiseIdealCount.keySet());
        } catch (Exception e) {
            log.error("Error creating bulk Reladomo unitemporal shift coverage plan mappings for shiftCafeMappingId: {}, days: {}",
                    shiftCafeMappingId, dayWiseIdealCount.keySet(), e);
            throw e;
        }
    }

    /**
     * Helper method to validate day names
     */
    private boolean isValidDayName(String dayStr) {
        if (dayStr == null || dayStr.trim().isEmpty()) {
            return false;
        }

        String upperDay = dayStr.toUpperCase().trim();
        return upperDay.equals(RosteringConstants.DAY_MONDAY) || upperDay.equals(RosteringConstants.DAY_TUESDAY) || 
               upperDay.equals(RosteringConstants.DAY_WEDNESDAY) || upperDay.equals(RosteringConstants.DAY_THURSDAY) || 
               upperDay.equals(RosteringConstants.DAY_FRIDAY) || upperDay.equals(RosteringConstants.DAY_SATURDAY) ||
               upperDay.equals(RosteringConstants.DAY_SUNDAY);
    }
}