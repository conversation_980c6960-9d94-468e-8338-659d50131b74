package com.stpl.tech.attendance.workflow.listener;

import com.stpl.tech.attendance.approval.entity.ApprovalRequest;
import com.stpl.tech.attendance.approval.entity.ApprovalStep;
import com.stpl.tech.attendance.approval.entity.SubApprovalStep;
import com.stpl.tech.attendance.approval.repository.ApprovalRequestRepository;
import com.stpl.tech.attendance.approval.repository.ApprovalStepRepository;
import com.stpl.tech.attendance.approval.repository.SubApprovalStepRepository;
import com.stpl.tech.attendance.entity.EmployeeAttendanceRequest;
import com.stpl.tech.attendance.enums.ApprovalStatus;
import com.stpl.tech.attendance.enums.ApprovalStepStatus;
import com.stpl.tech.attendance.enums.ApprovalType;
import com.stpl.tech.attendance.notification.dto.NotificationRequest;
import com.stpl.tech.attendance.notification.entity.Notification;
import com.stpl.tech.attendance.notification.service.NotificationService;
import com.stpl.tech.attendance.repository.EmpEligibilityMappingRepository;
import com.stpl.tech.attendance.repository.EmployeeAttendanceRequestRepository;
import com.stpl.tech.attendance.service.BiometricRegistrationService;
import com.stpl.tech.attendance.service.EmpEligibilityService;
import com.stpl.tech.attendance.service.TransferService;
import com.stpl.tech.attendance.service.EmployeeAttendanceService;
import com.stpl.tech.attendance.service.impl.EmployeeAttendanceServiceImpl;
import com.stpl.tech.attendance.util.DateTimeUtil;
import com.stpl.tech.attendance.constants.AppConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.task.service.delegate.DelegateTask;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
@RequiredArgsConstructor
public class ApprovalProcessEndListener implements TaskListener {

    private final ApprovalRequestRepository approvalRequestRepository;
    private final ApprovalStepRepository approvalStepRepository;
    private final SubApprovalStepRepository subApprovalStepRepository;
    private final NotificationService notificationService;
    private final BiometricRegistrationService biometricRegistrationService;
    private final EmpEligibilityService empEligibilityService;
    private final EmpEligibilityMappingRepository empEligibilityMappingRepository;
    private final TransferService transferService;
    private final EmployeeAttendanceService employeeAttendanceService;
    private final EmployeeAttendanceServiceImpl employeeAttendanceServiceImpl;
    private final EmployeeAttendanceRequestRepository employeeAttendanceRequestRepository;

    @Override
    public void notify(DelegateTask execution) {
        String processInstanceId = execution.getProcessInstanceId();
        String requestId = (String) execution.getVariable("requestId");
        String action = (String) execution.getVariable("action");
        String remarks = (String) execution.getVariable("remarks");
        String approverId = (String) execution.getVariable("approverId");

        log.info("Processing approval end event for request: {} with decision: {}", requestId, action);

        ApprovalRequest request = approvalRequestRepository.findById(Long.valueOf(requestId))
                .orElseThrow(() -> new RuntimeException("Approval request not found: " + requestId));

        ApprovalStep mainStep = approvalStepRepository.findByRequestIdAndStepNumber(Long.valueOf(requestId), request.getCurrentStep())
                .orElseThrow(() -> new RuntimeException("Approval step not found for request: " + requestId));
        if(!Objects.equals(mainStep.getStepNumber(), request.getTotalSteps())){
            request.setCurrentStep(request.getCurrentStep() + 1);
            approvalRequestRepository.save(request);
        }
            // Update status of remaining sub-steps
            List<SubApprovalStep> remainingSubSteps = subApprovalStepRepository.findByApprovalStepIdAndStatus(
                mainStep.getId(), ApprovalStepStatus.PENDING);
            
            for (SubApprovalStep remainingSubStep : remainingSubSteps) {
                remainingSubStep.setStatus(ApprovalStepStatus.SKIPPED);
                remainingSubStep.setUpdatedDate(DateTimeUtil.now());
                remainingSubStep.setUpdatedBy(String.valueOf(AppConstants.SYSTEM_EMPLOYEE_ID));
                subApprovalStepRepository.save(remainingSubStep);
            }

            mainStep.setStatus(action.equals("APPROVED") ? ApprovalStepStatus.APPROVED : ApprovalStepStatus.REJECTED);


        approvalStepRepository.save(mainStep);

        // Update request status if this was the last step
        if (Objects.equals(mainStep.getStepNumber(), request.getTotalSteps())) {
            request.setStatus(action.equals("APPROVED") ? ApprovalStatus.APPROVED : ApprovalStatus.REJECTED);
            request.setUpdatedDate(DateTimeUtil.now());
            request.setUpdatedBy(approverId);
            approvalRequestRepository.save(request);

            if(request.getRequestType().equals(ApprovalType.BIOMETRIC_REGISTRATION)){
                try {
                    biometricRegistrationService.handleApprovalResponse(request.getId(), request.getStatus(), approverId);
                    
                    // Handle eligibility mapping for approved biometric registration
                    if (request.getStatus().equals(ApprovalStatus.APPROVED)) {
                        handleEligibilityMapping(request);
                    }
                } catch (Exception e) {
                    log.error("Error handling biometric registration approval response: ", e);
                    throw new RuntimeException(e);
                }
            } else if(request.getRequestType().equals(ApprovalType.EMPLOYEE_TRANSFER)){
                // Handle transfer request approval
                handleTransferRequestApproval(request, action);
            } else if (request.getRequestType().equals(ApprovalType.EMPLOYEE_LEAVE)){
                // Handle leave request approval with new method for proper leave deduction
                employeeAttendanceService.handleApprovalResponse(request, request.getStatus(), "LEAVE");
            } else if (request.getRequestType().equals(ApprovalType.EMPLOYEE_OD)){
                // Handle OD request approval
                employeeAttendanceService.handleApprovalResponse(request, request.getStatus(), "OD");
            } else if (request.getRequestType().equals(ApprovalType.EMPLOYEE_WFH)){
                // Handle WFH request approval
                employeeAttendanceService.handleApprovalResponse(request, request.getStatus(), "WFH");
            } else if (request.getRequestType().equals(ApprovalType.EMPLOYEE_REGULARISATION)){
                // Handle regularisation request approval
                employeeAttendanceService.handleApprovalResponse(request, request.getStatus(), "REGULARISATION");
            } else if (request.getRequestType().equals(ApprovalType.ATTENDANCE_REQUEST_CANCELLATION)){
                // Get the attendance record for cancellation approval
                EmployeeAttendanceRequest attendanceRecord = employeeAttendanceRequestRepository.findById(request.getReferenceId())
                        .orElseThrow(() -> new RuntimeException("Attendance record not found for cancellation: " + request.getReferenceId()));
                employeeAttendanceServiceImpl.handleApprovalResponse(request, request.getStatus(), attendanceRecord.getType());
            }

            // Send notificationX
                    Map<String, Object> metadata = new HashMap<>();
            metadata.put("requestId", request.getId());
            metadata.put("requestType", request.getRequestType());
            metadata.put("status", request.getStatus());

            notificationService.sendNotification(NotificationRequest.builder()
                    .type(Notification.NotificationType.APPROVAL_COMPLETED)
                    .title("Request " + request.getStatus())
                    .message("Your " + request.getRequestType().getDisplayName() + " request has been " + request.getStatus().toString().toLowerCase())
                    .priority(Notification.Priority.MEDIUM)
                    .metadata(metadata)
                    .recipientIds(List.of(request.getRequesterId().toString()))
                            .requesterId(request.getRequesterId().toString())
                    .build()
            );
        }
    }

    /**
     * Handles the creation of eligibility mapping for an approved biometric registration
     * @param request The approved biometric registration request
     */
    private void handleEligibilityMapping(ApprovalRequest request) {
        // Create the attendance eligibility mapping
        empEligibilityService.createAttendanceEligibilityMapping(
            String.valueOf(request.getRequesterId()),
            String.valueOf(request.getUnitId())
        );
        
        // Refresh the unitEmployees cache for this unit to include the new employee
        empEligibilityService.refreshUnitEmployeesCache(request.getUnitId().intValue());
        
        log.info("Created eligibility mapping and refreshed cache for employee: {} in unit: {}", 
            request.getRequesterId(), request.getUnitId());
    }

    /**
     * Handles the approval of a transfer request
     * @param request The transfer request
     * @param action The approval action (APPROVED or REJECTED)
     */
    private void handleTransferRequestApproval(ApprovalRequest request, String action) {
        // Call the transfer service's handleApproval method
        transferService.handleTransferApproval(request.getReferenceId().toString(), request.getStatus());
    }
} 