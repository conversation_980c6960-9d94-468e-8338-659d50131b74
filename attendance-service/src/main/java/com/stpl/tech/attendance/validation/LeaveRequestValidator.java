package com.stpl.tech.attendance.validation;

import com.stpl.tech.attendance.cache.service.UserCacheService;
import com.stpl.tech.attendance.constants.AppConstants;
import com.stpl.tech.attendance.dto.ApplyLeaveRequest;
import com.stpl.tech.attendance.exception.BusinessException;
import com.stpl.tech.attendance.repository.EmployeeAttendanceRequestRepository;
import com.stpl.tech.attendance.service.AttendanceMetadataService;
import com.stpl.tech.attendance.service.LeaveBalanceService;
import com.stpl.tech.attendance.service.impl.AttendanceRequestUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Validator for leave request validation.
 * Handles all leave-specific validation logic including leave balance, weekend restrictions,
 * and date overlap validations.
 */
@Slf4j
@Component
public class LeaveRequestValidator extends AttendanceRequestValidator<ApplyLeaveRequest> {

    @Autowired
    private LeaveBalanceService leaveBalanceService;

    protected LeaveRequestValidator(AttendanceRequestUtil attendanceRequestUtil, UserCacheService userCacheService, AttendanceMetadataService attendanceMetadataService, EmployeeAttendanceRequestRepository employeeAttendanceRequestRepository) {
        super(attendanceRequestUtil, userCacheService, attendanceMetadataService, employeeAttendanceRequestRepository);
        this.leaveBalanceService = leaveBalanceService;
    }


    @Override
    public void validate(ApplyLeaveRequest request) {
        log.info("Validating leave request for employee");
        
        // Get employee ID from JWT context for validation
        Integer empId = validateEmployeeContext();
        
        // Validate basic request structure
        validateBasicRequest(request);
        
        // NEW: Validate payroll processing dates for all leave dates
        List<LocalDateTime> allLeaveDates = new ArrayList<>(request.getLeaveDates().keySet());
        validatePayrollProcessingDates(allLeaveDates);
        
        // Validate employee eligibility for leave
        validateFromMetadata(empId, AppConstants.LEAVE);

        // Validate leave balance before proceeding
        validateLeaveBalance(request, empId);
        
        // Check if any dates already have existing entries of any type
        List<LocalDateTime> datesToCheck = new ArrayList<>(request.getLeaveDates().keySet());
        validateNoDateRangeOverlaps(empId, datesToCheck);
        
        // For leave requests, also check if any existing requests overlap with the leave date range
        // This handles cases where someone has OD/WFH/regularisation on dates that fall within the leave period
//        validateLeaveDateRangeOverlaps(empId, datesToCheck);
        
        log.info("Leave request validation completed successfully for employee: {}", empId);
    }
    
    /**
     * Validates the basic structure of the leave request
     * 
     * @param request The leave request to validate
     * @throws BusinessException if basic validation fails
     */
    private void validateBasicRequest(ApplyLeaveRequest request) {
        if (request.getLeaveDates() == null || request.getLeaveDates().isEmpty()) {
            log.error("No leave dates provided in request");
            throw new BusinessException("No leave dates provided");
        }
        
        if (request.getLeaveType() == null || request.getLeaveType().trim().isEmpty()) {
            log.error("Leave type is required");
            throw new BusinessException("Leave type is required");
        }
    }
    
    /**
     * Validates leave balance for the requested leave type
     * 
     * @param request The leave request containing leave type and dates
     * @param empId Employee ID
     * @throws BusinessException if insufficient leave balance
     */
    private void validateLeaveBalance(ApplyLeaveRequest request, Integer empId) {
        // Calculate total days requested (considering half days)
        BigDecimal totalDaysRequested = calculateTotalLeaveDays(request.getLeaveDates(), empId, request.getLeaveType());
        
        // Validate leave balance using LeaveBalanceService
        leaveBalanceService.validateLeaveBalance(empId, request.getLeaveType(), totalDaysRequested);
        
        log.info("Leave balance validation successful for employee {}. Type: {}, Days Requested: {}", 
                empId, request.getLeaveType(), totalDaysRequested);
    }
    
    /**
     * Validates weekend restrictions if FIXED_WEEK_OFF is enabled
     * 
     * @param request The leave request
     * @param empId Employee ID
     * @throws BusinessException if weekend restrictions are violated
     */
    private void validateWeekendRestrictions(ApplyLeaveRequest request, Integer empId) {
        try {
            // This validation logic would check if FIXED_WEEK_OFF is enabled
            // and validate that leave is not being applied on weekends
            // For now, we'll keep the existing logic structure
            
            // TODO: Implement weekend restriction validation based on configuration
            // The complex weekend restriction logic from the service should be moved here
            log.debug("Weekend restriction validation completed for employee: {}", empId);
            
        } catch (Exception e) {
            log.error("Error during weekend restriction validation for employee: {}", empId, e);
            throw new BusinessException("Failed to validate weekend restrictions");
        }
    }
    
    /**
     * Calculates total leave days requested (considering half days)
     * 
     * @param leaveDates Map of leave dates to their types
     * @param empId Employee ID
     * @param leaveType Type of leave
     * @return Total days requested
     */
    private BigDecimal calculateTotalLeaveDays(Map<LocalDateTime, String> leaveDates, Integer empId, String leaveType) {
        BigDecimal totalDays = BigDecimal.ZERO;
        
        for (Map.Entry<LocalDateTime, String> entry : leaveDates.entrySet()) {
            String dateType = entry.getValue();
            if ("FULL_DAY".equals(dateType)) {
                totalDays = totalDays.add(BigDecimal.ONE);
            } else if ("HALF_DAY".equals(dateType)) {
                totalDays = totalDays.add(new BigDecimal("0.5"));
            } else {
                log.warn("Unknown date type: {} for employee: {}", dateType, empId);
                totalDays = totalDays.add(BigDecimal.ONE); // Default to full day if unknown
            }
        }
        
        return totalDays;
    }
}
