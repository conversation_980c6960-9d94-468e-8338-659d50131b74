package com.stpl.tech.attendance.validation;

/**
 * Base abstract validator class that defines the contract for all validators.
 * Each validator should implement the validate method for their specific request type.
 * 
 * @param <T> The type of request to validate
 */
public abstract class BaseValidator<T> {
    
    /**
     * Validates the given request object.
     * Implementations should throw appropriate exceptions when validation fails.
     * 
     * @param request The request object to validate
     * @throws RuntimeException or its subclasses when validation fails
     */
    public abstract void validate(T request);
    
    /**
     * Helper method to check if a string is null or empty
     * 
     * @param value The string to check
     * @return true if the string is null or empty, false otherwise
     */
    protected boolean isNullOrEmpty(String value) {
        return value == null || value.trim().isEmpty();
    }
    
    /**
     * Helper method to check if an object is null
     * 
     * @param value The object to check
     * @return true if the object is null, false otherwise
     */
    protected boolean isNull(Object value) {
        return value == null;
    }
}
