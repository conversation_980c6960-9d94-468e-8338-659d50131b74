package com.stpl.tech.attendance.notification.controller;

import com.stpl.tech.attendance.constants.ApiConstants;
import com.stpl.tech.attendance.controller.BaseController;
import com.stpl.tech.attendance.model.request.BulkMarkReadRequest;
import com.stpl.tech.attendance.model.response.ApiResponse;
import com.stpl.tech.attendance.model.response.NotificationListResponse;
import com.stpl.tech.attendance.notification.dto.NotificationResponse;
import com.stpl.tech.attendance.notification.entity.Notification;
import com.stpl.tech.attendance.notification.entity.NotificationRecipient;
import com.stpl.tech.attendance.notification.service.NotificationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

@Tag(name = "Notifications", description = "Notification management APIs")
@Slf4j
@RestController
@RequestMapping(ApiConstants.Paths.NOTIFICATIONS)
@RequiredArgsConstructor
public class NotificationController extends BaseController {

    private final NotificationService notificationService;

    @Operation(summary = "Get notifications for a user with pagination")
    @GetMapping("/user/{userId}")
    public ResponseEntity<ApiResponse<NotificationListResponse>> getUserNotifications(
            @PathVariable String userId,
            @RequestParam(defaultValue = "0") int pageNumber,
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestParam(required = false, defaultValue = "KETTLE_OPS") String type) {
        Pageable pageable = PageRequest.of(pageNumber, pageSize);
        Page<NotificationRecipient> notifications = notificationService.getUserNotifications(userId, pageable, type);
        
        List<NotificationResponse> notificationResponses = notifications.getContent().stream()
                .map(notificationService::mapToResponse)
                .collect(Collectors.toList());
        
        NotificationListResponse response = NotificationListResponse.builder()
                .notifications(notificationResponses)
                .build();
        
        return success(response, notifications);
    }

    @Operation(summary = "Bulk mark notifications as read")
    @PostMapping("/bulk-mark-read")
    public ResponseEntity<ApiResponse<Void>> bulkMarkAsRead(
            @Valid @RequestBody BulkMarkReadRequest request) {
        notificationService.bulkMarkAsRead(request.getNotificationIds(), request.getUserId());
        return success(null);
    }
} 