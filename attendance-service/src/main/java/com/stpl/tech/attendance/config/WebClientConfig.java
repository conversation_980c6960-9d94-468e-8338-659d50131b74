package com.stpl.tech.attendance.config;

import io.netty.channel.ChannelOption;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.WriteTimeoutHandler;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

@Configuration
public class WebClientConfig {

    @Value("${http.client.default.connect-timeout:25000}")
    private int defaultConnectTimeout;

    @Value("${http.client.default.read-timeout:25000}")
    private int defaultReadTimeout;

    @Value("${http.client.default.write-timeout:25000}")
    private int defaultWriteTimeout;

    @Value("${http.client.default.response-timeout:25000}")
    private int defaultResponseTimeout;

    @Bean
    public WebClient webClient() {
        return createWebClient(
            defaultConnectTimeout,
            defaultReadTimeout,
            defaultWriteTimeout,
            defaultResponseTimeout
        );
    }

    public WebClient createWebClient(int connectTimeout, int readTimeout, int writeTimeout, int responseTimeout) {
        HttpClient httpClient = HttpClient.create()
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, connectTimeout)
                .responseTimeout(Duration.ofMillis(responseTimeout))
                .doOnConnected(conn -> conn
                        .addHandlerLast(new ReadTimeoutHandler(readTimeout, TimeUnit.MILLISECONDS))
                        .addHandlerLast(new WriteTimeoutHandler(writeTimeout, TimeUnit.MILLISECONDS)));

        return WebClient.builder()
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .baseUrl("")  // Set empty base URL to allow full URLs
                .build();
    }
} 