package com.stpl.tech.attendance.config;

import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import com.stpl.tech.attendance.cache.constants.CacheConstants;
import com.stpl.tech.attendance.cache.listener.EmployeeCacheEventListener;
import com.stpl.tech.attendance.cache.listener.UnitCacheEventListener;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;

@Slf4j
@Configuration
public class HazelcastCacheListenerConfig {

    private final EmployeeCacheEventListener employeeCacheEventListener;
    private final UnitCacheEventListener unitCacheEventListener;
    private final HazelcastInstance hazelcastInstance;

    public HazelcastCacheListenerConfig(
            EmployeeCacheEventListener employeeCacheEventListener,
            UnitCacheEventListener unitCacheEventListener,
            @Qualifier("MasterHazelCastInstance") HazelcastInstance hazelcastInstance) {
        this.employeeCacheEventListener = employeeCacheEventListener;
        this.unitCacheEventListener = unitCacheEventListener;
        this.hazelcastInstance = hazelcastInstance;
    }

    /**
     * Register cache listeners after the application is ready
     * This ensures that all beans are properly initialized before registering listeners
     */
    @EventListener(ApplicationReadyEvent.class)
    public void registerCacheListeners() {
        log.info("Registering Hazelcast cache listeners...");
        
        try {
            // Register employee cache listener
            IMap<Integer, EmployeeBasicDetail> employeeCache = hazelcastInstance.getMap(CacheConstants.EMPLOYEES_CACHE);
            employeeCache.addEntryListener(employeeCacheEventListener, true);
            log.info("Successfully registered EmployeeCacheEventListener with employee cache");
            
            // Register unit cache listener
            IMap<Integer, Unit> unitsCache = hazelcastInstance.getMap(CacheConstants.UNITS_CACHE);
            unitsCache.addEntryListener(unitCacheEventListener, true);
            log.info("Successfully registered UnitCacheEventListener with units cache");
            
        } catch (Exception e) {
            log.error("Failed to register cache listeners", e);
        }
    }
} 