package com.stpl.tech.attendance.repository;

import com.stpl.tech.attendance.entity.EmployeeAttendanceRequestLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EmployeeAttendanceRequestLogRepository extends JpaRepository<EmployeeAttendanceRequestLog, Long> {
    
    /**
     * Find attendance status records by employee ID
     */
    List<EmployeeAttendanceRequestLog> findByEmpId(Integer empId);
    
    /**
     * Find attendance status records by employee ID and status
     */
    List<EmployeeAttendanceRequestLog> findByEmpIdAndStatus(Integer empId, String status);
    
    /**
     * Find attendance status records by employee ID and type
     */
    List<EmployeeAttendanceRequestLog> findByEmpIdAndType(Integer empId, String type);
    
    /**
     * Find attendance status records by employee ID, status, and type
     */
    List<EmployeeAttendanceRequestLog> findByEmpIdAndStatusAndType(Integer empId, String status, String type);
} 