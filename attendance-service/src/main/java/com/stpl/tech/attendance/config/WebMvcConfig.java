package com.stpl.tech.attendance.config;

import com.stpl.tech.attendance.interceptor.JwtAuthInterceptor;
import com.stpl.tech.attendance.interceptor.RequestIdInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Autowired
    private JwtAuthInterceptor jwtAuthInterceptor;

    @Autowired
    private RequestIdInterceptor requestIdInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // Add RequestIdInterceptor first (order 1) - generates request ID and sets up MDC
        registry.addInterceptor(requestIdInterceptor)
                .addPathPatterns("/**")
                .order(1);
        
        // Add JwtAuthInterceptor second (order 2) - handles authentication
        registry.addInterceptor(jwtAuthInterceptor)
                .addPathPatterns("/**")  // Apply to all paths
                .excludePathPatterns("/error") // Exclude error path
                .order(2);
    }
} 