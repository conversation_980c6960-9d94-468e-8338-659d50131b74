package com.stpl.tech.attendance.service.impl;

import com.stpl.tech.attendance.cache.service.EmployeeShiftDataCacheService;
import com.stpl.tech.attendance.cache.service.UserCacheService;
import com.stpl.tech.attendance.constants.AppConstants;
import com.stpl.tech.attendance.entity.AttendanceStatus;
import com.stpl.tech.attendance.entity.AttendanceType;
import com.stpl.tech.attendance.entity.EmpAttendanceBalanceData;
import com.stpl.tech.attendance.entity.EmployeeAttendanceRequest;
import com.stpl.tech.attendance.entity.EmployeeShiftInstances;
import com.stpl.tech.attendance.entity.RosteringEntity.Shift;
import com.stpl.tech.attendance.entity.EmployeeShiftUnitAttendance;
import com.stpl.tech.attendance.repository.EmpAttendanceBalanceDataRepository;
import com.stpl.tech.attendance.repository.EmployeeAttendanceRequestRepository;
import com.stpl.tech.attendance.repository.EmployeeShiftInstancesRepository;
import com.stpl.tech.attendance.repository.EmpShiftOverrideRepository;
import com.stpl.tech.attendance.repository.RosteringRepository.EmpShiftMappingRepository;
import com.stpl.tech.attendance.repository.RosteringRepository.ShiftRepository;
import com.stpl.tech.attendance.repository.EmployeeShiftUnitAttendanceRepository;
import com.stpl.tech.attendance.service.EmployeeShiftInstanceRecreationService;
import com.stpl.tech.attendance.service.UnitResolutionService;
import com.stpl.tech.attendance.service.AttendanceMetadataService;
import com.stpl.tech.attendance.util.ShiftHelper;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Duration;
import java.util.List;
import java.util.Optional;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class EmployeeShiftInstanceRecreationServiceImpl implements EmployeeShiftInstanceRecreationService {

    private final EmployeeShiftInstancesRepository employeeShiftInstancesRepository;
    private final EmpShiftOverrideRepository empShiftOverrideRepository;
    private final EmpShiftMappingRepository empShiftMappingRepository;
    private final ShiftRepository shiftRepository;
    private final UnitResolutionService unitResolutionService;
    private final EmployeeShiftDataCacheService employeeShiftDataCacheService;
    private final ShiftHelper shiftHelper;
    private final EmployeeShiftUnitAttendanceRepository employeeShiftUnitAttendanceRepository;
    private final EmployeeAttendanceRequestRepository employeeAttendanceRequestRepository;
    private final EmpAttendanceBalanceDataRepository empAttendanceBalanceDataRepository;
    private final UserCacheService userCacheService;
    private final AttendanceMetadataService attendanceMetadataService;

    @Override
    public void recreateShiftInstancesFromEffectiveDate(Integer empId, LocalDate effectiveDate, String updatedBy) {
        log.info("Recreating shift instances for employee {} from effective date {}", empId, effectiveDate);
        
        // Check if effective date is current date to carry forward actual data
        boolean isCurrentDate = effectiveDate.equals(LocalDate.now());
        
        // Mark existing instances as inactive from effective date
        markShiftInstancesInactiveFromEffectiveDate(empId, effectiveDate);
        
        // Recreate instances from effective date to end of current week (or month)
        LocalDate endDate = effectiveDate.plusWeeks(1); // Default to 1 week ahead
        recreateShiftInstancesInDateRange(empId, effectiveDate, endDate, updatedBy, isCurrentDate);
        
        // Update cache for affected dates
        for (LocalDate date = effectiveDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            employeeShiftDataCacheService.evictEmployeeShiftData(empId, date);
        }
    }

    @Override
    public void invalidateCacheForDateRange(Integer empId, LocalDate startDate, LocalDate endDate) {
        log.debug("Invalidating cache for employee {} from {} to {}", empId, startDate, endDate);

        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            // Invalidate employee shift data cache
            employeeShiftDataCacheService.evictEmployeeShiftData(empId, date);

            // Invalidate unit resolution caches
            unitResolutionService.evictEmployeeCaches(empId, date);

            // Also invalidate unit cache for the employee's unit on this date
            Integer unitId = unitResolutionService.getUnitIdForEmployee(empId, date);
            if (unitId != null) {
                unitResolutionService.evictUnitCaches(unitId, date);
            }
        }
    }

    @Override
    @Transactional
    public void recreateShiftInstancesInDateRange(Integer empId, LocalDate startDate, LocalDate endDate, String updatedBy) {
        recreateShiftInstancesInDateRange(empId, startDate, endDate, updatedBy, false);
    }

    public void recreateShiftInstancesInDateRange(Integer empId, LocalDate startDate, LocalDate endDate, String updatedBy, boolean carryForwardActualData) {
        log.info("Recreating shift instances for employee {} from {} to {}", empId, startDate, endDate);
        Map<LocalDate, EmployeeAttendanceRequest> attendanceRequestMap = employeeAttendanceRequestRepository
            .findByEmpIdAndDateBetweenAndStatus(empId, startDate, endDate, AppConstants.STATUS_APPROVED)
            .stream()
            .collect(Collectors.toMap(
                request -> request.getDate().toLocalDate(),
                request -> request,
                (existing, replacement) -> existing // Keep existing if duplicate dates
            ));


        String weekOffDay = empAttendanceBalanceDataRepository.findByEmpId(empId).map(EmpAttendanceBalanceData::getWeekOffDay).orElse(null);

        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            // Check if instance can be recreated
            if (!canRecreateShiftInstance(empId, date)) {
                log.debug("Cannot recreate shift instance for employee {} on date {} - actual values present", empId, date);
                continue;
            }
            
            // Check if this is a week off day for the employee
            boolean isWeekOffDay = isWeekOffDayForEmployee(empId, date, weekOffDay);
            
            // Get shift for this employee and date using ShiftHelper
            Shift shift = shiftHelper.getShiftForEmployee(empId, date);
            if (shift == null) {
                log.warn("No shift found for employee {} on date {}", empId, date);
                continue;
            }
            
            // Get unit ID for employee
            Integer unitId = unitResolutionService.getUnitIdForEmployee(empId, date);
            
            // Calculate expected times using ShiftHelper for universal shift handling
            LocalDateTime expectedStartTime;
            LocalDateTime expectedEndTime;
            
            // Check if this is a universal shift and use appropriate calculation
            if (shiftHelper.isUniversalShift(shift)) {
                expectedStartTime = shiftHelper.calculateExpectedStartTimeForUniversalShift(empId, date);
                expectedEndTime = shiftHelper.calculateExpectedEndTimeForUniversalShift(empId, date);
            } else {
                expectedStartTime = shiftHelper.calculateExpectedStartTime(shift, date);
                expectedEndTime = shiftHelper.calculateExpectedEndTime(shift, date);
            }
            
            BigDecimal idealHours = shiftHelper.calculateIdealHours(expectedStartTime, expectedEndTime);

            // Check if there's an attendance request for this date
            AttendanceStatus statusToSet = AttendanceStatus.ABSENT; // Default status
            String typeToSet = String.valueOf(AttendanceType.NORMAL);
            
            // If it's a week off day and no leave is marked, preserve week off status
            StatusAndType result;
            if (isWeekOffDay && !hasLeaveRequest(empId, date, attendanceRequestMap)) {
                statusToSet = AttendanceStatus.ABSENT;
                typeToSet = String.valueOf(AttendanceType.WEEK_OFF);
                result = new StatusAndType(statusToSet, typeToSet);
                log.debug("Preserving week off status for employee {} on date {}", empId, date);
            } else {
                result = setInstanceStatusAndType(empId, date, attendanceRequestMap, statusToSet, typeToSet);
                statusToSet = result.status();
                typeToSet = result.type();
            }

            // Create new instance
            EmployeeShiftInstances newInstance = EmployeeShiftInstances.builder()
                .empId(empId)
                .businessDate(date)
                .shiftId(shift.getShiftId())
                .unitId(unitId)
                .expectedStartTime(expectedStartTime)
                .expectedEndTime(expectedEndTime)
                .idealHours(idealHours)
                .instanceStatus("ACTIVE")
                .createdBy(updatedBy)
                .status(result.status())
                .type(result.type())
                .build();
            
            // If carrying forward actual data and this is the current date, preserve actual attendance data
            if (carryForwardActualData && date.equals(LocalDate.now())) {
                carryForwardActualAttendanceData(empId, date, newInstance);
            }
            
            // Save the new instance first to get the ID
            EmployeeShiftInstances savedInstance = employeeShiftInstancesRepository.save(newInstance);
            
            // If carrying forward actual data and this is the current date, carry forward unit attendance data
            if (carryForwardActualData && date.equals(LocalDate.now())) {
                carryForwardUnitAttendanceData(empId, date, savedInstance.getId());
            }
            log.debug("Created new shift instance for employee {} on date {} with shift {}", 
                     empId, date, shift.getShiftName());
        }
    }

    @Override
    @Transactional
    public void markShiftInstancesInactiveFromEffectiveDate(Integer empId, LocalDate effectiveDate) {
        log.info("Marking shift instances as inactive for employee {} from effective date {}", empId, effectiveDate);
        
        List<EmployeeShiftInstances> instances = employeeShiftInstancesRepository
            .findByEmpIdAndBusinessDateBetweenAndInstanceStatus(empId, effectiveDate, effectiveDate.plusMonths(1), "ACTIVE"); // Look ahead 1 month
        
        for (EmployeeShiftInstances instance : instances) {
            if (instance.getBusinessDate().isAfter(effectiveDate.minusDays(1)) && 
                "ACTIVE".equals(instance.getInstanceStatus())) {
                instance.setInstanceStatus("INACTIVE");
                instance.setUpdatedBy("SYSTEM");
                instance.setUpdatedAt(LocalDateTime.now());
                employeeShiftInstancesRepository.save(instance);
                log.debug("Marked shift instance as inactive for employee {} on date {}", empId, instance.getBusinessDate());
            }
        }
    }

    @Override
    public boolean canRecreateShiftInstance(Integer empId, LocalDate businessDate) {
        Optional<EmployeeShiftInstances> instanceOpt = employeeShiftInstancesRepository
            .findByEmpIdAndBusinessDateAndInstanceStatus(empId, businessDate, "ACTIVE");
        
        if (instanceOpt.isEmpty()) {
            return true; // No instance exists, can create
        }
        
        EmployeeShiftInstances instance = instanceOpt.get();
        
        // Cannot recreate if actual values are set
        return instance.getActualStartTime() == null && instance.getActualEndTime() == null;
    }

    /**
     * Carry forward actual attendance data from existing instance to new instance
     */
    private void carryForwardActualAttendanceData(Integer empId, LocalDate businessDate, EmployeeShiftInstances newInstance) {
        try {
            // Find the latest existing inactive instance for this date
            Optional<EmployeeShiftInstances> existingInstanceOpt = employeeShiftInstancesRepository
                .findLatestInactiveInstanceByEmpIdAndBusinessDate(empId, businessDate);
            
            if (existingInstanceOpt.isPresent()) {
                EmployeeShiftInstances existingInstance = existingInstanceOpt.get();
                
                // Only carry forward data if there's actual attendance data present
                boolean hasActualData = existingInstance.getActualStartTime() != null || 
                                      existingInstance.getActualEndTime() != null || 
                                      existingInstance.getActualHours() != null;
                
                if (!hasActualData) {
                    log.debug("No actual attendance data found to carry forward for employee {} on date {}", empId, businessDate);
                    return;
                }
                
                // Carry forward actual attendance data
                if (existingInstance.getActualStartTime() != null) {
                    newInstance.setActualStartTime(existingInstance.getActualStartTime());
                    log.debug("Carried forward actual start time: {} for employee {} on date {}", 
                             existingInstance.getActualStartTime(), empId, businessDate);
                }
                
                if (existingInstance.getActualEndTime() != null) {
                    newInstance.setActualEndTime(existingInstance.getActualEndTime());
                    log.debug("Carried forward actual end time: {} for employee {} on date {}", 
                             existingInstance.getActualEndTime(), empId, businessDate);
                }
                
                if (existingInstance.getActualHours() != null) {
                    newInstance.setActualHours(existingInstance.getActualHours());
                    log.debug("Carried forward actual hours: {} for employee {} on date {}", 
                             existingInstance.getActualHours(), empId, businessDate);
                }
                
                // Carry forward attendance status
                if (existingInstance.getStatus() != null) {
                    newInstance.setStatus(existingInstance.getStatus());
                    log.debug("Carried forward attendance status: {} for employee {} on date {}", 
                             existingInstance.getStatus(), empId, businessDate);
                }
                
                log.info("Successfully carried forward actual attendance data from latest inactive instance (ID: {}) for employee {} on date {}", 
                         existingInstance.getId(), empId, businessDate);
            } else {
                log.debug("No existing inactive instance found to carry forward data for employee {} on date {}", empId, businessDate);
            }
        } catch (Exception e) {
            log.error("Error carrying forward actual attendance data for employee {} on date {}", empId, businessDate, e);
        }
    }
    
    /**
     * Carry forward unit attendance data from old instance to new instance
     */
    private void carryForwardUnitAttendanceData(Integer empId, LocalDate businessDate, Long newInstanceId) {
        try {
            // Find the latest existing inactive instance for this date
            Optional<EmployeeShiftInstances> existingInstanceOpt = employeeShiftInstancesRepository
                .findLatestInactiveInstanceByEmpIdAndBusinessDate(empId, businessDate);
            
            if (existingInstanceOpt.isPresent()) {
                Long oldInstanceId = existingInstanceOpt.get().getId();
                
                // Find all unit attendance records for the old instance
                List<EmployeeShiftUnitAttendance> unitAttendanceRecords = employeeShiftUnitAttendanceRepository
                    .findByShiftInstanceId(oldInstanceId);
                
                if (unitAttendanceRecords.isEmpty()) {
                    log.debug("No unit attendance data found to carry forward for employee {} on date {}", empId, businessDate);
                    return;
                }
                
                for (EmployeeShiftUnitAttendance oldUnitAttendance : unitAttendanceRecords) {
                    // Create new unit attendance record with new instance ID
                    EmployeeShiftUnitAttendance newUnitAttendance = EmployeeShiftUnitAttendance.builder()
                        .shiftInstanceId(newInstanceId)
                        .actualUnitId(oldUnitAttendance.getActualUnitId())
                        .actualStartTime(oldUnitAttendance.getActualStartTime())
                        .actualEndTime(oldUnitAttendance.getActualEndTime())

                        .build();
                    
                    employeeShiftUnitAttendanceRepository.save(newUnitAttendance);
                    log.debug("Carried forward unit attendance data from instance {} to {} for unit {}", 
                             oldInstanceId, newInstanceId, oldUnitAttendance.getActualUnitId());
                }
                
                log.info("Successfully carried forward {} unit attendance records from latest inactive instance (ID: {}) for employee {} on date {}", 
                         unitAttendanceRecords.size(), oldInstanceId, empId, businessDate);
            } else {
                log.debug("No existing inactive instance found to carry forward unit attendance data for employee {} on date {}", empId, businessDate);
            }
        } catch (Exception e) {
            log.error("Error carrying forward unit attendance data for employee {} on date {} to new instance {}", empId, businessDate, newInstanceId, e);
        }
    }
    
    /**
     * Calculate actual hours between start and end time
     */
    private BigDecimal calculateActualHours(LocalDateTime startTime, LocalDateTime endTime) {
        if (startTime == null || endTime == null) {
            return BigDecimal.ZERO;
        }
        Duration duration = Duration.between(startTime, endTime);
        return BigDecimal.valueOf(duration.toMinutes()).divide(BigDecimal.valueOf(60), 2, java.math.RoundingMode.HALF_UP);
    }

    private StatusAndType setInstanceStatusAndType(Integer empId, LocalDate date, Map<LocalDate, EmployeeAttendanceRequest> attendanceRequestMap, AttendanceStatus statusToSet, String typeToSet) {
        EmployeeAttendanceRequest attendanceRequest = attendanceRequestMap.get(date);
        if (attendanceRequest != null && attendanceRequest.getType() != null)  {
            // Map attendance request type to appropriate status
            switch (attendanceRequest.getType().toUpperCase()) {
                case "LEAVE", "COMP_OFF", "LWP","WEEK_OFF":
                    statusToSet = AttendanceStatus.ABSENT;
                    typeToSet = attendanceRequest.getType();
                    break;
                case "OD", "WFH","REGULARISATION":
                    statusToSet = AttendanceStatus.PRESENT;
                    typeToSet = attendanceRequest.getType();
                    break;
                default:
                    break;
            }
            log.debug("Setting status to {} based on attendance request type {} for employee {} on date {}", 
                     statusToSet, attendanceRequest.getType(), empId, date);
        }
        return new StatusAndType(statusToSet, typeToSet);
    }
    
    /**
     * Check if the given date is a week off day for the employee
     */
    private boolean isWeekOffDayForEmployee(Integer empId, LocalDate date, String weekOffDay) {
        if (weekOffDay == null) {
            // If no week off day is configured, check if fixed week off is enabled for the employee's department
            try {
                EmployeeBasicDetail employee = userCacheService.getUserById(empId);
                if (employee != null && employee.getDepartmentId() != null) {
                    boolean isFixedWeekOff = attendanceMetadataService.isFixedWeekendAllowed(employee.getDepartmentId());
                    if (isFixedWeekOff) {
                        // If fixed week off is enabled, check if it's Saturday or Sunday
                        DayOfWeek dayOfWeek = date.getDayOfWeek();
                        return dayOfWeek == DayOfWeek.SATURDAY || dayOfWeek == DayOfWeek.SUNDAY;
                    }
                }
                // If fixed week off is not enabled or employee/department not found, return false
                return false;
            } catch (Exception e) {
                log.warn("Error checking fixed week off configuration for employee {}: {}", empId, e.getMessage());
                return false;
            }
        }
        
        // Check if the date matches the configured week off day
        DayOfWeek configuredWeekOff = DayOfWeek.valueOf(weekOffDay.toUpperCase());
        return date.getDayOfWeek() == configuredWeekOff;
    }
    
    /**
     * Check if there's a leave request for the given date
     */
    private boolean hasLeaveRequest(Integer empId, LocalDate date, Map<LocalDate, EmployeeAttendanceRequest> attendanceRequestMap) {
        EmployeeAttendanceRequest request = attendanceRequestMap.get(date);
        if (request != null && request.getType() != null) {
            String requestType = request.getType().toUpperCase();
            // Check if it's a leave type that should override week off
            return "LEAVE".equals(requestType) || "COMP_OFF".equals(requestType) || "LWP".equals(requestType);
        }
        return false;
    }
    

    
    /**
     * Record to hold status and type values
     */
    private record StatusAndType(AttendanceStatus status, String type) {}
} 