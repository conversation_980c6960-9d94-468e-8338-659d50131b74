package com.stpl.tech.attendance.context;

import lombok.Getter;
import lombok.Setter;

public class BiometricContext {
    private static final ThreadLocal<BiometricContext> context = new ThreadLocal<>();

    @Getter
    @Setter
    private String requestId;


    private BiometricContext() {}

    public static BiometricContext getInstance() {
        BiometricContext instance = context.get();
        if (instance == null) {
            instance = new BiometricContext();
            context.set(instance);
        }
        return instance;
    }

    public static void clear() {
        context.remove();
    }

    public static void setContext(String requestId) {
        BiometricContext instance = getInstance();
        instance.setRequestId(requestId);
    }
}
