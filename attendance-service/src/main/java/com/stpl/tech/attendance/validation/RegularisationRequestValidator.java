package com.stpl.tech.attendance.validation;

import com.stpl.tech.attendance.cache.service.UserCacheService;
import com.stpl.tech.attendance.constants.AppConstants;
import com.stpl.tech.attendance.dto.ApplyRegularisationRequest;
import com.stpl.tech.attendance.exception.BusinessException;
import com.stpl.tech.attendance.repository.EmployeeAttendanceRequestRepository;
import com.stpl.tech.attendance.service.AttendanceMetadataService;
import com.stpl.tech.attendance.service.impl.AttendanceRequestUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * Validator for regularisation request validation.
 * Handles all regularisation-specific validation logic including date overlaps,
 * time overlaps, and employee eligibility validations.
 */
@Slf4j
@Component
public class RegularisationRequestValidator extends AttendanceRequestValidator<ApplyRegularisationRequest> {


    protected RegularisationRequestValidator(AttendanceRequestUtil attendanceRequestUtil, UserCacheService userCacheService, AttendanceMetadataService attendanceMetadataService, EmployeeAttendanceRequestRepository employeeAttendanceRequestRepository) {
        super(attendanceRequestUtil, userCacheService, attendanceMetadataService, employeeAttendanceRequestRepository);
    }

    @Override
    public void validate(ApplyRegularisationRequest request) {
        log.info("Validating regularisation request for employee");
        
        // Get employee ID from JWT context for validation
        Integer empId = validateEmployeeContext();
        
        // Validate basic request structure
        validateBasicRequest(request);
        
        // NEW: Validate payroll processing date for regularisation date
        validatePayrollProcessingDates(Collections.singletonList(request.getDate()));
        
        // Validate employee eligibility for regularisation
        validateFromMetadata(empId, AppConstants.REGULARISATION);
        
        // Check if any dates already have existing entries of any type
        List<LocalDateTime> datesToCheck = Collections.singletonList(request.getDate());
        validateNoDateRangeOverlaps(empId, datesToCheck);
        
        // For regularisation requests, also check if any existing requests overlap with the date
        validateLeaveDateRangeOverlaps(empId, datesToCheck);
        
        // Validate time overlaps
        validateNoTimeOverlaps(empId, request.getDate(), request.getCheckIn(), request.getCheckOut());
        
        log.info("Regularisation request validation completed successfully for employee: {}", empId);
    }
    
    /**
     * Validates the basic structure of the regularisation request
     * 
     * @param request The regularisation request to validate
     * @throws BusinessException if basic validation fails
     */
    private void validateBasicRequest(ApplyRegularisationRequest request) {
        if (request.getDate() == null) {
            log.error("No date provided in regularisation request");
            throw new BusinessException("Date is required for regularisation request");
        }
        
        if (request.getCheckIn() == null) {
            log.error("Check-in time is required for regularisation request");
            throw new BusinessException("Check-in time is required for regularisation request");
        }
        
        if (request.getCheckOut() == null) {
            log.error("Check-out time is required for regularisation request");
            throw new BusinessException("Check-out time is required for regularisation request");
        }
        
        if (request.getReason() == null || request.getReason().trim().isEmpty()) {
            log.error("Reason is required for regularisation request");
            throw new BusinessException("Reason is required for regularisation request");
        }
        
        // Validate that check-out time is after check-in time
        if (request.getCheckOut().isBefore(request.getCheckIn())) {
            log.error("Check-out time cannot be before check-in time");
            throw new BusinessException("Check-out time cannot be before check-in time");
        }
        
        // Validate that the date is not in the future
        if (request.getDate().isAfter(LocalDateTime.now())) {
            log.error("Regularisation cannot be applied for future dates");
            throw new BusinessException("Regularisation cannot be applied for future dates");
        }
    }
}
