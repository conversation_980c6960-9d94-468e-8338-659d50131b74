package com.stpl.tech.attendance.model;

import com.stpl.tech.attendance.enums.AttendanceStatus;
import com.stpl.tech.attendance.enums.AttendanceType;
import com.stpl.tech.attendance.enums.PunchType;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class AttendanceResponse {
    private Long employeeId;
    private String employeeCode;
    private String employeeName;
    private String designation;
    private PunchType attendanceType;
    private LocalDateTime attendanceTime;
    private AttendanceStatus status;
    private LocalDateTime checkInTime;
    private LocalDateTime checkOutTime;
    private String checkInImageUrl;
    private String checkOutImageUrl;
    private String remarks;
    private String type;
    private LocalDateTime attendanceDate;
    private String hexColor; // Hex color code for attendance status visualization

    public AttendanceResponse(Long employeeId, String employeeName, String designation, AttendanceStatus status,
                              String employeeCode){
        this.employeeId = employeeId;
        this.employeeName = employeeName;
        this.designation = designation;
        this.status = status;
        this.employeeCode = employeeCode;
    }

    public AttendanceResponse() {

    }
}