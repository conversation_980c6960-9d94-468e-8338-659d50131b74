package com.stpl.tech.attendance.enums;

/**
 * Enum representing valid transfer reasons
 */
public enum TransferReason {
    PROMOTION("Promotion"),
    ORGANIZATIONAL_RESTRUCTURING("Organizational Restructuring"),
    SKILL_DEVELOPMENT("Skill Development"),
    PROJECT_REQUIREMENT("Project Requirement"),
    WORKLOAD_BALANCING("Workload Balancing"),
    PERSONAL_REQUEST("Personal Request"),
    PERFORMANCE_IMPROVEMENT("Performance Improvement"),
    OTHER("Other");

    private final String displayName;

    TransferReason(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }

    /**
     * Get TransferReason by display name
     */
    public static TransferReason fromDisplayName(String displayName) {
        for (TransferReason reason : values()) {
            if (reason.displayName.equals(displayName)) {
                return reason;
            }
        }
        return null;
    }

    /**
     * Check if a display name is valid
     */
    public static boolean isValid(String displayName) {
        return fromDisplayName(displayName) != null;
    }
} 