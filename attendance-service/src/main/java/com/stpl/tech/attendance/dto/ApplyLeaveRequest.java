package com.stpl.tech.attendance.dto;

import jakarta.annotation.Nullable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApplyLeaveRequest {
    
    /**
     * Map of dates to request types
     * Key: Date (LocalDateTime)
     * Value: Request type (FULL_DAY, FIRST_HALF, SECOND_HALF)
     */
    private Map<LocalDateTime, String> leaveDates;

    // LEAVE,COMP_OFF,LWP
    private String leaveType;
    
    /**
     * Comments for the leave application
     */
    @Nullable
    private String comments;
    
    /**
     * Documents related to the leave application (file paths or document references)
     */
    private String documents;
} 