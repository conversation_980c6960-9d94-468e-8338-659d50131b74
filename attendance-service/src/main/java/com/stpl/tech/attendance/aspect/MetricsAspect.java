package com.stpl.tech.attendance.aspect;

import com.stpl.tech.attendance.approval.dto.ApprovalDecision;
import com.stpl.tech.attendance.exception.ValidationException;
import com.stpl.tech.attendance.model.request.ApprovalActionRequest;
import com.stpl.tech.attendance.model.request.BulkApprovalActionRequest;
import com.stpl.tech.attendance.service.MetricsService;
import io.micrometer.core.instrument.Timer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

@Aspect
@Component
@Slf4j
@RequiredArgsConstructor
public class MetricsAspect {

    private final MetricsService metricsService;

//    @Around("@annotation(org.springframework.web.bind.annotation.PostMapping) || " +
//            "@annotation(org.springframework.web.bind.annotation.GetMapping)")
    public Object trackMetrics(ProceedingJoinPoint joinPoint) throws Throwable {
        String methodName = joinPoint.getSignature().getName();
        Timer.Sample timer = metricsService.startCheckInTimer();
        
        try {
            Object result = joinPoint.proceed();
            
            /*// Record success metrics based on method name and arguments
            if (methodName.contains("registerBiometric")) {
                metricsService.incrementBiometricRegistration();
                metricsService.incrementBiometricSuccess();
            } else if (methodName.contains("deregisterBiometric")) {
                metricsService.incrementBiometricDeregistration();
                metricsService.incrementBiometricSuccess();
            } else if (methodName.contains("getBiometricStatus")) {
                metricsService.incrementBiometricStatusCheck();
                metricsService.incrementBiometricSuccess();
            } else if (methodName.contains("processApprovalAction")) {
                handleApprovalActionMetrics(joinPoint.getArgs());
            } else if (methodName.contains("processBulkApprovalAction")) {
                handleBulkApprovalMetrics(joinPoint.getArgs());
            } else if (methodName.contains("getPendingApprovals")) {
                metricsService.incrementApprovalRequest();
                metricsService.incrementApprovalSuccess();
            } else if (methodName.contains("getEmployeeMetadata")) {
                handleMetadataMetrics(methodName, joinPoint.getArgs());
            } else if (methodName.contains("getEmployeesWithBiometric")) {
                handleMetadataMetrics(methodName, joinPoint.getArgs());
            } else if (methodName.contains("pairDevice")) {
                handleMetadataMetrics(methodName, joinPoint.getArgs());
            } else if (methodName.contains("validateDevicePairing")) {
                handleMetadataMetrics(methodName, joinPoint.getArgs());
            } else if (methodName.contains("markAttendance")) {
                handleAttendanceMetrics(joinPoint.getArgs());
            } else if (methodName.contains("getUserNotifications")) {
                handleNotificationMetrics(methodName, joinPoint.getArgs());
            } else if (methodName.contains("bulkMarkAsRead")) {
                handleNotificationMetrics(methodName, joinPoint.getArgs());
            }*/
            
            return result;
        } catch (Exception e) {
            // Record error metrics based on exception type
            if (e instanceof IllegalArgumentException || e instanceof ValidationException) {
                metricsService.incrementValidationError();
            } else {
                metricsService.incrementSystemError();
            }
            metricsService.incrementError();
            throw e;
        } finally {
            timer.stop(metricsService.getCheckInTimer());
        }
    }

    private void handleApprovalActionMetrics(Object[] args) {
        Timer.Sample timer = metricsService.startApprovalProcessTimer();
        try {
            for (Object arg : args) {
                if (arg instanceof ApprovalActionRequest request) {
                    if (request.getAction().equals("APPROVED")) {
                        metricsService.incrementApprovalApproved();
                        metricsService.incrementApprovalSuccess();
                    } else if (request.getAction().equals("REJECTED")) {
                        metricsService.incrementApprovalRejected();
                        metricsService.incrementApprovalSuccess();
                    }
                }
            }
        } catch (Exception e) {
            metricsService.incrementApprovalFailure();
            throw e;
        } finally {
            metricsService.recordApprovalProcessDuration(timer);
        }
    }

    private void handleBulkApprovalMetrics(Object[] args) {
        Timer.Sample timer = metricsService.startApprovalProcessTimer();
        try {
            metricsService.incrementBulkApproval();
            for (Object arg : args) {
                if (arg instanceof BulkApprovalActionRequest request) {
                    request.getRequests().forEach(action -> {
                        if (action.getAction().equals("APPROVED")) {
                            metricsService.incrementApprovalApproved();
                            metricsService.incrementApprovalSuccess();
                        } else if (action.getAction().equals("REJECTED")) {
                            metricsService.incrementApprovalRejected();
                            metricsService.incrementApprovalSuccess();
                        }
                    });
                }
            }
        } catch (Exception e) {
            metricsService.incrementApprovalFailure();
            throw e;
        } finally {
            metricsService.recordApprovalProcessDuration(timer);
        }
    }

    private void handleAttendanceMetrics(Object[] args) {
        Timer.Sample timer = metricsService.startCheckInTimer();
        try {
            for (Object arg : args) {
                if (arg instanceof com.stpl.tech.attendance.model.AttendancePunchRequest request) {
                    if (request.getPunchType().equals("CHECK_IN")) {
                        metricsService.incrementCheckIn();
                        metricsService.incrementCheckInSuccess();
                    } else if (request.getPunchType().equals("CHECK_OUT")) {
                        metricsService.incrementCheckOut();
                        metricsService.incrementCheckOutSuccess();
                    }
                }
            }
        } catch (Exception e) {
            if (args[0] instanceof com.stpl.tech.attendance.model.AttendancePunchRequest request) {
                if (request.getPunchType().equals("CHECK_IN")) {
                    metricsService.incrementCheckInFailure();
                } else if (request.getPunchType().equals("CHECK_OUT")) {
                    metricsService.incrementCheckOutFailure();
                }
            }
            throw e;
        } finally {
            metricsService.recordCheckInDuration(timer);
        }
    }

    private void handleMetadataMetrics(String methodName, Object[] args) {
        Timer.Sample timer = metricsService.startMetadataOperationTimer();
        try {
            switch (methodName) {
                case "getEmployeeMetadata":
                    metricsService.incrementMetadataRequest();
                    metricsService.incrementMetadataSuccess();
                    break;
                case "getEmployeesWithBiometric":
                    metricsService.incrementBiometricMetadataRequest();
                    metricsService.incrementMetadataSuccess();
                    break;
                case "pairDevice":
                    metricsService.incrementDevicePairing();
                    metricsService.incrementDeviceSuccess();
                    break;
                case "validateDevicePairing":
                    metricsService.incrementDeviceValidation();
                    metricsService.incrementDeviceSuccess();
                    break;
            }
        } catch (Exception e) {
            if (methodName.contains("Device")) {
                metricsService.incrementDeviceFailure();
            } else {
                metricsService.incrementMetadataFailure();
            }
            throw e;
        } finally {
            metricsService.recordMetadataOperationDuration(timer);
        }
    }

    private void handleNotificationMetrics(String methodName, Object[] args) {
        Timer.Sample timer = metricsService.startNotificationOperationTimer();
        try {
            switch (methodName) {
                case "getUserNotifications":
                    metricsService.incrementNotificationRequest();
                    metricsService.incrementNotificationSuccess();
                    break;
                case "bulkMarkAsRead":
                    metricsService.incrementBulkMarkRead();
                    metricsService.incrementNotificationSuccess();
                    break;
            }
        } catch (Exception e) {
            metricsService.incrementNotificationFailure();
            throw e;
        } finally {
            metricsService.recordNotificationOperationDuration(timer);
        }
    }
} 