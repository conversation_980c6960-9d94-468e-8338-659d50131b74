package com.stpl.tech.attendance.repository;

import com.stpl.tech.attendance.entity.BiometricAdditionalImage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Repository for BiometricAdditionalImage entity
 */
@Repository
public interface BiometricAdditionalImageRepository extends JpaRepository<BiometricAdditionalImage, Long> {
    
    /**
     * Find all additional images for a specific biometric registration
     * @param biometricRegistrationId The ID of the biometric registration
     * @return List of additional images
     */
    List<BiometricAdditionalImage> findByBiometricRegistrationId(Long biometricRegistrationId);
    
    /**
     * Delete all additional images for a specific biometric registration
     * @param biometricRegistrationId The ID of the biometric registration
     */
    void deleteByBiometricRegistrationId(Long biometricRegistrationId);
}
