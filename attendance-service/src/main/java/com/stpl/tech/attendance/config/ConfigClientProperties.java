package com.stpl.tech.attendance.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.security.Security;

@Data
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "app")
public class ConfigClientProperties {
    private Cors cors = new Cors();
} 