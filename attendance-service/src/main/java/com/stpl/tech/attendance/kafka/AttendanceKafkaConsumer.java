/*
package com.stpl.tech.attendance.kafka;

import com.stpl.tech.attendance.dto.AttendanceRequestDTO;
import com.stpl.tech.attendance.service.AttendanceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.messaging.handler.annotation.Payload;
import com.fasterxml.jackson.databind.ObjectMapper;

@Slf4j
@Component
@RequiredArgsConstructor
public class AttendanceKafkaConsumer {

    private final AttendanceService attendanceService;
    private final ObjectMapper objectMapper = new ObjectMapper();

    //@KafkaListener(topics = "${kafka.topic.attendance}", groupId = "${spring.kafka.consumer.group-id}")
    public void consumeAttendanceMessage(@Payload Object message) {
        try {
            log.info("Received message: {}", message);
            
            // Try to convert to AttendanceRequestDTO
            if (message instanceof String) {
                AttendanceRequestDTO request = objectMapper.readValue((String) message, AttendanceRequestDTO.class);
                log.info("Processed attendance for employee: {}", request.getEmpId());
                attendanceService.processAttendance(request);
            } else if (message instanceof AttendanceRequestDTO) {
                AttendanceRequestDTO request = (AttendanceRequestDTO) message;
                log.info("Processed attendance for employee: {}", request.getEmpId());
                attendanceService.processAttendance(request);
            } else {
                log.info("Received message of type: {}", message.getClass().getName());
                // For testing
                log.info("Message content: {}", objectMapper.writeValueAsString(message));
            }
        } catch (Exception e) {
            log.error("Error processing message: {}", message, e);
        }
    }
}
*/
