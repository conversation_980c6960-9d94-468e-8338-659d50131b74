package com.stpl.tech.attendance.dto.RosteringDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProfilesResponseDTO {
    private Integer employeeId;
    private Timestamp date;
    private Integer idealWorkHours;
    private Integer actualWorkHours;
    private Integer onTime;
} 