package com.stpl.tech.attendance.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BiometricRegistrationResponseDTO {
    private String status;
    private Double processing_time;
    private Result result;
    private String error;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private String BiometricId;
        private String userId;
    }
} 