package com.stpl.tech.attendance.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.math.BigDecimal;

@Data
@Entity
@Table(name = "APPLICATION_DATA")
public class ApplicationData implements Serializable {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "APPLICATION_ID")
    private Integer applicationId;

    @Column(name = "MAC_VALIDATION_ENABLED")
    private String macValidationEnabled;

    @Column(name = "GEO_VALIDATION_ENABLED")
    private String geoValidationEnabled;

    @Column(name = "GEO_VALIDATION_RADIUS")
    private BigDecimal geoValidationRadius;

    @Column(name = "CREATED_DATE")
    private LocalDateTime createdDate;

    @Column(name = "CREATED_BY")
    private String createdBy;

    @Column(name = "UPDATED_DATE")
    private LocalDateTime updatedDate;

    @Column(name = "UPDATED_BY")
    private String updatedBy;

    @Column(name = "DELETED_DATE")
    private LocalDateTime deletedDate;

    @Column(name = "DELETED_BY")
    private String deletedBy;

    @Column(name = "STATUS")
    private String status;
} 