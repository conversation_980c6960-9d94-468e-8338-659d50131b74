package com.stpl.tech.attendance.service.impl;

import com.stpl.tech.attendance.cache.service.HolidayCacheService;
import com.stpl.tech.attendance.dto.HolidayResponse;
import com.stpl.tech.attendance.entity.EmpHoliday;
import com.stpl.tech.attendance.repository.EmpHolidayRepository;
import com.stpl.tech.attendance.service.HolidayService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Service implementation for holiday management operations
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class HolidayServiceImpl implements HolidayService {
    
    private final EmpHolidayRepository holidayRepository;
    private final HolidayCacheService holidayCacheService;
    
    @Override
    public List<HolidayResponse> getHolidaysByFinancialYear(String financialYear) {
        log.debug("Getting holidays for financial year: {}", financialYear);
        
        // First try to get from cache
        List<HolidayResponse> cachedHolidays = holidayCacheService.getCachedHolidaysByFinancialYear(financialYear);
        if (cachedHolidays != null) {
            log.debug("Returning cached holidays for financial year: {}", financialYear);
            return cachedHolidays;
        }
        
        // If not in cache, fetch from database
        List<EmpHoliday> holidays = holidayRepository.findByFinancialYearOrderByHolidayDateAsc(financialYear);
        List<HolidayResponse> holidayResponses = holidays.stream()
                .map(this::convertToHolidayResponse)
                .collect(Collectors.toList());
        
        // Cache the result
        holidayCacheService.cacheHolidaysByFinancialYear(financialYear, holidayResponses);
        
        log.debug("Fetched and cached {} holidays for financial year: {}", holidayResponses.size(), financialYear);
        return holidayResponses;
    }
    
    @Override
    public List<HolidayResponse> getHolidaysByDateRange(LocalDate startDate, LocalDate endDate) {
        log.debug("Getting holidays for date range: {} to {}", startDate, endDate);
        
        // First try to get from cache
        List<HolidayResponse> cachedHolidays = holidayCacheService.getCachedHolidaysByDateRange(startDate, endDate);
        if (cachedHolidays != null) {
            log.debug("Returning cached holidays for date range: {} to {}", startDate, endDate);
            return cachedHolidays;
        }
        
        // If not in cache, fetch from database
        List<EmpHoliday> holidays = holidayRepository.findByDateRange(startDate, endDate);
        List<HolidayResponse> holidayResponses = holidays.stream()
                .map(this::convertToHolidayResponse)
                .collect(Collectors.toList());
        
        // Cache the result
        holidayCacheService.cacheHolidaysByDateRange(startDate, endDate, holidayResponses);
        
        log.debug("Fetched and cached {} holidays for date range: {} to {}", holidayResponses.size(), startDate, endDate);
        return holidayResponses;
    }
    
    @Override
    public List<HolidayResponse> getCurrentFinancialYearHolidays() {
        log.debug("Getting holidays for current financial year");
        
        String currentFinancialYear = getCurrentFinancialYear();
        return getHolidaysByFinancialYear(currentFinancialYear);
    }
    
    @Override
    public boolean isHoliday(LocalDate date) {
        log.debug("Checking if date is holiday: {}", date);
        
        // First try to get from cache
        Boolean cachedExists = holidayCacheService.getCachedHolidayExists(date);
        if (cachedExists != null) {
            log.debug("Returning cached holiday existence for date: {} - {}", date, cachedExists);
            return cachedExists;
        }
        
        // If not in cache, check database
        boolean exists = holidayRepository.existsByHolidayDate(date);
        
        // Cache the result
        holidayCacheService.cacheHolidayExists(date, exists);
        
        log.debug("Checked and cached holiday existence for date: {} - {}", date, exists);
        return exists;
    }
    
    @Override
    public HolidayResponse getHolidayByDate(LocalDate date) {
        log.debug("Getting holiday information for date: {}", date);
        
        // First try to get from cache
        HolidayResponse cachedHoliday = holidayCacheService.getCachedHolidayByDate(date);
        if (cachedHoliday != null) {
            log.debug("Returning cached holiday for date: {}", date);
            return cachedHoliday;
        }
        
        // If not in cache, fetch from database
        List<EmpHoliday> holidays = holidayRepository.findByHolidayDate(date);
        HolidayResponse holidayResponse = null;
        
        if (!holidays.isEmpty()) {
            holidayResponse = convertToHolidayResponse(holidays.get(0));
        }
        
        // Cache the result (including null for dates with no holidays)
        holidayCacheService.cacheHolidayByDate(date, holidayResponse);
        
        log.debug("Fetched and cached holiday for date: {} - {}", date, 
                holidayResponse != null ? holidayResponse.getHolidayName() : "null");
        return holidayResponse;
    }
    
    @Override
    public EmpHoliday saveHoliday(EmpHoliday holiday) {
        log.info("Saving holiday: {} on date: {}", holiday.getHolidayName(), holiday.getHolidayDate());
        
        // Set audit fields if not present
        if (holiday.getCreatedAt() == null) {
            holiday.setCreatedAt(java.time.LocalDateTime.now());
        }
        holiday.setUpdatedAt(java.time.LocalDateTime.now());
        
        EmpHoliday savedHoliday = holidayRepository.save(holiday);
        
        // Evict related cache entries
        holidayCacheService.evictHolidayCache(savedHoliday);
        
        log.info("Holiday saved successfully with ID: {}", savedHoliday.getId());
        
        return savedHoliday;
    }
    
    @Override
    public void deleteHoliday(Long holidayId) {
        log.info("Deleting holiday with ID: {}", holidayId);
        
        if (holidayRepository.existsById(holidayId)) {
            // Get the holiday before deleting to evict cache
            EmpHoliday holiday = holidayRepository.findById(holidayId).orElse(null);
            if (holiday != null) {
                // Evict related cache entries
                holidayCacheService.evictHolidayCache(holiday);
            }
            
            holidayRepository.deleteById(holidayId);
            log.info("Holiday deleted successfully");
        } else {
            log.warn("Holiday with ID {} not found for deletion", holidayId);
        }
    }
    
    @Override
    public List<String> getAllFinancialYears() {
        log.debug("Getting all available financial years");
        
        // First try to get from cache
        List<String> cachedYears = holidayCacheService.getCachedFinancialYears();
        if (cachedYears != null) {
            log.debug("Returning cached financial years");
            return cachedYears;
        }
        
        // If not in cache, fetch from database
        List<String> financialYears = holidayRepository.findAllFinancialYears();
        
        // Cache the result
        holidayCacheService.cacheFinancialYears(financialYears);
        
        log.debug("Fetched and cached {} financial years", financialYears.size());
        return financialYears;
    }
    
    @Override
    public List<HolidayResponse> getHolidaysByNamePattern(String namePattern) {
        log.debug("Getting holidays by name pattern: {}", namePattern);
        
        // First try to get from cache
        List<HolidayResponse> cachedHolidays = holidayCacheService.getCachedHolidaysByNamePattern(namePattern);
        if (cachedHolidays != null) {
            log.debug("Returning cached holidays for name pattern: {}", namePattern);
            return cachedHolidays;
        }
        
        // If not in cache, fetch from database
        List<EmpHoliday> holidays = holidayRepository.findByHolidayNamePattern(namePattern);
        List<HolidayResponse> holidayResponses = holidays.stream()
                .map(this::convertToHolidayResponse)
                .collect(Collectors.toList());
        
        // Cache the result
        holidayCacheService.cacheHolidaysByNamePattern(namePattern, holidayResponses);
        
        log.debug("Fetched and cached {} holidays for name pattern: {}", holidayResponses.size(), namePattern);
        return holidayResponses;
    }
    
    @Override
    public void refreshFinancialYearCache(String financialYear) {
        log.info("Refreshing cache for financial year: {}", financialYear);
        holidayCacheService.refreshFinancialYearCache(financialYear);
    }
    
    @Override
    public void evictAllCache() {
        log.info("Evicting all holiday cache entries");
        holidayCacheService.evictAllCache();
    }
    
    @Override
    public void evictFinancialYearCache(String financialYear) {
        log.info("Evicting cache for financial year: {}", financialYear);
        holidayCacheService.evictFinancialYearCache(financialYear);
    }
    
    /**
     * Convert EmpHoliday entity to HolidayResponse DTO
     * @param holiday Holiday entity
     * @return HolidayResponse DTO
     */
    private HolidayResponse convertToHolidayResponse(EmpHoliday holiday) {
        return HolidayResponse.builder()
                .holidayId(holiday.getId())
                .financialYear(holiday.getFinancialYear())
                .holidayName(holiday.getHolidayName())
                .holidayDate(holiday.getHolidayDate())
                .isFullDay(holiday.getIsFullDay())
                .holidayTypeDisplay(holiday.getHolidayTypeDisplay())
                .build();
    }
    
    /**
     * Get current financial year based on current date
     * @return Current financial year in format "YYYY-YY"
     */
    private String getCurrentFinancialYear() {
        LocalDate now = LocalDate.now();
        int currentYear = now.getYear();
        
        // Financial year typically starts from April 1st
        if (now.getMonthValue() >= 4) {
            // Current year to next year (e.g., 2024-25)
            return currentYear + "-" + String.valueOf(currentYear + 1).substring(2);
        } else {
            // Previous year to current year (e.g., 2023-24)
            return (currentYear - 1) + "-" + String.valueOf(currentYear).substring(2);
        }
    }
}
