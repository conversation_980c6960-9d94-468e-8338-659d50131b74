package com.stpl.tech.attendance.dto.RosteringDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShiftResponseDTO {
    private Integer shiftId; // Auto-generated from DB
    private String shiftName;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private Map<String, Integer> dayWiseIdealCount;
    private Integer idealCount;
    private boolean allDaySame;
    private String status;
    private String successMessage;
    private LocalDateTime creationTime;
}
