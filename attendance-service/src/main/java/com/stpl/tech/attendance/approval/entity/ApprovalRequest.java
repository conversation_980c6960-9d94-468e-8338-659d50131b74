package com.stpl.tech.attendance.approval.entity;

import com.stpl.tech.attendance.enums.ApprovalStatus;
import com.stpl.tech.attendance.enums.ApprovalType;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonManagedReference;

import java.time.LocalDateTime;
import java.util.List;

@Entity
@Table(name = "APPROVAL_REQUEST", indexes = {
    @Index(name = "idx_approval_request_reference", columnList = "REFERENCE_ID, REFERENCE_TYPE"),
    @Index(name = "idx_approval_request_unit", columnList = "UNIT_ID")
})
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ApprovalRequest {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "REQUEST_ID")
    private Long id;

    @Column(name = "REQUEST_TYPE", nullable = false)
    @Enumerated(EnumType.STRING)
    private ApprovalType requestType;

    @Column(name = "REFERENCE_ID", nullable = false)
    private Long referenceId;

    @Column(name = "UNIT_ID", nullable = false)
    private Long unitId;

    @Column(name = "REQUESTER_ID", nullable = false)
    private Long requesterId;

    @Column(name = "REQUESTER_NAME")
    private String requesterName;

    @Column(name = "REQUEST_DATE", nullable = false)
    private LocalDateTime requestDate;

    @Column(name = "DESCRIPTION")
    private String description;

    @Column(name = "METADATA", columnDefinition = "json")
    private String metadata;

    @Column(name = "PROCESS_INSTANCE_ID")
    private String processInstanceId;

    @Column(name = "STATUS", nullable = false)
    @Enumerated(EnumType.STRING)
    private ApprovalStatus status;

    @Column(name = "CURRENT_STEP", nullable = false)
    private Integer currentStep;

    @Column(name = "PROCESS_DEFINITION_VERSION")
    private Integer processDefinitionVersion;

    @Column(name = "TOTAL_STEPS")
    private Integer totalSteps;

    @Column(name = "CREATED_DATE", nullable = false)
    private LocalDateTime createdDate;

    @Column(name = "CREATED_BY", nullable = false)
    private String createdBy;

    @Column(name = "UPDATED_DATE", nullable = false)
    private LocalDateTime updatedDate;

    @Column(name = "UPDATED_BY", nullable = false)
    private String updatedBy;


    @OneToMany(mappedBy = "approvalRequest", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonManagedReference
    private List<ApprovalStep> approvalSteps;

    public ApprovalRequest(Long approvalRequestId) {
        this.id = approvalRequestId;
    }

    public ApprovalRequest(Long approvalRequestId,Long requesterId) {
        this.id = approvalRequestId;
        this.requesterId = requesterId;
    }
}