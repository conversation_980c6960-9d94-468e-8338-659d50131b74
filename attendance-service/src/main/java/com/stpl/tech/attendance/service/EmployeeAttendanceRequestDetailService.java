package com.stpl.tech.attendance.service;

import com.stpl.tech.attendance.entity.EmployeeAttendanceRequestDetail;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Service interface for managing employee attendance request details
 * This service handles day-wise leave entries with type and request type for proper leave deduction
 */
public interface EmployeeAttendanceRequestDetailService {
    
    /**
     * Create detail records for a leave application
     *
     * @param attendanceReqId The ID of the main attendance request
     * @param leaveDates      Map of dates to request types
     * @param leaveType       The type of leave (LEAVE, COMP_OFF, LWP)
     * @param userId          The user ID
     */
    void createLeaveDetailRecords(Long attendanceReqId,
                                  Map<LocalDateTime, String> leaveDates,
                                  String leaveType, String userId);
    
    /**
     * Get all detail records for a specific attendance request
     * @param attendanceReqId The ID of the attendance request
     * @return List of detail records
     */
    List<EmployeeAttendanceRequestDetail> getDetailsByAttendanceRequestId(Long attendanceReqId);
    
    /**
     * Get leave details for leave deduction calculation
     * @param attendanceReqId The ID of the attendance request
     * @return List of detail records with type and request type for leave deduction
     */
    List<EmployeeAttendanceRequestDetail> getLeaveDetailsForDeduction(Long attendanceReqId);
    
    /**
     * Calculate total leave days to deduct based on detail records
     * @param detailRecords List of detail records
     * @return Total days to deduct (0.5 for half day, 1.0 for full day)
     */
    BigDecimal calculateLeaveDeduction(List<EmployeeAttendanceRequestDetail> detailRecords);
    
    /**
     * Delete all detail records for a specific attendance request
     * @param attendanceReqId The ID of the attendance request
     */
    void deleteDetailsByAttendanceRequestId(Long attendanceReqId);
    
    /**
     * Update detail record
     * @param detail The detail record to update
     * @return Updated detail record
     */
    EmployeeAttendanceRequestDetail updateDetail(EmployeeAttendanceRequestDetail detail);
}
