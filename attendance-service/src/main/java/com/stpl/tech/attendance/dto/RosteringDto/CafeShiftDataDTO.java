package com.stpl.tech.attendance.dto.RosteringDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CafeShiftDataDTO {
    private Integer unitId;
    private String unitName;
    private List<ShiftDetailDTO> shifts;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ShiftDetailDTO {
        private Integer cafeShiftMappingId;
        private Integer shiftId;
        private String shiftName;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        private LocalDateTime creationTime;
        private String createdBy;
        private Map<String, Integer> dayWiseIdealCount;
        private Integer idealCount;
        private boolean allDaySame;
    }
}
