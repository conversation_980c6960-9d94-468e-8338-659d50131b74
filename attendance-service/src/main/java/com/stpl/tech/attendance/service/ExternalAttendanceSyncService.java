package com.stpl.tech.attendance.service;

import com.stpl.tech.attendance.dto.AttendanceSyncMessage;
import com.stpl.tech.attendance.dto.SyncStatistics;
import com.stpl.tech.attendance.entity.AttendanceSyncRecord;

import java.time.LocalDate;
import java.util.List;

public interface ExternalAttendanceSyncService {
    
    /**
     * Queue attendance record for external sync
     */
    void queueAttendanceForSync(Long attendanceRecordId);
    
    /**
     * Process attendance sync message from queue
     */
    void processAttendanceSync(AttendanceSyncMessage message);
    
    /**
     * Fetch and sync attendance data from external database
     */
    void fetchAndSyncAttendanceData();
    
    /**
     * Get sync status for an attendance record
     */
    AttendanceSyncRecord getSyncStatus(Long attendanceRecordId);
    
    /**
     * Retry failed sync records
     */
    void retryFailedSyncs();
    
    /**
     * Get sync statistics
     */
    SyncStatistics getSyncStatistics();
    
    /**
     * Resync attendance data for a specific date range and employee
     */
    void resyncAttendanceData(LocalDate fromDate, LocalDate toDate, Integer employeeId);
    
    /**
     * Resync all failed records
     */
    void resyncFailedRecords();
    
    /**
     * Get sync records by date range and employee
     */
    List<AttendanceSyncRecord> getSyncRecordsByDateRange(LocalDate fromDate, LocalDate toDate, Integer employeeId);
    
    /**
     * Get all failed sync records
     */
    List<AttendanceSyncRecord> getFailedSyncRecords();
} 