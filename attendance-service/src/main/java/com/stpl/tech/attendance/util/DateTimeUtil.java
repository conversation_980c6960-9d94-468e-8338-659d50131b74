package com.stpl.tech.attendance.util;

import java.time.LocalDateTime;
import java.time.ZoneId;

/**
 * Utility class for date/time operations with IST timezone
 */
public class DateTimeUtil {
    
    private static final ZoneId IST_ZONE = ZoneId.of("Asia/Kolkata");
    
    private DateTimeUtil() {
        // Private constructor to prevent instantiation
    }
    
    /**
     * Get current date and time in IST timezone
     * @return LocalDateTime in IST
     */
    public static LocalDateTime now() {
        return LocalDateTime.now(IST_ZONE);
    }
    
    /**
     * Convert UTC LocalDateTime to IST
     * @param utcDateTime UTC LocalDateTime
     * @return LocalDateTime in IST
     */
    public static LocalDateTime toIST(LocalDateTime utcDateTime) {
        return utcDateTime.atZone(ZoneId.of("UTC"))
                         .withZoneSameInstant(IST_ZONE)
                         .toLocalDateTime();
    }
    
    /**
     * Convert IST LocalDateTime to UTC
     * @param istDateTime IST LocalDateTime
     * @return LocalDateTime in UTC
     */
    public static LocalDateTime toUTC(LocalDateTime istDateTime) {
        return istDateTime.atZone(IST_ZONE)
                         .withZoneSameInstant(ZoneId.of("UTC"))
                         .toLocalDateTime();
    }
} 