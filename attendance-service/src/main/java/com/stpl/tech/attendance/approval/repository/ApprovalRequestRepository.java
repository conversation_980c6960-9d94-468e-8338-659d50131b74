package com.stpl.tech.attendance.approval.repository;

import com.stpl.tech.attendance.approval.entity.ApprovalRequest;
import com.stpl.tech.attendance.enums.ApprovalStatus;
import com.stpl.tech.attendance.enums.ApprovalType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface ApprovalRequestRepository extends JpaRepository<ApprovalRequest, Long> {


    ApprovalRequest findByReferenceIdAndRequestType(Long referenceId, ApprovalType requestType);

    @Query("SELECT ar FROM ApprovalRequest ar WHERE ar.id = :requestId AND ar.requestType = :requestType")
    ApprovalRequest findByIdAndRequestType(Long requestId, ApprovalType requestType);

    @Query("SELECT ar FROM ApprovalRequest ar WHERE ar.requesterId = :userId AND ar.status = 'PENDING'")
    List<ApprovalRequest> findPendingRequestsByRequester(@Param("userId") String userId);

    @Query("SELECT ar FROM ApprovalRequest ar WHERE ar.requestType = :requestType AND ar.status = 'PENDING'")
    List<ApprovalRequest> findPendingByRequestType(@Param("requestType") String requestType);

    List<ApprovalRequest> findByRequesterId(Long requesterId);

    @Query("SELECT ar FROM ApprovalRequest ar WHERE ar.requesterId = :userId AND ar.status IN ('APPROVED', 'REJECTED', 'CANCELLED')")
    Page<ApprovalRequest> findCompletedRequestsByRequester(@Param("userId") String userId, Pageable pageable);

    Optional<ApprovalRequest> findByProcessInstanceId(String processInstanceId);

    List<ApprovalRequest> findByReferenceId(Long referenceId);

    @Query(value = """
        SELECT DISTINCT ar.*
        FROM APPROVAL_REQUEST ar
        JOIN APPROVAL_STEP aps ON aps.REQUEST_ID = ar.REQUEST_ID
        JOIN SUB_APPROVAL_STEP sas ON sas.STEP_ID = aps.STEP_ID
        JOIN EMPLOYEE_DETAIL ed ON ed.EMP_ID = ar.REQUESTER_ID
        JOIN DESIGNATION d ON d.DESIGNATION_ID = ed.DESIGNATION_ID
        WHERE sas.APPROVER_ID = :approverId
          AND ar.STATUS != :status
          AND (:requesterIdsIsNull = TRUE OR ar.REQUESTER_ID IN (:requesterIds))
          AND (:typesIsNull = TRUE OR ar.REQUEST_TYPE IN (:types))
          AND (:designationsIsNull = TRUE OR d.DESIGNATION_NAME IN (:designations))
          AND (:statusesIsNull = TRUE OR ar.STATUS IN (:statuses))
          AND (:fromDate IS NULL OR ar.REQUEST_DATE >= :fromDate)
          AND (:toDate IS NULL OR ar.REQUEST_DATE <= :toDate)
        """,
            countQuery = """
        SELECT COUNT(DISTINCT ar.REQUEST_ID)
        FROM APPROVAL_REQUEST ar
        JOIN APPROVAL_STEP aps ON aps.REQUEST_ID = ar.REQUEST_ID
        JOIN SUB_APPROVAL_STEP sas ON sas.STEP_ID = aps.STEP_ID
        JOIN EMPLOYEE_DETAIL ed ON ed.EMP_ID = ar.REQUESTER_ID
        JOIN DESIGNATION d ON d.DESIGNATION_ID = ed.DESIGNATION_ID
        WHERE sas.APPROVER_ID = :approverId
          AND ar.STATUS != :status
          AND (:requesterIdsIsNull = TRUE OR ar.REQUESTER_ID IN (:requesterIds))
          AND (:typesIsNull = TRUE OR ar.REQUEST_TYPE IN (:types))
          AND (:designationsIsNull = TRUE OR d.DESIGNATION_NAME IN (:designations))
          AND (:statusesIsNull = TRUE OR ar.STATUS IN (:statuses))
          AND (:fromDate IS NULL OR ar.REQUEST_DATE >= :fromDate)
          AND (:toDate IS NULL OR ar.REQUEST_DATE <= :toDate)
        """, nativeQuery = true
    )
    Page<ApprovalRequest> findByApproverIdAndStatusNotWithStepsAndRequesterIds(
            @Param("approverId") Long approverId,
            @Param("status") String status,
            @Param("requesterIds") List<Long> requesterIds,
            @Param("requesterIdsIsNull") Boolean requesterIdsIsNull,
            @Param("types") List<String> types,
            @Param("typesIsNull") Boolean typesIsNull,
            @Param("designations") List<String> designations,
            @Param("designationsIsNull") Boolean designationsIsNull,
            @Param("statuses") List<String> statuses,
            @Param("statusesIsNull") Boolean statusesIsNull,
            @Param("fromDate") LocalDateTime fromDate,
            @Param("toDate") LocalDateTime toDate,
            Pageable pageable
    );

    @Query("SELECT ar FROM ApprovalRequest ar WHERE ar.referenceId = :referenceId AND ar.requestType = :requestType AND ar.status IN :statuses")
    ApprovalRequest findByReferenceIdAndRequestTypeAndStatus(Long referenceId, ApprovalType requestType, List<ApprovalStatus> statuses);
}