package com.stpl.tech.attendance.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.*;

@Documented
@Constraint(validatedBy = BiometricDeviceIdValidator.class)
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidBiometricDeviceId {
    String message() default "Invalid biometric device ID format. Expected format: terminalId_unitType_unitId";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
} 