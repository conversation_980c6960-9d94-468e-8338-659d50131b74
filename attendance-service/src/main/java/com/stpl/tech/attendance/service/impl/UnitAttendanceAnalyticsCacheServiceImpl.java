package com.stpl.tech.attendance.service.impl;

import com.stpl.tech.attendance.cache.service.UnitCacheService;
import com.stpl.tech.attendance.cache.service.UserCacheService;
import com.stpl.tech.attendance.dto.UnitAttendanceAnalyticsDTO;
import com.stpl.tech.attendance.entity.AttendanceRecord;
import com.stpl.tech.attendance.entity.EmployeeShiftInstances;
import com.stpl.tech.attendance.entity.RosteringEntity.RosteringConstants;
import com.stpl.tech.attendance.repository.AttendanceRecordRepository;
import com.stpl.tech.attendance.repository.EmployeeShiftInstancesRepository;
import com.stpl.tech.attendance.service.EmpEligibilityService;
import com.stpl.tech.attendance.service.UnitAttendanceAnalyticsCacheService;
import com.stpl.tech.attendance.service.UnitResolutionService;
import com.stpl.tech.attendance.util.DateTimeUtil;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class UnitAttendanceAnalyticsCacheServiceImpl implements UnitAttendanceAnalyticsCacheService {
    
    private final UserCacheService userCacheService;
    private final UnitCacheService unitCacheService;
    private final EmpEligibilityService empEligibilityService;
    private final EmployeeShiftInstancesRepository employeeShiftInstancesRepository;
    private final AttendanceRecordRepository attendanceRecordRepository;
    private final UnitResolutionService unitResolutionService;
    private final MetadataServiceImpl metaDataServiceImpl;

    @Override
    @Cacheable(value = "unitAttendanceAnalytics", key = "#unitId + '_' + #businessDate")
    public UnitAttendanceAnalyticsDTO getUnitAnalytics(Integer unitId, LocalDate businessDate) {
        log.debug("Calculating unit analytics for unit: {} on date: {}", unitId, businessDate);
        
        try {
            // Get unit details
            UnitBasicDetail unit = unitCacheService.getUnitBasicDetail(unitId);
            if (unit == null) {
                throw new RuntimeException("Unit not found with ID: " + unitId);
            }
            
            // Get all employees eligible for attendance at this unit on this date
            List<EmployeeBasicDetail> eligibleEmployees = unitResolutionService.getEmployeesForUnit(unitId, businessDate).stream()
                .map(userCacheService::getUserById)
                .filter(employee -> employee != null)
                .collect(Collectors.toList());
            
            // Get shift instances for these employees on this date
            List<EmployeeShiftInstances> shiftInstances = getShiftInstancesForEmployees(eligibleEmployees, businessDate);

            
            // Calculate analytics
            return calculateUnitAnalytics(unit, businessDate, eligibleEmployees, shiftInstances);
            
        } catch (Exception e) {
            log.error("Error calculating unit analytics for unit: {} on date: {}", unitId, businessDate, e);
            throw new RuntimeException("Failed to calculate unit analytics", e);
        }
    }
    
    @Override
    @Async("asyncTaskExecutor")
    public void updateUnitAnalyticsCacheAsync(Integer unitId, LocalDate businessDate, Integer employeeId) {
        try {
            log.debug("Updating unit analytics cache for unit: {} on date: {} with employee: {}", unitId, businessDate, employeeId);
            
            // Get current cached analytics
            UnitAttendanceAnalyticsDTO currentAnalytics = getUnitAnalytics(unitId, businessDate);
            
            // Get employee details
            EmployeeBasicDetail employee = userCacheService.getUserById(employeeId);
            if (employee == null) {
                log.warn("Employee not found for cache update: {}", employeeId);
                return;
            }
            
            // Get employee's shift instance for this date
            EmployeeShiftInstances shiftInstance = employeeShiftInstancesRepository.findByEmpIdAndBusinessDateAndInstanceStatus(employeeId, businessDate, RosteringConstants.ACTIVE).orElse(null);
            
            // Get employee's attendance records for this date
            List<AttendanceRecord> employeeAttendanceRecords = attendanceRecordRepository.findByEmployeeIdAndDate(employeeId, businessDate);
            
            // Update analytics with new employee data
            UnitAttendanceAnalyticsDTO updatedAnalytics = updateAnalyticsWithEmployeeData(
                currentAnalytics, employee, shiftInstance, employeeAttendanceRecords);
            
            // Evict and recache with updated data
            metaDataServiceImpl.evictAndRecacheUnitAnalytics(unitId, businessDate);
            
            log.debug("Successfully updated unit analytics cache for unit: {} on date: {} with employee: {}", unitId, businessDate, employeeId);
            
        } catch (Exception e) {
            log.error("Error updating unit analytics cache for unit: {} on date: {} with employee: {}", unitId, businessDate, employeeId, e);
        }
    }
    
    @Override
    @Scheduled(cron = "0 0 5 * * ?") // Every day at 5 AM
    @CacheEvict(value = "unitAttendanceAnalytics", allEntries = true)
    public void clearUnitAttendanceAnalyticsCache() {
        log.info("Clearing all unit attendance analytics cache");
    }
    
    /**
     * Evict and recache unit analytics with updated data
     */


    
    /**
     * Update analytics with new employee data
     */
    private UnitAttendanceAnalyticsDTO updateAnalyticsWithEmployeeData(UnitAttendanceAnalyticsDTO currentAnalytics,
                                                                      EmployeeBasicDetail employee,
                                                                      EmployeeShiftInstances shiftInstance,
                                                                      List<AttendanceRecord> employeeAttendanceRecords) {
        
        // Check if employee was already counted
        boolean wasAlreadyCounted = isEmployeeAlreadyCounted(currentAnalytics, employee.getId());
        
        if (wasAlreadyCounted) {
            // Employee was already counted, recalculate everything to ensure accuracy
            return recalculateAnalyticsWithEmployeeData(currentAnalytics, employee, shiftInstance, employeeAttendanceRecords);
        } else {
            // Employee is new, add their data to existing analytics
            return addEmployeeDataToAnalytics(currentAnalytics, employee, shiftInstance, employeeAttendanceRecords);
        }
    }
    
    /**
     * Check if employee was already counted in current analytics
     */
    private boolean isEmployeeAlreadyCounted(UnitAttendanceAnalyticsDTO analytics, Integer employeeId) {
        // This is a simplified check - in a real implementation, you might want to track individual employees
        // For now, we'll assume if the employee has attendance records, they were counted
        return true; // Always recalculate for accuracy
    }
    
    /**
     * Recalculate analytics with employee data
     */
    private UnitAttendanceAnalyticsDTO recalculateAnalyticsWithEmployeeData(UnitAttendanceAnalyticsDTO currentAnalytics,
                                                                           EmployeeBasicDetail employee,
                                                                           EmployeeShiftInstances shiftInstance,
                                                                           List<AttendanceRecord> employeeAttendanceRecords) {
        
        // Get unit details
        UnitBasicDetail unit = unitCacheService.getUnitBasicDetail(currentAnalytics.getUnitId());
        if (unit == null) {
            return currentAnalytics;
        }
        
        // Get all employees eligible for attendance at this unit on this date
        List<EmployeeBasicDetail> eligibleEmployees =  unitResolutionService.getEmployeesForUnit(currentAnalytics.getUnitId(), currentAnalytics.getBusinessDate()).stream()
            .map(userCacheService::getUserById)
            .filter(emp -> emp != null)
            .collect(Collectors.toList());
        
        // Get shift instances for these employees on this date
        List<EmployeeShiftInstances> shiftInstances = getShiftInstancesForEmployees(eligibleEmployees, currentAnalytics.getBusinessDate());

        
        // Recalculate analytics
        return calculateUnitAnalytics(unit, currentAnalytics.getBusinessDate(), eligibleEmployees, shiftInstances);
    }
    
    /**
     * Add employee data to existing analytics
     */
    private UnitAttendanceAnalyticsDTO addEmployeeDataToAnalytics(UnitAttendanceAnalyticsDTO currentAnalytics,
                                                                 EmployeeBasicDetail employee,
                                                                 EmployeeShiftInstances shiftInstance,
                                                                 List<AttendanceRecord> employeeAttendanceRecords) {
        
        // Create a copy of current analytics
        UnitAttendanceAnalyticsDTO updatedAnalytics = UnitAttendanceAnalyticsDTO.builder()
            .unitId(currentAnalytics.getUnitId())
            .unitName(currentAnalytics.getUnitName())
            .unitCode(currentAnalytics.getUnitCode())
            .businessDate(currentAnalytics.getBusinessDate())
            .idealEmployeeCount(currentAnalytics.getIdealEmployeeCount())
            .actualEmployeeCount(currentAnalytics.getActualEmployeeCount())
            .onTimeEmployeeCount(currentAnalytics.getOnTimeEmployeeCount())
            .onLeaveEmployeeCount(currentAnalytics.getOnLeaveEmployeeCount())
            .totalIdealWorkingHours(currentAnalytics.getTotalIdealWorkingHours())
            .totalActualWorkingHours(currentAnalytics.getTotalActualWorkingHours())
            .attendancePercentage(currentAnalytics.getAttendancePercentage())
            .onTimePercentage(currentAnalytics.getOnTimePercentage())
            .workingHoursEfficiency(currentAnalytics.getWorkingHoursEfficiency())
            .build();
        
        // Add employee's ideal hours if they have a shift
        if (shiftInstance != null) {
            updatedAnalytics.setTotalIdealWorkingHours(
                updatedAnalytics.getTotalIdealWorkingHours().add(shiftInstance.getIdealHours())
            );
        }
        
        // Add employee's actual hours if they have attendance
        if (!employeeAttendanceRecords.isEmpty()) {
            BigDecimal employeeActualHours = calculateEmployeeActualHours(shiftInstance);
            BigDecimal employeeIdealHours = shiftInstance != null ? shiftInstance.getIdealHours() : BigDecimal.ZERO;
            
            // Cap actual hours at ideal hours
            BigDecimal cappedActualHours = employeeActualHours.min(employeeIdealHours);
            updatedAnalytics.setTotalActualWorkingHours(
                updatedAnalytics.getTotalActualWorkingHours().add(cappedActualHours)
            );
            
            // Update actual employee count
            updatedAnalytics.setActualEmployeeCount(updatedAnalytics.getActualEmployeeCount() + 1);
            
            // Check if employee was on time
            if (isEmployeeOnTime(shiftInstance, employeeAttendanceRecords)) {
                updatedAnalytics.setOnTimeEmployeeCount(updatedAnalytics.getOnTimeEmployeeCount() + 1);
            }
        }
        
        // Recalculate percentages
        updatedAnalytics.setAttendancePercentage(
            calculatePercentage(updatedAnalytics.getActualEmployeeCount(), updatedAnalytics.getIdealEmployeeCount())
        );
        updatedAnalytics.setOnTimePercentage(
            calculatePercentage(updatedAnalytics.getOnTimeEmployeeCount(), updatedAnalytics.getActualEmployeeCount())
        );
        updatedAnalytics.setWorkingHoursEfficiency(
            calculateWorkingHoursEfficiency(updatedAnalytics.getTotalActualWorkingHours(), updatedAnalytics.getTotalIdealWorkingHours())
        );
        
        return updatedAnalytics;
    }
    
    /**
     * Check if employee was on time
     */
    private boolean isEmployeeOnTime(EmployeeShiftInstances shiftInstance, List<AttendanceRecord> attendanceRecords) {
        if (shiftInstance == null || attendanceRecords.isEmpty()) {
            return false;
        }
        
        // Find first check-in of the day
        LocalDateTime firstCheckIn = attendanceRecords.stream()
            .filter(record -> "CHECK_IN".equals(record.getPunchType().name()))
            .map(AttendanceRecord::getPunchTime)
            .min(LocalDateTime::compareTo)
            .orElse(null);
        
        if (firstCheckIn == null) {
            return false;
        }
        
        // Check if check-in is on time (before or equal to expected start time)
        return !firstCheckIn.isAfter(shiftInstance.getExpectedStartTime());
    }
    
    // Helper methods from the original service implementation
    private List<EmployeeBasicDetail> getEligibleEmployeesForUnit(Integer unitId, LocalDate businessDate) {
        return userCacheService.getAllUserCache().values().stream()
            .filter(emp -> emp.getStatus().name().equals("ACTIVE"))
            .filter(emp -> "CAFE".equalsIgnoreCase(emp.getDepartmentName()))
            .filter(emp -> isEligibleForAttendance(emp.getId(), unitId, businessDate))
            .collect(Collectors.toList());
    }
    
    private boolean isEligibleForAttendance(Integer empId, Integer unitId, LocalDate businessDate) {
        try {
            return empEligibilityService.isEligibleForAttendance(
                empId.toString(), 
                unitId.toString()
            );
        } catch (Exception e) {
            log.warn("Error checking eligibility for employee: {} at unit: {} on date: {}", empId, unitId, businessDate, e);
            return false;
        }
    }
    
    private List<EmployeeShiftInstances> getShiftInstancesForEmployees(List<EmployeeBasicDetail> employees, LocalDate businessDate) {
        List<Integer> empIds = employees.stream()
            .map(EmployeeBasicDetail::getId)
            .collect(Collectors.toList());
        
        return employeeShiftInstancesRepository.findByEmpIdsAndDateRange(empIds, businessDate, businessDate);
    }
    
    private List<AttendanceRecord> getAttendanceRecordsForEmployees(List<EmployeeBasicDetail> employees, LocalDate businessDate) {
        List<Integer> empIds = employees.stream()
            .map(EmployeeBasicDetail::getId)
            .collect(Collectors.toList());
        
        return attendanceRecordRepository.findByEmployeeIdInAndDate(empIds, businessDate);
    }
    
    private UnitAttendanceAnalyticsDTO calculateUnitAnalytics(UnitBasicDetail unit, LocalDate businessDate,
                                                              List<EmployeeBasicDetail> eligibleEmployees,
                                                              List<EmployeeShiftInstances> shiftInstances) {
        
        // Create maps for efficient lookup
        Map<Integer, EmployeeShiftInstances> shiftInstanceMap = shiftInstances.stream()
            .collect(Collectors.toMap(EmployeeShiftInstances::getEmpId, si -> si));
        
//        Map<Integer, List<AttendanceRecord>> attendanceMap = attendanceRecords.stream()
//            .collect(Collectors.groupingBy(AttendanceRecord::getEmployeeId));
        
        // Calculate metrics
        int idealEmployeeCount = eligibleEmployees.size();
        int actualEmployeeCount = (int) eligibleEmployees.stream()
            .filter(emp -> shiftInstanceMap.containsKey(emp.getId()) && Objects.nonNull(
                    shiftInstanceMap.get(emp.getId()).getActualStartTime()))
            .count();
        
        int onTimeEmployeeCount = calculateOnTimeEmployees(eligibleEmployees, shiftInstanceMap);
        int onLeaveEmployeeCount = 0; // TODO: Implement leave logic later
        
        BigDecimal totalIdealWorkingHours = calculateTotalIdealHours(shiftInstances);
        BigDecimal totalActualWorkingHours = calculateTotalActualHours(eligibleEmployees, shiftInstanceMap);
        
        // Calculate percentages
        BigDecimal attendancePercentage = calculatePercentage(actualEmployeeCount, idealEmployeeCount);
        BigDecimal onTimePercentage = calculatePercentage(onTimeEmployeeCount, actualEmployeeCount);
        BigDecimal workingHoursEfficiency = calculateWorkingHoursEfficiency(totalActualWorkingHours, totalIdealWorkingHours);
        
        return UnitAttendanceAnalyticsDTO.builder()
            .unitId(unit.getId())
            .unitName(unit.getName())
            .unitCode(unit.getLocationCode())
            .businessDate(businessDate)
            .idealEmployeeCount(idealEmployeeCount)
            .actualEmployeeCount(actualEmployeeCount)
            .onTimeEmployeeCount(onTimeEmployeeCount)
            .onLeaveEmployeeCount(onLeaveEmployeeCount)
            .totalIdealWorkingHours(totalIdealWorkingHours)
            .totalActualWorkingHours(totalActualWorkingHours)
            .attendancePercentage(attendancePercentage)
            .onTimePercentage(onTimePercentage)
            .workingHoursEfficiency(workingHoursEfficiency)
            .build();
    }
    
    private int calculateOnTimeEmployees(List<EmployeeBasicDetail> employees,
                                       Map<Integer, EmployeeShiftInstances> shiftInstanceMap) {
        return (int) employees.stream()
            .filter(emp -> {
                EmployeeShiftInstances shiftInstance = shiftInstanceMap.get(emp.getId());
                
                if (shiftInstance == null) {
                    return false;
                }
                
                // Find first check-in of the day
                LocalDateTime firstCheckIn = shiftInstance.getActualStartTime();
                
                if (firstCheckIn == null) {
                    return false;
                }
                
                // Check if check-in is on time (before or equal to expected start time)
                return !firstCheckIn.isAfter(shiftInstance.getExpectedStartTime());
            })
            .count();
    }
    
    private BigDecimal calculateTotalIdealHours(List<EmployeeShiftInstances> shiftInstances) {
        return shiftInstances.stream()
            .map(EmployeeShiftInstances::getIdealHours)
            .reduce(BigDecimal.ZERO, BigDecimal::add)
            .setScale(2, RoundingMode.HALF_UP);
    }
    
    private BigDecimal calculateTotalActualHours(List<EmployeeBasicDetail> employees,
                                                Map<Integer, EmployeeShiftInstances> shiftInstanceMap) {
        return employees.stream()
            .map(emp -> {
                EmployeeShiftInstances shiftInstance = shiftInstanceMap.get(emp.getId());
                
                if (shiftInstance == null) {
                    return BigDecimal.ZERO;
                }
                
                // Calculate actual hours from attendance records
                BigDecimal actualHours = calculateEmployeeActualHours(shiftInstance);
                
                // Cap at ideal hours
                return actualHours.min(shiftInstance.getIdealHours());
            })
            .reduce(BigDecimal.ZERO, BigDecimal::add)
            .setScale(2, RoundingMode.HALF_UP);
    }
    
    private BigDecimal calculateEmployeeActualHours(EmployeeShiftInstances shiftInstance) {
        LocalDateTime firstCheckIn = shiftInstance.getActualStartTime();
        LocalDateTime lastCheckOut = shiftInstance.getActualEndTime();
        if (firstCheckIn == null) {
            return BigDecimal.ZERO;
        }
        if (lastCheckOut == null) {
            lastCheckOut = DateTimeUtil.now();
        }
        
        // Calculate hours
        long minutes = java.time.Duration.between(firstCheckIn, lastCheckOut).toMinutes();
        return BigDecimal.valueOf(minutes).divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP);
    }
    
    private BigDecimal calculatePercentage(int numerator, int denominator) {
        if (denominator == 0) {
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(numerator)
            .multiply(BigDecimal.valueOf(100))
            .divide(BigDecimal.valueOf(denominator), 2, RoundingMode.HALF_UP);
    }
    
    private BigDecimal calculateWorkingHoursEfficiency(BigDecimal actualHours, BigDecimal idealHours) {
        if (idealHours.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return actualHours
            .multiply(BigDecimal.valueOf(100))
            .divide(idealHours, 2, RoundingMode.HALF_UP);
    }
} 