package com.stpl.tech.attendance.notification.repository;

import com.stpl.tech.attendance.notification.entity.Notification;
import com.stpl.tech.attendance.notification.entity.NotificationRecipient;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface NotificationRecipientRepository extends JpaRepository<NotificationRecipient, String> {
    
    @Query("SELECT nr FROM NotificationRecipient nr WHERE nr.userId = :userId")
    Page<NotificationRecipient> findByUserId(@Param("userId") String userId, Pageable pageable);
    
    @Query("SELECT nr FROM NotificationRecipient nr WHERE nr.userId = :userId AND nr.status = :status")
    Page<NotificationRecipient> findByUserIdAndStatus(
        @Param("userId") String userId,
        @Param("status") NotificationRecipient.DeliveryStatus status,
        Pageable pageable
    );
    
    @Query("SELECT nr FROM NotificationRecipient nr WHERE nr.notificationId = :notificationId")
    List<NotificationRecipient> findByNotificationId(@Param("notificationId") String notificationId);
    
    @Query("SELECT nr FROM NotificationRecipient nr WHERE nr.notificationId = :notificationId AND nr.userId = :userId")
    NotificationRecipient findByNotificationIdAndUserId(
        @Param("notificationId") String notificationId,
        @Param("userId") String userId
    );

    List<NotificationRecipient> findByNotificationIdInAndUserId(List<String> notificationIds, String userId);


    @Query("SELECT nr FROM NotificationRecipient nr WHERE nr.userId = :userId AND nr.notification.type IN :types ORDER BY nr.createdDate DESC")
    Page<NotificationRecipient> findByUserIdAndTypeInOrderByCreatedDateDesc(
        @Param("userId") String userId,  
        @Param("types") List<Notification.NotificationType> types,
        Pageable pageable);
} 