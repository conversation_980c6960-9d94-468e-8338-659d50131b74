package com.stpl.tech.attendance.service;

import com.stpl.tech.attendance.entity.DailyAttendanceSummary;
import com.stpl.tech.attendance.entity.DailyAttendanceSummaryLogs;

/**
 * Service interface for managing DailyAttendanceSummaryLogs
 * Handles logging of changes and restoration of previous data
 */
public interface DailyAttendanceSummaryLogsService {
    
    /**
     * Log the current state of a DailyAttendanceSummary before making changes
     * @param summary The current summary to log
     * @param actionType The type of action being performed
     * @param updatedBy The user performing the action
     * @return The created log entry
     */
    DailyAttendanceSummaryLogs logCurrentState(DailyAttendanceSummary summary, 
                                              DailyAttendanceSummaryLogs.ActionStatus actionStatus,String actionType,
                                              String updatedBy);
    
    /**
     * Restore a DailyAttendanceSummary to its previous state from logs
     * @param summaryId The ID of the summary to restore
     * @param updatedBy The user performing the restoration
     * @return The restored summary, or null if no previous state exists
     */
    DailyAttendanceSummary restoreFromLogs(Long summaryId, String updatedBy);
    
    /**
     * Find the most recent log entry for a specific summary
     * @param summaryId The ID of the summary
     * @return The most recent log entry, or empty if none exists
     */
    java.util.Optional<DailyAttendanceSummaryLogs> findMostRecentLog(Long summaryId);
    
    /**
     * Update existing log entry or create new one if none exists
     * This ensures that log entries are updated rather than creating duplicates
     * @param summary The current summary to log
     * @param actionStatus The action status
     * @param actionType The type of action being performed
     * @param updatedBy The user performing the action
     * @return The updated/created log entry
     */
    DailyAttendanceSummaryLogs updateOrCreateLog(DailyAttendanceSummary summary,
                                                DailyAttendanceSummaryLogs.ActionStatus actionStatus,
                                                String actionType,
                                                String updatedBy,
                                                String status);
}
