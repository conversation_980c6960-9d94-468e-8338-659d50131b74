package com.stpl.tech.attendance.model.response;

import com.stpl.tech.attendance.enums.BiometricStatus;
import lombok.Builder;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@Builder
public class BiometricRegistrationResponse {
    private String empId;
    private String empCode;
    private String unitId;
    private String deviceId;
    private String biometricId;
    private String biometricUserId;
    private String imageUrl;
    private Double latitude;
    private Double longitude;
    private BiometricStatus status;
    private LocalDateTime registrationDate;
    private LocalDateTime lastUpdatedDate;
    private String registeredBy;
    private String lastUpdatedBy;
    private String registrationImageUrl;
} 