package com.stpl.tech.attendance.entity;

import com.stpl.tech.attendance.enums.PunchType;
import com.stpl.tech.attendance.enums.SpecialCaseType;
import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDateTime;

@Entity
@Table(name = "attendance_records")
@Data
public class AttendanceRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "employee_id", nullable = false)
    private Integer employeeId;

    @Column(name = "unit_id", nullable = false)
    private Integer unitId;

    @Column(name = "punch_time", nullable = false)
    private LocalDateTime punchTime;

    @Enumerated(EnumType.STRING)
    @Column(name = "punch_type", nullable = false)
    private PunchType punchType;

    @Column(name = "mac_address")
    private String macAddress;

    @Column(name = "geo_location")
    private String geoLocation;

    @Column(name = "created_by", nullable = false)
    private String createdBy;

    @Column(name = "remarks")
    private String remarks;

    @Column(name = "is_special_case")
    private Boolean isSpecialCase;

    @Enumerated(EnumType.STRING)
    @Column(name = "special_case_type")
    private SpecialCaseType specialCaseType;

    @Column(name = "image_url")
    private String imageUrl;

    @Column(name = "original_image_url")
    private String originalImageUrl;

    @Column(name = "biometric_id")
    private String biometricId;

    @Column(name = "biometric_device_id")
    private String biometricDeviceId; // Format: terminalId_unitType_unitId
} 