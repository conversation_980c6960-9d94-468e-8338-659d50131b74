package com.stpl.tech.attendance.entity;

import com.stpl.tech.attendance.enums.BiometricStatus;
import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.io.Serializable;
import java.time.LocalDateTime;

@Entity
@Table(name = "BIOMETRIC_REGISTRATION")
@Data
public class BiometricRegistration implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "emp_id", nullable = false)
    private String empId;

    @Column(name = "unit_id", nullable = false)
    private String unitId;

    @Column(name = "device_id", nullable = false)
    private String deviceId;

    @Column(name = "biometric_id")
    private String biometricId;

    @Column(name = "biometric_user_id")
    private String biometricUserId;

    @Column(name = "image_url")
    private String imageUrl;

    @Column(name = "original_image_url")
    private String originalImageUrl;

  /*  @Column(name = "cloudfront_key")
    private String cloudfrontKey;*/

    @Column(name = "latitude")
    private Double latitude;

    @Column(name = "longitude")
    private Double longitude;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private BiometricStatus status = BiometricStatus.PENDING;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "updated_by")
    private String updatedBy;

  /*  @Column(name = "image")
    private String image;*/
} 