package com.stpl.tech.attendance.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class BiometricDeviceIdUtil {

    /**
     * Generate biometric device ID in format: terminalId_unitType_unitId
     */
    public String generateBiometricDeviceId(String terminalId, String unitType, Integer unitId) {
        if (terminalId == null || terminalId.isEmpty()) {
            terminalId = "UNKNOWN";
        }
        
        if (unitType == null || unitType.isEmpty()) {
            unitType = "OFFICE";
        }
        
        if (unitId == null) {
            unitId = 0;
        }
        
        return String.format("%s_%s_%s", terminalId, unitType, unitId);
    }

    /**
     * Parse biometric device ID to extract components
     */
    public BiometricDeviceComponents parseBiometricDeviceId(String biometricDeviceId) {
        if (biometricDeviceId == null || biometricDeviceId.isEmpty()) {
            return null;
        }
        
        String[] parts = biometricDeviceId.split("_");
        if (parts.length >= 3) {
            return BiometricDeviceComponents.builder()
                .terminalId(parts[0])
                .unitType(parts[1])
                .unitId(parts[2])
                .build();
        }
        
        log.warn("Invalid biometric device ID format: {}", biometricDeviceId);
        return null;
    }

    /**
     * Extract biometric ID (unitId) from device ID for external service
     */
    public String extractBiometricId(String biometricDeviceId) {
        BiometricDeviceComponents components = parseBiometricDeviceId(biometricDeviceId);
        return components != null ? components.getUnitId() : null;
    }

    /**
     * Validate biometric device ID format
     */
    public boolean isValidBiometricDeviceId(String biometricDeviceId) {
        if (biometricDeviceId == null || biometricDeviceId.isEmpty()) {
            return false;
        }
        
        String[] parts = biometricDeviceId.split("_");
        return parts.length >= 3 && 
               !parts[0].isEmpty() && 
               !parts[1].isEmpty() && 
               !parts[2].isEmpty();
    }

    public static class BiometricDeviceComponents {
        private String terminalId;
        private String unitType;
        private String unitId;

        // Builder pattern
        public static BiometricDeviceComponentsBuilder builder() {
            return new BiometricDeviceComponentsBuilder();
        }

        // Getters and setters
        public String getTerminalId() { return terminalId; }
        public void setTerminalId(String terminalId) { this.terminalId = terminalId; }
        
        public String getUnitType() { return unitType; }
        public void setUnitType(String unitType) { this.unitType = unitType; }
        
        public String getUnitId() { return unitId; }
        public void setUnitId(String unitId) { this.unitId = unitId; }

        public static class BiometricDeviceComponentsBuilder {
            private BiometricDeviceComponents components = new BiometricDeviceComponents();

            public BiometricDeviceComponentsBuilder terminalId(String terminalId) {
                components.terminalId = terminalId;
                return this;
            }

            public BiometricDeviceComponentsBuilder unitType(String unitType) {
                components.unitType = unitType;
                return this;
            }

            public BiometricDeviceComponentsBuilder unitId(String unitId) {
                components.unitId = unitId;
                return this;
            }

            public BiometricDeviceComponents build() {
                return components;
            }
        }
    }
} 