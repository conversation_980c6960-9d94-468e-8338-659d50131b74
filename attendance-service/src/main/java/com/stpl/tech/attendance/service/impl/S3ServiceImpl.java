package com.stpl.tech.attendance.service.impl;

import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.model.DeleteObjectRequest;
import com.amazonaws.util.Base64;
import com.stpl.tech.attendance.service.S3Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.time.LocalDateTime;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@Service
@RequiredArgsConstructor
public class S3ServiceImpl implements S3Service {

    private final AmazonS3 amazonS3;

    @Value("${aws.s3.bucket.name:product.image.dev}")
    private String bucketName;

    @Value("${aws.cloudfront.domain:d1nqp92n3q8zl7.cloudfront.net}")
    private String cloudfrontDomain;

    @Value("${aws.cloudfront.leave-documents.domain:d1nqp92n3q8zl7.cloudfront.net}")
    private String leaveDocumentsCloudfrontDomain;




    @Override
    public String uploadBase64Image(String base64Image,String basePath, String key) {
        try {
            log.info("Uploading image to S3 with key: {}", key);
            
            // Remove data URL prefix if present
            String base64Data = base64Image;
            if (base64Image.contains(",")) {
                base64Data = base64Image.split(",")[1];
            }

            // Decode base64 string to bytes
            byte[] imageBytes = Base64.decode(base64Data);

            // Create metadata
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(imageBytes.length);
            metadata.setContentType("image/jpeg");

            // Upload to S3
            PutObjectRequest putObjectRequest = new PutObjectRequest(
                bucketName,
               basePath + "/" + key,
                new ByteArrayInputStream(imageBytes),
                metadata
            );

            amazonS3.putObject(putObjectRequest);
            log.info("Successfully uploaded image to S3 with key: {}", key);

            return key;
        } catch (Exception e) {
            log.error("Error uploading image to S3: ", e);
            throw new RuntimeException("Failed to upload image to S3: " + e.getMessage());
        }
    }





    @Override
    public String getCloudfrontUrl(String key) {
        return "https://" + cloudfrontDomain + "/" + key;
    }

    @Override
    public String getLeaveDocumentCloudfrontUrl(String key) {
        return "https://" + leaveDocumentsCloudfrontDomain + "/" + key;
    }

    @Override
    public void deleteImage(String key) {
        try {
            log.info("Deleting image from S3 with key: {}", key);
            DeleteObjectRequest deleteObjectRequest = new DeleteObjectRequest(bucketName, key);
            amazonS3.deleteObject(deleteObjectRequest);
            log.info("Successfully deleted image from S3 with key: {}", key);
        } catch (Exception e) {
            log.error("Error deleting image from S3: ", e);
            throw new RuntimeException("Failed to delete image from S3: " + e.getMessage());
        }
    }


    @Override
    public String uploadLeaveDocument(MultipartFile file, String basePath, String key) {
        try {
            log.info("Uploading leave document to S3 with key: {}", key);
            
            // Create metadata
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(file.getSize());
            metadata.setContentType(file.getContentType());
            metadata.addUserMetadata("original-filename", file.getOriginalFilename());

            // Upload to S3 directly from MultipartFile input stream
            PutObjectRequest putObjectRequest = new PutObjectRequest(
                bucketName,
                basePath + "/" + key,
                file.getInputStream(),
                metadata
            );

            amazonS3.putObject(putObjectRequest);
            log.info("Successfully uploaded leave document to S3 with key: {}", key);

            return key;
        } catch (Exception e) {
            log.error("Error uploading leave document to S3: ", e);
            throw new RuntimeException("Failed to upload leave document to S3: " + e.getMessage());
        }
    }

} 