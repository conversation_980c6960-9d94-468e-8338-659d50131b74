package com.stpl.tech.attendance.dto.RosteringDto;

import com.stpl.tech.attendance.dto.EmployeeMetadataDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.domain.Page;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HierarchyEmployeesResponseDTO {
    private Page<EmployeeMetadataDTO> employees;
    private GenericFilterMetadataDTO filterMetadata;
    private Integer unitId;
} 