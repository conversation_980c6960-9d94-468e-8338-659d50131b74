package com.stpl.tech.attendance.service;

import com.stpl.tech.attendance.model.request.ApprovalActionRequest;
import com.stpl.tech.attendance.model.response.ApprovalRequestResponse;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface ApprovalService {
    List<ApprovalRequestResponse> getPendingApprovals(Long employeeId);
    
    Page<ApprovalRequestResponse> getCompletedApprovals(Long employeeId, Pageable pageable);
    
    ApprovalRequestResponse processApprovalAction(Long requestId, ApprovalActionRequest request);
    
    List<ApprovalRequestResponse> getApprovalHistory(Long requestId);
} 