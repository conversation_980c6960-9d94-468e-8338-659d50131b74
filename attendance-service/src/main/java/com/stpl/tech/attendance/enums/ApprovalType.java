package com.stpl.tech.attendance.enums;

import lombok.Getter;
import org.checkerframework.checker.units.qual.A;

@Getter
public enum ApprovalType {
    BIOMETRIC_REGISTRATION("Biometric Registration"),
    EMPLOYEE_TRANSFER("Transfer"),
    EMPLOYEE_LEAVE("Leave"),
    EMPLOYEE_OD("OD"),
    EMPLOYEE_WFH("WFH"),
    EMPLOYEE_REGULARISATION("Regularisation"),
    ATTENDANCE_REQUEST_CANCELLATION("Attendance Cancellation");
    private final String displayName;

    ApprovalType(String displayName) {
        this.displayName = displayName;
    }

}
