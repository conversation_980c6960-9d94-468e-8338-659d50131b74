package com.stpl.tech.attendance.util;

import com.stpl.tech.attendance.constants.ApiConstants;
import com.stpl.tech.attendance.entity.EmpShiftOverride;
import com.stpl.tech.attendance.entity.RosteringEntity.RosteringConstants;
import com.stpl.tech.attendance.entity.RosteringEntity.Shift;
import com.stpl.tech.attendance.entity.RosteringEntity.EmpShiftMapping;
import com.stpl.tech.attendance.repository.EmpShiftOverrideRepository;
import com.stpl.tech.attendance.repository.RosteringRepository.EmpShiftMappingRepository;
import com.stpl.tech.attendance.repository.RosteringRepository.ShiftRepository;
import com.stpl.tech.attendance.domain.EmpShiftMappingFinder;
import com.stpl.tech.attendance.domain.EmpShiftMappingList;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * Utility class for handling shift-related calculations and operations.
 * 
 * <h3>Night Shift Handling</h3>
 * This class properly handles night shifts that span across midnight. For example:
 * <ul>
 *   <li><strong>Night Shift:</strong> Start: 10:00 PM on Jan 24, End: 7:00 AM on Jan 25</li>
 *   <li><strong>Evening Shift:</strong> Start: 8:00 PM on Jan 24, End: 4:00 AM on Jan 25</li>
 * </ul>
 * 
 * <p>The logic detects night shifts by comparing start and end times:
 * <ul>
 *   <li>If end time < start time → Night shift (end time is on next business day)</li>
 *   <li>If end time >= start time → Regular shift (both times on same day)</li>
 * </ul>
 * 
 * <p>This ensures that attendance calculations, shift scheduling, and reporting
 * correctly handle shifts that cross date boundaries.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ShiftHelper {

    private final ShiftRepository shiftRepository;
    private final EmpShiftOverrideRepository empShiftOverrideRepository;
    private final EmpShiftMappingRepository empShiftMappingRepository;

    /**
     * Get shift for employee on specific date (with override priority)
     * @param empId Employee ID
     * @param businessDate Business date
     * @return Shift or null if not found
     */
    public Shift getShiftForEmployee(Integer empId, LocalDate businessDate) {
        // First check for temporary override
        EmpShiftOverride override = getActiveOverrideForDate(empId, businessDate);
        
        if (override != null) {
            Shift overrideShift = shiftRepository.findById(override.getShiftId()).orElse(null);
            if (overrideShift != null) {
                log.debug("Using override shift {} for employee {} on date {}", 
                         overrideShift.getShiftName(), empId, businessDate);
                return overrideShift;
            }
        }
        
        // Check regular shift mapping using Reladomo
        com.stpl.tech.attendance.domain.EmpShiftMapping regularMapping = getEmpShiftMappingForDate(empId, businessDate);
        
        if (regularMapping != null) {
            Shift regularShift = shiftRepository.findById(regularMapping.getShiftId()).orElse(null);
            if (regularShift != null) {
                log.debug("Using regular shift {} for employee {} on date {}", 
                         regularShift.getShiftName(), empId, businessDate);
                return regularShift;
            }
        }
        
        // Use default universal shift
        Shift defaultShift = getDefaultUniversalShift();
        log.debug("Using default shift {} for employee {} on date {}", 
                 defaultShift.getShiftName(), empId, businessDate);
        return defaultShift;
    }

    /**
     * Get active override for specific date
     * @param empId Employee ID
     * @param businessDate Business date
     * @return Override or null if not found
     */
    public EmpShiftOverride getActiveOverrideForDate(Integer empId, LocalDate businessDate) {
        return empShiftOverrideRepository.findByEmpIdAndStartDateLessThanEqualAndEndDateGreaterThanEqualAndStatus(
            empId, businessDate, businessDate, "ACTIVE");
    }

    /**
     * Get employee shift mapping for specific date using Reladomo
     * @param empId Employee ID
     * @param businessDate Business date
     * @return Mapping or null if not found
     */
    public com.stpl.tech.attendance.domain.EmpShiftMapping getEmpShiftMappingForDate(Integer empId, LocalDate businessDate) {
        try {
            // Convert LocalDate to Timestamp for Reladomo
            Timestamp businessDateTimestamp = Timestamp.valueOf(businessDate.atStartOfDay());
            Timestamp currentTime = Timestamp.valueOf(LocalDateTime.now());
            Timestamp infinityTimestamp = java.sql.Timestamp.valueOf(RosteringConstants.INFINITY_TIME);
            
            // Use Reladomo to find active shift mappings for the employee at the business date
            EmpShiftMappingList mappings = EmpShiftMappingFinder.findMany(
                EmpShiftMappingFinder.empId().eq(empId)
                .and(EmpShiftMappingFinder.status().eq("ACTIVE"))
                .and(EmpShiftMappingFinder.businessDate().eq(businessDateTimestamp))
                .and(EmpShiftMappingFinder.processingDate().eq(infinityTimestamp))
            );
            
            if (mappings != null && !mappings.isEmpty()) {
                com.stpl.tech.attendance.domain.EmpShiftMapping mapping = mappings.get(0);
                log.debug("Found Reladomo shift mapping for employee {} on date {}: shiftId={}", 
                         empId, businessDate, mapping.getShiftId());
                return mapping;
            }
            
            log.debug("No active Reladomo shift mapping found for employee {} on date {}", empId, businessDate);
            return null;
            
        } catch (Exception e) {
            log.error("Error getting Reladomo shift mapping for employee {} on date {}", empId, businessDate, e);
            return null;
        }
    }

    /**
     * Get default universal shift
     * @return Default shift
     */
    public Shift getDefaultUniversalShift() {
        // Try to find a shift with the universal shift ID first
        Shift defaultShift = shiftRepository.findById(ApiConstants.UNIVERSAL_SHIFT_ID).orElse(null);
        
        if (defaultShift != null && "ACTIVE".equals(defaultShift.getStatus())) {
            log.debug("Found universal shift with ID {}: {}", ApiConstants.UNIVERSAL_SHIFT_ID, defaultShift.getShiftName());
            return defaultShift;
        }
        
        // Fallback to finding by name
        defaultShift = shiftRepository.findByShiftNameAndStatus("UNIVERSAL Shift", "ACTIVE")
            .orElseGet(() -> {
                // If not found, create a default shift or use the first available shift
                List<Shift> activeShifts = shiftRepository.findByStatus("ACTIVE");
                if (!activeShifts.isEmpty()) {
                    return activeShifts.get(0);
                }
                throw new RuntimeException("No active shifts found in the system");
            });
        
        return defaultShift;
    }

    /**
     * Calculate expected start time based on shift and business date
     * For universal shifts, use expected arrival time from mapping if available
     * @param shift Shift
     * @param businessDate Business date
     * @return Expected start time
     */
    public LocalDateTime calculateExpectedStartTime(Shift shift, LocalDate businessDate) {
        // Check if this is a universal shift
        if (isUniversalShift(shift)) {
            log.debug("Calculating expected start time for universal shift: {}", shift.getShiftName());
            return businessDate.atTime(shift.getStartTime().toLocalTime());
        }
        
        return calculateShiftStartTime(shift, businessDate);
    }

    /**
     * Calculate expected start time for universal shift with expected arrival time
     * @param empId Employee ID
     * @param businessDate Business date
     * @return Expected start time based on expected arrival time
     */
    public LocalDateTime calculateExpectedStartTimeForUniversalShift(Integer empId, LocalDate businessDate) {
        // Get the shift mapping to find expected arrival time
        com.stpl.tech.attendance.domain.EmpShiftMapping mapping = getEmpShiftMappingForDate(empId, businessDate);
        
        if (mapping != null && mapping.getExpectedStartDate() != null) {
            // Use the expected arrival time from the mapping
            LocalDateTime expectedArrivalTime = businessDate.atTime(mapping.getExpectedStartDate().toLocalDateTime().toLocalTime());
            log.debug("Using expected arrival time for universal shift: {} for employee {} on date {}", 
                     expectedArrivalTime, empId, businessDate);
            return expectedArrivalTime;
        }
        
        // Fallback to default shift start time
        Shift universalShift = getDefaultUniversalShift();
        LocalDateTime defaultStartTime = businessDate.atTime(universalShift.getStartTime().toLocalTime());
        log.debug("Using default start time for universal shift: {} for employee {} on date {}", 
                 defaultStartTime, empId, businessDate);
        return defaultStartTime;
    }

    /**
     * Calculate expected end time based on shift and business date
     * For universal shifts, add default hours to expected arrival time
     * @param shift Shift
     * @param businessDate Business date
     * @return Expected end time
     */
    public LocalDateTime calculateExpectedEndTime(Shift shift, LocalDate businessDate) {
        // Check if this is a universal shift
        if (isUniversalShift(shift)) {
            log.debug("Calculating expected end time for universal shift: {}", shift.getShiftName());
            return businessDate.atTime(shift.getEndTime().toLocalTime());
        }
        
        return calculateShiftEndTime(shift, businessDate);
    }

    /**
     * Utility method to calculate shift end time considering night shifts that span across midnight
     * @param shift Shift
     * @param businessDate Business date
     * @return LocalDateTime representing the end time on the appropriate date
     */
    public LocalDateTime calculateShiftEndTime(Shift shift, LocalDate businessDate) {
        // Get the start and end times as LocalTime for comparison
        LocalTime shiftStartTime = shift.getStartTime().toLocalTime();
        LocalTime shiftEndTime = shift.getEndTime().toLocalTime();
        
        // Check if this is a night shift that spans across midnight
        // If end time is earlier than start time, it means the shift goes into the next day
        if (shiftEndTime.isBefore(shiftStartTime)) {
            // This is a night shift - end time is on the next business day
            LocalDate endDate = businessDate.plusDays(1);
            LocalDateTime expectedEndTime = endDate.atTime(shiftEndTime);
            log.debug("Night shift detected: start time {} on {}, end time {} on {} (next day)", 
                     shiftStartTime, businessDate, shiftEndTime, endDate);
            return expectedEndTime;
        } else {
            // Regular shift - start and end time on the same day
            LocalDateTime expectedEndTime = businessDate.atTime(shiftEndTime);
            log.debug("Regular shift: start time {} and end time {} on {}", 
                     shiftStartTime, shiftEndTime, businessDate);
            return expectedEndTime;
        }
    }

    /**
     * Utility method to calculate shift start time (always on the business date)
     * @param shift Shift
     * @param businessDate Business date
     * @return LocalDateTime representing the start time on the business date
     */
    public LocalDateTime calculateShiftStartTime(Shift shift, LocalDate businessDate) {
        return businessDate.atTime(shift.getStartTime().toLocalTime());
    }

    /**
     * Calculate expected end time for universal shift with expected arrival time
     * @param empId Employee ID
     * @param businessDate Business date
     * @return Expected end time based on expected arrival time + default hours
     */
    public LocalDateTime calculateExpectedEndTimeForUniversalShift(Integer empId, LocalDate businessDate) {
        // Get the expected start time (which uses expected arrival time for universal shifts)
        LocalDateTime expectedStartTime = calculateExpectedStartTimeForUniversalShift(empId, businessDate);
        
        // Add default universal shift hours to get end time
        LocalDateTime expectedEndTime = expectedStartTime.plusHours(ApiConstants.DEFAULT_UNIVERSAL_SHIFT_HOURS.longValue());
        
        log.debug("Calculated expected end time for universal shift: {} (start: {} + {} hours) for employee {} on date {}", 
                 expectedEndTime, expectedStartTime, ApiConstants.DEFAULT_UNIVERSAL_SHIFT_HOURS, empId, businessDate);
        
        return expectedEndTime;
    }

    /**
     * Check if a shift is a universal shift
     * @param shift Shift to check
     * @return true if universal shift, false otherwise
     */
    public boolean isUniversalShift(Shift shift) {
        return shift != null && (
            ApiConstants.UNIVERSAL_SHIFT_ID.equals(shift.getShiftId()) ||
            "UNIVERSAL Shift".equalsIgnoreCase(shift.getShiftName()) ||
            shift.getShiftName().toLowerCase().contains("universal")
        );
    }

    /**
     * Calculate ideal hours between start and end time
     * @param startTime Start time
     * @param endTime End time
     * @return Ideal hours
     */
    public java.math.BigDecimal calculateIdealHours(LocalDateTime startTime, LocalDateTime endTime) {
        java.time.Duration duration = java.time.Duration.between(startTime, endTime);
        return java.math.BigDecimal.valueOf(duration.toMinutes() / 60.0);
    }
} 