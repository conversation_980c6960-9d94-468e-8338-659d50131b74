package com.stpl.tech.attendance.exception;

/**
 * Custom exception for Cafe Live Dashboard related errors
 */
public class CafeLiveDashboardException extends RuntimeException {
    
    private final String errorCode;
    
    public CafeLiveDashboardException(String message) {
        super(message);
        this.errorCode = "CAFE_DASHBOARD_ERROR";
    }
    
    public CafeLiveDashboardException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = "CAFE_DASHBOARD_ERROR";
    }
    
    public CafeLiveDashboardException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
    
    public CafeLiveDashboardException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
} 