package com.stpl.tech.attendance.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmployeeShiftInstancesWeekResponseDTO {
    private Integer empId;
    private String weekType; // "CURRENT_WEEK" or "NEXT_WEEK"
    private LocalDate weekStart;
    private LocalDate weekEnd;
    private List<EmployeeShiftInstancesDTO> shiftInstances;
    private int totalInstances;
} 