package com.stpl.tech.attendance.model;

import com.stpl.tech.attendance.enums.ImageType;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AttendanceImageRequest {
    private String id;           // Random ID for the image
    private String base64Image;  // Base64 image data
    private ImageType imageType; // Image type (PROCESSED, CROPPED, etc.)
} 