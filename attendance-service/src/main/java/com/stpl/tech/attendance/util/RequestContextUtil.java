package com.stpl.tech.attendance.util;

import org.slf4j.MDC;

/**
 * Utility class for managing request context and providing easy access to request-related information.
 * This class provides static methods to access request ID, user context, and other MDC values.
 */
public class RequestContextUtil {

    private static final String REQUEST_ID_KEY = "requestId";
    private static final String USER_ID_KEY = "userId";
    private static final String UNIT_ID_KEY = "unitId";
    private static final String SESSION_ID_KEY = "sessionId";

    /**
     * Get the current request ID
     * @return request ID or null if not set
     */
    public static String getRequestId() {
        return MDC.get(REQUEST_ID_KEY);
    }

    /**
     * Get the current user ID
     * @return user ID or null if not set
     */
    public static String getUserId() {
        return MDC.get(USER_ID_KEY);
    }

    /**
     * Get the current unit ID
     * @return unit ID or null if not set
     */
    public static String getUnitId() {
        return MDC.get(UNIT_ID_KEY);
    }

    /**
     * Get the current session ID
     * @return session ID or null if not set
     */
    public static String getSessionId() {
        return MDC.get(SESSION_ID_KEY);
    }

    /**
     * Get the current user ID as Integer
     * @return user ID as Integer or null if not set or invalid
     */
    public static Integer getUserIdAsInteger() {
        String userId = getUserId();
        if (userId != null) {
            try {
                return Integer.parseInt(userId);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }

    /**
     * Get the current unit ID as Integer
     * @return unit ID as Integer or null if not set or invalid
     */
    public static Integer getUnitIdAsInteger() {
        String unitId = getUnitId();
        if (unitId != null) {
            try {
                return Integer.parseInt(unitId);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }

    /**
     * Check if request context is available
     * @return true if request ID is set, false otherwise
     */
    public static boolean hasRequestContext() {
        return getRequestId() != null;
    }

    /**
     * Get a formatted string with current request context
     * @return formatted string with request ID, user ID, and unit ID
     */
    public static String getRequestContextString() {
        StringBuilder sb = new StringBuilder();
        sb.append("[requestId:").append(getRequestId()).append("]");
        
        String userId = getUserId();
        if (userId != null) {
            sb.append("[userId:").append(userId).append("]");
        }
        
        String unitId = getUnitId();
        if (unitId != null) {
            sb.append("[unitId:").append(unitId).append("]");
        }
        
        return sb.toString();
    }

    /**
     * Set a custom MDC value
     * @param key the MDC key
     * @param value the MDC value
     */
    public static void setMdcValue(String key, String value) {
        MDC.put(key, value);
    }

    /**
     * Get an MDC value
     * @param key the MDC key
     * @return the MDC value or null if not set
     */
    public static String getMdcValue(String key) {
        return MDC.get(key);
    }

    /**
     * Remove an MDC value
     * @param key the MDC key to remove
     */
    public static void removeMdcValue(String key) {
        MDC.remove(key);
    }

    /**
     * Clear all MDC values
     */
    public static void clearMdc() {
        MDC.clear();
    }
} 