package com.stpl.tech.attendance.config;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;

/**
 * Database configuration for Spring Data JPA with HikariCP connection pool
 * Configured with max pool size of 5 as requested
 */
@Slf4j
@Configuration
public class DatabaseConfig {

    @Value("${spring.datasource.url}")
    private String databaseUrl;

    @Value("${spring.datasource.username}")
    private String databaseUsername;

    @Value("${spring.datasource.password}")
    private String databasePassword;

    @Value("${spring.datasource.driver-class-name}")
    private String driverClassName;

    // Connection pool configuration for Spring Data JPA
    @Value("${attendance.jpa.datasource.max-pool-size:2}")
    private int maxPoolSize;
    
    @Value("${attendance.jpa.datasource.min-idle:2}")
    private int minIdle;
    
    @Value("${attendance.jpa.datasource.connection-timeout:30000}")
    private long connectionTimeout;
    
    @Value("${attendance.jpa.datasource.idle-timeout:600000}")
    private long idleTimeout;
    
    @Value("${attendance.jpa.datasource.max-lifetime:1800000}")
    private long maxLifetime;
    
    @Value("${attendance.jpa.datasource.leak-detection-threshold:60000}")
    private long leakDetectionThreshold;

    /**
     * Primary DataSource bean for Spring Data JPA operations
     * Configured with HikariCP connection pool with max size of 5
     */
    @Bean
    @Primary
    public DataSource dataSource() {
        log.info("Initializing HikariCP DataSource for Spring Data JPA with max pool size: {}", maxPoolSize);
        
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl(databaseUrl);
        config.setUsername(databaseUsername);
        config.setPassword(databasePassword);
        config.setDriverClassName(driverClassName);
        
        // Connection pool settings - Max size 5 as requested
        config.setMaximumPoolSize(maxPoolSize);
        config.setMinimumIdle(minIdle);
        config.setConnectionTimeout(connectionTimeout);
        config.setIdleTimeout(idleTimeout);
        config.setMaxLifetime(maxLifetime);
        config.setLeakDetectionThreshold(leakDetectionThreshold);
        
        // Connection validation
        config.setConnectionTestQuery("SELECT 1");
        config.setValidationTimeout(5000);
        
        // Pool name for monitoring
        config.setPoolName("JpaHikariPool");
        
        // MySQL specific optimizations
        config.addDataSourceProperty("cachePrepStmts", "true");
        config.addDataSourceProperty("prepStmtCacheSize", "250");
        config.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");
        config.addDataSourceProperty("useServerPrepStmts", "true");
        config.addDataSourceProperty("useLocalSessionState", "true");
        config.addDataSourceProperty("rewriteBatchedStatements", "true");
        config.addDataSourceProperty("cacheResultSetMetadata", "true");
        config.addDataSourceProperty("cacheServerConfiguration", "true");
        config.addDataSourceProperty("elideSetAutoCommits", "true");
        config.addDataSourceProperty("maintainTimeStats", "false");
        
        // Disable autocommit for proper transaction management
        config.addDataSourceProperty("autoCommit", "false");
        
        HikariDataSource dataSource = new HikariDataSource(config);
        
        log.info("HikariCP DataSource initialized successfully with pool size: {}, min idle: {}, connection timeout: {}ms", 
                maxPoolSize, minIdle, connectionTimeout);
        
        return dataSource;
    }

    /**
     * JdbcTemplate bean for raw SQL operations
     * Uses the same DataSource as Spring Data JPA
     */
    @Bean
    public JdbcTemplate jdbcTemplate(DataSource dataSource) {
        log.info("Initializing JdbcTemplate with configured DataSource");
        return new JdbcTemplate(dataSource);
    }

    /**
     * Get connection pool statistics for monitoring
     * @param dataSource The HikariDataSource
     * @return Pool statistics as a string
     */
    public String getPoolStats(DataSource dataSource) {
        if (dataSource instanceof HikariDataSource) {
            HikariDataSource hikariDataSource = (HikariDataSource) dataSource;
            return String.format(
                "Pool: %s, Active: %d, Idle: %d, Total: %d, Max: %d",
                hikariDataSource.getPoolName(),
                hikariDataSource.getHikariPoolMXBean().getActiveConnections(),
                hikariDataSource.getHikariPoolMXBean().getIdleConnections(),
                hikariDataSource.getHikariPoolMXBean().getTotalConnections(),
                hikariDataSource.getMaximumPoolSize()
            );
        }
        return "DataSource is not HikariDataSource";
    }
} 