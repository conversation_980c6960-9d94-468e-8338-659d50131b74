package com.stpl.tech.attendance.config;

import com.gs.fw.common.mithra.bulkloader.BulkLoader;
import com.gs.fw.common.mithra.bulkloader.BulkLoaderException;
import com.gs.fw.common.mithra.connectionmanager.SourcelessConnectionManager;
import com.gs.fw.common.mithra.databasetype.DatabaseType;
import com.gs.fw.common.mithra.databasetype.MariaDatabaseType;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.TimeZone;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class MithraConnectionManager implements SourcelessConnectionManager , ApplicationContextAware {

    private static ApplicationContext applicationContext;

    @Value("${spring.datasource.url}")
    private String databaseUrl;

    @Value("${spring.datasource.username}")
    private String databaseUsername;

    @Value("${spring.datasource.password}")
    private String databasePassword;

    @Value("${spring.datasource.driver-class-name}")
    private String driverClassName;

    // Connection pool configuration properties
    @Value("${attendance.datasource.max-pool-size:2}")
    private int maxPoolSize;
    
    @Value("${attendance.datasource.min-idle:2}")
    private int minIdle;
    
    @Value("${attendance.datasource.connection-timeout:30000}")
    private long connectionTimeout;
    
    @Value("${attendance.datasource.idle-timeout:600000}")
    private long idleTimeout;
    
    @Value("${attendance.datasource.max-lifetime:1800000}")
    private long maxLifetime;
    
    @Value("${attendance.datasource.leak-detection-threshold:60000}")
    private long leakDetectionThreshold;

    private static HikariDataSource dataSource;

    private static MithraConnectionManager instance;

    public static MithraConnectionManager getInstance() {
        if (applicationContext == null) {
            throw new RuntimeException("ApplicationContext not initialized. Cannot get MithraConnectionManager instance.");
        }
        return applicationContext.getBean(MithraConnectionManager.class);
    }

    @PostConstruct
    private void init(){
        log.info("Initializing MithraConnectionManager with HikariCP configuration");
        
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl(databaseUrl);
        config.setUsername(databaseUsername);
        config.setPassword(databasePassword);
        config.setDriverClassName(driverClassName);
        
        // Connection pool settings
        config.setMaximumPoolSize(maxPoolSize);
        config.setMinimumIdle(minIdle);
        config.setConnectionTimeout(connectionTimeout);
        config.setIdleTimeout(idleTimeout);
        config.setMaxLifetime(maxLifetime);
        config.setLeakDetectionThreshold(leakDetectionThreshold);
        
        // Connection validation
        config.setConnectionTestQuery("SELECT 1");
        config.setValidationTimeout(5000);
        
        // Pool name for monitoring
        config.setPoolName("MithraHikariPool");
        
        // MySQL specific optimizations
        config.addDataSourceProperty("cachePrepStmts", "true");
        config.addDataSourceProperty("prepStmtCacheSize", "250");
        config.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");
        config.addDataSourceProperty("useServerPrepStmts", "true");
        config.addDataSourceProperty("useLocalSessionState", "true");
        config.addDataSourceProperty("rewriteBatchedStatements", "true");
        config.addDataSourceProperty("cacheResultSetMetadata", "true");
        config.addDataSourceProperty("cacheServerConfiguration", "true");
        config.addDataSourceProperty("elideSetAutoCommits", "true");
        config.addDataSourceProperty("maintainTimeStats", "false");
        
        // Disable autocommit for proper transaction management
        config.addDataSourceProperty("autoCommit", "false");
        
        this.dataSource = new HikariDataSource(config);
        
        log.info("MithraConnectionManager initialized with pool size: {}, min idle: {}, connection timeout: {}ms", 
                maxPoolSize, minIdle, connectionTimeout);
    }

    @PreDestroy
    private void cleanup() {
        log.info("Shutting down MithraConnectionManager and closing connection pool");
        if (dataSource != null && !dataSource.isClosed()) {
            dataSource.close();
            log.info("MithraConnectionManager connection pool closed successfully");
        }
    }

    @Override
    public Connection getConnection() {
        try {
            Connection connection = dataSource.getConnection();
            log.debug("Retrieved connection from pool. Active connections: {}", 
                    dataSource.getHikariPoolMXBean().getActiveConnections());
            return connection;
        } catch (SQLException e) {
            log.error("Failed to get database connection from pool", e);
            throw new RuntimeException("Failed to get database connection", e);
        }
    }

    @Override
    public DatabaseType getDatabaseType() {
        return MariaDatabaseType.getInstance();
    }

    @Override
    public BulkLoader createBulkLoader() throws BulkLoaderException {
        throw new BulkLoaderException("Bulk loading not supported");
    }

    @Override
    public TimeZone getDatabaseTimeZone() {
        return TimeZone.getDefault();
    }

    @Override
    public String getDatabaseIdentifier() {
        return "default";
    }

    @Override
    public void setApplicationContext(@NotNull ApplicationContext applicationContext) throws BeansException {
        MithraConnectionManager.applicationContext = applicationContext;
    }

    /**
     * Get connection pool statistics for monitoring
     */
    public String getPoolStats() {
        if (dataSource != null && !dataSource.isClosed()) {
            return String.format("Pool: %s, Active: %d, Idle: %d, Total: %d", 
                    dataSource.getPoolName(),
                    dataSource.getHikariPoolMXBean().getActiveConnections(),
                    dataSource.getHikariPoolMXBean().getIdleConnections(),
                    dataSource.getHikariPoolMXBean().getTotalConnections());
        }
        return "Pool not available";
    }
}