package com.stpl.tech.attendance.repository;

import com.stpl.tech.attendance.entity.ApplicationInstallationData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.util.List;
import java.util.Optional;

@Repository
public interface ApplicationInstallationDataRepository extends JpaRepository<ApplicationInstallationData, Integer> {
    
    /**
     * Find all active installations for a given unit
     * @param unitId The unit ID to search for
     * @param status The status to filter by (typically "ACTIVE")
     * @return List of active installations for the unit
     */
    List<ApplicationInstallationData> findByUnitIdAndStatus(Integer unitId, String status);
    Optional<ApplicationInstallationData> findByMachineIdAndStatus(String machineId, String status);
    Optional<ApplicationInstallationData> findByMachineId(String machineId);

} 