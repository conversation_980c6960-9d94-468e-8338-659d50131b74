package com.stpl.tech.master.core.external.cache;

import com.stpl.tech.util.AppUtils;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class SessionDetail implements Serializable {
    private static final long serialVersionUID = -5216404700526537437L;

    private int userId;

    private Date loginTime;

    private String sessionKey;

    private int unitId;

    private Date lastAccessTime;

    public SessionDetail(int userId, Date loginTime, String sessionKey, int unitId) {
        super();
        this.userId = userId;
        this.loginTime = loginTime;
        this.sessionKey = sessionKey;
        this.unitId = unitId;
        this.lastAccessTime = AppUtils.getCurrentTimestamp();
    }

    public int getUserId() {
        return userId;
    }

    public Date getLoginTime() {
        return loginTime;
    }

    public String getSessionKey() {
        return sessionKey;
    }

    public int getUnitId() {
        return unitId;
    }

    @Override
    public String toString() {
        return "SessionDetail [userId=" + userId + ", loginTime=" + loginTime + ", sessionKey=" + sessionKey
                + ", unitId=" + unitId + "]";
    }

    public Date getLastAccessTime() {
        return lastAccessTime;
    }

    public void setLastAccessTime(Date lastAccessTime) {
        this.lastAccessTime = lastAccessTime;
    }
}