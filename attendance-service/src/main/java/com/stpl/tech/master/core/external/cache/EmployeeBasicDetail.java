package com.stpl.tech.master.core.external.cache;

import com.stpl.tech.master.domain.model.EmploymentStatus;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.io.Serializable;

@Data
@AllArgsConstructor
public class EmployeeBasicDetail implements Serializable {

    private static final long serialVersionUID = -1854003503992144900L;
    
    private int id;
    private String name;
    private String emailId;
    private EmploymentStatus status;
    private String departmentName;
    private Integer designationId;
    private String designation;
    private String employeeCode;
    private String contactNumber;
    private String mappingStatus;
    private String hrExecutive;
    private String sdpContact;
    private Integer mealAllowanceLimit;
    private int maxAllocatedUnits = -1;
    private Integer reportingManagerId;
    private String slackChannel;
    private Integer departmentId;
    private Integer userPolicyId;
    private Integer companyId;
    private String gender;
    private String imagekey;
    private Integer locCode;
    public EmployeeBasicDetail() {

    }






} 