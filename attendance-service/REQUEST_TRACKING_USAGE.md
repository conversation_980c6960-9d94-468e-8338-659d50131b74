# Request Tracking System - Usage Guide

## Overview
A simple, generic request tracking system that automatically tracks API requests using annotations. 

## Features
- ✅ **Zero Performance Impact** - All operations are async
- ✅ **Non-Intrusive** - Failures don't affect main operations  
- ✅ **Easy to Use** - Just add `@TrackRequest` annotation
- ✅ **Automatic** - No manual code changes needed

## Quick Start

### 1. Add the annotation to any method you want to track:

```java
@RestController
public class MyController {

    @TrackRequest
    @GetMapping("/api/employees")
    public ResponseEntity<List<Employee>> getEmployees() {
        // Your existing code - no changes needed!
        return employeeService.getAllEmployees();
    }
    
    @TrackRequest(description = "Create new employee")
    @PostMapping("/api/employees")
    public ResponseEntity<Employee> createEmployee(@RequestBody EmployeeRequest request) {
        // Your existing code - no changes needed!
        return employeeService.createEmployee(request);
    }
    
    // Only track failures (useful for critical operations)
    @TrackRequest(trackSuccess = false, trackFailure = true)
    @PostMapping("/api/critical-operation")
    public ResponseEntity<?> criticalOperation() {
        // Only failures will be tracked
        return criticalService.performOperation();
    }
}

### 2. Update reference ID after business operation completes:

```java
@TrackRequest
@PostMapping("/api/attendance/punch-with-images")
public ResponseEntity<ApiResponse<AttendancePunchResponse>> markAttendanceWithImages(
        @RequestBody AttendancePunchRequest request) {
    
    // Your existing business logic
    AttendancePunchResponse response = attendancePunchService.processAttendancePunch(request, true);
    
    // After everything is done, update the tracking record with business reference ID
    String requestId = RequestIdInterceptor.getCurrentRequestId();
    if (requestId != null) {
        // RECOMMENDED: Use atomic update to prevent race conditions
        requestTrackingService.completeWithReferenceIdAsync(requestId, response.getAttendanceRequestId());
        
        // ALTERNATIVE: Update reference ID only (if you want to keep completion time separate)
        // requestTrackingService.updateReferenceIdAsync(requestId, response.getAttendanceRequestId());
    }
    
    return success(response);
}
```

### 2. That's it! The system automatically tracks:

- **Request Start Time** - When the method begins
- **Request Completion Time** - When the method finishes
- **Unit ID** - From JWT context
- **Request ID** - From MDC context (RequestIdInterceptor)
- **Error Details** - If the method fails (message + error code)

## What Gets Tracked

| Field | Source | Description |
|-------|--------|-------------|
| `request_id` | MDC Context | Unique request identifier |
| `unit_id` | JWT Context | Unit ID from authentication |
| `api_identifier` | Auto-extracted | API path (e.g., "/api/attendance/punch") |
| `reference_id` | Manual update | Business reference ID (e.g., attendance request ID) |
| `request_time` | Auto-generated | When tracking started |
| `completion_time` | Auto-generated | When method completed |
| `error_message` | Exception | Error message if failed |
| `error_code` | Exception | Exception class name if failed |

## Database Table

```sql
CREATE TABLE request_tracking (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    request_id VARCHAR(50) NOT NULL UNIQUE,
    unit_id INT NOT NULL,
    api_identifier VARCHAR(200) NOT NULL,
    reference_id VARCHAR(100),
    request_time TIMESTAMP NOT NULL,
    completion_time TIMESTAMP NULL,
    error_message TEXT,
    error_code VARCHAR(50),
    updated_at TIMESTAMP NOT NULL
);
```

## Performance Benefits

- **Async Operations** - All tracking happens in background
- **Separate Thread Pool** - Uses `asyncTaskExecutor` 
- **Non-Blocking** - Main request flow continues immediately
- **Graceful Degradation** - Tracking failures don't affect main logic
- **Race Condition Safe** - Uses atomic database updates to prevent data loss

## Race Condition Prevention

The system prevents race conditions between completion time and reference ID updates by using atomic database operations:

### ❌ **Before (Race Condition Risk):**
```java
// These can overwrite each other if called simultaneously
requestTrackingService.markSuccessAsync(requestId);           // Updates completion time
requestTrackingService.updateReferenceIdAsync(requestId, refId); // Updates reference ID
```

### ✅ **After (Race Condition Safe):**
```java
// Single atomic operation - no race condition
requestTrackingService.completeWithReferenceIdAsync(requestId, refId);
```

### **Available Methods:**
- `completeWithReferenceIdAsync()` - **RECOMMENDED** - Sets both completion time and reference ID atomically
- `updateReferenceIdAsync()` - Updates only reference ID (preserves completion time)
- `markSuccessAsync()` - Updates only completion time (preserves reference ID)
- `markFailedAsync()` - Updates completion time and error details (preserves reference ID)

## Example Queries

### Find slow requests:
```sql
SELECT * FROM request_tracking 
WHERE completion_time IS NOT NULL 
AND TIMESTAMPDIFF(MILLISECOND, request_time, completion_time) > 5000;
```

### Find failed requests:
```sql
SELECT * FROM request_tracking 
WHERE error_code IS NOT NULL 
ORDER BY request_time DESC;
```

### Get unit performance:
```sql
SELECT unit_id, 
       COUNT(*) as total_requests,
       COUNT(CASE WHEN error_code IS NULL THEN 1 END) as successful,
       COUNT(CASE WHEN error_code IS NOT NULL THEN 1 END) as failed
FROM request_tracking 
GROUP BY unit_id;
```

### Find requests by business reference ID:
```sql
SELECT * FROM request_tracking 
WHERE reference_id = 'ATT_12345' 
ORDER BY request_time DESC;
```

### Get API performance with reference IDs:
```sql
SELECT api_identifier, 
       COUNT(*) as total_requests,
       COUNT(CASE WHEN error_code IS NULL THEN 1 END) as successful,
       COUNT(CASE WHEN error_code IS NOT NULL THEN 1 END) as failed,
       AVG(TIMESTAMPDIFF(MILLISECOND, request_time, completion_time)) as avg_response_time
FROM request_tracking 
WHERE completion_time IS NOT NULL
GROUP BY api_identifier;
```

## Configuration

The system uses your existing `asyncTaskExecutor` bean. If you don't have one, add this to your config:

```java
@Bean
public ThreadPoolTaskExecutor asyncTaskExecutor() {
    ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
    executor.setCorePoolSize(5);
    executor.setMaxPoolSize(20);
    executor.setQueueCapacity(100);
    executor.setThreadNamePrefix("RequestTracking-");
    executor.initialize();
    return executor;
}
```

## Troubleshooting

### Tracking not working?
1. Check if `RequestIdInterceptor` is setting request ID in MDC
2. Check if `JwtContext` has unit ID
3. Verify AOP is enabled (`@EnableAspectJAutoProxy`)

### Performance issues?
1. Check `asyncTaskExecutor` thread pool size
2. Monitor database performance for the tracking table
3. Consider adding database indexes if needed

## Migration from Old System

If you were using the old attendance-specific tracking:

1. **Keep the old table** - Don't delete it yet
2. **Add `@TrackRequest`** to your APIs
3. **Verify tracking works** with new system
4. **Migrate data** if needed
5. **Remove old code** when ready

## Future Enhancements

- **Custom Metrics** - Track additional fields per request type
- **Alerting** - Notify on high failure rates
- **Dashboard** - Real-time monitoring interface
- **Export** - CSV/Excel reports
