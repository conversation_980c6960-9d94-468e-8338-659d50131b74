# Controller API Documentation

## Overview

This document provides comprehensive documentation for the three main controllers in the Attendance Service:

1. **RosteringController** - Manages employee shift assignments, rostering metadata, and shift-cafe mappings
2. **EmployeeShiftInstancesController** - Handles shift instance creation, recreation, and retrieval
3. **UnitAttendanceAnalyticsController** - Provides analytics and reporting for unit attendance

## Table of Contents

- [RosteringController](#rosteringcontroller)
- [EmployeeShiftInstancesController](#employeeshiftinstancescontroller)
- [UnitAttendanceAnalyticsController](#unitattendanceanalyticscontroller)
- [Common Patterns](#common-patterns)
- [Error Handling](#error-handling)
- [Authentication & Authorization](#authentication--authorization)

---

## RosteringController

### Overview
The `RosteringController` manages employee shift assignments, rostering metadata, and shift-cafe mappings. It provides APIs for creating, updating, and retrieving shift data, employee assignments, and hierarchical employee data.

**Base Path:** `/api/roster`
**Controller Class:** `com.stpl.tech.attendance.controller.RosteringController`

### Dependencies
- `RosteringService` - Main rostering business logic
- `ReladomoService` - Handles bitemporal data operations
- `MetadataService` - Provides metadata for rostering features
- `UnitCacheService` - Caches unit data
- `EmployeeShiftDataCacheService` - Caches employee shift data

### API Endpoints

#### 1. Get Rostering Metadata
```http
GET /api/roster/get-rostering-metadata
```

**Description:** Retrieves metadata flags for rostering features based on the current user's permissions.

**Authentication:** Required (JWT token)

**Response:**
```json
{
  "success": true,
  "data": {
    "featureFlags": {
      "canCreateShifts": true,
      "canAssignEmployees": true,
      "canViewAnalytics": true
    },
    "permissions": {
      "unitId": 123,
      "employeeId": 456
    }
  }
}
```

**Business Logic:**
- Extracts user ID and unit ID from JWT context
- Determines user permissions for rostering features
- Returns feature flags and permission data

#### 2. Get Cafe Live Dashboard with Filters
```http
POST /api/roster/dashboard/roster-live-dashboard
```

**Description:** Retrieves live dashboard data with generic filters for cafe/unit management.

**Request Body:**
```json
{
  "filters": {
    "dateRange": {
      "startDate": "2024-01-01",
      "endDate": "2024-01-31"
    },
    "unitIds": [1, 2, 3],
    "shiftIds": [1, 2, 3],
    "employeeIds": [1, 2, 3]
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "totalEmployees": 50,
    "totalShifts": 10,
    "attendanceRate": 85.5,
    "unitData": [
      {
        "unitId": 1,
        "unitName": "Cafe A",
        "employeeCount": 15,
        "shiftCount": 3
      }
    ]
  }
}
```

#### 3. Get Shift Employees with Filters
```http
POST /api/roster/shifts/shift-employees
```

**Description:** Retrieves employees assigned to shifts with generic filters.

**Request Body:**
```json
{
  "date": "2024-01-15",
  "filters": {
    "unitIds": [1, 2],
    "shiftIds": [1, 2],
    "employeeStatus": "ACTIVE"
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "totalEmployees": 25,
    "shifts": [
      {
        "shiftId": 1,
        "shiftName": "Morning Shift",
        "employees": [
          {
            "employeeId": 1,
            "employeeName": "John Doe",
            "unitId": 1
          }
        ]
      }
    ]
  }
}
```

#### 4. Get Employee Shift Data
```http
GET /api/roster/shifts/emp-shift-data/{empId}?startDate=2024-01-01&endDate=2024-01-31
```

**Description:** Retrieves shift assignments for a specific employee within a date range.

**Path Parameters:**
- `empId` (Integer, required): Employee ID

**Query Parameters:**
- `startDate` (LocalDate, optional): Start date filter
- `endDate` (LocalDate, optional): End date filter

**Response:**
```json
{
  "success": true,
  "data": {
    "employeeId": 1,
    "employeeName": "John Doe",
    "shifts": [
      {
        "shiftId": 1,
        "shiftName": "Morning Shift",
        "startTime": "08:00",
        "endTime": "16:00",
        "date": "2024-01-15"
      }
    ]
  }
}
```

#### 5. Update Employee Shifts
```http
POST /api/roster/shifts/emp-shift-update
```

**Description:** Updates employee shift assignments with support for both immediate and upcoming shift updates.

**Request Body:**
```json
{
  "empId": 1,
  "shiftId": 2,
  "unitId": 1,
  "updateUpcomingShifts": false,
  "effectiveDate": "2024-01-15"
}
```

**Business Logic:**
- If `updateUpcomingShifts` is false: Creates immediate shift mapping override
- If `updateUpcomingShifts` is true: Uses Reladomo service for bitemporal updates
- Sets `updatedBy` field from JWT context

#### 6. Get Cafe Shift Data
```http
GET /api/roster/shifts/cafe-shift-data/{unitId}
```

**Description:** Retrieves shift data for specific cafes/units.

**Path Parameters:**
- `unitId` (Integer, required): Unit ID

**Response:**
```json
{
  "success": true,
  "data": {
    "unitId": 1,
    "unitName": "Cafe A",
    "shifts": [
      {
        "shiftId": 1,
        "shiftName": "Morning Shift",
        "employeeCount": 5,
        "startTime": "08:00",
        "endTime": "16:00"
      }
    ]
  }
}
```

#### 7. Get Hierarchy Employees with Filters
```http
POST /api/roster/employees/get-hierarchy-employees?searchTerm=john&page=0&size=10
```

**Description:** Retrieves employee hierarchy data with generic filters and pagination.

**Request Body:**
```json
{
  "filters": {
    "unitIds": [1, 2],
    "employeeStatus": "ACTIVE",
    "roleIds": [1, 2]
  }
}
```

**Query Parameters:**
- `searchTerm` (String, optional): Search term for employee names
- `page` (int, default: 0): Page number
- `size` (int, default: 10): Page size

**Response:**
```json
{
  "success": true,
  "data": {
    "totalEmployees": 100,
    "page": 0,
    "size": 10,
    "employees": [
      {
        "employeeId": 1,
        "employeeName": "John Doe",
        "unitId": 1,
        "role": "Manager",
        "hierarchyLevel": 1
      }
    ]
  }
}
```

#### 8. Create Shift
```http
POST /api/roster/shifts/create
```

**Description:** Creates a new shift.

**Request Body:**
```json
{
  "shiftName": "Evening Shift",
  "startTime": "16:00",
  "endTime": "00:00",
  "unitId": 1,
  "description": "Evening shift for cafe operations"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "shiftId": 3,
    "shiftName": "Evening Shift",
    "startTime": "16:00",
    "endTime": "00:00",
    "createdBy": "456",
    "createdDate": "2024-01-15T10:30:00"
  }
}
```

#### 9. Update Shift
```http
POST /api/roster/shifts/update
```

**Description:** Updates an existing shift.

**Request Body:**
```json
{
  "shiftId": 3,
  "shiftName": "Evening Shift Updated",
  "startTime": "17:00",
  "endTime": "01:00",
  "unitId": 1,
  "description": "Updated evening shift"
}
```

#### 10. Get All Shifts
```http
GET /api/roster/shifts
```

**Description:** Retrieves all active shifts.

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "shiftId": 1,
      "shiftName": "Morning Shift",
      "startTime": "08:00",
      "endTime": "16:00",
      "isActive": true
    }
  ]
}
```

#### 11. Create Shift-Cafe Mapping
```http
POST /api/roster/shifts/cafe-shift-mapping/create
```

**Description:** Maps a shift to a cafe/unit.

**Request Body:**
```json
{
  "shiftId": 1,
  "unitId": 1,
  "effectiveDate": "2024-01-15"
}
```

#### 12. Update Shift-Cafe Mapping
```http
POST /api/roster/shifts/cafe-shift-mapping/update
```

**Description:** Updates an existing shift-cafe mapping.

#### 13. Get All Units
```http
GET /api/roster/all-units
```

**Description:** Retrieves all units managed by the current user.

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "unitId": 1,
      "unitName": "Cafe A",
      "unitType": "CAFE",
      "isActive": true
    }
  ]
}
```

#### 14. Cache Management APIs

**Refresh Cache for Specific Employee:**
```http
POST /api/roster/cache/refresh/employee/{empId}
```

**Refresh All Cache:**
```http
POST /api/roster/cache/refresh/all
```

---

## EmployeeShiftInstancesController

### Overview
The `EmployeeShiftInstancesController` manages shift instances - the actual scheduled shifts for employees on specific dates. It provides APIs for creating, recreating, and retrieving shift instances with various filtering options.

**Base Path:** `/api/employee-shift-instances`
**Controller Class:** `com.stpl.tech.attendance.controller.EmployeeShiftInstancesController`

### Dependencies
- `EmployeeShiftSchedulerService` - Handles shift instance scheduling and recreation

### API Endpoints

#### 1. Recreate Shift Instances
```http
POST /api/employee-shift-instances/recreate
```

**Description:** Manually recreates shift instances for specific date range and employees.

**Request Body:**
```json
{
  "startDate": "2024-01-01",
  "endDate": "2024-01-31",
  "employeeIds": [1, 2, 3],
  "unitIds": [1, 2],
  "forceRecreation": false
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "totalInstancesCreated": 150,
    "totalEmployees": 3,
    "dateRange": {
      "startDate": "2024-01-01",
      "endDate": "2024-01-31"
    },
    "details": {
      "employeeId": 1,
      "instancesCreated": 50
    }
  }
}
```

**Business Logic:**
- Validates date range and employee/unit IDs
- Recreates shift instances based on employee shift mappings
- Returns detailed statistics of the recreation process

#### 2. Get Shift Instances for Employee
```http
GET /api/employee-shift-instances/employee/{empId}?startDate=2024-01-01&endDate=2024-01-31
```

**Description:** Retrieves shift instances for a specific employee within a date range.

**Path Parameters:**
- `empId` (Integer, required): Employee ID

**Query Parameters:**
- `startDate` (LocalDate, required): Start date
- `endDate` (LocalDate, required): End date

**Response:**
```json
{
  "success": true,
  "data": {
    "employeeId": 1,
    "employeeName": "John Doe",
    "totalInstances": 25,
    "shiftInstances": [
      {
        "instanceId": 1,
        "shiftId": 1,
        "shiftName": "Morning Shift",
        "businessDate": "2024-01-15",
        "startTime": "08:00",
        "endTime": "16:00",
        "status": "SCHEDULED"
      }
    ]
  }
}
```

#### 3. Get Shift Instances for Multiple Employees
```http
POST /api/employee-shift-instances/employees?startDate=2024-01-01&endDate=2024-01-31
```

**Description:** Retrieves shift instances for multiple employees within a date range.

**Request Body:**
```json
[1, 2, 3, 4, 5]
```

**Response:**
```json
{
  "success": true,
  "data": {
    "totalInstances": 125,
    "totalEmployees": 5,
    "employeeData": [
      {
        "employeeId": 1,
        "employeeName": "John Doe",
        "instanceCount": 25,
        "shiftInstances": [...]
      }
    ]
  }
}
```

#### 4. Get Shift Instances for Date
```http
GET /api/employee-shift-instances/date/{businessDate}
```

**Description:** Retrieves all shift instances for a specific date.

**Path Parameters:**
- `businessDate` (LocalDate, required): Business date

**Response:**
```json
{
  "success": true,
  "data": {
    "businessDate": "2024-01-15",
    "totalInstances": 50,
    "unitData": [
      {
        "unitId": 1,
        "unitName": "Cafe A",
        "instanceCount": 25,
        "shiftInstances": [...]
      }
    ]
  }
}
```

#### 5. Get Current Week Shift Instances
```http
GET /api/employee-shift-instances/current-week?empId=1
```

**Description:** Retrieves shift instances for the current week.

**Query Parameters:**
- `empId` (Integer, optional): Filter by specific employee

**Response:**
```json
{
  "success": true,
  "data": {
    "weekStart": "2024-01-15",
    "weekEnd": "2024-01-21",
    "totalInstances": 35,
    "dailyData": [
      {
        "date": "2024-01-15",
        "instanceCount": 5,
        "shiftInstances": [...]
      }
    ]
  }
}
```

#### 6. Get Next Week Shift Instances
```http
GET /api/employee-shift-instances/next-week?empId=1
```

**Description:** Retrieves shift instances for the next week.

#### 7. Get Employee Shift Instances by Month
```http
GET /api/employee-shift-instances/employee/{empId}/month?year=2024&month=1
```

**Description:** Retrieves shift instances for a specific employee by month with detailed attendance information.

**Path Parameters:**
- `empId` (Integer, required): Employee ID

**Query Parameters:**
- `year` (int, required): Year
- `month` (int, required): Month (1-12)

**Response:**
```json
{
  "success": true,
  "data": {
    "employeeId": 1,
    "employeeName": "John Doe",
    "year": 2024,
    "month": 1,
    "totalInstances": 25,
    "attendanceData": [
      {
        "date": "2024-01-15",
        "shiftInstance": {...},
        "attendanceStatus": "PRESENT",
        "punchInTime": "08:05",
        "punchOutTime": "16:00",
        "lateMinutes": 5
      }
    ]
  }
}
```

#### 8. Get Aggregated Employee Shift Instances by Month
```http
GET /api/employee-shift-instances/employee/{empId}/month/aggregated?year=2024&month=1
```

**Description:** Retrieves aggregated shift instances for a specific employee by month with summary statistics.

**Response:**
```json
{
  "success": true,
  "data": {
    "employeeId": 1,
    "employeeName": "John Doe",
    "year": 2024,
    "month": 1,
    "totalDays": 25,
    "totalHours": 200,
    "onTimeDays": 20,
    "lateDays": 3,
    "absentDays": 2,
    "attendancePercentage": 88.0,
    "averageWorkingHours": 8.0,
    "summary": {
      "present": 20,
      "absent": 2,
      "late": 3,
      "halfDay": 0
    }
  }
}
```

---

## UnitAttendanceAnalyticsController

### Overview
The `UnitAttendanceAnalyticsController` provides analytics and reporting for unit attendance. It offers APIs for managers to view attendance analytics across their managed units and for specific units.

**Base Path:** `/api/unit-attendance-analytics`
**Controller Class:** `com.stpl.tech.attendance.controller.UnitAttendanceAnalyticsController`

### Dependencies
- `UnitAttendanceAnalyticsService` - Handles analytics calculations and data aggregation

### API Endpoints

#### 1. Get Manager Unit Analytics
```http
GET /api/unit-attendance-analytics/manager?businessDate=2024-01-15
```

**Description:** Retrieves attendance analytics for all units managed by the current user for a specific date.

**Query Parameters:**
- `businessDate` (LocalDate, required): Business date for analytics

**Response:**
```json
{
  "success": true,
  "data": {
    "managerId": 123,
    "businessDate": "2024-01-15",
    "totalUnits": 5,
    "totalEmployees": 150,
    "overallAttendanceRate": 85.5,
    "units": [
      {
        "unitId": 1,
        "unitName": "Cafe A",
        "totalEmployees": 30,
        "presentEmployees": 25,
        "absentEmployees": 3,
        "lateEmployees": 2,
        "attendanceRate": 83.3,
        "averageWorkingHours": 7.8
      }
    ],
    "summary": {
      "totalPresent": 125,
      "totalAbsent": 15,
      "totalLate": 10,
      "averageWorkingHours": 7.9
    }
  }
}
```

**Business Logic:**
- Extracts manager ID from JWT context
- Retrieves all units managed by the manager
- Calculates attendance statistics for each unit
- Aggregates data across all units

#### 2. Get Unit Analytics
```http
GET /api/unit-attendance-analytics/unit/{unitId}?businessDate=2024-01-15
```

**Description:** Retrieves attendance analytics for a specific unit and date.

**Path Parameters:**
- `unitId` (Integer, required): Unit ID

**Query Parameters:**
- `businessDate` (LocalDate, required): Business date for analytics

**Response:**
```json
{
  "success": true,
  "data": {
    "unitId": 1,
    "unitName": "Cafe A",
    "businessDate": "2024-01-15",
    "totalEmployees": 30,
    "presentEmployees": 25,
    "absentEmployees": 3,
    "lateEmployees": 2,
    "attendanceRate": 83.3,
    "averageWorkingHours": 7.8,
    "employeeDetails": [
      {
        "employeeId": 1,
        "employeeName": "John Doe",
        "attendanceStatus": "PRESENT",
        "punchInTime": "08:05",
        "punchOutTime": "16:00",
        "workingHours": 7.9,
        "lateMinutes": 5
      }
    ],
    "shiftData": [
      {
        "shiftId": 1,
        "shiftName": "Morning Shift",
        "employeeCount": 15,
        "presentCount": 13,
        "attendanceRate": 86.7
      }
    ]
  }
}
```

#### 3. Get Manager Analytics for Today
```http
GET /api/unit-attendance-analytics/manager/today
```

**Description:** Retrieves attendance analytics for all units managed by the current user for today.

**Response:** Same as manager analytics but for current date.

#### 4. Get Manager Analytics for Yesterday
```http
GET /api/unit-attendance-analytics/manager/yesterday
```

**Description:** Retrieves attendance analytics for all units managed by the current user for yesterday.

#### 5. Get Unit Analytics for Today
```http
GET /api/unit-attendance-analytics/unit/{unitId}/today
```

**Description:** Retrieves attendance analytics for a specific unit for today.

#### 6. Get Unit Analytics for Yesterday
```http
GET /api/unit-attendance-analytics/unit/{unitId}/yesterday
```

**Description:** Retrieves attendance analytics for a specific unit for yesterday.

---

## Common Patterns

### Authentication & Authorization
All controllers extend `BaseController` and use JWT authentication:
- User ID and unit ID are extracted from JWT context
- Permissions are validated based on user role and unit access
- Unauthorized requests return 401/403 status codes

### Error Handling
Controllers use consistent error handling patterns:
- Business exceptions are caught and converted to appropriate HTTP status codes
- Validation errors return 400 Bad Request
- Authentication errors return 401 Unauthorized
- Authorization errors return 403 Forbidden
- Server errors return 500 Internal Server Error

### Response Format
All APIs return responses in the following format:
```json
{
  "success": true/false,
  "data": {...},
  "error": {
    "code": "ERROR_CODE",
    "message": "Error description"
  }
}
```

### Logging
Controllers use SLF4J logging with consistent patterns:
- Request parameters are logged at INFO level
- Business operations are logged at INFO level
- Errors are logged at ERROR level with stack traces
- Performance metrics are logged at DEBUG level

### Caching
Controllers use caching for performance optimization:
- Unit data is cached using `UnitCacheService`
- Employee shift data is cached using `EmployeeShiftDataCacheService`
- Cache refresh APIs are provided for data consistency

### Validation
Controllers use Bean Validation annotations:
- `@Valid` for request body validation
- `@PathVariable` for path parameter validation
- `@RequestParam` for query parameter validation
- Custom validators for business rule validation

### Pagination
APIs that return large datasets support pagination:
- `page` parameter for page number (0-based)
- `size` parameter for page size
- Response includes total count and pagination metadata

### Filtering
APIs support generic filtering using `GenericFilterRequestDTO`:
- Date range filters
- Unit ID filters
- Employee ID filters
- Status filters
- Custom business filters

---

## Integration Examples

### Frontend Integration
```javascript
// Get rostering metadata
const metadata = await fetch('/api/roster/get-rostering-metadata', {
  headers: { 'Authorization': `Bearer ${token}` }
});

// Create shift assignment
const assignment = await fetch('/api/roster/shifts/emp-shift-update', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    empId: 1,
    shiftId: 2,
    unitId: 1,
    updateUpcomingShifts: false
  })
});

// Get analytics for today
const analytics = await fetch('/api/unit-attendance-analytics/manager/today', {
  headers: { 'Authorization': `Bearer ${token}` }
});
```

### Mobile App Integration
```java
// Android/Kotlin example
val response = apiService.getEmployeeShiftData(
    employeeId = 123,
    startDate = "2024-01-01",
    endDate = "2024-01-31"
)

// iOS/Swift example
let response = try await apiService.getShiftInstancesForEmployee(
    empId: 123,
    startDate: "2024-01-01",
    endDate: "2024-01-31"
)
```

### Third-party Integration
```python
# Python example
import requests

headers = {'Authorization': f'Bearer {token}'}
response = requests.post(
    'https://api.example.com/api/roster/dashboard/roster-live-dashboard',
    json={'filters': {'unitIds': [1, 2, 3]}},
    headers=headers
)
```

---

## Best Practices

### Performance Optimization
1. Use caching for frequently accessed data
2. Implement pagination for large datasets
3. Use appropriate HTTP methods (GET for read, POST for create/update)
4. Minimize response payload size
5. Use compression for large responses

### Security
1. Always validate user permissions
2. Sanitize input parameters
3. Use HTTPS for all communications
4. Implement rate limiting
5. Log security events

### Monitoring
1. Log all API calls with appropriate levels
2. Monitor response times
3. Track error rates
4. Set up alerts for critical failures
5. Use health check endpoints

### Testing
1. Unit test all business logic
2. Integration test API endpoints
3. Performance test under load
4. Security test for vulnerabilities
5. User acceptance testing

---

## Troubleshooting

### Common Issues

1. **Authentication Errors**
   - Check JWT token validity
   - Verify user permissions
   - Ensure proper authorization headers

2. **Validation Errors**
   - Check request body format
   - Verify required parameters
   - Validate date formats

3. **Cache Issues**
   - Refresh cache using provided APIs
   - Check cache configuration
   - Monitor cache hit rates

4. **Performance Issues**
   - Check database query performance
   - Monitor cache usage
   - Review API response times

### Debug Information
Controllers log detailed information for debugging:
- Request parameters and headers
- Business operation details
- Error stack traces
- Performance metrics

### Support
For technical support:
1. Check application logs
2. Review API documentation
3. Contact development team
4. Check system health endpoints 