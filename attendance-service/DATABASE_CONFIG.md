# Database Connection Pool Configuration

## Overview

The attendance service uses a **hybrid database approach** with different connection pools for different use cases:

1. **Spring Data JPA** - Primary database operations (max pool size: 5)
2. **Reladomo/Mithra** - Bitemporal data operations (max pool size: 10)
3. **MongoDB** - Document storage
4. **H2** - Testing (in-memory)

## Spring Data JPA Connection Pool

### Configuration

The Spring Data JPA connection pool is configured in `DatabaseConfig.java` with the following settings:

```java
@Value("${attendance.jpa.datasource.max-pool-size:5}")
private int maxPoolSize;

@Value("${attendance.jpa.datasource.min-idle:2}")
private int minIdle;

@Value("${attendance.jpa.datasource.connection-timeout:30000}")
private long connectionTimeout;
```

### Default Settings

| Property | Default Value | Description |
|----------|---------------|-------------|
| `max-pool-size` | 5 | Maximum number of connections in the pool |
| `min-idle` | 2 | Minimum number of idle connections |
| `connection-timeout` | 30000ms | Connection acquisition timeout |
| `idle-timeout` | 600000ms (10 min) | Idle connection timeout |
| `max-lifetime` | 1800000ms (30 min) | Maximum connection lifetime |
| `leak-detection-threshold` | 60000ms (1 min) | Connection leak detection |

### Configuration Properties

Add these properties to your configuration server or application properties:

```yaml
attendance:
  jpa:
    datasource:
      max-pool-size: 5
      min-idle: 2
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000
```

## Monitoring

### Endpoints

The service provides monitoring endpoints for the connection pool:

- `GET /api/v1/database/pool-stats` - Detailed pool statistics
- `GET /api/v1/database/pool-status` - Pool status summary
- `GET /api/v1/database/health` - Database connectivity test

### Example Response

```json
{
  "success": true,
  "data": {
    "poolName": "JpaHikariPool",
    "activeConnections": 2,
    "idleConnections": 3,
    "totalConnections": 5,
    "maximumPoolSize": 5,
    "minimumIdle": 2,
    "utilizationPercentage": 40.0
  }
}
```

## Usage

### Spring Data JPA Repositories

All JPA repositories automatically use the configured connection pool:

```java
@Repository
public interface AttendanceRecordRepository extends JpaRepository<AttendanceRecord, Long> {
    // Uses the configured HikariCP pool with max size 5
}
```

### JdbcTemplate

For raw SQL operations:

```java
@Autowired
private JdbcTemplate jdbcTemplate;

// Uses the same connection pool
jdbcTemplate.query("SELECT * FROM attendance_records", ...);
```

## Performance Considerations

### Pool Size Optimization

- **Max Pool Size: 5** - Suitable for moderate load
- **Min Idle: 2** - Ensures minimum connections are always available
- **Connection Timeout: 30s** - Prevents long waits for connections

### Monitoring Recommendations

1. Monitor pool utilization via `/api/v1/database/pool-stats`
2. Set up alerts for high utilization (>80%)
3. Consider increasing pool size if utilization is consistently high
4. Monitor connection leaks via leak detection threshold

### Best Practices

1. **Use Connection Pooling**: All database operations use the configured pool
2. **Monitor Pool Health**: Regular checks via monitoring endpoints
3. **Optimize Queries**: Use proper indexing and query optimization
4. **Handle Exceptions**: Implement proper error handling for database operations
5. **Use Transactions**: Ensure proper transaction management

## Troubleshooting

### Common Issues

1. **Connection Timeout**: Increase `connection-timeout` if connections are slow
2. **Pool Exhaustion**: Increase `max-pool-size` if you see "Connection is not available" errors
3. **Connection Leaks**: Check `leak-detection-threshold` logs for connection leaks
4. **High Utilization**: Monitor pool stats and consider optimization

### Logs to Monitor

```bash
# HikariCP logs
com.zaxxer.hikari

# Hibernate SQL logs
org.hibernate.SQL

# Spring Data JPA logs
org.springframework.orm.jpa
```

## Migration from Auto-Configuration

This configuration replaces Spring Boot's auto-configured DataSource with a custom HikariCP configuration that provides:

- Explicit control over connection pool settings
- Better monitoring capabilities
- Consistent configuration across environments
- Performance optimization for MySQL/MariaDB 