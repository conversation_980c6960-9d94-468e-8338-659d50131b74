package com.stpl.tech.attendance.domain;
import java.sql.Timestamp;
public class ShiftCoveragePlan extends ShiftCoveragePlanAbstract
{
	public ShiftCoveragePlan(Timestamp processingDate
	)
	{
		super(processingDate
		);
		// You must not modify this constructor. <PERSON><PERSON><PERSON> calls this internally.
		// You can call this constructor. You can also add new constructors.
	}

	public ShiftCoveragePlan()
	{
		this(java.sql.Timestamp.valueOf("9999-12-01 23:59:00.000"));
	}
}
