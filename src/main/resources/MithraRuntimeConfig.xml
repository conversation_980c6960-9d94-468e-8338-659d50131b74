<?xml version="1.0" encoding="UTF-8"?>
<MithraRuntime>

    <MithraRuntimeConfiguration>
        <ConnectionManager className="com.stpl.tech.attendance.config.MithraConnectionManager">
            <!-- Use the HikariCP-based connection manager instead of XA -->
            <!-- Connection pool settings are now managed by MithraConnectionManager -->
        </ConnectionManager>
    </MithraRuntimeConfiguration>

    <classpath>
        <include>src/main/resources/*.xml</include>
    </classpath>

    <MithraObjectConfiguration
            className="com.stpl.tech.attendance.domain.EmpShiftMapping"
            cacheType="partial"/>
</MithraRuntime>
